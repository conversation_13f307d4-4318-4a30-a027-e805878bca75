import { reactive } from 'vue'

interface ISatte {
  loading: boolean // 设置全局 loading 状态
}

const state = reactive<ISatte>({
  loading: false
})

export function useGlobalLoading() {
  // 设置滚动元素的组件实例对象
  const setGlobalLoading = (data: any) => {
    state.loading = data
  }

  const getGlobalLoading = () => {
    return state.loading
  }

  return {
    state,
    setGlobalLoading,
    getGlobalLoading
  }
}
