import { MenuData, RouterMenu, RouterType } from 'types'
import { cloneDeep } from 'lodash-es'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

/**
 * 排序name
 */
let sortName: Array<RouterType['name']> = []

/**
 * 当前侧边栏数据
 */
const menuData = ref<Array<MenuData>>([])

const menuDataMap = new Map<string, MenuData>()

/**
 * 菜单操作
 */
export function useMenu() {
  /**
   * 初始化菜单数据
   * @param data
   */
  const initMenuData = (routerList: RouterMenu[]) => {
    menuData.value = convertRouterToMenu(routerList)
    setMenuDataMap(routerList)
  }
  /**
   * 初始化后台获取的排序name
   */
  const initSortName = (arr: Array<RouterType['name']>) => {
    sortName = arr
  }
  /**
   * 当前激活的侧边栏
   */
  const activeMenu = computed<MenuData | undefined>(() => {
    const route = useRoute()
    let data: MenuData | undefined = undefined
    function _cycel(name: string) {
      const _data = menuDataMap.get(name as RouterType['name'])
      if (_data && _data.parent) {
        _cycel(_data.parent)
      } else {
        data = _data
      }
    }
    _cycel(route.name as string)
    return data
  })

  /**
   * 当前激活的面包屑
   */
  const breadcrumbsData = computed<Array<MenuData>>(() => {
    const route = useRoute()
    const data = route.matched.map((v) => menuDataMap.get(v.name as string) as MenuData)

    let lastItem = data[data.length - 1]
    if (lastItem && lastItem.meta.parent) {
      let laset2Item = data[data.length - 2]
      if (laset2Item && laset2Item.name != lastItem.meta.parent && activeMenu.value) {
        data.splice(-1, 0, activeMenu.value)
      }
    }
    return data
  })
  /**
   * 当前所在一级菜单下的所有子路由
   */
  const routerSublevel = computed<Array<MenuData>>(() => {
    const route = useRoute()
    const pattern = /^(\/[^/]+)\/.*/ // 匹配第一个 / 后的内容
    const match = route.path.match(pattern)
    if (match) {
      const result = match[1] // 获取匹配的结果
      const name = route.matched.find((v) => v.path == result)?.name
      if (name) {
        return sortDataSource(cloneDeep(menuDataMap.get(name as string)?.children || []), sortName)
      } else {
        return []
      }
    } else {
      return []
    }
  })

  return {
    menuData,
    breadcrumbsData,
    activeMenu,
    initMenuData,
    initSortName,
    menuDataMap,
    routerSublevel
  }
}

/**
 * 摊平数据 改为map类型
 */
function setMenuDataMap(routerMenus: RouterMenu[]) {
  cloneDeep(routerMenus).forEach((v) => {
    if (v.children && v.children.length) {
      setMenuDataMap(v.children)
    }
    menuDataMap.set(v.name as RouterType['name'], routerMenuMapData(v))
  })
}
/**
 * RouterMenu 映射为 MenuData数据格式
 * @param menu
 * @returns
 */
function routerMenuMapData(menu: RouterMenu): MenuData {
  return {
    name: menu.name as RouterType['name'],
    path: menu.path as RouterType['path'],
    title: menu.meta.title,
    meta: menu.meta,
    icon: menu.meta.icon || '',
    isHide: menu.meta.isHide || false,
    parent: menu.meta.parent || null,
    children: (menu.children || []).map((v) => routerMenuMapData(v)) || null
  }
}

/**
 * 路由转为菜单数据
 * @param routerMenu
 * @param all 是否使用全部数据
 * @returns
 */
function convertRouterToMenu(routerMenus: RouterMenu[], all?: boolean): MenuData[] {
  function _cycel(routerMenus: RouterMenu[]) {
    return routerMenus
      .filter((v) => !v.meta.isHide)
      .map((routerMenu) => {
        const { name, path, meta, children } = routerMenu
        const { title, icon, isHide, parent } = meta

        const menuData: MenuData = {
          name: name as RouterType['name'],
          path: path as RouterType['path'],
          title,
          meta: meta,
          icon: icon || '',
          isHide: isHide || false,
          parent: parent as RouterType['name'],
          children: null
        }

        if (children && children.length > 0) {
          let childrenArr = _cycel(children)
          menuData.children = sortDataSource(cloneDeep(childrenArr), sortName)
        }
        return menuData
      })
  }
  return (_cycel(routerMenus).find((v) => all || (v.name as string) == 'Index') || {}).children || []
}

/**
 * 菜单排序 根据后台返回的菜单列表进行排序
 * @param {MenuData[]} source 菜单数据
 * @param {Array<RouterType['name']>} sortName 后台返回的name集合 排序好的
 * @returns
 */
function sortDataSource(source: MenuData[], sortName: Array<RouterType['name']> = []) {
  let defaultOrder = sortName.length + 1

  source = source.filter((v) => !v.isHide)
  source = source.filter((v) => !v.isHide)
  source.sort((a, b) => {
    let indexA = sortName.indexOf(a.name)
    let indexB = sortName.indexOf(b.name)

    indexA = indexA === -1 ? defaultOrder : indexA
    indexB = indexB === -1 ? defaultOrder : indexB

    return indexA - indexB
  })

  source.forEach((item) => {
    if (item.children && item.children.length > 0) {
      item.children = sortDataSource(
        item.children.filter((v) => !v.isHide),
        sortName
      )
    }
  })
  return source
}
