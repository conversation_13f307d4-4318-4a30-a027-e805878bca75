import { reactive } from 'vue'
import { router } from '@/router'
import { RouteLocationRaw } from 'vue-router'

interface ISatte {
  params: object // 设置全局路由 params
}

const state = reactive<ISatte>({
  params: {}
})

export function useRouterBack() {
  const routerReplace = (route: RouteLocationRaw) => {
    state.params = route.params
    router.replace(route)
  }

  const routerBack = (route: RouteLocationRaw) => {
    state.params = route.params
    router.replace(route)
  }

  const getRoute = () => {
    return state
  }

  const resetRouteParams = () => {
    state.params = {}
  }

  return {
    routeParams: getRoute(),
    routerReplace,
    routerBack,
    resetRouteParams
  }
}
