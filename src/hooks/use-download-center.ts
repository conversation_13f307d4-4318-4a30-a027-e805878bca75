import { message, notification } from 'ant-design-vue'
import { usePagePermission } from './use-page-permission'
import { h } from 'vue'
import { useRouter } from 'vue-router'
import downLoaddatas from '@/layout/template1/components/downLoad/data'
const { setOpen } = downLoaddatas()
export const useDownloadCenter = () => {
  const { isPagePermission } = usePagePermission()
  const router = useRouter()
  const goCenter = () => {
    const key = `open${Date.now()}`
    message.success({
      key,
      content: () => [
        '提交导出任务成功，请前往右上角-',
        h(
          'span',
          {
            style: { color: '#1677ff', cursor: 'pointer' },
            onClick: () => {
              console.log('123123123')
              setOpen()
              // 或者 window.location.href = '/download-center'
            }
          },
          '下载中心'
        ),
        '-,查看导出进度并进行下载'
      ],
      duration: 3
    })
  }
  return {
    goCenter
  }
}
