import { ref, watch, onMounted, nextTick } from 'vue'
import { useLogin } from './use-login'
import { statsApi, messagegListApi } from '@/api/common'

// 定义消息项的类型
interface NewsItem {
  id: number
  title: string
  content: string
  created_at: string
  category: number
  is_read: number
  sub_type?: number
  related_data?: any
  isExpand?: boolean
  isShow?: boolean
}

export function useNewsNotify() {
  const { isLogin } = useLogin()
  const hasUnreadNotices = ref(false)
  const latestUnreadNotice = ref<NewsItem | null>(null)

  // 检查是否有未读更新通知
  const checkUnreadNotices = async () => {
    try {
      const res = await statsApi({})
      const unreadNotices = res.data.unread_notices || 0

      if (unreadNotices > 0) {
        hasUnreadNotices.value = true

        // 获取更新通知列表
        const params = {
          page: 1,
          page_size: 20,
          category: '2', // 更新通知
          sub_type: ''
        }

        const listRes = await messagegListApi(params)
        const result = listRes.data.list || []
        const newsList = result.map((v: any) => ({
          ...v,
          isExpand: false
        }))

        // 找到最新的一条未读更新通知
        const unreadNoticesList = newsList.filter((item: NewsItem) => item.is_read == 1 && item.category == 2)
        if (unreadNoticesList.length > 0) {
          // 按创建时间排序，找到最新的一条
          const latest = unreadNoticesList.sort(
            (a: NewsItem, b: NewsItem) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          )[0]

          latestUnreadNotice.value = latest
        }
      } else {
        hasUnreadNotices.value = false
        latestUnreadNotice.value = null
      }
    } catch (error) {
      console.log('检查未读更新通知失败：', error)
    }
  }

  // 监听登录状态变化
  watch(
    () => isLogin.value,
    (newValue) => {
      if (newValue) {
        // 登录成功后检查未读更新通知
        nextTick(() => {
          setTimeout(() => {
            checkUnreadNotices()
          }, 500) // 延迟0.5秒确保页面完全加载
        })
      }
    },
    { immediate: true }
  )

  // 组件挂载时检查未读更新通知
  onMounted(() => {
    if (isLogin.value) {
      setTimeout(() => {
        checkUnreadNotices()
      }, 500)
    }
  })

  return {
    hasUnreadNotices,
    latestUnreadNotice,
    checkUnreadNotices
  }
}
