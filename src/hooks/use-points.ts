import { computed, ref } from 'vue'
import { getPointBalance as getPointBalanceApi } from '@/api/common'
interface PointData {
  alert: string
  balance: number
}
const pointData = ref<PointData>({
  alert: '',
  balance: 0
})
export function usePoints() {
  const getPointBalance = async () => {
    try {
      let res: any = await getPointBalanceApi()
      pointData.value = res.data
      return Promise.resolve(res.data)
    } catch (err) {
      console.log(err)
    }
  }
  // 是否还有积分
  const hasPoints = computed(() => pointData.value?.balance >= 0)
  return {
    pointData,
    getPointBalance,
    hasPoints
  }
}
