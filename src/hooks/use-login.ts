import { localStg } from '@/utils'
import { useApp } from './use-app'
import { useRouter } from './use-router'
import { ref, h } from 'vue'
import { RouterMenu, RouterType, UserInfo } from 'types'
import { router } from '@/router'
import { authRouter } from '@/router/helpers'
import { useMenu } from './use-menu'
import { getRoleMenuList } from '@/api/common'
import { getRequestReouter } from '@/router/disponneRouter'
import { Router } from 'vue-router'
import { useAuth } from './use-auth'
import { loginOutApi } from '@/views/login/index/index.api'
import { isFunction } from 'lodash-es'
import { Modal } from 'ant-design-vue'
type loginParams = { userInfo: UserInfo; token: string } | { token: string }

/**是否登录 */
const isLogin = ref<boolean>(false)
let forceLogoutModal = ref<any>()
/**登录操作 */
export function useLogin() {
  const { useInfo } = useApp()
  /**
   *登录
   * @param param
   * @returns {boolean}
   */
  const login = async (params: loginParams): Promise<boolean> => {
    try {
      let token = ''
      if ('userInfo' in params && params.userInfo) {
        // 登录页使用
        useInfo.value = params.userInfo
        token = params.token
        localStg.set('token', token)
        localStg.set('userInfo', params.userInfo)
      } else if (params.token) {
        // 使用token 去获取用户信息 请求菜单列表  常用于自动登录
        token = params.token
        localStg.set('token', token)
        useInfo.value = localStg.get('userInfo')
      } else {
        console.log('登录失败')
        isLogin.value = false
        return false
      }

      if (!isLogin.value) {
        await getAuthRouter()
      }

      isLogin.value = true

      if (window.__wxobs__ && window.__wxobs__.identify && isFunction(window.__wxobs__.identify) && useInfo.value) {
        window.__wxobs__.identify({
          account: useInfo.value.account || '',
          name: useInfo.value.name || '',
          realname: useInfo.value.realname || '',
          phone: useInfo.value.phone || '',
          useragent: navigator.userAgent || ''
        })
      }
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 退出登录
   */
  const logout = async () => {
    // 获取重定向地址
    const redirect = localStg.get('redirect')
    const query = {}
    if (!['null', null, 'undefined', undefined, ''].includes(redirect)) query.redirect = redirect
    var { routerPush } = useRouter()
    try {
      let res = await loginOutApi()
      isLogin.value = false
      useInfo.value = undefined
      localStg.clear()
      routerPush({ name: 'Login' })
    } catch (error) {
      isLogin.value = false
      useInfo.value = undefined
      localStg.clear()
      routerPush({ name: 'Login' })
      // console.log(error, '*-*-*-*')
    }
  }

  /**
   * 设置菜单 刷新token 刷新菜单权限列表，刷新路由权限，返回首页
   */
  const setMenuInfo = async () => {
    try {
      await getAuthRouter()
      return true
    } catch {
      return false
    }
  }
  // 强制下线/被迫下线提示弹窗
  const forceLogout = async (data) => {
    console.log("localStg.get('isForceLogout')", localStg.get('isForceLogout'))
    if (forceLogoutModal.value) return
    forceLogoutModal.value = await Modal.info({
      title: '登录异常',
      width: '482px',
      icon: null,
      centered: true,
      content: h('div', {}, [
        h(
          'div',
          { style: { color: '#313233', 'line-height': '20px', 'padding-top': '8px' } },
          '您的账号在其他地方登录，请重新登录。非本人登录，建议修改密码。'
        ),
        h(
          'div',
          { style: { color: '#fe9d35', 'line-height': '17px', 'padding-top': '8px' } },
          `详情：${localStg.get('isForceLogout') || data}`
        )
      ]),
      okText: '重新登录',
      class: 'force-logout',
      async onOk() {
        console.log('ok')
        // modal.destroy()
        // await logout()
        localStg.remove('isForceLogout')
        // return Promise.reject('error')
        forceLogoutModal.value.destroy()
      }
    })
    console.log('forceLogoutModal', forceLogoutModal.value)
  }
  return {
    isLogin,
    login,
    logout,
    setMenuInfo,
    forceLogout
  }
}
function formatFiledData(node: any) {
  const { initFiledsNames } = useAuth()
  if (node.slug === 'customer_service_group') {
    initFiledsNames(node.fields)
  }

  if (node.children) {
    node.children.forEach((child) => {
      formatFiledData(child)
    })
  }
  if (Array.isArray(node)) {
    node.forEach((item) => {
      formatFiledData(item)
    })
  }
}
/**
 * 获取角色菜单
 */
const getAuthRouter = async () => {
  const { initMenuData, initSortName } = useMenu()
  const { initBtnNams } = useAuth()
  let { data } = await getRoleMenuList()
  formatFiledData(data)
  let { names, requestRouterMap, btnNames } = getRequestReouter(data)
  initSortName(names as Array<RouterType['name']>)
  initBtnNams(btnNames)
  const routerList = authRouter(names as Array<RouterType['name']>, router, requestRouterMap)

  // 设置默认重定向
  // if (names[0] && requestRouterMap.get(names[0])) {
  //   console.log(requestRouterMap.get(names[0]))
  //   let oneRouter = requestRouterMap.get(names[0])
  // }
  setBaseRouterRedirect(router, names, requestRouterMap)

  initMenuData(routerList as Array<RouterMenu>)
}

/**
 * 设置index默认调整的路径
 */

function setBaseRouterRedirect(router: Router, names: Array<string>, requestRouterMap: Map<string, any>) {
  let url: string = ''
  for (let i = 0; i < names.length; i++) {
    let item = requestRouterMap.get(names[i])
    if (item.isHide) {
      url = item.path
      break
    }
  }
  if (url) {
    let indexRouter = router.getRoutes().find((v) => v.name == 'Index')
    if (indexRouter) {
      indexRouter.redirect = '/' + url
    }
  }
}
