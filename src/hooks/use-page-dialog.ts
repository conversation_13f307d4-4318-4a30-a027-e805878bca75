import { ref } from 'vue'
let component = ref(null)
let open = ref(false)
let options = ref(null)
export function usePageDialog() {
  const setComponent = (com: any) => {
    component.value = com
  }
  const setOpen = (bol) => {
    open.value = bol
  }
  const setOptions = (data) => {
    options.value = data
  }

  return {
    open,
    component,
    options,
    setComponent,
    setOpen,
    setOptions
  }
}
