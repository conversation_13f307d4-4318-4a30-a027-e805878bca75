import { onUnmounted } from 'vue'
import { localStg } from '@/utils'

export const useTabsKeys = () => {
  const initTabKeys = 'initKey'
  const initTabsKeys = (list) => {
    let key = list[0]
    if (localStg.get(initTabKeys)) {
      const oldKey = localStg.get(initTabKeys)
      key = list.includes(oldKey) ? oldKey : list[0]
    }
    return key
  }
  const setInitKey = (val) => {
    localStg.set(initTabKeys, val)
  }
  onUnmounted(() => {
    localStg.remove(initTabKeys)
  })
  return {
    initTabsKeys,
    setInitKey
  }
}
const getCurrUrl = (val, LinkCharacter) => {
  return `${window.location.href}${LinkCharacter}${val}`
}
