import { getThemeVar, cssVarAction, baseCssVarAction, setCssVariables } from '@/theme'
import { RecursivePartial, Setting, ThemeConfig } from 'types'
import { computed, ref, toRef } from 'vue'
import { debounce, cloneDeep } from 'lodash-es'

const themeConfig = toRef<Setting>({
  uiComponents: 'element',
  mode: 'around',
  pageAnimation: 'fade-slide',
  layout: {
    headerHeight: 0,
    sidebarWidth: 0,
    isTag: false,
    tagHeight: undefined,
    minSidebarWidth: 0,
    foldSidebarWidth: 0
  },
  fontSize: 0,
  borderRadius: 0,
  height: 0,
  colors: {
    primary: '',
    info: '',
    success: '',
    warning: '',
    error: '',
    textColor: ''
  },
  isMobileWidth: 0,
  lineHeight: 0,
  space: 16,
  env: {},
  files: []
})

/**小型侧边栏允许固定 */
const isMinSidebarFixed = ref(false)

/**侧边栏是否固定 */
const isSidebarFixed = ref(false)

/**侧边栏是否打开 */
export const isSidebarFold = ref(true)
/**
 * 手机端侧边栏是否打开
 */
const isMobileSidebarFold = ref(false)

/**主题 */
const themeVar = ref<Record<string, any>>({})

/**主题框架模式 */
const mode = toRef<ThemeConfig['mode']>('around')

/**
 * 初始化主题
 */
export function initTheme(_themeConfig: Setting) {
  // TODO 还需要添加其它组件库的css action
  themeVar.value = getThemeVar(baseCssVarAction(_themeConfig))
  console.log('%c通用变量useTheme->themeVar的key值：', 'color: blue', cloneDeep(themeVar.value))
  mode.value = _themeConfig.mode
  themeConfig.value = cloneDeep(_themeConfig)
}

export function useTheme() {
  /**主题配色 */
  const themeOverrides = computed(() => {
    return {
      common: {
        ...getThemeVar(cssVarAction(themeConfig.value))
      }
    }
  })

  /**
   * 设置主题色
   */
  const setTheme = debounce((config: RecursivePartial<ThemeConfig>) => {
    themeConfig.value = deepMerge(themeConfig.value, config)
    setThemeVarColor()
  }, 100)

  /**设置变量 */
  const setThemeVarColor = (config?: ThemeConfig) => {
    config = config ? config : (themeConfig.value as ThemeConfig)
    setCssVariables(getThemeVar(cssVarAction(config)))
  }

  /**
   * mode == minSidebar 小型侧边栏设置侧边栏固定
   */
  const setMinSidebarFixed = (bol: boolean) => {
    if (mode.value == 'minSidebar') {
      isMinSidebarFixed.value = bol
    } else {
      isMinSidebarFixed.value = false
    }
    checkMode('minSidebar')
  }

  /**
   * 是否折叠侧边栏
   * @param bol
   */
  const checkSidebarFold = (bol: boolean) => {
    if (['minSidebar', 'top'].includes(mode.value)) {
      isSidebarFixed.value = false
      return
    }
    isMinSidebarFixed.value = false
    isSidebarFixed.value = bol
    isMobileSidebarFold.value = bol
    if (isSidebarFixed.value) {
      isSidebarFold.value = true
    } else {
      isSidebarFold.value = false
    }
    setThemeVarColor()
  }

  /**
   * 切换模式
   */
  const checkMode = (_mode: ThemeConfig['mode']) => {
    if (['around', 'toAndDown', 'sidebarFullHeader'].includes(_mode)) {
      isSidebarFold.value = true
      isMinSidebarFixed.value = false
    } else if (_mode == 'minSidebar') {
      if (isMinSidebarFixed.value) {
        isSidebarFold.value = true
      } else {
        isSidebarFold.value = false
      }
    } else if (_mode == 'top') {
      isSidebarFold.value = true
    } else {
      isSidebarFold.value = true
    }
    themeConfig.value.mode = _mode
    mode.value = _mode
    isSidebarFixed.value = false
    setThemeVarColor()
  }

  /**
   * 是否为分离菜单模式，是的情况下，路由要改变
   */
  const isSplitSidebar = computed(() => mode.value == 'sidebarFullHeader')

  return {
    mode,
    initTheme,
    checkMode,
    isSplitSidebar,
    setMinSidebarFixed,
    checkSidebarFold,
    themeOverrides,
    setThemeVarColor,
    setTheme,
    themeConfig,
    themeVar,
    isSidebarFold,
    isMobileSidebarFold
  }
}
/**
 * 深层次替换对象的值
 * @param target
 * @param source
 * @returns
 */
function deepMerge(target: any, source: any) {
  // 检查如果 source 不是一个对象，则直接返回 target
  if (typeof source !== 'object' || source === null) {
    return target
  }

  // 遍历 source 的所有属性
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      // 如果 source 的属性是一个对象，则递归调用 deepMerge
      if (typeof source[key] === 'object' && source[key] !== null) {
        // 如果 target[key] 不存在或者不是一个对象，则将其初始化为空对象
        if (!target[key] || typeof target[key] !== 'object') {
          target[key] = {}
        }
        // 递归调用 deepMerge
        deepMerge(target[key], source[key])
      } else {
        // 如果 source 的属性是基本类型，则直接赋值给 target
        target[key] = source[key]
      }
    }
  }

  return target
}
