import { isArray, isString } from 'lodash-es'

/**
 * 用户按钮权限组
 */
let btnNames: Map<string, { title: string }> = new Map()
/**
 * 客服组字段权限
 */
let filedsNames: [string] = []

/**'
 * 权限hook
 */
export function useAuth() {
  /**
   * 判断是否有权限
   */
  const isAuth = (value: any) => {
    let isAuthArr = []
    let bol = true
    if (isArray(value)) {
      isAuthArr.push(...value)
    } else if (isString(value)) {
      isAuthArr.push(value)
    }
    if (!isAuthArr.some((v) => btnNames.has(v))) {
      bol = false
    }
    return bol
  }

  /**
   * 初始化btn按钮组所有权限
   */
  const initBtnNams = (map: Map<string, { title: string }>) => {
    btnNames = map
  }
  const initFiledsNames = (names: [string]) => (filedsNames = names)

  return {
    btnNames,
    filedsNames,
    initFiledsNames,
    initBtnNams,
    isAuth
  }
}
