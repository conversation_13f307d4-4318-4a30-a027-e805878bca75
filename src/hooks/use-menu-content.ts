import { ref } from 'vue'
import { getServiceStatData } from '@/api/common'

import { localStg } from '@/utils'
import { debounce } from 'lodash-es'
const timer = ref(true)
const status = ref(false)
const number = ref(0)
const dataInfo = ref(null)
const getData = debounce(async () => {
  try {
    // if (timer.value) {
    if (!localStg.get('token')) return
    let { data } = await getServiceStatData()
    let _score = data.score
    let arr = []
    for (const key in _score) {
      if (_score[key] && _score[key] != 5) {
        arr.push(_score[key])
      }
    }
    status.value = arr.length > 0 ? true : false
    timer.value = false
    //   setTimeout(() => {
    //     timer.value = true
    //   }, 1500)
    // }
  } catch (error) {
    console.error(error)
  }
}, 1300)

export function useMenuContentData() {
  return {
    getData,
    number,
    dataInfo,
    status
  }
}
