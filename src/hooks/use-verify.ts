import { nextTick, ref } from 'vue'
import { debounce } from 'lodash-es'
import { message } from 'ant-design-vue'
// 安全校验
export const useVerify = (domId: string, slidingId: string, reloadFun: Function) => {
  /**
   * 点击密钥
   */
  const nvc = ref('')
  let isRun = false
  // 是否出现滑条
  const isSider = ref(false)
  let btn: HTMLElement | null

  /**
   * 延迟到获取完点击密钥在执行
   */
  const delay = () => {
    let num = 0
    return new Promise((reslove, reject) => {
      const timer = setInterval(() => {
        num++
        if (isRun) {
          reslove(true)
          clearInterval(timer)
        } else if (num > (1000 / 100) * 3) {
          clearInterval(timer)
          reject({ msg: '获取按钮密钥失败' })
        }
      }, 100)
    })
  }

  // 绑定事件
  // nextTick(() => {
  //   btn = document.getElementById(domId)
  //   if (btn) btn.onclick = debounce(() => onclick(), 100)
  // })

  const onclick = async () => {
    isRun = false
    // 每次点击初始化
    await initNvc(
      async (data: any) => {
        nvc.value = data
        isRun = true
        await reloadFun.apply(reloadFun)
        removeSider()
        isRun = false
      },
      () => {
        message.warning('操作频繁')
      }
    )
    // window.nvc.getNVCValAsync(function (nvcVal: string) {
    //   console.log('nvcVal', nvcVal)
    //   nvc.value = nvcVal
    //   isRun = true
    // })
  }
  // 清理滑动条
  const removeSider = () => {
    if (slidingId) {
      isSider.value = false
      const slidingDom = document.getElementById(slidingId)
      if (slidingDom) slidingDom.innerHTML = ''
    }
  }

  // 校验是否通过
  const isNvcPass = (json: any) => {
    if (!json.data) return true
    if (json.code === 100 || json.code === 200 || json.code === 0) {
      return true
    } else if (json.code === 800 || json.code === 900) {
      message.warning('操作频繁')
      return false
    } else if (json.code === 40003) {
      removeSider()
      // window.nvc.getNC({
      //   renderTo: slidingId
      // })
      isSider.value = true
      return false
    }
  }

  // 初始化配置
  function initNvc(successCallBack: Function, errorCallBack: Function) {
    return new Promise((reslove) => {
      window.initAliyunCaptcha({
        // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
        SceneId: '4htbo6hw',
        // 验证码模式，popup表示弹出式，embed表示嵌入式。无需修改
        mode: 'embed',
        // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
        element: `#${slidingId}`,
        // 触发验证码弹窗或无痕验证的元素
        button: `#${domId}`,
        success: function (captchaVerifyParam: any) {
          console.log(captchaVerifyParam, '验证成功')
          successCallBack(captchaVerifyParam)
        },
        fail: function (err) {
          console.log('验证失败', err)
          removeSider()
          isSider.value = false
          errorCallBack()
        },
        getInstance: function (instance: any) {
          console.log(instance, 'getInstance')
        },
        slideStyle: {
          width: 360,
          height: 40
        }
      })
      reslove(true)
    })
  }

  // 初始化配置
  // function initNvc(successCallBack: Function, errorCallBack: Function) {
  //   return new Promise((reslove) => {
  //     AWSC.use('nvc', function (state: any, module: any) {
  //       window.nvc = module.init({
  //         appkey: 'FFFF0N0000000000B7C2',
  //         scene: 'nvc_other',
  //         success(data: any) {
  //           successCallBack(data)
  //         },
  //         // 前端二次验证失败时触发该回调参数
  //         fail() {
  //           removeSider()
  //           isSider.value = false
  //           errorCallBack()
  //         }
  //       })
  //       reslove(true)
  //     })
  //   })
  // }
  return {
    nvc,
    delay,
    isSider,
    isNvcPass,
    onclick
  }
}
