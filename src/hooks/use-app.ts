// 存储项目中需要的状态

import { UserInfo } from 'types'
import { ref, nextTick, computed } from 'vue'
import { useTheme } from './use-theme'

/**
 * 重载页面
 */
const reloadFlag = ref<boolean>(false)

/**用户信息 */
const useInfo = ref<UserInfo>()

// 监听屏幕改变
const width = ref(1920)
function onResize() {
  width.value = window?.innerWidth
}

/**
 * 缓存数据
 */
const keepAliveList = ref<Array<Array<string>>>([])

function isMobileDevice() {
  const userAgent = navigator.userAgent
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
}

/**
 * 系统状态信息
 */
export function useApp() {
  onResize()
  window?.addEventListener('resize', onResize)

  /**是否为手机端  手机端应该隐藏侧边栏*/
  const isMobile = computed(() => {
    // let width = window.innerWidth
    const { themeConfig } = useTheme()
    return width.value <= themeConfig.value.isMobileWidth || isMobileDevice()
  })

  /**
   * 重载页面
   * @param duration - 重载的延迟时间(ms)
   */
  const reloadPage = async (duration = 0) => {
    reloadFlag.value = false
    await nextTick()
    if (duration) {
      setTimeout(() => {
        reloadFlag.value = true
      }, duration)
    } else {
      reloadFlag.value = true
    }
    setTimeout(() => {
      document.documentElement.scrollTo({ left: 0, top: 0 })
    }, 100)
  }

  /**
   * 设置页面缓存
   * TODO 以后要设置页面的标签页 需要改成多个的
   */
  const setKeepAlive = (names: Array<string>) => {
    keepAliveList.value = [names]
  }

  return {
    isMobile,
    useInfo,

    reloadFlag,
    reloadPage,

    keepAliveList,
    setKeepAlive
  }
}
