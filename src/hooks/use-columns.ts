import { save_report_column, get_report_column_list } from '@/api/common'
const tableColumnsData = {}
let fetchCacheData: any = null

function diffList(list = [], originList = []) {
  // 根据本地列列表，比对服务器返回的，补充缺失的
  ;(originList || []).forEach((v, i) => {
    if (!list.find((g) => g.dataIndex == v.dataIndex)) {
      list.splice(i, 0, {
        ...v,
        check: true
      })
    }
  })
  // 根据本地返回 ，比对服务器返回的，删除服务器返回多余的，本地的覆盖测试的
  ;(list || []).forEach((v, i) => {
    let item = originList.find((g) => g.dataIndex == v.dataIndex)
    if (!item) {
      list.splice(i, 1)
    } else {
      list.splice(i, 1, {
        ...item,
        check: v.check
      })
    }
  })
  return list
}

export const useColumns = () => {
  const setColumnsKey = (url, user, data, list, type) => {
    tableColumnsData[`${url.fullPath}_${user.id}`] = data
    tableColumnsData[`list${url.fullPath}_${user.id}`] = list
    let newObj = {}
    newObj[`${url.fullPath}_${user.id}`] = tableColumnsData[`${url.fullPath}_${user.id}`]
    newObj[`list${url.fullPath}_${user.id}`] = tableColumnsData[`list${url.fullPath}_${user.id}`]
    const params = {
      url: url.fullPath,
      content: JSON.stringify(newObj)
    }
    if (type) {
      save_report_column(params).then((res) => {
        iniiColumnData()
      })
    }
  }
  const iniiColumnData = async () => {
    const { data } = await get_report_column_list()
    fetchCacheData = { data }
  }
  const getColumnsKey = async (url, user, originList = []) => {
    try {
      const { data } = fetchCacheData || (await get_report_column_list())
      fetchCacheData = { data }

      let tableColumnsData = {}
      if (data.length) {
        const foundItem = data.find((item) => item.url === url.fullPath)
        if (foundItem && foundItem.content) {
          tableColumnsData = JSON.parse(foundItem.content)
        }
      }

      return diffList(tableColumnsData[`${url.fullPath}_${user.id}`] || [], originList)
    } catch (error) {
      console.error('Error:', error)
    }
  }
  const getColumnsList = async (url, user, originList = []) => {
    const { data } = fetchCacheData || (await get_report_column_list())
    fetchCacheData = { data }
    let tableColumnsData = {}
    if (data.length) {
      const foundItem = data.find((item) => item.url === url.fullPath)
      if (foundItem && foundItem.content) {
        tableColumnsData = JSON.parse(foundItem.content)
      }
    }

    return diffList(tableColumnsData[`list${url.fullPath}_${user.id}`] || [], originList)
  }
  return {
    setColumnsKey,
    getColumnsKey,
    getColumnsList,
    iniiColumnData
  }
}
