import { RouterMap, RouterMenu, RouterType } from 'types'
import { router } from '@/router'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { message } from 'ant-design-vue'

interface PushParams {
  name?: RouterType['name']
  path?: RouterType['path']
  query?: Record<string, any>
}

/**
 * 路由列表，根路由数据
 */
const routerList = ref<Array<RouterMenu>>([])

/**
 * 路由 name映射
 */
let routerMap: Map<string, any> = new Map()

export function useRouter() {
  /**
   * 路由跳转
   */
  const routerPush = (params: PushParams) => {
    // 跳转的name不存在的时候 判断path是否存在 存在跳转
    if (!params.name && params.path) {
      router.push(params)
      return
    }
    if (isRoute(params.name)) {
      router.push(params)
    } else {
      return message.warning('暂无权限，请联系管理员')
    }
  }

  /**
   * 打开新标签页
   */
  const routerResolve = (params: PushParams) => {
    // 跳转的name不存在的时候 判断path是否存在 存在跳转
    if (!params.name && params.path) {
      const resolved = router.resolve(params)
      window.open(resolved.href, '_blank')
      return
    }
    if (isRoute(params.name)) {
      const resolved = router.resolve(params)
      window.open(resolved.href, '_blank')
    } else {
      return message.warning('暂无权限，请联系管理员')
    }
  }

  /**
   * 不保留历史记录的路由跳转
   */
  const routerReplace = (params: PushParams) => {
    router.replace(params)
  }

  /**
   * 跳转到首页
   */
  const routerToBase = () => {
    console.log(router, '你到底是什么')

    router.push({ path: '/' })
  }

  /**
   * 设置根路由路由列表
   */
  const setRouterList = (list: Array<RouterMenu>) => {
    routerList.value = cloneDeep(list) || []
  }

  /**
   * 设置路由name映射关系
   */
  const setRouterMap = (map: RouterMap) => {
    routerMap = map
  }

  /**
   * 判断是否存在当前路由
   */
  const isRoute = (name: any) => {
    return router.hasRoute(name)
  }

  return {
    routerPush,
    routerReplace,
    routerList,
    setRouterList,
    routerToBase,
    setRouterMap,
    routerMap,
    isRoute,
    routerResolve
  }
}
