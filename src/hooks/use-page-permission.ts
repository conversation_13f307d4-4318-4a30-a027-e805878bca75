import { useMenu } from './use-menu'

export const usePagePermission = () => {
  // name 是路由的name
  const isPagePermission = (name) => {
    const { menuDataMap } = useMenu()
    const list = menuDataMap.keys()
    // const list =  menuDataNames(menuData.value)
    if ([...list].includes(name)) {
      return true
    } else {
      return false
    }
  }
  return {
    isPagePermission
  }
}

function menuDataNames(data = [], arr = []) {
  for (let item of data) {
    arr.push(item.name)
    if (item.children && item.children.length) menuDataNames(item.children, arr)
  }
  return arr
}
