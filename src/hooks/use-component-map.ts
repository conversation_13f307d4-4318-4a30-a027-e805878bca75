import { UI } from 'types'
import { dashToCamelCase } from '@/utils'
import { isFunction, isObject, isString } from 'lodash-es'
import { Component, h, getCurrentInstance, ComponentInternalInstance } from 'vue'

/**
 * 处理props 映射
 * @param mapProps  组件库拿到传递过来的props如何做映射
 * @param ctx 当前组件实例
 * @returns
 */
const disponeProps = (
  // TODO类型有问题 后期修改
  // mapProps: UI.UiCompMap['props'],
  mapProps: any,
  ctx: ComponentInternalInstance
) => {
  const returnProps: any = {}
  const props = ctx.props
  const originProps = ctx.type.props
  for (const key in originProps) {
    if (isString(mapProps[key])) {
      returnProps[mapProps[key]] = props[dashToCamelCase(key)]
    } else if (isObject(mapProps[key]) && !mapProps[key].dispone) {
      returnProps[mapProps[key]['map']] = props[dashToCamelCase(key)]
    } else if (isObject(mapProps[key]) && isFunction(mapProps[key].dispone)) {
      returnProps[mapProps[key]['map']] = mapProps[key].dispone(props[dashToCamelCase(key)])
    }
  }
  return returnProps
}

/**
 * 处理emits
 * @param mapEvents 组件库映射方法
 * @param ctx 当前组件实例
 * @returns
 */
const disponeEvents = (
  // TODO类型有问题，后期修改
  mapEvents: any,
  ctx: ComponentInternalInstance
) => {
  const returnEmits: any = {}
  const originEmits = ctx.type.emits

  for (const key in originEmits) {
    const mapKey = dashToCamelCase('on-' + key)
    const mapMapKey = dashToCamelCase('on-' + mapEvents[key]['map'])
    if (isString(mapEvents[key])) {
      returnEmits[mapKey] = (...args: any) => {
        ctx.emit(key, ...args)
      }
    } else if (isObject(mapEvents[key]) && !mapEvents[key].dispone) {
      returnEmits[mapMapKey] = (...args: any) => {
        ctx.emit(key, ...args)
      }
    } else if (isObject(mapEvents[key]) && isFunction(mapEvents[key].dispone)) {
      returnEmits[mapMapKey] = (...args: any) => {
        ctx.emit(key, mapEvents[key].dispone(...args))
      }
    }
  }
  return returnEmits
}

/**
 * 处理插槽
 * TODO这个地方需要加个判断，如果组件内没有这个插槽的情况下，要提示出来
 * @param mapSlots
 * @param ctx
 * @returns
 */
const disponeSlots = (
  // TODO类型有问题，后期修改
  mapSlots: any,
  ctx: ComponentInternalInstance
) => {
  const returnSlots: any = {}
  const ctxSlots = ctx?.slots
  const ctxSlotsKeys = Object.keys(ctxSlots)
  const noSlots = ctxSlotsKeys.filter((v) => !Object.keys(mapSlots).includes(v))
  if (noSlots.length)
    console.error(
      `${ctx.type.name} 组件上不存在这些插槽：${noSlots.toString()}\n 请前往src/types/components.d.ts里面添加`
    )

  for (const key in ctxSlots) {
    const slotsFun = ctxSlots[key] as Function
    if (isString(mapSlots[key])) {
      returnSlots[mapSlots[key]] = (...args: any) => slotsFun(args)
    } else if (isObject(mapSlots[key]) && !mapSlots[key].dispone) {
      returnSlots[mapSlots[key]['map']] = (...args: any) => slotsFun(args)
    } else if (isObject(mapSlots[key]) && isFunction(mapSlots[key].dispone)) {
      returnSlots[mapSlots[key]['map']] = (...args: any) => slotsFun(mapSlots[key].dispone(args[0]))
    }
  }

  // 需要加上 不然如果为空的情况下会报错 必须要有一个default
  if (!returnSlots.default) {
    returnSlots.default = () => ''
  }

  return returnSlots
}

/**
 * 映射指定props，event，slots属性
 **/
export function useComponentMap<T extends UI.UiCompMap>(component: { comp: Component; map: T }) {
  const ctx = getCurrentInstance() as ComponentInternalInstance

  const ctxAttrs = ctx.attrs
  const noProps = Object.keys(ctxAttrs).filter((v) => !['class', 'style'].includes(v))
  if (noProps.length)
    console.error(
      `${ctx.type.name} 组件上不存在这些属性：${noProps.toString()}\n 请前往src/types/components.d.ts里面添加`
    )

  const props = disponeProps(component.map.props, ctx)

  const events = disponeEvents(component.map.events, ctx)

  const slots = disponeSlots(component.map.slots, ctx)

  return () => h(component.comp, { ...props, ...events }, { ...slots })
}
