import { message } from 'ant-design-vue'
import { usePagePermission } from './use-page-permission'
import { useRouter } from 'vue-router'

export const usePageDetails = () => {
  const { isPagePermission } = usePagePermission()
  const router = useRouter()
  const goDetailsPage = (name, query) => {
    if (isPagePermission(name)) {
      router.push({ name, query: { ...query } })
    } else {
      message.warning('暂无配置权限，请联系账号管理员')
    }
  }
  return {
    goDetailsPage
  }
}
