import '@/assets/css/global.scss'
import 'virtual:uno.css'
import 'virtual:svg-icons-register'
import { setupRouter } from '@/router'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'
import { setupTheme } from './theme'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
// import 'default-passive-events'
import { Modal } from 'ant-design-vue'
import AppLoading from '@/components/ui/common/AppLoading/index.vue'

import NgElement from '@ng_visualization/plugins/index.js'
import '@ng_visualization/style/index.scss'
import 'vant/lib/index.css'
import { setupVant } from '@ng_visualization/vant/index.js'

import { themeUi } from './components'
import '@/utils/rem.js'

import Directive from '@/plugins/Directive'
const pinia = createPinia()
async function setupApp() {
  const appLoading = createApp(AppLoading)
  Modal.props.centered.default = true
  appLoading.use(Antd)
  appLoading.mount('#appLoading')

  const app = createApp(App)
  for (let i in Directive) {
    app.directive(i, Directive[i])
  }
  setupVant(app)
  app.use(Antd).use(NgElement).use(pinia)
  app.use(themeUi)
  await setupTheme()
  await setupRouter(app)
  appLoading.unmount()
  app.mount('#app')
}
setupApp()
