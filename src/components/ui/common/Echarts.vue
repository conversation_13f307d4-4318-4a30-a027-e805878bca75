<template>
  <div>
    <div :id="id" :style="{ width: '100%', height: parentHeight }"></div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts'
  import { watch, onUnmounted } from 'vue'

  // const props = defineProps(['id', 'data','parentHeight'])
  const props = defineProps({
    id: {
      type: String,
      default: 'defaultId' // 设置 id 的默认值为 'defaultId'
    },
    data: {
      type: Object,
      default: {} // 设置 data 的默认值为一个空数组
    },
    parentHeight: {
      type: String,
      default: '400px' // 设置 parentHeight 的默认值为 '100px'
    }
  })
  const emits = defineEmits(['echartClick', 'legendselectchanged'])
  let myChart = null

  const initEcharts = (id) => {
    if (!props.id) return
    // 基于准备好的dom，初始化echarts实例
    myChart = echarts.init(document.getElementById(props.id))
    // 绘制图表
    myChart.setOption(props.data)
    window.addEventListener('resize', function () {
      myChart.resize()
    })
    myChart.on('click', (params) => {
      emits('echartClick', params)
    })
    myChart.on('legendselectchanged', (params) => {
      emits('legendselectchanged', params)
    })
  }

  onUnmounted(() => {
    myChart && myChart.clear && myChart.clear()
  })

  watch(
    () => props.data,
    (newVal) => {
      if (props.id) {
        setTimeout(() => {
          initEcharts(props.id)
        }, 10)
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
