<template>
  <template v-if="data">
    <EyeOutlined class="pl-1 cursor-pointer" @click="getPhone(0)" v-if="data[showKey] === 1" />
    <SvgIcon v-else class="pl-1 cursor-pointer" icon="eye" @click="getPhone(1)" />
  </template>
</template>

<script setup>
  import { EyeOutlined } from '@ant-design/icons-vue'
  import { change_phone } from '@/api/common'
  const props = defineProps(['data', 'showKey', 'goalkey', 'type', 'argKey'])
  const emits = defineEmits(['update:data'])

  const interview = (arr, data) => {
    if (typeof arr === 'String') {
      return data[arr]
    }
    let curr = data
    for (let i = 0; i < arr.length; i++) {
      if (Object.prototype.toString.call(curr[arr[i]]) === '[object Object]') {
        curr = curr[arr[i]]
      } else {
        curr = curr[arr[i]]
        break
      }
    }
    return curr
  }
  const setValue = (path, obj, value) => {
    if (!obj || !path || path.length === 0) {
      return obj
    }
    let current = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!current[key]) {
        current[key] = {}
      }
      current = current[key]
    }
    current[path[path.length - 1]] = value
    return obj
  }
  // 获取手机号
  const getPhone = async (status) => {
    let argKeyStr = ''
    if (typeof props.argKey === 'string') {
      argKeyStr = props.data[props.argKey]
    } else if (Array.isArray(props.argKey)) {
      argKeyStr = interview(props.argKey, props.data)
    }
    try {
      let result = await change_phone({
        type: props.type,
        id: argKeyStr,
        is_show: status
      })

      if (typeof props.goalkey === 'string') {
        emits('update:data', { ...props.data, [props.goalkey]: result.data.phone, [props.showKey]: status })
      } else if (Array.isArray(props.goalkey)) {
        const resutData = setValue(props.goalkey, props.data, result.data.phone)
        emits('update:data', { ...props.data, ...resutData, [props.showKey]: status })
      }
    } catch (error) {
      console.error(error)
    }
  }
</script>

<style scoped></style>
