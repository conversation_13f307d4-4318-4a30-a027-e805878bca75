<template>
  <div class="comp_select_item" :class="[isMobile && 'flex-wrap']">
    <!-- 自定义插槽 -->
    <template v-if="slotsArr.length">
      <div
        v-for="{ component, item } in slotsArr"
        class="slotItem"
        :class="isCurrent(item?.value) ? 'active' : ''"
        @click="select(item)"
      >
        <component :is="component" :current="isCurrent(item?.value)" :item="item"></component>
      </div>
    </template>
    <!-- 默认选择 -->
    <template v-else-if="['radio', 'check'].includes(props.type)">
      <a-space class="flex flex-wrap" :class="{ checkbox_btn_group: props.type == 'radio' }">
        <div
          :class="{
            active: isCurrent(item.value),
            checkbox_btn_item: props.type == 'radio'
          }"
          v-for="item in preOptions"
          :key="item.id"
        >
          <a-checkbox
            class="checkbox_btn"
            :class="{
              hide: !isCurrent(item.value)
            }"
            :disabled="Boolean(item.disabled) || item.value === isDisabledValue"
            :checked="isCurrent(item.value)"
            @click="select(item)"
            >{{ item.label }}
          </a-checkbox>
        </div>
      </a-space>
    </template>
    <template v-else>
      <a-button
        class="item"
        v-for="(item, index) in preOptions"
        @click="select(item)"
        ghost
        :disabled="Boolean(item.disabled) || item.value === isDisabledValue"
        :style="{ color: Boolean(item.disabled) || item.value === isDisabledValue ? '#e8e8e8' : '' }"
        :class="[isCurrent(item.value) ? 'active' : '', { readonly: readonly }]"
        :type="isCurrent(item.value) ? 'primary' : ''"
      >
        <template v-if="multiple && index != 0 && index !== preOptions.length - 1">
          <!-- <img src="./images/select_1.png" class="icon" v-show="!isCurrent(item.value) || isGray" />
          <img src="./images/select_2.png" class="icon" v-show="isCurrent(item.value) && !isGray" /> -->
          <a-checkbox :checked="isCurrent(item.value)" class="mr-6px"></a-checkbox>
        </template>
        {{ item.label }}
      </a-button>
    </template>
  </div>
</template>
<script setup lang="ts">
  import { useApp } from '@/hooks'
  import { Item, selectItemProps } from './type'
  import { isArray, isNull, isObject } from 'lodash-es'
  import { computed, onMounted, useSlots, ref, type Component } from 'vue'
  defineOptions({ name: 'SelectItem' })
  const slotsArr = ref<Array<{ component: Component; item: Item }>>([])
  const { isMobile } = useApp()
  const emits = defineEmits(['update:modelValue', 'update:item'])
  const props = defineProps(selectItemProps)
  /**
   * 判断当前为选中的
   * @param value
   */
  const isCurrent = (value: any) => {
    if (isArray(props.modelValue)) {
      return props.modelValue.includes(value)
    } else {
      return props.modelValue == value
    }
  }

  //   处理options
  const preOptions = computed(() => {
    if (props.options && isArray(props.options)) {
      return props.options.map((v) => {
        return {
          label: v[props.fieldNames.label],
          value: v[props.fieldNames.value],
          ...v
        }
      })
    } else if (props.options && isObject(props.options)) {
      return Object.keys(props.options).map((key: string) => {
        return {
          label: (props.options as any)[key],
          value: key
        }
      })
    }
  })

  /**
   * 选择触发事件
   * @param item {Item}
   */
  const select = (item: Item) => {
    if (item) {
      // 多选操作
      if (props.multiple) {
        let arr = props.modelValue && isArray(props.modelValue) ? props.modelValue : [props.modelValue]
        arr = arr.filter((v) => v)
        let index = arr.indexOf(item.value)
        // 处理自定义
        if (item.value === 'selfDefine') {
          arr = ['selfDefine']
        }
        if (item.value !== 'selfDefine') {
          let l = arr.indexOf('selfDefine')
          if (l !== -1) {
            arr.splice(l, 1)
          }
          if (index == -1) {
            arr.push(item.value)
          } else {
            arr.splice(index, 1)
          }
        }

        // 当有默认值的时候
        if (expressNull(item[props.fieldNames.value as keyof Item]) == expressNull(props.defaultValue) || !arr.length) {
          arr = [props.defaultValue]
        } else {
          let index = arr.findIndex((value) => expressNull(value) == expressNull(props.defaultValue))
          if (index != -1) arr.splice(index, 1)
        }

        emits('update:modelValue', arr && arr.length == 1 && isNull(arr[0]) ? null : arr)
        emits(
          'update:item',
          arr.map((v) => preOptions.value?.find((g) => expressNull(g.value) == expressNull(v)))
        )
      } else {
        if (item.disabled) {
          return false
        }
        emits('update:modelValue', item.value)
        emits('update:item', item)
      }
    }
  }

  const expressNull = (value: any) => {
    return value || null
  }

  /**
   * 初始化选中项
   */
  const initSelect = () => {
    if (props.defaultValue) {
      select(preOptions.value?.find((g) => g.value == props.defaultValue) || null)
    }
  }

  //   初始化slots
  function initSlots() {
    const slots = useSlots()
    if (slots.default) {
      slots.default().forEach((v) => {
        slotsArr.value.push({ component: v, item: preOptions.value?.find((g) => g.value == v.props?.name) || null })
      })
    }
  }

  onMounted(() => {
    initSlots()
    initSelect()
  })
</script>
<style lang="scss" scoped>
  .comp_select_item {
    display: flex;
    .ant-btn + .ant-btn {
      margin-left: 0;
      border-left: none;
    }
    .slotItem {
      border-radius: 2px;
      font-size: 14px;
      border: 1px solid #dfe0e2;
      transition: border-color 0.2s;
      &.active {
        border: 1px solid var(--primary-color);
      }
    }
    .item {
      // min-width: 72px;
      height: 34px;
      padding: 4px 16px;
      border-radius: 0;
      line-height: 1;
      font-size: 14px;
      cursor: pointer;
      border: 1px solid #dfe0e2;
      transition: border-color 0.2s;
      &:first-child {
        border-radius: 4px 0px 0px 4px;
      }
      &:last-child {
        border-radius: 0px 4px 4px 0px;
      }
      &.active {
        border: 1px solid var(--primary-color);
        border-left: 1px solid var(--primary-color);
        & + .active {
          border-left: none;
        }
      }
      &.readonly {
        pointer-events: none;
      }
      .icon {
        position: absolute;
        bottom: 0;
        right: -1px;
        width: 17px;
        height: 16px;
      }
    }
    .checkbox_btn_group {
      border-radius: 20px;
      border: 1px solid #e2e5ea;
      display: flex;
      align-items: center;
      padding: 4px;

      .checkbox_btn_item {
        padding: 6px 16px;
        &.active {
          background-color: #e5f0ff;
          border-radius: 16px;
        }
        .checkbox_btn {
          :deep(.ant-checkbox-checked),
          :deep(.ant-checkbox-input),
          :deep(.ant-checkbox-inner) {
            border-radius: 50%;
            overflow: hidden;
          }
          &.hide {
            :deep(.ant-checkbox) {
              width: 0 !important;
              overflow: hidden;
            }
          }
        }
      }
    }
  }

  .isDisabledClass {
    color: #999 !important;
  }
</style>
