import { ExtractPropTypes, PropType } from 'vue'

export const selectItemProps = {
  /**
   * 类型 radio 可选 展示风格
   */
  type: {
    type: String,
    default: ''
  },
  /**
   * 配置项
   */
  options: {
    type: [Array, Object],
    default: () => []
  },
  /**
   * 映射关系
   */
  fieldNames: {
    type: Object,
    default: () => ({ label: 'label', value: 'value' })
  },
  /**
   * 双向数据绑定
   */
  modelValue: {
    type: [String, Number, Array, null],
    default: ''
  },
  /**
   * 当前选中的item,多选的时候为数组
   */
  item: {
    type: [Object, Array],
    default: () => {}
  },
  /**
   * 默认选中的属性，当它选中的时候，其它全部不选中
   */
  defaultValue: {
    type: [String, Number, null],
    default: ''
  },
  /**
   * 多选
   **/
  multiple: {
    type: Boolean,
    default: false
  },
  /**
   * 多选
   **/
  isDisabledValue: {
    type: [String, Number],
    default: ''
  },
  /**
   * 是否只读
   **/
  readonly: {
    type: Boolean,
    default: false
  },
  /**
   * 是否默认灰色
   **/
  isGray: {
    type: Boolean,
    default: false
  }
}
export declare type SelectItemProps = ExtractPropTypes<typeof selectItemProps>

export const selectItemItemProps = {
  /**
   * 对应父级的value
   */
  name: {
    type: String,
    default: ''
  },
  /**
   * 当前item
   */
  item: {
    type: Object as PropType<Item>,
    default: () => {}
  },
  /**
   * 是否为当前选中的
   */
  current: {
    type: [String, Number, Array, Boolean],
    default: ''
  }
}
export declare type SelectItemItemProps = ExtractPropTypes<typeof selectItemItemProps>

export type Item = { label: string; value: string | number; disabled: boolean } | null
