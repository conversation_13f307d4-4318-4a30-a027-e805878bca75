<template>
  <div class="comp_text_flod" :style="{ ...style }" :class="{ open: data.preValue }">
    <div class="text_box" :ref="textBox">
      <slot></slot>
    </div>
    <div class="fun_btn" @click="operate" v-if="data.showText">
      <span v-show="!data.preValue">...</span>
      <span class="text"> {{ data.preValue ? '折叠' : '展开' }}</span>
    </div>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'TextFlod'
  }
</script>
<script setup lang="ts">
  import { computed, onMounted, reactive } from 'vue'
  import { textFlodProps } from './types'

  const porps = defineProps(textFlodProps)
  const data = reactive({
    preValue: true,
    showText: true, // 是否显示折叠文字
    el: null
  })
  const textBox = (el) => {
    if (el) {
      data.el = el
    }
  }

  const height = computed(() => {
    return Number(porps.fontSize) * Number(porps.lineHeight) * Number(porps.row)
  })
  const style = computed(() => {
    return {
      fontSize: Number(porps.fontSize) + 'px',
      lineHeight: Number(porps.lineHeight),
      height: height.value + 'px'
    }
  })
  const operate = () => {
    data.preValue = !data.preValue
  }

  onMounted(() => {
    if (data.el.clientHeight > height.value) {
      data.preValue = false
      data.showText = true
    } else {
      data.preValue = false
      data.showText = false
    }
  })
</script>
<style lang="scss" scoped>
  .comp_text_flod {
    overflow: hidden;
    position: relative;
    &.open {
      height: auto !important;
      &::after {
        content: '';
        display: inline;
        width: 30px;
      }
    }
    .fun_btn {
      position: absolute;
      bottom: 0;
      right: 0;
      background-color: #fff;
      cursor: pointer;
      .text {
        color: var(--primary-color);
      }
    }
  }
</style>
