<template>
  <div class="site-input-group-wrapper">
    <a-input-group compact class="flex">
      <a-input-number
        v-model:value="state.startNum"
        class="site-input-left start_number"
        :precision="0"
        :min="0"
        :max="state.endNum || 10000"
        :placeholder="startPlaceholder"
        @blur="emitData"
      />
      <a-input class="site-input-split" placeholder="~" disabled />
      <a-input-number
        v-model:value="state.endNum"
        class="site-input-right end_number"
        :precision="0"
        :min="state.startNum"
        :max="10000"
        :placeholder="endPlaceholder"
        @blur="emitData"
      />
    </a-input-group>
  </div>
</template>

<script setup>
  import { reactive, watch } from 'vue'

  const props = defineProps({
    modelValue: {
      type: [Array, String],
      default: () => []
    },
    startPlaceholder: {
      type: String,
      default: '请输入'
    },
    endPlaceholder: {
      type: String,
      default: '请输入'
    }
  })
  const emit = defineEmits(['update:modelValue', 'change'])

  const state = reactive({
    startNum: '',
    endNum: ''
  })

  watch(
    () => props.modelValue,
    () => {
      initData()
    },
    { immediate: true, deep: true }
  )
  // 初始化数据
  function initData() {
    if (props.modelValue?.length) {
      state.startNum = props.modelValue[0]
      state.endNum = props.modelValue[1]
    } else {
      state.startNum = undefined
      state.endNum = undefined
    }
  }
  // 向上传递数据
  function emitData() {
    const data = {
      start: state.startNum,
      end: state.endNum
    }
    emit('update:modelValue', [state.startNum, state.endNum])
    emit('change', data)
  }
</script>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }
  .start_number,
  .end_number {
    flex: 1;
    text-align: center;
  }
  .site-input-group-wrapper .site-input-split {
    background-color: #fff;
    width: 30px;
    border-left: 0;
    pointer-events: none;
  }
  .site-input-group-wrapper .site-input-right {
    border-left-width: 0;
  }

  .site-input-group-wrapper .site-input-right:hover,
  .site-input-group-wrapper .site-input-right:focus {
    border-left-width: 1px;
  }

  .site-input-group-wrapper .ant-input-rtl.site-input-right {
    border-right-width: 0;
  }

  .site-input-group-wrapper .ant-input-rtl.site-input-right:hover,
  .site-input-group-wrapper .ant-input-rtl.site-input-right:focus {
    border-right-width: 1px;
  }
  [data-theme='dark'] .site-input-group-wrapper .site-input-split {
    background-color: transparent;
  }
</style>
