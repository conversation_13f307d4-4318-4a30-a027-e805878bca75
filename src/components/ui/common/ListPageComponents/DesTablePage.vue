<template>
  <a-card class="common_page_warp" :bordered="false" :headStyle="{ border: 'none' }">
    <template #title>
      <div class="h-34px line-height-34px" v-if="slots.tableWarp && slots.title">
        <slot name="title"></slot>
      </div>

      <!-- <div v-else></div> -->
    </template>
    <template #extra>
      <slot name="extra"></slot>
    </template>
    <slot name="search"> </slot>
    <slot name="action"> </slot>
    <div class="mt-16px" v-if="slots.tableWarp">
      <slot name="tableWarp"></slot>
    </div>
    <slot name="pagination" v-if="pagination">
      <div class="flex w-full flex-justify-end">
        <a-pagination v-bind="pagination" @change="changePages" />
      </div>
    </slot>
  </a-card>
</template>

<script setup lang="ts">
  import { useSlots } from 'vue'

  defineProps(['pagination'])
  const emits = defineEmits(['changePages'])
  const slots = useSlots()

  const changePages = (page, pageSize) => {
    emits('changePages', { page, pageSize })
  }
</script>

<style scoped lang="scss">
  .hyd {
    :deep(.ant-card-head-wrapper) {
      .ant-card-head-title {
        display: flex;
        align-items: center;
        // margin-left: 11px;
      }
      .ant-card-head-title::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 18px;
        border-radius: 2px;
        background: var(--primary-color); /* 线的颜色 */
        margin-right: 8px;
      }
    }
  }
</style>
