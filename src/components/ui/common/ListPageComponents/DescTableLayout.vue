<template>
  <div class="desc-table-layout common_page_table">
    <slot name="action"></slot>
    <a-spin :spinning="data.isLoading">
      <div class="desc-table-layout-list">
        <a-empty class="no-data" :image="Empty.PRESENTED_IMAGE_SIMPLE" v-if="!data.list || !data.list.length" />
        <template v-else>
          <template v-if="data.dataSourceKey">
            <div class="desc-table-layout-list-item" v-for="(item, index) in data.list">
              <slot name="desc" :data="item"></slot>
              <a-table v-bind="data" bordered :data-source="item[data.dataSourceKey]" :pagination="false">
                <template v-slot:[key]="scope" v-for="key in Object.keys(slots)">
                  <slot :name="key" :scope="scope" :data="item" :pindex="index"></slot>
                </template>
              </a-table>
              <slot name="mask" :data="item"></slot>
            </div>
          </template>
          <template v-else>
            <div
              :class="[
                'desc-table-layout-list-item',
                Object.keys(slots).includes('desc') ? '' : 'table-base-layout-no-top-border'
              ]"
              v-for="(item, index) in data.list"
            >
              <slot name="desc" :data="item"></slot>
              <a-table v-bind="data" bordered :data-source="datalist[index]" :pagination="false">
                <template v-slot:[key]="scope" v-for="key in Object.keys(slots)">
                  <slot :name="key" :scope="scope" :data="item" :pindex="index"></slot>
                </template>
              </a-table>
              <slot name="mask" :data="item"></slot>
            </div>
          </template>
        </template>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { Empty } from 'ant-design-vue'
  import { computed, useSlots } from 'vue'
  const props = defineProps(['data'])
  const slots = useSlots()
  const datalist = computed(() => {
    let arr = []
    if (props.data.list.length) {
      props.data.list.forEach((item) => {
        arr.push([item])
      })
    }
    return arr
  })
</script>

<style scoped lang="scss">
  .desc-table-layout {
    width: 100%;
    overflow-x: auto;
  }
  .desc-table-layout-list {
    width: 100%;
    min-width: 1500px;
  }
  .no-data {
    width: 100%;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .desc-table-layout-list-item {
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius-medium);
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 0px !important;
    }
    :deep(.ant-table-wrapper .ant-table-container table > tbody > tr:first-child > *:last-child) {
      border-inline-end: none;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child) {
      border-inline-end: none;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container) {
      border-inline-start: none;
      border-inline-end: none;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-bottom: none;
      vertical-align: top;
    }
    :deep(.ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-tbody > tr > td.ant-table-cell-row-hover) {
      background: #fff !important;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child) {
      border-start-start-radius: 0px;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child) {
      border-start-end-radius: 0px;
    }
    :deep(.ant-table-wrapper .ant-table-container) {
      border-start-start-radius: 0px;
      border-start-end-radius: 0px;
    }
    :deep(.ant-table-wrapper table) {
      border-radius: 0px !important;
    }
    :deep(.ant-table-thead > tr > th) {
      background: transparent;
      border-radius: 0px;
      color: var(--text-color-gray);
      font-weight: normal;
    }
    :deep(.ant-table-thead > tr > th::before) {
      background-color: transparent;
      border-radius: 0px;
    }

    :deep(.ant-table-thead > tr > th) {
      &.ant-table-cell-fix-right {
        background-color: #fff;
      }
    }
    :deep(.ant-table-tbody > tr > td) {
      &.ant-table-cell-fix-right {
        background-color: #fff;
      }
    }
  }
  .table-base-layout-no-top-border {
    border-top: none;
  }
</style>
