<template>
  <a-card>
    <a-row class="desc-card">
      <template v-for="item in data">
        <a-col v-bind="item.layout" class="pb-10">
          <div class="flex pb-4">
            <div class="desc-card-title">{{ item.title }}</div>
            <slot name="suffix" :data="item"></slot>
          </div>
          <a-space direction="vertical">
            <slot v-if="!item.noData" :name="item.key" :data="item"></slot>
            <div v-else class="desc-card-no-data-text">{{ item.noData }}</div>
          </a-space>
        </a-col>
      </template>
    </a-row>
  </a-card>
</template>

<script setup lang="ts">
  defineProps(['data'])
</script>

<style scoped lang="scss">
  @import '../../../../assets/css/mixin_scss_fn';
  .desc-card-title {
    @include set_font_config(--font-size-huge, --text-color-base);
    font-weight: bold;
  }
  .desc-card-no-data-text {
    @include set_font_config(--font-size, --text-color-gray);
  }
</style>
