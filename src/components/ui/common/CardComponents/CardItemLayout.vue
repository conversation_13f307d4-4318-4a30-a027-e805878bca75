<!-- @Auther: 杨鹏飞-->
<!-- @Date: 2023/10/10 -10 - 10 -1:54 下午-->
<!-- @Description: -->

<template>
  <div class="card-item-layout">
    <div class="card-item-layout-icon" v-if="Object.keys(slots).includes('icon')">
      <slot name="icon"></slot>
    </div>
    <a-card class="card-item-layout-card" size="small" :headStyle="headStyle">
      <template #title>
        <slot name="title">12</slot>
      </template>
      <template #extra>
        <slot name="extra">123</slot>
      </template>
      <slot></slot>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { useSlots } from 'vue'

  defineProps(['headStyle'])
  const slots = useSlots()
</script>

<style scoped lang="scss">
  .card-item-layout {
    display: flex;
  }
  .card-item-layout-icon {
    padding-right: 20px;
  }
  .card-item-layout-card {
    flex: 1;
  }
</style>
