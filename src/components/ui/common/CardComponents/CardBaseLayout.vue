<!-- @Auther: 杨鹏飞-->
<!-- @Date: 2023/10/10 -10 - 10 -1:50 下午-->
<!-- @Description: -->

<template>
  <a-card class="CardLayout common_page_warp" :bodyStyle="bodyStyle" :bordered="false" :headStyle="{ border: 'none' }">
    <template #title>
      <slot name="title"></slot>
      <!-- <div v-else></div> -->
    </template>
    <template #extra>
      <slot name="extra"></slot>
    </template>
    <slot name="content"></slot>
  </a-card>
</template>

<script setup lang="ts">
  defineProps(['bodyStyle'])
  import { getConfig } from '@/utils'
</script>

<style scoped></style>
