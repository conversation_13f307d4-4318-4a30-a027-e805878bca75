<template>
  <a-modal v-model:open="pageVisible" width="80%" centered :footer="false">
    <div class="page-details-warp">
      <component :is="CurrentCompoent[currName]" :pagedetail="detail"></component>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { useDetailsModal } from '@/hooks'
  const { CurrentCompoent } = useDetailsModal()

  const props = defineProps({
    currName: {
      type: String,
      default: ''
    },
    detail: {
      type: Object,
      default: {}
    },
    visible: {
      type: Boolean,
      default: false
    },
    handleOk: {
      type: Function, //成功回调
      default: null
    },
    remove: {
      type: Function, //传入移除节点方法,这里是createApp中的方法
      default: null
    }
  })
  const pageVisible = ref(false)
  pageVisible.value = props.visible

  // 监听显示的消失，需要移除dom
  watch(
    () => pageVisible.value,
    (val) => {
      !val && props.remove()
    }
  )
</script>
<style scoped lang="scss">
  .page-details-warp {
    max-height: 500px;
    overflow: auto;
  }
</style>
