import { createApp } from 'vue'
import DetailsModalCom from './index.vue'
import Antd from 'ant-design-vue'
export default function DetailsModalFun(options) {
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)
  const app = createApp(DetailsModalCom, {
    ...options,
    visible: true,
    remove() {
      app.unmount(mountNode)
      document.body.removeChild(mountNode)
    }
  })
  app.use(Antd)
  return app.mount(mountNode)
}
