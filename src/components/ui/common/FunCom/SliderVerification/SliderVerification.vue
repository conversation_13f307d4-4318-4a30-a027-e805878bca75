<template>
  <Modal
    v-model:open="pageVisible"
    centered
    title="短信验证"
    @ok="_sure"
    @cancel="pageVisible = false"
    okText="确定"
    cancelText="取消"
  >
    <Form :model="state.formState" name="VerifyPhone" :label-col="{ span: 4 }" autocomplete="off" ref="VerifyPhoneForm">
      <FormItem>
        <Alert message="为保证数据安全，验证后方可查看数据" type="error" />
      </FormItem>
      <FormItem label="手机号">
        <span>{{ phone }}</span>
        <Button class="ml-8" type="primary" :disabled="state.countdown !== 60" @click="getCode">{{
          state.btntext
        }}</Button>
        <!--        <Button v-else class="ml-8" type="primary" >再次获取验证码({{ state.countdown }}s)</Button>-->
      </FormItem>
      <FormItem label="验证码" name="code" :rules="[{ required: true, message: '请输入验证码' }]">
        <div class="flex">
          <Tooltip placement="top" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title> <div>验证码可从已认证过的邮箱及短信内查看</div> </template>
            <QuestionCircleFilled class="font-size-12px c-#C5C6CC mr-4px!" />
          </Tooltip>
          <Input v-model:value="state.formState.code" placeholder="请输入验证码" />
        </div>
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup>
  import { onMounted, reactive, ref, watch } from 'vue'
  import { Modal, Form, FormItem, Input, Alert, Button, Tooltip, message } from 'ant-design-vue'
  import { localStg } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { set_Captcha } from '@/api/common'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    handleOk: {
      type: Function, //成功回调
      default: null
    },
    remove: {
      type: Function, //传入移除节点方法,这里是createApp中的方法
      default: null
    }
  })

  const pageVisible = ref(false)
  const VerifyPhoneForm = ref(null)
  const phone = ref(null)
  const timer = ref(null)
  pageVisible.value = props.visible

  onMounted(() => {
    phone.value = localStg.get('userInfo').phone
    state.countdown = localStg.get('countdown') ? Number(localStg.get('countdown')) : 60
    if (localStg.get('countdown')) {
      countdownTimer()
    }
  })

  const state = reactive({
    countdown: 60,
    btntext: '获取验证码',
    formState: {
      code: undefined
    }
  })

  // 监听显示的消失，需要移除dom
  watch(
    () => pageVisible.value,
    (val) => {
      !val && props.remove()
      timer.value && clearTimeout(timer.value)
    }
  )

  // 确认
  const _sure = async () => {
    const values = await VerifyPhoneForm.value.validate()
    if (typeof props.handleOk === 'function') {
      const result = await props.handleOk(values)
      if (!result) {
        pageVisible.value = false
      }
    }
  }

  const getCode = async () => {
    const result = await set_Captcha({
      phone: localStg.get('userInfo').phone,
      tp: 'get_order_phone'
    })
    if (result.code === 0) {
      countdownTimer()
    } else {
      message.warning(result.msg)
    }
  }
  const countdownTimer = () => {
    if (state.countdown === 0) {
      state.countdown = 60
      state.btntext = `获取验证码`
      localStg.remove('countdown')
    } else {
      state.countdown--
      localStg.set('countdown', state.countdown)
      state.btntext = `重新发送(${state.countdown})s`
      timer.value = setTimeout(function () {
        countdownTimer()
      }, 1000)
    }
  }
</script>
