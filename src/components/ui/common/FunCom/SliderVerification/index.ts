import { createApp } from 'vue'
import SliderVerificationCom from './SliderVerification.vue'
export default function SliderVerification(options) {
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)
  const app = createApp(SliderVerificationCom, {
    ...options,
    visible: true,
    remove() {
      app.unmount(mountNode) //创建完后要进行销毁
      document.body.removeChild(mountNode)
    }
  })
  return app.mount(mountNode)
}
