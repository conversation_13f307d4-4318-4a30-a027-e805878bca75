import { ExtractPropTypes, PropType } from 'vue'

type a = {
  url: string
  name?: string
  fileName?: string
  file?: File
  video?: { isVideo: boolean; src: string; cover: string }
}

export const uploadProps = {
  modelValue: {
    type: [Array as PropType<a[]>, String, Number],
    deflaut: () => []
  },
  type: {
    type: String,
    deflaut: 'box'
  },
  // 文件类型
  accept: {
    type: String,
    default: '*'
  },
  // 列表长度
  max: {
    type: [String, Number],
    default: 999
  },
  /**
   * 限制文件长宽高
   */
  wh: {
    type: String,
    default: ''
  },
  //文件大小 m
  size: {
    type: [String, Number],
    default: '-1'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 水印
  water: {
    type: Boolean,
    default: false
  },
  /**
   * 是否要拖拽
   */
  directory: {
    type: Boolean,
    default: false
  },
  /**
   * 存放到那个目录
   * 支持对象 {image:"",video:""} || ''
   */
  use: {
    type: [String, Object],
    default: 'goods'
  },
  // 文件上传前操作return false
  beforeUpload: {
    type: Function,
    default: (file) => {
      return file
    }
  },
  // 是否运行多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 文件预览 image-viewer 是否插入 body
  previewTeleported: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 105
  },
  height: {
    type: Number,
    default: 105
  },
  preview: {
    type: Boolean,
    default: true
  },
  from: {
    type: String,
    default: true
  },
  // 混合类型，如视频+图片
  // 使用方式：
  // :mixedType="{
  //    image: {size: 2},
  //    video: {size: 2,duration: 10},
  // }"
  mixedType: {
    type: Object,
    default: () => {
      return {}
    }
  }
}

export declare type UploadProps = ExtractPropTypes<typeof uploadProps>
