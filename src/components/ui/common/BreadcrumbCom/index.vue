<template>
  <div class="breadcrum-wrapper" :class="[!pointData.alert && !isMobile && 'mt--15px!']" v-if="!route.query?.dialog">
    <div class="flex-y-center" v-for="(item, index) in data">
      <div class="flex-y-center" :class="[item.pathname && 'item-hover cursor-pointer']" @click="eventClick(item)">
        <img :src="requireImg('breadCrumBack.png')" class="h-16px w-16px mr-8px" v-show="index === 0" />
        <span class="title">{{ item.title }}</span>
      </div>

      <RightOutlined class="font-size-12px c-#979797 ml-4px mr-4px" v-show="data.length - 1 > index" />
    </div>
  </div>
</template>

<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { onMounted } from 'vue'
  import { RightOutlined } from '@ant-design/icons-vue'
  import { requireImg } from '@/utils'
  import { usePoints, useApp } from '@/hooks'
  const { pointData } = usePoints()
  const router = useRouter()
  const route = useRoute()
  const { isMobile } = useApp()
  defineProps(['data'])

  const eventClick = (item) => {
    if (item.pathname) {
      router.push({
        name: item.pathname
      })
    }
  }
</script>

<style scoped lang="scss">
  .breadcrum-wrapper {
    height: 34px;
    background: #ffffff;
    border-radius: 0px 0px 6px 6px;
    width: 100%;
    margin-bottom: 16px;
    padding-left: 24px;
    display: flex;
    align-items: center;
    .title {
      font-size: 14px;
      color: #141414;
      font-weight: 400;
      line-height: 20px;
    }
    .item-hover {
      .title {
        color: rgba(0, 0, 0, 0.45);
      }
      &:hover {
        .title {
          color: var(--primary-color);
        }
      }
    }
  }
</style>
