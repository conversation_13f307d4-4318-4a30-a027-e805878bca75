<template>
  <a-row :gutter="[16, 8]" class="search_base_layout">
    <template v-for="(item, index) in data" :key="item.field">
      <a-col v-if="setMore(index, item) && item.type" v-bind="{ ...item.layout, xl: 6 }">
        <slot v-if="item.type === 'input.text'" :name="item.field">
          <a-input
            v-model:value.trim="formData[item.field]"
            @pressEnter="changeFormValue(true)"
            @blur="changeFormValue(true)"
            @keydown.space.prevent
            allowClear
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'inputNumber.text'" :name="item.field">
          <a-input-number
            v-model:value="formData[item.field]"
            @pressEnter="changeFormValue(true)"
            @blur="changeFormValue(true)"
            allowClear
            v-bind="item.props"
            :controls="false"
            class="w-full"
          />
        </slot>
        <slot v-else-if="item.type === 'select'" :name="item.field">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            @change="changeFormValue(true)"
            allowClear
            :show-search="true"
            :filterOption="onFiterOption"
            class="w-full"
            v-bind="item.props"
          ></a-select>
        </slot>
        <slot v-else-if="item.type === 'goods'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            allowClear
            :options="goodsList"
            :fieldNames="{ value: 'unique_id', label: 'goods_name' }"
            class="w-full"
            :show-search="true"
            :filter-option="false"
            v-bind="item.props"
            @search="fetchList"
          >
            <template v-if="state.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </slot>
        <slot v-else-if="item.type === 'joint_date'" :name="item.field">
          <div class="joint_date">
            <a-space class="w-full">
              <a-space-compact block>
                <a-select
                  v-if="!item.props?.leftContent?.field"
                  :style="{ width: '30%' }"
                  :getPopupContainer="(triggerNode: any) => triggerNode?.parentNode?.parentNode"
                  :allowClear="false"
                  placeholder="时间类型"
                  :options="item.props.options"
                  @change="(e) => dateTypeChange(e)"
                  v-model:value="item.field"
                  :showArrow="true"
                >
                </a-select>
                <a-select
                  v-else
                  :style="{ width: '30%' }"
                  :getPopupContainer="(triggerNode: any) => triggerNode?.parentNode?.parentNode"
                  :allowClear="item.props.leftAllowClear"
                  placeholder="时间类型"
                  :options="item.props.options"
                  @change="(e) => dateTypeChange(e, item)"
                  v-model:value="item.props.leftContent.value"
                  :showArrow="true"
                >
                </a-select>
                <a-range-picker
                  :style="{ width: '100%' }"
                  :allowClear="showDateClear"
                  v-model:value="formData[item.field]"
                  valueFormat="YYYY-MM-DD"
                  :presets="getDatePresetsOptions(item)"
                  @calendarChange="onCalendarChange"
                  @openChange="onOpenChange"
                  @change="(_: any, value: any) => onChnageJointDate(_, value, item)"
                  v-bind="item.props"
                  :showTime="false"
                />
              </a-space-compact>
            </a-space>
          </div>
        </slot>
        <slot v-else-if="item.type === 'date'" :name="item.field">
          <a-range-picker
            :allowClear="showDateClear"
            v-model:value="formData[item.field]"
            valueFormat="YYYY-MM-DD"
            class="w-full"
            :presets="getDatePresetsOptions(item)"
            @calendarChange="onCalendarChange"
            @openChange="onOpenChange"
            @change="
              (_, value) => {
                changeFormValue(true)
                ;(formData as any)[item.field] = _
              }
            "
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'day'" :name="item.field">
          <a-date-picker
            :allowClear="showDateClear"
            v-model:value="formData[item.field]"
            valueFormat="YYYY-MM-DD"
            class="w-full"
            @change="changeFormValue(true)"
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'number.range'" :name="item.field">
          <RangeNumber v-model="formData[item.field]" v-bind="item.props" @change="changeFormValue(true)" />
        </slot>
        <slot v-else-if="item.type === 'cascader'" :name="item.field">
          <a-cascader
            :open="showCascader"
            :show-search="true"
            :filterOption="onFiterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            :allow-clear="true"
            class="w-full"
            :expandTrigger="expandTriggerFilter(item.props)"
            @change="changeFormValue(true, undefined, item.props)"
            @dropdownVisibleChange="onDropdownVisibleChange($event, props)"
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'admin'">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            @change="changeFormValue(true)"
            :options="userList"
            :show-search="true"
            :filterOption="onFiterAdminOption"
            :fieldNames="{ label: 'realname', value: 'id' }"
            allowClear
            class="w-full"
            v-bind="item.props"
          ></a-select>
        </slot>
        <slot v-else-if="item.type === 'department'">
          <a-tree-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            @change="changeFormValue(true)"
            :tree-data="departmentList"
            tree-node-filter-prop="name"
            :popupClassName="'department'"
            allow-clear
            show-search
            style="width: 100%"
            :fieldNames="{ label: 'name', value: 'id' }"
            :show-checked-strategy="SHOW_PARENT"
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'checkbox'">
          <div class="checkbox-wrapper">
            <a-checkbox v-model:checked="formData[item.field]" @change="changeFormValue(true)">{{
              item.props.label
            }}</a-checkbox>
          </div>
        </slot>
        <slot v-else-if="item.type === 'treeSelect'">
          <a-tree-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            @change="changeFormValue(true)"
            :tree-data="item.props.options"
            tree-node-filter-prop="name"
            :popupClassName="'department_id'"
            allow-clear
            show-search
            style="width: 100%"
            :fieldNames="item.fieldNames"
            v-bind="item.props"
            :multiple="item.multiple || false"
          />
        </slot>
      </a-col>
    </template>
    <a-col v-if="isBtn" v-bind="actions.layout">
      <a-space align="center" :size="16" :class="isMobile && 'flex-wrap'">
        <a-button type="primary" @click="changeFormValue(true)">查询</a-button>
        <a-button @click="changeFormValue(false)">重置</a-button>
        <a-button type="link" class="pa-0" @click="changeFormValue(true, 'refresh')" v-if="btnNames.includes('refresh')"
          >刷新</a-button
        >
        <a-button type="link" class="pa-0" @click="changeFormValue(true, 'export')" v-if="btnNames.includes('export')"
          >导出</a-button
        >
        <a-button
          type="link"
          class="pa-0"
          @click="changeFormValue(true, 'special_export')"
          v-if="btnNames.includes('special_export')"
          >导出</a-button
        >
        <a-button
          type="primary"
          @click="changeFormValue(true, 'batchCheckCustomer')"
          v-if="btnNames.includes('batchCheckCustomer')"
          >批量添加客服</a-button
        >
        <a-button
          type="primary"
          @click="changeFormValue(true, 'batchSearchCustomer')"
          v-if="btnNames.includes('batchSearchCustomer')"
          >批量查询客服</a-button
        >

        <div v-if="actions.foldNum !== 0 && actions.foldNum < data.length">
          <a-button class="p-0" type="link" @click="showConfigFold" v-if="props.useConfig">{{
            isShowConfig ? '收起' : '展开'
          }}</a-button>
          <a-button class="p-0" type="link" @click="showFold(foldNum <= actions.foldNum)" v-else>
            {{ foldNum <= actions.foldNum ? '展开' : '收起' }}
          </a-button>
          <RightOutlined style="color: #737ba6" />
        </div>
      </a-space>
    </a-col>
    <slot name="btns"> </slot>
    <a-dropdown
      v-if="state.batchSetData.isShow"
      v-model:open="state.dropOpen"
      :disabled="!state.batchSetData.isSelected"
    >
      <a-button class="flex flex-items-center ml-8px">
        <SvgIcon icon="batch" class="mr-4px"></SvgIcon>
        批量操作
        <UpOutlined class="upoutlined" :class="[!state.dropOpen ? 'rotate-180' : 'rotate-0']" />
      </a-button>
      <template #overlay>
        <a-menu>
          <template v-for="(item, index) in state.batchSetData.list" :key="index">
            <a-menu-item
              v-if="!item?.auth?.length || (item?.auth?.length && isAuth(item.auth))"
              @click="state.batchSetData.callback(item)"
            >
              <div class="dot_item">
                {{ item.text }}
              </div>
            </a-menu-item></template
          >
        </a-menu>
      </template>
    </a-dropdown>
  </a-row>
</template>

<script setup lang="ts">
  import { getDatePresetsOptions } from '@/utils'
  import { getUserListApi, get_search_goods_list } from '@/api/common'
  import { get_department_info } from '@/views/system/manage/organization/index.api'
  import { RightOutlined, UpOutlined } from '@ant-design/icons-vue'
  import { onMounted, ref, nextTick, watch } from 'vue'
  import { throttle, debounce } from 'lodash-es'
  import { Dayjs } from 'dayjs'
  import dayjs from 'dayjs'
  import { useAuth, useApp } from '@/hooks'
  const { isAuth } = useAuth()
  const { isMobile } = useApp()
  type RangeValue = [Dayjs, Dayjs]
  const dates = ref<RangeValue>()
  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    actions: {
      type: Object,
      default: {}
    },
    showDateClear: {
      type: Boolean,
      default: true
    },
    btnNames: {
      //多余按钮
      type: Array,
      default: []
    },
    isBtn: {
      type: Boolean,
      default: true
    },
    // 批量操作数据
    batchSetData: {
      type: Object,
      default: () => {}
    },
    // 是否使用配置项控制筛选条件显隐
    useConfig: Boolean
  })
  const emits = defineEmits(['changeValue', 'selectedDate', 'onOpenChange'])
  const foldNum = ref(props.actions.foldNum)
  const formData = ref({})

  // 手动控制联机选择器的显隐
  const showCascader = ref(false)

  // 获取报表商品列表
  let goodsList = ref([])
  const state = ref({
    fetching: false,
    account_fetching: false,
    remark_fetching: false,
    goods_fetching: false,
    dropOpen: false, // 批量操作展示
    batchSetData: {
      // 批量操作相关数据
      isShow: false,
      list: [],
      isSelected: false,
      callback: () => {}
    } as {
      isShow: boolean
      list: any[]
      isSelected: boolean
      callback: Function
    }
  })
  const fetchList = debounce(async (value) => {
    try {
      state.value.fetching = true
      const params = {
        page: 1,
        page_size: 100,
        goods_name: value,
        source: formData.value.product_type || 0
      }
      if (!value.trim()) {
        goodsList.value = []
        state.value.fetching = false
        return
      }
      let res = await get_search_goods_list(params)
      goodsList.value = res?.data?.list || []
      state.value.fetching = false
    } catch (error) {
      state.value.fetching = false
    }
    console.log(formData.value, 'list')
  }, 300)

  const onChnageJointDate = (_: any, value: any, item: any) => {
    changeFormValue(true)
    formData.value[item.field] = value
  }

  // select搜索
  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }
  // 负责人搜索
  const onFiterAdminOption = (value: any, option: any) => {
    if (option.realname.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  // 控制是否配置项显隐
  const isShowConfig = ref(false)
  const showConfigFold = () => {
    isShowConfig.value = !isShowConfig.value
  }

  const showFold = (status: any) => {
    foldNum.value = status ? props.data.length : props.actions.foldNum
  }

  const changeFormValue = throttle((status: any, type = undefined, propsOption: any) => {
    if (propsOption) {
      if (propsOption.changeOnSelect) showCascader.value = false
    }
    if (status && type == undefined) {
      if (!formData.value.product_id) goodsList.value = []
      emits('changeValue', { status, formData: formData.value, page: 1 })
    } else if (status && type === 'refresh') {
      emits('changeValue', { status, formData: { ...formData.value }, type })
    } else if (status && type === 'export') {
      emits('changeValue', { status, formData: { ...formData.value }, type })
    } else if (status && type === 'special_export') {
      emits('changeValue', { status, formData: { ...formData.value }, type })
    } else if (status && type === 'batchCheckCustomer') {
      emits('changeValue', { status, formData: { ...formData.value }, type })
    } else if (status && type === 'batchSearchCustomer') {
      emits('changeValue', { status, formData: { ...formData.value }, type })
    } else {
      goodsList.value = []
      const initFormData = {}
      props.data.forEach((item: { field: string | number; value: any }) => {
        // ;(initFormData as any)[item.field] = item.value
        ;(initFormData as any)[item.field] = undefined
      })
      formData.value = initFormData
      emits('changeValue', { status, formData: formData.value })
    }
  }, 1000)

  // 联机选择器的展开方式
  const expandTriggerFilter = (props: any) => {
    if (!props) return 'click'
    if (props.expandTrigger) return props.expandTrigger
    if (props.changeOnSelect) return 'hover'
    if (!props.changeOnSelect) return 'click'
    return 'click'
  }

  // 联机选择器的显示隐藏
  const onDropdownVisibleChange = (visible) => {
    console.log('onDropdownVisibleChange', visible)
    showCascader.value = visible
  }

  const setMore = (index: any, item: any) => {
    if (props.useConfig) {
      if (!isShowConfig.value && item.useConfig) {
        return false
      }
      return true
    } else {
      if (props.actions && props.actions.foldNum) {
        return ++index <= foldNum.value
      } else {
        return true
      }
    }
  }

  const onCalendarChange = (val: RangeValue) => {
    dates.value = val
    emits('selectedDate', dates.value)
  }
  const onOpenChange = (open: boolean) => {
    emits('onOpenChange', open)
  }

  // 获取负责人列表
  const userList = ref([])
  const getUserList = async () => {
    try {
      let res = await getUserListApi()
      userList.value = res.data || []
    } catch (error) {}
  }

  // 获取部门列表
  const departmentList = ref([])
  const getdepartmentList = async () => {
    try {
      let res = await get_department_info({})
      departmentList.value = res.data || []
    } catch (error) {}
  }

  onMounted(() => {
    nextTick(() => {
      // 如果数据里面有负责人的话请求负责人接口
      if ((props.data || []).some((v: any) => v.type == 'admin')) {
        getUserList()
      }
      // 如果数据里面有部门数据，请求部分接口
      if ((props.data || []).some((v: any) => v.type == 'department')) {
        getdepartmentList()
      }
      const initFormData = {}
      props.data.forEach((item: { field: string | number; value: any; type?: any; props?: any }) => {
        ;(initFormData as any)[item.field] = item.value
        if (item.type == 'joint_date' && item?.props?.leftContent?.field) {
          ;(initFormData as any)[item.props.leftContent.field] = item.props.leftContent.value
        }
      })
      formData.value = initFormData
      if (props.useConfig) {
        // 使用配置项控制显隐
        foldNum.value = props.data.length
      }
    })
  })
  // 选择时间筛选类型
  const dateTypeChange = (e: any, item?: any) => {
    console.log('e', e)
    const initFormData = formData.value
    let time = ['created_at', 'callback_time_map', 'apply_map', 'add_fans_time']
    props.data.forEach((item: { field: string | number; value: any; type: any; props?: any }) => {
      if (item.type == 'joint_date') {
        if (item?.props?.leftContent?.field) {
          initFormData[item.props.leftContent.field] = e
          // initFormData[item.field] = undefined
        } else {
          item.field = e
          let timeKey = ''
          time.forEach((i) => {
            if ((initFormData as any)[i] && (initFormData as any)[i].length > 0) {
              timeKey = i
            }
          })
          ;(initFormData as any)[item.field] = (initFormData as any)[timeKey]
          ;(initFormData as any)[timeKey] = []
        }
      }
    })
    formData.value = initFormData
    if (
      item?.props?.leftContent?.field &&
      formData.value[item.field]?.length &&
      formData.value[item.field]?.every((it) => it)
    )
      changeFormValue(true)
  }
  // 向外暴露筛选数据
  const getFormData = () => {
    return {
      ...formData.value
    }
  }
  // 监听批量操作数据
  watch(
    () => props.batchSetData,
    () => {
      if (props.batchSetData) {
        state.value.batchSetData.isShow = Boolean(props.batchSetData.isShow)
        state.value.batchSetData.isSelected = Boolean(props.batchSetData.isSelected)
        state.value.batchSetData.list = props.batchSetData.list
        state.value.batchSetData.callback = props.batchSetData.callback
      }
    },
    {
      deep: true,
      immediate: true
    }
  )
  // 向外暴露方法
  defineExpose({ getFormData, changeFormValue, formData })
</script>
<style lang="scss">
  .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-treenode {
    width: 100%;
  }
  .ant-tree-select-dropdown
    .ant-select-tree-list-holder-inner
    .ant-select-tree-treenode
    .ant-select-tree-node-content-wrapper {
    width: 100%;
  }
  .joint_date {
    .ant-space-item {
      width: 100%;
      height: 34px;
      .ant-space-compact-block {
        height: 100%;
      }
      .ant-select {
        height: 100%;
        z-index: auto;
      }
      .ant-picker {
        height: 100%;
      }
    }
  }
  .checkbox-wrapper {
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dfdfdf;
    padding: 5px 6px;
    // cursor: pointer;
    .ant-checkbox-wrapper {
      display: flex;
      width: 100%;
      .ant-checkbox {
        flex: none;
      }
    }
  }
  .upoutlined {
    transition: all 0.25s ease-in-out;
  }
</style>
