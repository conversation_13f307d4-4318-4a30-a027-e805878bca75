<template>
  <a-popover placement="bottomRight" trigger="click" v-model:open="state.showPop">
    <template #content>
      <div style="min-width: 300px">
        <div class="flex flex-justify-between flex-items-center pb-1">
          <span class="fw-semibold">列展示</span>
          <a-button type="link" @click="reset">重置</a-button>
        </div>
        <VueDraggable
          :list="state.list"
          itemKey="dataIndex"
          class="drag cursor-pointer common_scroll"
          @end="onEnd"
          draggable=".draggable"
          chosen-class="chosenClass"
        >
          <template #item="{ element, index }">
            <div :class="{ draggable: !element.fixed }" class="bg flex flex-justify-between">
              <div>
                <a-checkbox v-model:checked="element.check" :disabled="element.disabled" @change="onEnd"></a-checkbox>
                <span class="m-l-10px">{{ element.title }}</span>
              </div>
              <SvgIcon icon="drag" class="c-#333 text-19px" v-if="!element.fixed" />
            </div>
          </template>
        </VueDraggable>
        <div class="flex flex-justify-end flex-items-center mt-10px">
          <a-button size="small" @click="state.showPop = false">取消</a-button>
          <a-button type="primary" size="small" @click="save">应用</a-button>
        </div>
        <!-- <a-tree
          draggable
          checkable
          blockNode
          :selectable="false"
          :height="300"
          v-model:checkedKeys="state.checkAllArr"
          :showIcon="true"
          :showLine="false"
          :tree-data="state.list"
          @select="selectItem"
          @drop="onDrop"
          :fieldNames="{
            children: 'children',
            title: 'title',
            key: 'dataIndex'
          }"
        >
        </a-tree> -->
      </div>
    </template>
    <SettingOutlined />
  </a-popover>
</template>

<script setup>
  import VueDraggable from 'vuedraggable'
  import { SettingOutlined, DragOutlined } from '@ant-design/icons-vue'
  import { computed, onMounted, reactive, watch } from 'vue'
  import { useColumns } from '@/hooks'
  import { useRoute } from 'vue-router'
  import { localStg } from '@/utils'
  import { cloneDeep } from 'lodash-es'
  const { setColumnsKey, getColumnsKey, getColumnsList } = useColumns()
  const route = useRoute()
  const props = defineProps(['column', 'data', 'types'])
  const emits = defineEmits(['update:data'])
  const initactions = () => {
    const arr = []
    props.column.forEach((item) => arr.push(item.dataIndex))
    return arr
  }

  ;(async () => {
    let newList = await getColumnsKey(route, localStg.get('userInfo'), props.column)
    if (newList) {
      const arr = []
      newList.forEach((item) => arr.push(item.dataIndex))
      state.checkAllArr = arr
      const columnList = await getColumnsList(route, localStg.get('userInfo'), props.column)
      state.list = columnList.map((v) => {
        return {
          ...v,
          check: v.hasOwnProperty('check') ? v.check : true
        }
      })
      newList = newList.filter((v) => v.check)
      emits('update:data', [...newList])
    } else {
      state.checkAllArr = initactions()
      state.list = props.column.map((v) => {
        return {
          ...v,
          check: true
        }
      })
    }
  })()

  const onEnd = (v) => {
    let arr = []
    arr = state.list.filter((v) => v.check)
    setColumnsKey(route, localStg.get('userInfo'), arr, state.list)
    emits('update:data', [...arr])
  }

  const state = reactive({
    list: [],
    checkAllArr: [],
    curry: 0,
    goal: 0,
    showPop: false
  })
  const onDrop = (info) => {
    const { dragNode, node, dropPosition } = info
    // 如果拖拽的节点有 fixed 属性，则不允许被拖拽
    if (dragNode.fixed) {
      // 取消拖拽操作
      return
    }
    // 如果拖拽的节点没有 fixed 属性，并且被放置在拥有 fixed 属性的节点的前面，则取消拖拽操作
    if (node?.fixed == 'left') {
      // 取消拖拽操作
      return
    }
    if (node?.fixed == 'right') {
      // 取消拖拽操作
      return
    }
    let newArr = cloneDeep(state.list)
    const targetIndex = state.list.findIndex((v) => v.dataIndex === dragNode.dataIndex)
    if (targetIndex != -1) {
    }
    const drapData = newArr.splice(targetIndex, 1)
    let index = Number(dragNode.pos.split('-')[1]) > Number(node.pos.split('-')[1]) ? dropPosition : dropPosition - 1

    let arr = state.list.filter((v) => v.dataIndex !== dragNode.dataIndex)
    arr.splice(index, 0, drapData[0])
    const newarr = []
    arr.forEach((item, index) => {
      if (state.checkAllArr.includes(item.dataIndex)) {
        newarr.push(item)
      }
    })
    state.list = arr

    setColumnsKey(route, localStg.get('userInfo'), newarr, state.list)
    emits('update:data', [...newarr])
  }
  const selectItem = (selectedKeys, { node }) => {
    if (node.selected) {
      console.log('取消选中')
    } else {
      console.log('选中')
    }
  }
  const reset = () => {
    let col = props.column
    col = col.map((v) => {
      return {
        ...v,
        check: true
      }
    })
    state.list = cloneDeep(col)
    setColumnsKey(route, localStg.get('userInfo'), col, state.list)
    emits('update:data', [...col])
  }
  const swapArr = (arr, curry, goal) => {
    arr[curry] = arr.splice(goal, 1, arr[curry])[0]
    return arr
  }
  const save = () => {
    // let arr = []
    // arr = state.list.filter((v) => v.check)
    setColumnsKey(route, localStg.get('userInfo'), state.list, state.list, 'save')
    state.showPop = false
  }
  // watch(
  //   () => state.checkAllArr,
  //   () => {
  //     const arr = []
  //     arr = state.list

  //     setColumnsKey(route, localStg.get('userInfo'), arr, state.list, props.types)
  //     emits('update:data', [...arr])
  //   }
  // )
</script>

<style scoped lang="scss">
  .drag {
    max-height: 300px;
    overflow: auto;
    padding-right: 10px;
  }
  .icon-list {
    opacity: 0;
  }
  .item-warp-text:hover {
    .icon-list {
      opacity: 1;
    }
  }
  .bg {
    padding: 5px;
    background-color: #f0f0f0;
    border-radius: 5px;
    margin-bottom: 10px;
    color: #333;
    &:last-child {
      margin-bottom: 0px;
    }
  }

  .chosenClass {
    background-color: #f0f0f0;
    opacity: 0.2;
  }
</style>
