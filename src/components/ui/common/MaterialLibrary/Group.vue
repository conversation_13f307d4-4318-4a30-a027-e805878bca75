<template>
  <div class="group_wrapper" :class="[useType == 'dialog' ? 'dialog' : '', isMobile ? '100%' : 'w-266px']">
    <!-- <div class="flex flex-items-center flex-justify-between">
      <span class="left_title">全部图片</span>
      <a-dropdown trigger="['click']">
        <span class="el-dropdown-link">
          <SvgIcon icon="sort" v-if="state.order_by !== 0" class="text-18px mt-5px cursor-pointer"></SvgIcon>
          <SvgIcon icon="up_icon" v-else class="text-18px mt-5px cursor-pointer"></SvgIcon>
        </span>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="updateFn(0)">按时间升序排列</a-menu-item>
            <a-menu-item @click="updateFn(1)">按时间降序排列</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div class="line"></div> -->

    <div class="content common_scroll">
      <a-collapse
        v-model:activeKey="activeNames"
        @change="handleChange"
        class="custom-collapse"
        :bordered="false"
        expandIconPosition="end"
        :ghost="true"
      >
        <a-collapse-panel header="所有素材" key="1">
          <template #extra><SvgIcon icon="library_file" /></template>
          <div
            @click="changeGroup(item, index)"
            v-for="(item, index) in list"
            :key="index"
            :class="['content-item', cutIdx == index ? 'content-cutItem' : 'content-noItem']"
          >
            <div class="font_color text_overflow">{{ item.name }}({{ item.count }})</div>
            <div class="content-item-icons flex flex-items-center" v-show="cutIdx == index">
              <SvgIcon
                class="m-r-8px library_del"
                icon="library_del"
                v-auth="['delMaterialGroup']"
                @click.stop="delGroup(item, index)"
              />
              <a-popover v-model:open="item.popoverShow" placement="bottomRight" trigger="click">
                <template #content>
                  <div class="input-box">
                    <a-input
                      v-model:value.trim="state.cutGroupName"
                      maxlength="10"
                      placeholder="请输入分组名称"
                      showCount
                      type="text"
                    />
                    <div style="display: flex; justify-content: center; margin-top: 10px">
                      <a-button @click="changeGroupName(item)" type="primary">确定</a-button>
                    </div>
                  </div>
                </template>
                <SvgIcon
                  class="library_edit"
                  icon="library_edit"
                  v-auth="['editMaterialGroup']"
                  @click.stop="openPopover(item)"
                />
              </a-popover>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
    <div class="line"></div>
    <div class="flex flex-items-center justify-around btns">
      <a-popover placement="bottom" trigger="click" v-model:open="state.addGroupPopoverShow">
        <template #content>
          <div class="input-box">
            <a-input
              v-model:value.trim="state.groupName"
              maxlength="8"
              placeholder="请输入分组名称"
              showCount
              type="text"
            />
            <div style="display: flex; justify-content: center; margin-top: 10px">
              <a-button @click="add" type="primary">确定</a-button>
            </div>
          </div>
        </template>
        <div class="header-addBtn" v-auth="['addMaterialGroup']">
          <a-button @click="state.addGroupPopoverShow = true" class="bg-#F4F3F5 c-primary border-none"
            >新增分组</a-button
          >
        </div>
      </a-popover>
      <Upload
        v-auth="['addMaterial']"
        class="upload_button"
        :use="{ image: 'goods', video: 'video_speed/original' }"
        type="button"
        @uploadSuccess="uploadSuccess"
        @complete="handleFileComplete"
        multiple
        :accept="state.acceptTyep"
        :size="300"
        from="sucai"
      >
        <a-button type="primary" v-if="props.type == 'video'">上传视频</a-button>
        <a-button type="primary" v-else-if="props.type == 'image'">上传图片</a-button>
        <a-button type="primary" v-else>上传素材</a-button>
      </Upload>
    </div>
  </div>
</template>

<script setup>
  import { SvgIcon } from '@/components'
  import { fileEdit, fileSave, deleteFile } from './index.api'
  import { reactive, ref, createVNode } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { useTheme, useApp } from '@/hooks'
  import { localStg, getConfig } from '@/utils'
  const { themeVar } = useTheme()
  const { isMobile } = useApp()
  import { DeleteOutlined, FormOutlined } from '@ant-design/icons-vue'
  const emit = defineEmits([
    'change',
    'addSuccess',
    'changeGroupName',
    'delSuccess',
    'sortChange',
    'uploadSuccess',
    'complete'
  ])
  const props = defineProps({
    list: {
      type: Array,
      default: []
    },
    cutIdx: {
      type: [Number, String],
      default: 0
    },
    useType: {
      type: String,
      default: 'dialog'
    },
    type: {
      type: String,
      default: null
    }
  })

  // 设置按钮控制器
  const buttonController = (name, keys) => {
    if (props.useType == 'page') {
      return true
      // return getBtnAuth(name, keys)
    } else {
      return true
    }
  }

  const uploadSuccess = () => {
    emit('uploadSuccess')
  }

  const handleFileComplete = (data) => {
    console.log(data, 'data')
    emit(
      'complete',
      data.map((v) => {
        if (v?.fileInfo?.type == 'video') {
          return {
            ...v,
            m3u8Src: convertToM3U8(v.content),
            video_cover: convertToImg(v.content)
          }
        } else return v
      })
    )
  }

  const updateFn = (status) => {
    state.order_by = status
    emit('sortChange', { order_by: status })
  }
  const state = reactive({
    groupName: '',
    cutGroupName: '',
    addGroupPopoverShow: false,
    order_by: 0,
    acceptTyep:
      props.type == 'video'
        ? '.mp4,.mov,.mkv'
        : props.type == 'image'
          ? '.jpg,.jpeg,.png,.bmp,.gif,.PNG,.JPEG,.GIF,.JPG,'
          : '.jpg,.jpeg,.png,.bmp,.gif,.PNG,.JPEG,.GIF,.JPG,.mp4,.mov,.mkv'
  })
  const activeNames = ref(['1'])
  const handleChange = (val) => {}
  // 排序类型
  const handleCommand = () => {}
  const changeGroupName = async (item) => {
    let { cutIdx, list } = props
    try {
      if (!state.cutGroupName) {
        message.warning('分组名称不允许为空')
        return
      }

      let res = await fileEdit({ pid: list[cutIdx].id, file_name: state.cutGroupName })
      item.popoverShow = false
      emit('changeGroupName', res)
      message.success('修改成功')
      state.cutGroupName = ''
    } catch (error) {
      console.error(error)
    }
  }
  const showChangePop = () => {
    let { cutIdx, list } = props
    if (!state.cutGroupName) {
      state.cutGroupName = list[cutIdx].name
    }
  }
  // 打开编辑框
  const openPopover = (item) => {
    item.popoverShow = true
    state.cutGroupName = item.name
  }
  // 添加
  const add = async () => {
    try {
      if (!state.groupName) {
        message.warning('分组名称不允许为空')
        return
      }
      let res = await fileSave({ file_name: state.groupName, useShop: 'shop_id' })
      state.groupName = ''
      emit('addSuccess', res)
      message.success('添加成功')
      state.addGroupPopoverShow = false
    } catch (error) {
      console.error(error)
    }
  }

  // 删除分组
  const delGroup = async (item, index) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否确定删除'),
        async onOk() {
          try {
            let res = await deleteFile({ group_id: item.id })
            message.success('删除成功')
            emit('delSuccess')
          } catch (error) {
          } finally {
            Modal.destroyAll()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const changeGroup = (item, index) => {
    localStg.set('materialId', item.id)
    emit('change', { ...item, index })
  }

  const convertToM3U8 = (mp4Url) => {
    const fileName = mp4Url.split('/').pop()
    const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)
    const m3u8Url = `${path.replace('/original/', '/')}${fileName.split('.')[0]}.m3u8`

    return m3u8Url
  }
  const convertToImg = (mp4Url) => {
    const fileName = mp4Url.split('/').pop()
    const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)

    let upload_type = getConfig('UPLOAD_TYPE') || 'oss'
    let img
    if (upload_type == 'cos') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_0.jpg`
    } else if (upload_type == 'oss') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_1.jpg`
    } else {
      img = ''
    }

    return img
  }
</script>

<style lang="scss" scoped>
  .tk-sort {
    color: #404040;
    font-size: 16px;
    cursor: pointer;
  }
  .input-box {
    padding: 10px 10px 0 10px;
  }
  .tk-remove-1-copy {
    color: #404040;
    font-size: 14px;
  }
  .tk-bianji {
    color: #404040;
    margin-left: 10px;
    font-size: 15px;
  }
  :deep(.custom-collapse .ant-collapse-content-box) {
    padding: 0;
  }
  :deep(.ant-collapse-extra) {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  :deep(.ant-collapse-header-text) {
    margin-left: 20px;
  }
  :deep(.ant-collapse-header) {
    padding: 0;
  }
  .group_wrapper {
    height: calc(100vh - 190px);
    background: linear-gradient(180deg, #fcfcfe 0%, #fbfdfe 100%);
    border-radius: 8px;
    padding: 8px 24px;
    .left_title {
      font-size: 14px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      color: #313233;
    }
    .line {
      width: 100%;
      height: 1px;
      background: #dfe1e6;
      margin: 15px 0;
    }

    .library_del {
      font-size: 16px;
      color: #64666b;
    }
    .library_edit {
      font-size: 20px;
      color: #64666b;
    }

    .library_del:hover,
    .library_edit:hover {
      color: var(--primary-color);
    }

    :deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
      background: linear-gradient(180deg, #fcfcfe 0%, #fbfdfe 100%);
      color: var(--primary-color);
      padding: 8px 0;
    }
    :deep(.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
      padding: 0;
    }

    .content {
      overflow-x: hidden;
      height: calc(100vh - 290px);
      padding-right: 20px;
      .content-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 11px 8px 11px 24px;
        width: 100%;
        box-sizing: border-box;
        background: linear-gradient(180deg, #fcfcfe 0%, #fbfdfe 100%);
        .font_color {
          color: #404040;
        }
        .content-item-icons {
          display: flex;
          align-items: center;

          img:nth-child(1) {
            margin-right: 10px;
          }
        }
      }

      .content-noItem:hover {
        background: #eaf0fd;
        border-radius: 4px;
        padding: 11px 8px 11px 24px;

        .font_color {
          color: var(--primary-color);
        }
      }

      .content-cutItem {
        background: #eaf0fd;
        border-radius: 4px;
        // color: var(--primary-color);
        padding: 11px 8px 11px 24px;
        .font_color {
          color: var(--primary-color);
        }
      }
    }
    &.dialog {
      // width: 205px;
      height: calc(100vh - 245px);
      .content {
        height: calc(100vh - 420px);
        // overflow: auto;
      }
    }
  }
</style>
