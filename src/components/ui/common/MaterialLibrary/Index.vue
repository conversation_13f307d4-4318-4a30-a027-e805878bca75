<template>
  <div>
    <div class="material_library_wrapper">
      <div class="box flex" :class="isMobile ? 'flex-col' : ''">
        <Group
          @change="changeGroup"
          @changeGroupName="changeGroupName"
          @addSuccess="groupAddSuccess"
          @delSuccess="groupDelSuccess"
          @uploadSuccess="uploadSuccess"
          @complete="handleFileComplete"
          :list="state.groupArr"
          :type="type"
          :cutIdx="state.cutIdx"
          :useType="props.useType"
        />
        <div class="right">
          <a-spin :spinning="state.loading">
            <div class="right-content">
              <div class="flex-y-center" style="margin-bottom: 16px; justify-content: space-between">
                <div class="right-content-row1">
                  <span class="row1_left">{{ itemName }}</span>
                  <span class="row1_right_tips" v-if="props.size != Infinity"
                    >最多只能添加{{ props.size }}{{ props.type == 'video' ? '个视频' : '张图片' }}</span
                  >
                  <a-dropdown trigger="['click']">
                    <span class="el-dropdown-link">
                      <SvgIcon
                        icon="sort"
                        v-if="state.fileListParams.order_by !== 0"
                        class="text-18px mt-5px ml8px cursor-pointer"
                      ></SvgIcon>
                      <SvgIcon icon="up_icon" v-else class="text-18px mt-5px ml8px cursor-pointer"></SvgIcon>
                    </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="sortChange(0)">按时间升序排列</a-menu-item>
                        <a-menu-item @click="sortChange(1)">按时间降序排列</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
              <SearchBaseLayout
                ref="SearchRef"
                class="mb16px"
                :data="searchData"
                @changeValue="searchPicName"
                :actions="actions"
              >
                <template #btns>
                  <a-button
                    class="bg-#F4F3F5 border-none"
                    v-auth="['delMaterial']"
                    @click="mutiDelete"
                    danger
                    :disabled="state.checkedList.length == 0"
                    type="text"
                    >删除</a-button
                  >
                </template>
              </SearchBaseLayout>
              <div class="list" :class="{ dialog_h: props.useType === 'dialog' }">
                <div
                  class="list-item"
                  :class="{ hide_margin: ['image', 'video'].includes(props.type) }"
                  v-for="(it, idx) in state.fileList"
                  :key="idx"
                >
                  <div
                    class="list-item-pop"
                    :class="[it.file_type == 'img' ? 'isImg' : 'isVideo']"
                    @click.stop="changeCheck(idx, 'out')"
                  ></div>
                  <a-checkbox
                    @change="changeCheck(idx, 'in')"
                    :style="{ opacity: it.check ? 1 : '' }"
                    class="list-item-cut list-item-popBtn"
                    v-model:checked="it.check"
                    :key="it.id"
                    :disabled="checkboxDisabled(it)"
                  />

                  <div class="list-item-del list-item-popBtn flex flex-items-center flex-justify-center">
                    <a-tooltip color="#FFFFFF" placement="bottom">
                      <template #title>
                        <span class="c-#323233">删除</span>
                      </template>
                      <SvgIcon v-auth="['delMaterial']" @click="delPic(it, idx)" icon="del_img" />
                    </a-tooltip>
                    <div class="m-l-20px m-r-20px">|</div>
                    <a-tooltip color="#FFFFFF" placement="bottom">
                      <template #title>
                        <span class="c-#323233">预览</span>
                      </template>
                      <SvgIcon v-if="it.file_type == 'img'" @click.stop="previewImage(idx)" icon="look_img" />
                    </a-tooltip>
                    <a-tooltip color="#FFFFFF" placement="bottom">
                      <template #title>
                        <span class="c-#323233">{{ !state.paused ? '播放' : '暂停' }}</span>
                      </template>
                      <template v-if="it.file_type == 'video'">
                        <PlayCircleOutlined v-if="!state.paused" color="#FFFFFF" @click.stop="playVideo(it.id)" />
                        <PauseCircleOutlined v-else color="#FFFFFF" @click.stop="playVideo(it.id)" />
                      </template>
                    </a-tooltip>
                  </div>

                  <a-image
                    v-if="!state.videoList.includes(it.extension)"
                    :ref="'imagePreview' + it.id"
                    style="width: 104px; height: 104px; border-radius: 8px 8px 0px 0px; object-fit: cover"
                    :src="it.file_url"
                    :preview="{
                      visible: it.visible,
                      onVisibleChange: (value) => setVisible(idx, value)
                    }"
                  />
                  <video
                    v-else
                    :ref="(el) => (refVideoList[it.id] = el)"
                    style="width: 104px; height: 104px; border-radius: 8px 8px 0px 0px; object-fit: cover"
                    :src="it.file_url"
                    controls
                  />

                  <div
                    class="list-item-title"
                    v-loading="it.fileNameChangeLoading"
                    :class="{ 'mt-6px': it.file_type == 'video' }"
                  >
                    <span v-if="props.editFileName">
                      <a-input
                        v-model:value="it.file_name"
                        placeholder="请输入文件名"
                        @blur="handleFileNameBlur($event, it)"
                        v-if="it.is_edit"
                      ></a-input>
                      <span @click="handleFileNameClick(it)" v-else>{{ it.file_name }}</span>
                    </span>
                    <span v-else>{{ it.file_name }}</span>
                  </div>
                </div>
              </div>
              <div style="text-align: center" v-if="state.fileList.length == 0">
                <a-config-provider :locale="zhCN">
                  <a-empty description="暂无数据" />
                </a-config-provider>
              </div>
              <div :class="{ btns: useType === 'dialog' }">
                <Pagination
                  class="m-t-24px"
                  v-model:page="state.fileListParams.page"
                  v-model:pageSize="state.fileListParams.page_size"
                  :pageSizeOptions="['18', '36', '72', '144']"
                  :total="state.fileListTotal"
                  @change="changePage"
                ></Pagination>

                <div class="right-btns" v-if="props.useType === 'dialog'">
                  <a-button :style="{ marginRight: '9px' }" @click="close" :plain="true">取消</a-button>
                  <a-button :loading="subLoading" type="primary" @click="subData">确定</a-button>
                </div>
              </div>
            </div>
          </a-spin>
        </div>
        <!-- <a-spin :spinning="state.loading">
            <div class="right-content">
              <div class="flex-y-center" style="margin-bottom: 20px; justify-content: space-between">
                <div class="right-content-row1">
                  <span class="row1_left">所有素材</span>
                  <span class="row1_right">/</span>
                  <span class="row1_right">广告素材</span>
                  <span class="row1_right_tips" v-if="props.size != Infinity"
                    >最多只能添加{{ props.size }}{{ props.type == 'video' ? '个视频' : '张图片' }}</span
                  >
                </div>
                <a-space v-auth="['delMaterial']" align="end">
                  <a-button @click="mutiDelete" type="text">删除</a-button>
                </a-space>
              </div>
              <a-row class="list" :gutter="[10, 20]">
                <a-col
                  class="list-item"
                  :class="{ hide_margin: ['image', 'video'].includes(props.type) }"
                  v-for="(it, idx) in state.fileList"
                  :key="idx"
                >
                  <div
                    class="list-item-pop"
                    :class="[it.file_type == 'img' ? 'isImg' : 'isVideo']"
                    @click.stop="changeCheck(idx, 'out')"
                  ></div>
                  <a-checkbox
                    @change="changeCheck(idx, 'in')"
                    :style="{ opacity: it.check ? 1 : '' }"
                    class="list-item-cut list-item-popBtn"
                    v-model:checked="it.check"
                    :key="it.id"
                    :disabled="checkboxDisabled(it)"
                  />
                  <SearchOutlined
                    v-if="it.file_type == 'img'"
                    class="list-item-look list-item-popBtn"
                    :style="{ fontSize: '16px', color: '#fff' }"
                    @click.stop="previewImage(idx)"
                  />
                  <div class="list-item-del list-item-popBtn" @click="delPic(it, idx)" v-auth="['delMaterial']">
                    删除
                  </div>

                  <a-image
                    v-if="!state.videoList.includes(it.extension)"
                    :ref="'imagePreview' + it.id"
                    style="width: 116px; height: 116px; border-radius: 6px; object-fit: contain"
                    :src="it.file_url"
                    :preview="{
                      visible: it.visible,
                      onVisibleChange: (value) => setVisible(idx, value)
                    }"
                  />
                  <video v-else style="width: 116px; height: 116px; border-radius: 6px" :src="it.file_url" controls />

                  <div
                    class="list-item-title"
                    v-loading="it.fileNameChangeLoading"
                    :class="{ 'mt-6px': it.file_type == 'video' }"
                  >
                    <span v-if="props.editFileName">
                      <a-input
                        v-model:value="it.file_name"
                        placeholder="请输入文件名"
                        @blur="handleFileNameBlur($event, it)"
                        v-if="it.is_edit"
                      ></a-input>
                      <span @click="handleFileNameClick(it)" v-else>{{ it.file_name }}</span>
                    </span>
                    <span v-else>{{ it.file_name }}</span>
                  </div>
                </a-col>
              </a-row>
              <div style="text-align: center" v-if="state.fileList.length == 0">
                <a-config-provider :locale="zhCN">
                  <a-empty />
                </a-config-provider>
              </div>
              <Pagination
                v-model:page="state.fileListParams.page"
                v-model:pageSize="state.fileListParams.page_size"
                :total="state.fileListTotal"
                @change="changePage"
              ></Pagination>

              <div class="right-btns" v-if="props.useType === 'dialog'">
                <a-button :style="{ marginRight: '9px' }" @click="close" :plain="true">取消</a-button>
                <a-button type="primary" @click="subData">确定</a-button>
              </div>
            </div>
          </a-spin> -->
      </div>
    </div>
  </div>
</template>
<script setup name="MaterialLibrary" lang="ts">
  import { SvgIcon } from '@/components'
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import { fileSave, getFileList, updateNameFile, deleteFile, uploadImage } from './index.api'
  import { debounce, getImageInfoByPath, checkFileFormat, localStg } from '@/utils'
  import { onBeforeMount, onBeforeUnmount, reactive, ref, watchEffect, createVNode, computed } from 'vue'
  import { PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons-vue'
  import { message, Modal } from 'ant-design-vue'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  import Group from './Group.vue'
  import { useAuth, useApp } from '@/hooks'
  const { isMobile } = useApp()
  const { isAuth } = useAuth()
  const refVideoList = ref({})
  const props = defineProps({
    modelValue: {
      type: Boolean,
      deflaut: false
    },
    size: {
      type: Number,
      default: Infinity
    },
    fileSize: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: ''
    },
    extension: {
      type: String,
      default: ''
    },
    editFileName: {
      type: Boolean,
      default: false
    },
    useType: {
      type: String,
      default: 'dialog'
    },
    fileCheckedList: {
      type: Array,
      default: () => []
    },
    // 评价特殊使用
    isComments: {
      type: Boolean,
      default: false
    },
    isH5Upload: {
      type: Boolean,
      default: false
    }
  })
  console.log('props', props.useType)
  const SearchRef = ref()
  const state = reactive({
    checkedList: [],
    xx: '',
    isChecks: false,
    pageNo: 1,
    modelPageNo: 10,
    picName: '',
    type: undefined,
    loading: false,
    cutIdx: 0,
    groupArr: [] as any,
    fileList: [] as any,
    cutFiles: {} as any,
    videoList: ['.mp4', '.mov', '.mkv', '.MOV', '.MP4', '.MKV'],
    // videoAccept: '.mp4',
    // imageAccept: '.jpg,.jpeg,.png,.bmp,.gif,.PNG,.JPEG,.GIF,.JPG,',
    acceptTyep:
      props.type == 'video'
        ? '.mp4,.mov,.mkv'
        : props.type == 'image'
          ? '.jpg,.jpeg,.png,.bmp,.gif,.PNG,.JPEG,.GIF,.JPG,'
          : '.jpg,.jpeg,.png,.bmp,.gif,.PNG,.JPEG,.GIF,.JPG,.mp4,.mov,.mkv',
    fileListParams: {
      page: 1,
      page_size: 18,
      group_id: 0,
      useShop: 'shop_id',
      pid: 0,
      file_name: '',
      file_type: undefined,
      order_by: 0
    } as any,
    fileListPageSizeCount: props.useType == 'dialog' ? [18, 36, 72] : [18, 36, 72, 144],
    fileListTotal: 0,
    fileListPageCount: 0,
    paused: false
  })
  const searchData = ref([
    {
      field: 'file_type',
      type: 'select',
      value: undefined,
      props: {
        options: [
          { label: '图片', value: 'img' },
          { label: '视频', value: 'video' }
        ],
        placeholder: '请选择素材类型'
      },
      layout: {
        xs: 24,
        sm: 8,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'picName',
      value: undefined,
      props: {
        placeholder: '请输入素材名称'
      },
      layout: {
        xs: 24,
        sm: 8,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 8,
      md: 8,
      lg: 8,
      xl: 6,
      xxl: 4
    }
  }
  watchEffect(() => {
    state.fileListPageCount = Math.ceil(state.fileListTotal / state.fileListParams.page_size)
  })

  // 可选择的文件数量
  const canChooseNum = computed(() => {
    // 组件外已有的文件数量
    const fileCheckedLength = props.fileCheckedList?.length || 0
    return props.size - fileCheckedLength
  })

  const checkboxDisabled = computed(() => (item: any) => {
    // 组件中选中的文件数量
    const checkedLength = state.checkedList?.length || 0
    // 是否超过数量限制
    const isThan = checkedLength > canChooseNum.value - 1
    return !item.check && isThan
  })

  // 设置按钮控制器
  const buttonController = (name, keys) => {
    if (props.useType == 'page') {
      return isAuth(keys)
    } else {
      return true
    }
  }
  // 搜索图片名称
  const searchPicName = async (data) => {
    try {
      state.fileListParams.file_name = data.formData.picName
      state.fileListParams.file_type = data.formData.file_type
        ? data.formData.file_type
        : props.type == 'video'
          ? 'video'
          : props.type == 'image'
            ? 'img'
            : ''
      let res = await getFileList(state.fileListParams)
      state.fileList = changeChecks(res.data.file) || []
      state.fileListTotal = res.data?.total || 0
      state.groupArr = res.data.group || []
    } catch (error) {}
  }

  // 实现自定义组件v-model绑定
  const emit = defineEmits(['update:modelValue', 'event', 'update:fileCheckedList'])
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  const changeGroupName = (item) => {
    fileList()
  }
  // 图片预览
  const previewImage = (index) => {
    state.fileList[index].visible = true
  }
  const setVisible = (index, value) => {
    state.fileList[index].visible = value
  }
  //播放暂停视频
  const playVideo = (id) => {
    if (refVideoList?.value[id]?.paused) {
      refVideoList?.value[id].play()
      state.paused = true
    } else {
      refVideoList?.value[id].pause()
      state.paused = false
    }
  }
  // 添加分组成功
  const groupAddSuccess = () => {
    fileList()
  }
  // 删除分组成功
  const groupDelSuccess = () => {
    // 需要将active置为0，要不会报错，找不到对应的索引
    state.cutIdx = 0
    fileList()
  }
  // 上传成功
  const uploadSuccess = debounce(() => {
    message.success('上传成功')
  }, 500)

  const handleFileComplete = async (data) => {
    try {
      state.loading = true
      for (const v of data) {
        await uploadChange(v)
      }
    } catch (error) {
      console.log(error)
    } finally {
      state.loading = false
    }
  }

  const fileList = async () => {
    try {
      // state.cutIdx = 0
      state.fileListParams.file_type = props.type == 'video' ? 'video' : props.type == 'image' ? 'img' : ''
      let params = {
        ...state.fileListParams,
        page_size: 999
      }
      if (props.fileSize) params.file_size = props.fileSize
      if (props.extension) params.extension = props.extension
      let res = await getFileList(params)
      state.groupArr = res.data.group || []
      initFileListParams()

      // 自动选择分组功能
      let materialId = localStg.get('materialId')
      if (materialId) {
        let index = state.groupArr.findIndex((v) => v.id == materialId)
        if (index != -1) changeGroup({ ...state.groupArr[index], index })
        else await changeGroup({ ...state.groupArr[state.cutIdx], index: state.cutIdx })
      } else {
        await changeGroup({ ...state.groupArr[state.cutIdx], index: state.cutIdx })
      }
    } catch (error) {
      console.error(error)
    }
  }
  fileList()
  // 删除图片
  const delPic = (item, index) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否确定删除'),
        onOk() {
          deleteFile({ file_ids: String(item.id) })
          fileList()
          message.success('删除成功')
          state.cutFiles = {}
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      // error != 'cancel' && commonMethods.catchMsg(error)
    }
  }
  // 批量删除
  const mutiDelete = () => {
    try {
      if (state.fileList.filter((v) => v.check).map((v) => v.id).length == 0) {
        return message.warning('请选择要删除的素材')
      }
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否确定删除'),
        onOk() {
          let ids = state.fileList
            .filter((v) => v.check)
            .map((v) => v.id)
            .join(',')
          deleteFile({ file_ids: ids })
          message.success('删除成功')
          state.cutFiles = {}
          state.checkedList = []
          fileList()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  const initFileListParams = () => {
    state.fileListParams = {
      page: 1,
      page_size: 18,
      group_id: 0,
      useShop: 'shop_id',
      pid: 0,
      page_count: 0
    }
    if (props.fileSize) state.fileListParams.file_size = props.fileSize
    if (props.extension) state.fileListParams.extension = props.extension
  }

  // 文件重命名
  const handleFileNameClick = (it) => {
    // if (!buttonController('StockLibrary', ['editFileName'])) return
    it.copy_name = it.file_name
    it.is_edit = true
  }
  const handleFileNameBlur = async (event, it) => {
    it.is_edit = false
    if (!event.target.value) {
      message.warning('文件名称不能为空')
      it.file_name = it.copy_name
      return
    }
    try {
      it.fileNameChangeLoading = true
      const params = {
        file_id: it.id,
        name: event.target.value
      }
      const res = await updateNameFile(params)
      message.success('修改成功')
    } catch (error) {
      console.error(error)
    } finally {
      it.fileNameChangeLoading = false
    }
  }
  // const handleFileNameBlur = (value, it) => {
  //   it.is_edit = false
  // }

  // 更新checks
  const changeChecks = (arr) => {
    ;(arr || []).map((val) => (state.cutFiles[val.id] ? (val.check = true) : (val.check = false)))
    return arr || []
  }
  // sort时间排序
  const sortChange = (val) => {
    state.fileListParams.order_by = val
    changeGroup({ ...state.groupArr[state.cutIdx], index: state.cutIdx })
  }
  // 切换分组
  const itemName = ref('全部图片')
  const changeGroup = async (val) => {
    try {
      state.loading = true
      let { index, id, pid, name } = val
      state.fileListParams.page = 1
      state.fileListParams.group_id = id
      state.fileListParams.pid = pid
      state.fileListParams.file_name = undefined
      itemName.value = name
      state.fileListParams.file_type = props.type == 'video' ? 'video' : props.type == 'image' ? 'img' : ''
      state.fileListParams.picName = undefined
      SearchRef.value.formData.file_type = undefined
      SearchRef.value.formData.picName = ''
      let res = await getFileList({ ...state.fileListParams })
      state.fileList = changeChecks(res.data.file) || []
      state.fileList = state.fileList.map((item) => {
        return {
          ...item,
          visible: false
        }
      })
      // state.groupArr = res.data.group || []
      state.fileListTotal = res.data?.total || 0
      state.cutIdx = index
      state.loading = false
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  // 切换分页
  const changePage = async (val) => {
    try {
      state.loading = true
      let { page, page_size } = val
      state.fileListParams.file_type = props.type == 'video' ? 'video' : props.type == 'image' ? 'img' : ''
      state.fileListParams.page = page
      state.fileListParams.page_size = page_size
      let res = await getFileList(state.fileListParams)
      state.fileList = changeChecks(res.data.file)
      state.fileListTotal = res.data?.total || 0
      state.loading = false
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }

  const uploadChange = async (v) => {
    if (v.type === 'success') {
      try {
        state.loading = true
        let { id } = state.groupArr[state.cutIdx]
        let { content, fileName, file, video_cover } = v
        // const isImage = checkFileFormat(fileName) === 'image'
        // const picInfo = isImage ? await getImageInfoByPath([content]) : null
        const fileInfo = v.fileInfo || {}
        await fileSave({
          pid: id,
          file_url: content,
          file_name: fileName,
          useShop: 'shop_id',
          file_size: file.size,
          width: fileInfo.width,
          height: fileInfo.height,
          video_len: fileInfo.duration,
          m3u8_url: v.m3u8Src || '',
          video_cover: video_cover || ''
        })
        state.fileListParams.page = 1
        // let res = await getFileList(state.fileListParams)
        // state.fileList = res.data.file || []
        // state.fileListTotal = res.data.total
        await fileList()
        state.loading = false
      } catch (error) {
        console.error(error)
      } finally {
        state.loading = false
      }
      state.groupArr[state.cutIdx].id
    }
  }

  // 修改选中
  const changeCheck = (i, type) => {
    let cutItem = state.fileList[i]
    console.log(cutItem.check)
    if (!cutItem.check) {
      if (type == 'in') {
        state.fileList[i].check = false
        delete state.cutFiles[cutItem.id]
        const delIndex = state.checkedList.findIndex((v) => v.id == cutItem.id)
        state.checkedList.splice(delIndex, 1)
      } else {
        if (state.checkedList.length > canChooseNum.value - 1) {
          return message.warning(`最多只能添加${props.size}${props.type == 'video' ? '条视频' : '张图片'}。`)
        }
        state.fileList[i].check = true
        state.cutFiles[cutItem.id] = cutItem
        state.checkedList.push(cutItem)
      }
    } else {
      console.log(2222)
      if (type == 'out') {
        state.fileList[i].check = false
        delete state.cutFiles[cutItem.id]
        const delIndex = state.checkedList.findIndex((v) => v.id == cutItem.id)
        state.checkedList.splice(delIndex, 1)
      } else {
        if (state.checkedList.length > canChooseNum.value - 1) {
          return message.warning(`最多只能添加${props.size}${props.type == 'video' ? '个视频' : '张图片'}。`)
        }
        state.fileList[i].check = true
        state.cutFiles[cutItem.id] = cutItem
        state.checkedList.push(cutItem)
      }
    }
  }
  // 获取图片信息
  /**
   * 获取文件类型
   * @param file
   */
  function getFileTypeByMimeType(file: File) {
    var mime = file.type
    if (mime.startsWith('image/')) {
      return 'image'
    } else if (mime.startsWith('video/')) {
      return 'video'
    } else {
      return 'unknown'
    }
  }
  /**
   * 获取宽高比
   */
  function gcd(a: number, b: number) {
    return b ? gcd(b, a % b) : a
  }
  function simplifyFraction(numerator: number, denominator: number) {
    const sign = numerator * denominator < 0 ? -1 : 1
    const absoluteNumerator = Math.abs(numerator)
    const absoluteDenominator = Math.abs(denominator)
    const greatestCommonDivisor = gcd(absoluteNumerator, absoluteDenominator)

    let obj = {
      numerator: (absoluteNumerator / greatestCommonDivisor) * sign,
      denominator: (absoluteDenominator / greatestCommonDivisor) * sign
    }
    return obj.numerator + ' / ' + obj.denominator
  }
  function getImageDimensions(file: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      const url = URL.createObjectURL(file)
      reader.onload = function () {
        if (getFileTypeByMimeType(file) === 'image') {
          const img = new Image()
          img.onload = function () {
            resolve({
              type: 'image',
              width: img.width,
              height: img.height,
              size: Number(file.size / 1024 / 1024).toFixed(2) + 'm',
              ratio: simplifyFraction(img.width, img.height)
            })
          }
          img.onerror = function () {
            reject(new Error('Failed to load image'))
          }
          img.src = url
        } else if (getFileTypeByMimeType(file) === 'video') {
          const video = document.createElement('video')
          video.src = url
          video.addEventListener('loadeddata', () => {
            const width = video.videoWidth
            const height = video.videoHeight
            const duration = video.duration
            console.log({ width, height, duration, size: Number(file.size / 1024 / 1024).toFixed(2) })
            resolve({
              type: 'video',
              width,
              height,
              duration,
              size: Number(file.size / 1024 / 1024).toFixed(2) + 'm',
              ratio: simplifyFraction(width, height)
            })
          })
        } else {
          resolve({})
        }
      }

      reader.onerror = function () {
        reject(new Error('Failed to read file'))
      }

      reader.readAsDataURL(file)
    })
  }
  // 提交选中的数据
  const subLoading = ref(false)
  const subData = async () => {
    if (Object.keys(state.cutFiles).length > props.size) {
      return message.warning(`最多只能选中${props.size}个`)
    }

    // 选中素材库以点击顺序导出
    if (props.isH5Upload) {
      subLoading.value = true
      const h5Imglist = await Promise.all(
        state.checkedList.map(async (item: any) => {
          if (item.extension.indexOf('gif') !== -1) {
            return item
          } else {
            try {
              const response = await fetch(item.file_url)
              const blob = await response.blob()
              const file = new File([blob], item.file_name, { type: 'image/' })
              const formData = new FormData()
              formData.append('images', file)
              const h5Item = await uploadImage(formData)
              h5Item.data[0].fileInfo = await getImageDimensions(file)
              return h5Item.data
            } catch (error) {
              console.log(error, '获取评论信息失败')
              message.error(error?.msg || '上传失败，请重新上传')
            }
          }
        })
      )
      const newArr = h5Imglist.filter((v) => v)
      if (newArr.length) {
        const status = state.checkedList.some((v: any) => v.height >= 8192)
        if (status) {
          message.warning('当前图片长度已超出限制，可能会导致审核不通过，请知晓。')
        }
      }
      subLoading.value = false
      emit('event', { cmd: 'material', data: h5Imglist })
    } else {
      emit('event', { cmd: 'material', data: state.checkedList })
    }
  }
  onBeforeMount(() => {
    console.log(props.fileSize, 'fileSize')
    if (props.fileSize) state.fileListParams.file_size = props.fileSize
  })
  onBeforeUnmount(() => {
    state.cutFiles = {}
  })
</script>

<style lang="scss" scoped>
  .material_library_wrapper {
    .upload_button {
      margin-bottom: 10px;
    }
    .box {
      width: 100%;
      background: #ffffff;
      border-radius: 6px;
    }

    .right {
      flex: 1;

      .right-content {
        margin-left: 24px;
        min-height: calc(100vh - 245px);

        .right-content-row1 {
          display: flex;
          align-items: center;
          span:nth-child(1) {
            font-size: 16px;
            font-weight: 500;
            color: #313232;
          }

          span:nth-child(2) {
            font-size: 14px;
            color: #404040;
          }

          span:nth-child(3) {
            font-size: 14px;
            color: #404040;
          }
        }

        .right-content-row2 {
          padding-right: 20px;
          justify-content: space-between;
        }

        .list {
          display: flex;
          flex-wrap: wrap;
          max-height: calc(100vh - 360px);
          overflow-x: hidden;
          gap: 30px 15px;

          .list-item {
            position: relative;
            width: 106px;
            height: 133px;
            border-radius: 8px;
            border: 1px solid #ebebeb;

            &:hover {
              .list-item-pop.isImg,
              .list-item-popBtn {
                opacity: 1 !important;
              }
              .list-item-pop.isVideo {
                display: none;
              }
              .list-item-del {
                display: flex;
              }
            }

            .list-item-pop {
              cursor: pointer;
              position: absolute;
              z-index: 2;
              width: 100%;
              height: 133px;
              left: 0;
              top: 0;
              background: rgba(0, 0, 0, 0);
            }

            .list-item-popBtn {
              opacity: 0;
              position: absolute;
              z-index: 3;
            }

            .list-item-cut {
              width: 13px;
              height: 13px;
              left: 10px;
              top: 10px;
            }

            .list-item-look {
              left: 56px;
              top: 50px;
              cursor: pointer;
            }

            .list-item-del {
              cursor: pointer;
              left: 0px;
              bottom: 26px;
              color: #ffffff;
              font-size: 12px;
              background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #888888 100%);
              opacity: 0.81;
              display: none;
              width: 100%;
              height: 30px;
              line-height: 30px;
              text-align: center;
            }

            .list-item-title {
              font-size: 12px;
              width: 91px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              position: absolute;
              z-index: 5;
              margin-left: 5px;
              :deep(.ant-input) {
                height: 20px;
              }
              .isVideo {
                margin-top: 10px;
              }
            }
          }
          .list-item {
            &.hide_margin:nth-of-type(5n + 5) {
              margin-right: 0;
            }
          }
        }
        .dialog_h {
          max-height: calc(100vh - 465px);
        }
      }
      .dialog {
        .right {
          .right-content {
            .list {
            }
          }
        }
      }
      .btns {
        position: absolute;
        bottom: -15px;
        right: 0;
        .right-btns {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin: 20px 0px 0 0;
        }
      }
    }
  }
  :deep(.ant-select.ant-select-in-form-item) {
    width: inherit;
  }

  .list::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  .list::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }
  .list::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 10px;
    display: block;
    padding-left: 30px;
  }
  .row1_right_tips {
    color: #85878a;
    font-size: 12px;
    margin-left: 8px;
  }
</style>
