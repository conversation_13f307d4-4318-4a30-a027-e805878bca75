// 导出请求封装方法
import http from '@/utils/request'

// 获取账户信息
export const getUserInfo = (data: any) => {
  return http('get', `/common/user/info`, data)
}

// 获取账户权限信息
export const getUserAuth = (data: any) => {
  return http('get', `/common/user/permission`, data)
}

// 获取cos临时密钥
export const getCosAuth = (data: any) => {
  return http('get', `/common/file/credential`, data)
}

/**
 * 获取店铺列表
 * https://www.apifox.cn/web/project/2014698/apis/api-63590656
 */
export const getShopListApi = (data: any) => {
  return http('get', `/admin/shop/list`, { page: 1, page_size: 10000, ...data })
}
// 获取素材库数据
export const getFileList = (data: any) => {
  return http('get', `/common/file/list`, data)
}
/**
 * 编辑素材库文件名称
 */
export const updateNameFile = (data: any) => {
  return http('post', `/common/file/update_name`, data)
}
/**
 * 删除素材库文件
 */
export const deleteFile = (data: any) => {
  return http('post', `/common/file/delete`, data)
}

// 上传素材库图片
export const fileSave = (data: any) => {
  return http('post', `/common/file/save`, data)
}
// 素材库文件/修改文件或分组名称
export const fileEdit = (data: any) => {
  return http('post', `/common/file/edit`, data)
}

/**
 * 重置token
 * @param {String} shop_id 店铺id
 * @returns
 */
export const cutShopUpdateTokenApi = (shop_id: any) => {
  return http('post', `/common/token/reset`, {
    shop_id
  })
}

// upload_image
export const uploadImage = (formData: any) => {
  return http('post', `/admin/ad_h5/upload_image`, formData, { noTip: true, timeout: 60000 })
}

/**
 * 导出
 * @param {String} type :  order-> 订单导出  params： 搜索条件 转为json字符串
 * @returns
 */
export const exportCreate = (data: any) => {
  return http('post', `/common/exportdata/create`, data)
}

/**
 * 客服信息
 */
export const kefuInfo = (data: any) => {
  return http('get', `/common/kefu/info`, data)
}
