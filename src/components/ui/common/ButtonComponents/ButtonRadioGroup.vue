<template>
  <a-row :gutter="[12, 12]" class="a-row-btn">
    <a-col>
      <a-radio-group :value="data.value" @change="changeValue">
        <a-radio-button
          v-for="item in data.list"
          :key="item.value"
          v-bind="item"
          :class="{ hight: item.type == 'refund' && item.total }"
        >
          {{ item.label }} ({{ item.total }})
        </a-radio-button>
      </a-radio-group>
    </a-col>
  </a-row>
</template>

<script setup>
  const props = defineProps(['data'])
  const emits = defineEmits(['changeValue'])
  const changeValue = (e) => {
    emits('changeValue', { value: e.target.value })
  }
</script>

<style scoped lang="scss">
  .a-row-btn {
    margin-top: 24px;
  }
  .ant-radio-button-wrapper.hight {
    color: red;
    border-color: red;
    z-index: 12;
  }
  .hight.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
    background-color: red;
  }
  .hight.ant-radio-button-wrapper:not(:first-child)::before {
    background-color: red;
  }
</style>
