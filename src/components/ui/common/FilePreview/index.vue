<template>
  <div :class="containerMap[viewRatio].css || 'default'" ref="fileEl">
    <div class="preview_box" :style="{ height: height }">
      <!-- 播放按钮 -->
      <div class="flex-center w-100% h-100% text-12px color-white">
        <SvgIcon v-if="showPlay" class="svg_play" :icon="src ? 'play' : 'play'" @click="onPreivew(src)"></SvgIcon>
        <img v-if="!isShowVideo" :src="videoSrcHanlder(src) || requireImg('error.png')" alt="图片" />
        <video v-else :src="src" style="width: 100%; height: 100%"></video>
      </div>
      <!-- 有视频播放的时候 -->
    </div>
    <a-modal
      v-model:open="state.previewShow"
      :closable="false"
      centered
      destroyOnClose
      :footer="null"
      width="460px"
      wrapClassName="modalWrapClassName"
    >
      <div class="close" @click="state.previewShow = false">关闭</div>
      <video :src="state.sourceUrl" autoplay controls class="video"></video>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, ref, reactive } from 'vue'
  import { requireImg, getConfig } from '@/utils'
  const fileEl = ref<HTMLElement | null>(null)
  let height = ref('0px')

  defineOptions({ name: 'FilePreview' })
  let props = defineProps({
    ratio: {
      type: String,
      default: ''
    },

    src: {
      type: String,
      default: ''
    },
    showPlay: {
      type: Boolean,
      default: true
    },
    // 目前无法得知视频来源是平台上传的，还是外面的，所以通过此方式在不同页面处理,外面传来的需要展示video
    // 有的地方是上传视频的时候是没有封面的，所以需要设置为true，显示video标签
    isShowVideo: {
      type: Boolean,
      default: false
    },
    imageSrc: {
      type: String,
      default: ''
    }
  })
  let containerMap: any = {
    '16:9': {
      css: 'file_container across',
      getHeight: (width: number) => {
        return Math.ceil((width * 9) / 16)
      }
    },
    '9:16': {
      css: 'file_container vertical',
      getHeight: (width: number) => {
        return Math.ceil((width * 16) / 9)
      }
    },
    default: {
      css: 'file_container default',
      getHeight: (width: number) => {
        return Math.ceil((width * 16) / 9)
      }
    }
  }
  const state = reactive({
    previewShow: false,
    sourceUrl: ''
    // sourceUrl: 'https://cos.juhaom.com/wxFile/prod/1694519759216_sDKJGiO9SpuP.mp4'
    //https://cos.juhaom.com/wxFile/prod/1694089831191_rs3aBURn7pKo.mp4
  })
  let viewRatio = computed(() => {
    return props.ratio || 'default'
  })
  const isVideo = (src: string) => {
    let s = src.toLowerCase()
    return s.endsWith('.mp4') || s.endsWith('.mov') || s.endsWith('.mkv') || s.endsWith('.MKV')
  }
  const isM3u8 = (src: string) => {
    return src.endsWith('.m3u8') || src.endsWith('.M3U8')
  }
  const convertToImg = (mp4Url) => {
    const fileName = mp4Url.split('/').pop()
    const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)

    let upload_type = getConfig('UPLOAD_TYPE') || 'oss'
    let img
    if (upload_type == 'cos') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_0.jpg`
    } else if (upload_type == 'oss') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_1.jpg`
    } else {
      img = ''
    }
    return img
  }
  function convertM3U8ToImage(m3u8Url: string) {
    // 提取视频文件名，不包括扩展名
    const videoFileName = m3u8Url?.split('/')?.pop().split('.').shift()
    // 拼接成对应的图片 URL
    const imageUrl = m3u8Url.replace(videoFileName + '.m3u8', videoFileName + '_0.jpg')
    return imageUrl
  }
  const videoSrcHanlder = (src: string) => {
    let url = ''
    if (isM3u8(src)) {
      url = convertM3U8ToImage(src)
    } else if (isVideo(src)) {
      url = props.imageSrc ? props.imageSrc : convertToImg(src)
    }
    return url
  }
  function getDomSize() {
    if (fileEl.value) {
      console.log("gai'baigaibai你")
      height.value = containerMap[viewRatio.value].getHeight(fileEl.value.offsetWidth) + 'px'
      fileEl.value.style.height = Math.ceil((fileEl.value.offsetWidth * 16) / 9) + 'px'
    }
  }
  // 视频预览
  const onPreivew = (url: any) => {
    if (!url) return false
    state.previewShow = true
    state.sourceUrl = url
  }
  onMounted(() => {
    getDomSize()
    fileEl.value && fileEl.value.addEventListener('resize', getDomSize)
  })
  onUnmounted(() => {
    fileEl.value && fileEl.value.removeEventListener('resize', getDomSize)
  })
</script>
<style lang="scss" scoped>
  .file_container {
    width: 100%;
    background-color: #e4e5e6;
    border-radius: 2px;
    border: 1px solid #e4e5e6;
    display: flex;
    align-items: center;
    justify-content: center;
    background-repeat: no-repeat; /* 防止背景重复 */
    background-size: 10px 4px;
    .preview_box {
      position: relative;
    }
    .svg_play {
      width: 26px;
      height: 26px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
      z-index: 100;
    }
    &.across {
      .preview_box {
        width: 100%;
        background-color: #d5d6d6;
      }
    }
    &.vertical {
      border: 1px solid var(--primary-color);
      .preview_box {
        width: 100%;
        background-color: var(--primary-color);
      }
    }
  }
</style>
