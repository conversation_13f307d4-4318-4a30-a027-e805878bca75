<template>
  <a-modal
    v-model:open="exportsData.visible"
    title="导出"
    :footer="null"
    :maskClosable="false"
    centered
    destroyOnClose
    :width="830"
  >
    <div class="spec-export-wrapper" :class="[isMobile && 'mobile-spec-export-wrapper']">
      <div class="left common">
        <div class="title">
          <a-checkbox v-model:checked="state.checkedAll" :indeterminate="indeterminate" @change="checkAll">
            导出全部（{{ state.currentChecked.length }}/{{ state.options.length }}）
          </a-checkbox>
        </div>
        <div class="content">
          <a-checkbox-group
            class="checkbox-group-wrapper"
            v-model:value="state.currentChecked"
            :options="state.options"
          >
            <template #label="{ label }">
              <a-tooltip placement="topLeft">
                <template #title v-if="label.length > 9">{{ label }}</template>
                <div class="text_overflow_row1 max-w-120px">
                  {{ label }}
                </div>
              </a-tooltip>
            </template>
          </a-checkbox-group>
        </div>
      </div>
      <a-divider class="line" :type="!isMobile ? 'vertical' : 'horizontal'" />
      <div class="right common">
        <div class="title flex-y-center justify-between mr-16px">
          <span>当前选定字段（{{ state.currentChecked.length }}）</span>
          <a-button type="link" size="small" class="p-0! h-auto" @click="state.currentChecked = []">重置</a-button>
        </div>
        <draggable v-model="state.currentChecked" itemKey="key" tag="div" class="draggable-wrapper">
          <template #item="{ element: item }">
            <div class="item">
              <div class="c-#444444 line-height-20px">
                <img class="w-10px h-9px mr-8px" :src="requireImg('base/dragger.png')" alt="" />
                <span>{{ item }}</span>
              </div>
              <CloseOutlined class="font-size-12px" @click="deleteItem(item)" />
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <div class="text-right mt-16px">
      <a-button @click="emits('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm">导出</a-button>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
  import { reactive, watch, onMounted, computed } from 'vue'
  import { randomWord, requireImg } from '@/utils'
  import { getExportDataHeader, exportCreate } from '@/api/common'

  import { useDownloadCenter } from '@/hooks'
  import { CloseOutlined } from '@ant-design/icons-vue'
  import draggable from 'vuedraggable'
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const { goCenter } = useDownloadCenter()
  /**
   * exportsData
   *  visible: false,
      type: 'wx_customer_export', // 导出的type
      params: data.params // 筛选条件
   */
  const props = defineProps({
    exportsData: {
      type: Object,
      default: () => {}
    }
  })
  const emits = defineEmits(['onEvent'])
  interface Item {
    label: string
    value: string
  }
  const state = reactive({
    loading: false,
    checkedAll: false,
    indeterminate: false,
    currentChecked: [] as string[],
    checkedList: [] as string[],
    options: [] as string[]
  })
  const indeterminate = computed(() => {
    if (state.currentChecked.length !== state.options.length) {
      state.checkedAll = false
    } else {
      state.checkedAll = true
    }
    return state.currentChecked.length == state.options.length || !state.currentChecked.length ? false : true
  })
  // watch(
  //   () => state.currentChecked,
  //   (val: any) => {
  //     state.checkedList = state.options.filter((it: any) => val.includes(it.value))
  //   }
  // )
  const init = async () => {
    try {
      if (props.exportsData.type) {
        let res = await getExportDataHeader({ type: props.exportsData.type })
        // state.options = res.data.list.map((it: string) => {
        //   return {
        //     label: it,
        //     value: randomWord(false, 5)
        //   }
        // })
        state.options = res.data.list || []
        state.currentChecked = res.data.user_list || []
      }
    } catch (err) {
      console.log(err)
    }
  }
  onMounted(() => {
    init()
  })

  const checkAll = (val: any) => {
    if (val.target.checked) {
      state.checkedAll = true
      state.currentChecked = state.options
    } else {
      state.checkedAll = false
      state.currentChecked = []
    }
  }
  const deleteItem = (item: any) => {
    state.currentChecked = state.currentChecked.filter((it) => it !== item)
  }
  const submitForm = async () => {
    try {
      state.loading = true
      if (!state.currentChecked.length) {
        return message.warning('请选择一项进行导出')
      }
      let params = {
        type: props.exportsData.type,
        params: JSON.stringify(props.exportsData.params),
        headers: JSON.stringify(state.currentChecked)
      }
      await exportCreate(params)
      goCenter()
      emits('onEvent', { cmd: 'submit' })
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .spec-export-wrapper {
    background: #fafafa;
    border-radius: 8px;
    height: 374px;
    display: flex;
    .common {
      padding: 16px 0 16px 16px;
    }
    .left {
      width: 60%;
    }
    .right {
      width: 40%;
    }
    .line {
      height: 100%;
      margin: 0;
      border-inline-start: 1px solid #e6e6e6;
    }
    .title {
      color: #0c1d29;
      line-height: 20px;
    }
    :deep(.ant-checkbox-inner) {
      height: 16px;
      width: 16px;
    }
    .content {
      height: 320px;
      overflow: auto;
    }
    .checkbox-group-wrapper {
      width: 100%;
      margin-top: 14px;
      display: flex;
      // grid-template-columns: repeat(4, 1fr); /* 默认4列 */
      gap: 16px;
      :deep(.ant-checkbox-wrapper) {
        color: #444;
      }
    }
    .draggable-wrapper {
      margin-top: 14px;
      height: 300px;
      overflow: auto;
      padding-right: 16px;
      .item {
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px 8px;
        margin-bottom: 8px;
        cursor: pointer;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .mobile-spec-export-wrapper {
    flex-direction: column;
    overflow: auto;
    .left {
      width: 100%;
    }
    .right {
      width: 100%;
    }
    .content {
      height: auto;
    }
    .draggable-wrapper {
      height: auto;
    }
  }
</style>
