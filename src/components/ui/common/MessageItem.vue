<template>
  <div class="message-item">
    <div class="message-item-label">
      <slot name="label"> {{ label }} : </slot>
    </div>
    <div class="message-item-content">
      <slot name="content">
        {{ content }}
      </slot>
    </div>
  </div>
</template>

<script setup>
  defineProps(['label', 'content'])
</script>

<style scoped lang="scss">
  .message-item {
    display: flex;
    box-sizing: border-box;
    .message-item-label {
      color: #cccccc;
      flex-shrink: 0;
    }
    .message-item-content {
      flex: 1;
      padding: 0 10px;
      box-sizing: border-box;
      word-break: break-word;
    }
  }
</style>
