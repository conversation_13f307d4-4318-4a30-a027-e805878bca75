<template>
  <div class="comp_area">
    <div class="area_list">
      <a-select
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        v-model:value="state.province"
        placeholder="省"
        class="area_select"
        @change="provinceChange"
      >
        <a-select-option v-for="item in state.provinceList" :key="item.id" :value="item.id">{{
          item.name
        }}</a-select-option>
      </a-select>
      <a-select
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        ref="cityRef"
        v-model:value="state.city"
        placeholder="市"
        class="area_select"
        :open="cityOpen"
        @change="cityChange"
      >
        <a-select-option v-for="item in state.cityList" :key="item.id" :label="item.name" :value="item.id">{{
          item.name
        }}</a-select-option>
      </a-select>
      <a-select
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        ref="countyRef"
        v-model:value="state.county"
        placeholder="区"
        class="area_select"
        :open="countyOpen"
        @change="countyChange"
      >
        <a-select-option v-for="item in state.countyList" :key="item.id" :label="item.name" :value="item.id">{{
          item.name
        }}</a-select-option>
      </a-select>
    </div>
    <div class="area_info">
      <a-textarea
        :auto-size="{ minRows: 2, maxRows: 3 }"
        v-model:value="state.address"
        :maxlength="40"
        placeholder="请输入详细地址"
        @input="handleAdressInput"
        @blur="emitData"
      />
    </div>
  </div>
</template>

<script setup name="Area" lang="tsx">
  import { ref, reactive, nextTick, watch, computed } from 'vue'
  import areaList from './area.json'
  import { noEmoji } from '@/utils'

  const props = defineProps({
    modelValue: {
      type: Array,
      deflaut: () => []
    },
    info: {
      type: String,
      deflaut: ''
    }
  })

  const cityRef = ref(null)
  const countyRef = ref(null)

  const state = reactive({
    province: null,
    provinceName: null,
    provinceList: [],
    city: null,
    cityName: null,
    cityList: [],
    county: null,
    countyName: null,
    countyList: [],
    address: ''
  })

  const emit = defineEmits(['update:modelValue', 'change', 'update:info'])

  watch(
    () => props.modelValue,
    () => {
      initData()
    },
    { immediate: true, deep: true }
  )

  watch(
    () => props.info,
    () => {
      state.address = props.info || ''
    },
    { immediate: true, deep: true }
  )

  // const info = computed({
  //   set(val) {
  //     emit('update:info', val)
  //   },
  //   get() {
  //     return props.info
  //   }
  // })

  const handleAdressInput = (e) => {
    console.log('handleAdressInput', e)
    if (noEmoji().test(e.target.value)) {
      state.address = e.target.value.replaceAll(noEmoji(), '').trim()
    }
  }

  function initData() {
    getSelectList('province')
    if (props.modelValue.length) {
      state.province = props.modelValue[0]
      state.city = props.modelValue[1]
      state.county = props.modelValue[2]

      getSelectList('city', props.modelValue[0])
      getSelectList('county', props.modelValue[1])

      getNameByCode('province', props.modelValue[0])
      getNameByCode('city', props.modelValue[1])
      getNameByCode('county', props.modelValue[2])
    }
  }

  function getSelectList(type: string, code: unknown) {
    switch (type) {
      case 'province':
        setData('province', areaList, code)
        break

      case 'city':
        setData('city', state.provinceList, code)
        break

      case 'county':
        setData('county', state.cityList, code)
        break
    }

    function setData(name: string, data: any[], code: any) {
      if (name !== 'province' && code) {
        data = (data.find((item: { id: any }) => item.id === code) || {}).childs
      }

      state[name + 'List'] = (data || []).map((item: { name: any; id: any; childs: any }) => {
        return {
          name: item.name,
          id: item.id,
          childs: item.childs
        }
      })
    }
  }

  function getNameByCode(name: string, code: unknown) {
    const findData = state[name + 'List'].find((item: { id: any }) => item.id === code) || {}
    state[name + 'Name'] = findData.name
  }

  // 选择省
  const cityOpen = ref(false)
  function provinceChange(val: any) {
    getSelectList('city', val)
    getNameByCode('province', val)
    state.countyList = []
    state.city = null
    state.cityName = null
    state.county = null
    state.countyName = null
    nextTick(() => {
      // cityRef.value.toggleMenu()
      cityOpen.value = true
    })
    emitData()
  }

  // 选择市
  const countyOpen = ref(false)
  function cityChange(val: any) {
    cityOpen.value = false
    getSelectList('county', val)
    getNameByCode('city', val)
    state.county = null
    state.countyName = null
    nextTick(() => {
      // countyRef.value.toggleMenu()
      countyOpen.value = true
    })
    emitData()
  }

  // 选择区
  function countyChange(val: any) {
    countyOpen.value = false
    const { name } = state.countyList.find((item) => item.id === val) || {}
    state.countyName = name
    emitData()
  }

  function emitData() {
    const data = {
      provinceCode: state.province,
      provinceName: state.provinceName,
      cityCode: state.city,
      cityName: state.cityName,
      countyCode: state.county,
      countyName: state.countyName,
      info: state.address
    }
    emit('update:modelValue', [state.province, state.city, state.county])
    emit('change', data)
  }
</script>

<style lang="scss" scoped>
  // @import url('../common.less');
  .comp_area {
    display: inline-block;
    .area_select {
      width: 100px;
      margin-right: 5px;

      &:last-child {
        margin-right: 0;
      }
    }
    .area_info {
      margin-top: 10px;
    }
  }
</style>
