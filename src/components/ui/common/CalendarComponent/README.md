# CalendarComponent 日历组件

这是一个可复用的日历组件，支持日期选择、月份导航、历史数据展示等功能。

## 功能特性

- 📅 完整的日历视图
- 🎯 日期选择功能
- 📊 历史数据展示
- 🎨 自定义样式支持
- 📱 响应式设计

## 使用方法

### 基本用法

```vue
<template>
  <CalendarComponent
    :current-date="currentDate"
    :selected-dates="selectedDates"
    :historical-data="historicalData"
    @date-click="handleDateClick"
    @month-change="handleMonthChange"
  />
</template>

<script setup>
  import CalendarComponent from '@/components/ui/common/CalendarComponent/index.vue'

  const currentDate = ref(new Date())
  const selectedDates = ref([])
  const historicalData = ref({})

  const handleDateClick = (date) => {
    console.log('点击的日期:', date)
  }

  const handleMonthChange = (newDate) => {
    currentDate.value = newDate
  }
</script>
```

## Props

| 属性名         | 类型   | 默认值     | 说明             |
| -------------- | ------ | ---------- | ---------------- |
| currentDate    | Date   | new Date() | 当前显示的日期   |
| selectedDates  | Array  | []         | 已选择的日期数组 |
| historicalData | Object | {}         | 历史数据对象     |

## Events

| 事件名       | 参数    | 说明           |
| ------------ | ------- | -------------- |
| date-click   | date    | 点击日期时触发 |
| month-change | newDate | 月份变化时触发 |

## 数据结构

### selectedDates 数组项结构

```javascript
{
  date: Date,        // 日期对象
  amount: number,     // 数量
  key: string        // 唯一标识
}
```

### historicalData 对象结构

```javascript
{
  '2024-01-15': {
    amount: 100
  },
  '2024-01-16': {
    amount: 150
  }
}
```

## 样式类名

组件使用以下 CSS 类名：

- `.calendar-section` - 日历容器
- `.calendar-header` - 日历头部
- `.month-year` - 年月显示
- `.calendar-grid` - 日历网格
- `.week-header` - 星期标题行
- `.week-day` - 星期标题单元格
- `.date-grid` - 日期网格
- `.date-cell` - 日期单元格
- `.other-month` - 非当前月份
- `.today` - 今天
- `.past` - 过去的日期
- `.selected` - 已选择的日期
- `.historical` - 有历史数据的日期
- `.clickable` - 可点击的日期

## 注意事项

1. 组件依赖 Ant Design Vue 的图标组件
2. 日期格式统一使用 'YYYY-MM-DD' 格式
3. 历史数据通过 `historicalData` prop 传入，键为日期字符串
4. 组件会自动处理月份切换和日期选择逻辑
