<template>
  <div class="calendar-section">
    <div class="calendar-header">
      <a-button type="text" @click="prevMonth">
        <LeftOutlined />
      </a-button>
      <span class="month-year"> {{ currentYear }}年 {{ currentMonth }}月 </span>
      <a-button type="text" @click="nextMonth">
        <RightOutlined />
      </a-button>
    </div>

    <div class="calendar-grid">
      <!-- 星期标题 -->
      <div class="week-header">
        <div v-for="day in weekDays" :key="day" class="week-day">{{ day }}</div>
      </div>

      <!-- 日期网格 -->
      <div
        class="date-grid"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseLeave"
      >
        <div
          v-for="date in calendarDates"
          :key="date.key"
          :class="getDateClass(date)"
          @click="handleDateClick(date)"
          @mouseenter="handleMouseEnter(date)"
        >
          <div class="date-cell-inner">{{ date.day }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, reactive } from 'vue'
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'

  const props = defineProps({
    currentDate: {
      type: Date,
      default: () => new Date()
    },
    selectedDates: {
      type: Array,
      default: () => []
    },
    historicalData: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['date-click', 'month-change', 'batch-select'])

  // 拖动相关状态
  const dragState = reactive({
    isDragging: false,
    startDate: null,
    endDate: null,
    draggedDates: []
  })

  const weekDays = ['一', '二', '三', '四', '五', '六', '日']

  // 计算当前年月
  const currentYear = computed(() => props.currentDate.getFullYear())
  const currentMonth = computed(() => props.currentDate.getMonth() + 1)

  // 计算日历日期
  const calendarDates = computed(() => {
    const year = currentYear.value
    const month = currentMonth.value
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1)

    const dates = []
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate)
      date.setDate(startDate.getDate() + i)

      const isCurrentMonth = date.getMonth() === month - 1
      const isToday = date.getTime() === today.getTime()
      const isPast = date < today
      const isSelected = props.selectedDates.some((d) => d.date.getTime() === date.getTime())
      const hasHistoricalData = props.historicalData[formatDate(date)]

      dates.push({
        date,
        day: date.getDate(),
        key: formatDate(date),
        isCurrentMonth,
        isToday,
        isPast,
        isSelected,
        hasHistoricalData
      })
    }

    return dates
  })

  // 格式化日期
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 获取日期样式类
  const getDateClass = (date) => {
    const isInDragRange =
      dragState.isDragging && dragState.draggedDates.some((d) => d.getTime() === date.date.getTime())

    // 检查是否在连续选中范围内
    const rangeInfo = getDateRangeInfo(date)

    return {
      'date-cell': true,
      'other-month': !date.isCurrentMonth,
      today: date.isToday,
      past: date.isPast,
      selected: date.isSelected,
      historical: date.hasHistoricalData,
      clickable: !date.isPast,
      'drag-range': isInDragRange,
      'range-start': rangeInfo.isRangeStart,
      'range-end': rangeInfo.isRangeEnd,
      'range-middle': rangeInfo.isRangeMiddle,
      'in-range': rangeInfo.isInRange
    }
  }

  // 获取日期在范围中的位置信息
  const getDateRangeInfo = (date) => {
    if (!date.isSelected || props.selectedDates.length < 2) {
      return {
        isRangeStart: false,
        isRangeEnd: false,
        isRangeMiddle: false,
        isInRange: false
      }
    }

    // 按时间排序选中的日期
    const sortedDates = [...props.selectedDates].sort((a, b) => a.date.getTime() - b.date.getTime())
    const currentTime = date.date.getTime()

    // 找到当前日期在排序数组中的位置
    const currentIndex = sortedDates.findIndex((d) => d.date.getTime() === currentTime)

    if (currentIndex === -1) {
      return {
        isRangeStart: false,
        isRangeEnd: false,
        isRangeMiddle: false,
        isInRange: false
      }
    }

    // 检查是否有连续的日期范围
    const isInContinuousRange = checkIfInContinuousRange(sortedDates, currentIndex)

    if (!isInContinuousRange) {
      return {
        isRangeStart: false,
        isRangeEnd: false,
        isRangeMiddle: false,
        isInRange: false
      }
    }

    const isFirst = currentIndex === 0
    const isLast = currentIndex === sortedDates.length - 1
    const isRangeStart = isFirst || !isConsecutive(sortedDates[currentIndex - 1].date, date.date)
    const isRangeEnd = isLast || !isConsecutive(date.date, sortedDates[currentIndex + 1].date)
    const isRangeMiddle = !isRangeStart && !isRangeEnd

    return {
      isRangeStart,
      isRangeEnd,
      isRangeMiddle,
      isInRange: true
    }
  }

  // 检查日期是否在连续范围内
  const checkIfInContinuousRange = (sortedDates, currentIndex) => {
    const currentDate = sortedDates[currentIndex].date

    // 检查前一个日期是否连续
    const hasPrevContinuous = currentIndex > 0 && isConsecutive(sortedDates[currentIndex - 1].date, currentDate)

    // 检查后一个日期是否连续
    const hasNextContinuous =
      currentIndex < sortedDates.length - 1 && isConsecutive(currentDate, sortedDates[currentIndex + 1].date)

    return hasPrevContinuous || hasNextContinuous
  }

  // 检查两个日期是否连续
  const isConsecutive = (date1, date2) => {
    const nextDay = new Date(date1)
    nextDay.setDate(nextDay.getDate() + 1)
    return nextDay.getTime() === date2.getTime()
  }

  // 处理日期点击
  const handleDateClick = (date) => {
    emit('date-click', date)
  }

  // 拖动事件处理
  const handleMouseDown = (event) => {
    // 只在左键点击时开始拖动
    if (event.button === 0) {
      dragState.isDragging = true
      dragState.startDate = null
      dragState.endDate = null
      dragState.draggedDates = []
    }
  }

  const handleMouseMove = (event) => {
    if (dragState.isDragging) {
      // 拖动过程中，可以在这里添加视觉反馈
    }
  }

  const handleMouseUp = () => {
    if (dragState.isDragging && dragState.startDate && dragState.endDate) {
      // 发送批量选择事件
      emit('batch-select', {
        startDate: dragState.startDate,
        endDate: dragState.endDate,
        dates: dragState.draggedDates
      })
    }

    // 重置拖动状态
    dragState.isDragging = false
    dragState.startDate = null
    dragState.endDate = null
    dragState.draggedDates = []
  }

  const handleMouseLeave = () => {
    if (dragState.isDragging) {
      handleMouseUp()
    }
  }

  const handleMouseEnter = (date) => {
    if (dragState.isDragging && !date.isPast) {
      if (!dragState.startDate) {
        dragState.startDate = date.date
      }
      dragState.endDate = date.date

      // 计算拖动范围内的所有日期
      updateDraggedDates()
    }
  }

  // 更新拖动范围内的日期
  const updateDraggedDates = () => {
    if (!dragState.startDate || !dragState.endDate) return

    const start = new Date(dragState.startDate)
    const end = new Date(dragState.endDate)

    // 确保开始日期在结束日期之前
    if (start > end) {
      ;[dragState.startDate, dragState.endDate] = [dragState.endDate, dragState.startDate]
    }

    const dates = []
    const current = new Date(dragState.startDate)
    const endDate = new Date(dragState.endDate)

    while (current <= endDate) {
      dates.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    dragState.draggedDates = dates
  }

  // 月份导航
  const prevMonth = () => {
    const newDate = new Date(props.currentDate)
    newDate.setMonth(newDate.getMonth() - 1)
    emit('month-change', newDate)
  }

  const nextMonth = () => {
    const newDate = new Date(props.currentDate)
    newDate.setMonth(newDate.getMonth() + 1)
    emit('month-change', newDate)
  }
</script>

<style lang="scss" scoped>
  .calendar-section {
    width: 448px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;

    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 12px;
      height: 40px;
      border-bottom: 1px solid #f0f0f0;

      .month-year {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.88);
      }

      button {
        width: 24px;
        height: 24px;
        color: rgba(0, 0, 0, 0.25);
        transition: color 0.3s;

        &:hover {
          color: rgba(0, 0, 0, 0.88);
        }

        .anticon {
          font-size: 12px;
        }
      }
    }

    .calendar-grid {
      padding: 8px 12px;

      .week-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        height: 30px;
        margin-bottom: 8px;

        .week-day {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          line-height: 30px;
        }
      }

      .date-grid {
        cursor: pointer;
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        row-gap: 4px;
        column-gap: 0;

        .date-cell {
          width: 100%;
          display: flex;
          justify-content: center;
          position: relative;
          margin: 0 auto;
          z-index: 1;

          .date-cell-inner {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            line-height: 24px;
            border-radius: 2px;
            transition:
              background 0.3s,
              border 0.3s;
          }

          &:hover .date-cell-inner {
            background: rgba(0, 0, 0, 0.04);
          }

          &.other-month {
            .date-cell-inner {
              color: rgba(0, 0, 0, 0.25);
            }
          }

          &.past {
            .date-cell-inner {
              color: rgba(0, 0, 0, 0.25);
              cursor: not-allowed;
            }

            &:hover .date-cell-inner {
              background: transparent;
            }
          }

          &.today {
            .date-cell-inner {
              font-weight: 500;
              color: #1677ff;
              border: 1px solid #1677ff;
            }
          }

          &.selected {
            .date-cell-inner {
              background: #1677ff;
              color: #fff;
            }
          }

          /* 范围选择样式 */
          &.in-range {
            position: relative;

            /* 为范围内的日期添加背景连接 */
            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 0;
              right: 0;
              height: 24px;
              background: #e6f7ff;
              transform: translateY(-50%);
              z-index: 0;
            }

            .date-cell-inner {
              position: relative;
              z-index: 1;
              background: #1677ff;
              color: #fff;
            }

            /* 范围开始 */
            &.range-start::before {
              left: 50%;
              border-radius: 0 2px 2px 0;
            }

            /* 范围结束 */
            &.range-end::before {
              right: 50%;
              border-radius: 2px 0 0 2px;
            }

            /* 范围中间 */
            &.range-middle::before {
              border-radius: 0;
            }

            /* 如果既是开始又是结束（单独的日期） */
            &.range-start.range-end::before {
              display: none;
            }
          }

          &.historical .date-cell-inner {
            position: relative;
            &::after {
              content: '';
              position: absolute;
              bottom: 2px;
              left: 50%;
              transform: translateX(-50%);
              width: 6px;
              height: 2px;
              background: #faad14;
              border-radius: 1px;
            }
          }

          &.drag-range .date-cell-inner {
            background: #e6f7ff;
            color: #1677ff;
            font-weight: 500;
          }
        }
      }
    }
  }
</style>
