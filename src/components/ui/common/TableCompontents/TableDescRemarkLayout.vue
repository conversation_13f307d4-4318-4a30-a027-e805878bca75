<template>
  <div
    :class="[
      'table-base-layout',
      Object.keys(slots).includes('desc') ? '' : 'table-base-layout-no-top-border',
      Object.keys(slots).includes('remark') ? '' : 'table-base-layout-no-bottom-border'
    ]"
    class="common_page_table"
  >
    <div v-if="Object.keys(slots).includes('desc')" class="table-base-layout-desc" :style="data.desc.descStyle">
      <slot name="desc"> </slot>
    </div>
    <a-table v-bind="data.props">
      <template v-slot:[item]="scope" v-for="item in Object.keys(slots)">
        <slot :name="item" :scope="scope"></slot>
      </template>
    </a-table>
    <div class="table-base-layout-remark" v-if="Object.keys(slots).includes('remark')" :style="data.remark.remarkStyle">
      <slot name="remark"> </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useSlots } from 'vue'
  const slots = useSlots()
  defineProps(['data'])
</script>

<style scoped lang="scss">
  .table-base-layout {
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius-medium);
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 0px !important;
    }
    :deep(.ant-table-wrapper .ant-table-container table > tbody > tr:first-child > *:last-child) {
      border-inline-end: none;
    }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          > .ant-table-container
          > .ant-table-content
          > table
          > tbody
          > tr
          > td:last-child
      ) {
      border-inline-end: none;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child) {
      border-inline-end: none;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container) {
      border-inline-start: none;
      border-inline-end: none;
    }
    :deep(.ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-tbody > tr > td.ant-table-cell-row-hover) {
      background: #fff !important;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child) {
      border-start-start-radius: 0px;
    }
    :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child) {
      border-start-end-radius: 0px;
    }
    :deep(.ant-table-wrapper .ant-table-container) {
      border-start-start-radius: 0px;
      border-start-end-radius: 0px;
    }
    :deep(.ant-table-wrapper table) {
      border-radius: 0px !important;
    }
    :deep(.ant-table-thead > tr > th) {
      background: transparent;
      border-radius: 0px;
      color: var(--text-color-gray);
      font-weight: normal;
    }
    :deep(.ant-table-thead > tr > th::before) {
      background-color: transparent;
      border-radius: 0px;
    }
  }
  .table-base-layout-no-top-border {
    border-top: none;
  }
  .table-base-layout-no-bottom-border {
    border-bottom: none;
  }
  .table-base-layout .table-base-layout-desc {
    background: rgba(36, 47, 87, 0.05);
    padding: 5px 12px;
  }
  .table-base-layout .table-base-layout-remark {
    //background: rgba(36, 47, 87, 0.05);
    //border-top: 1px solid #f0f0f0;
    padding: 5px 12px;
  }
</style>
