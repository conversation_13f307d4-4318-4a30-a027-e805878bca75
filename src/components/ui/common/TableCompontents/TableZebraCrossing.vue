<template>
  <div
    :class="[
      'table-zebra-crossing',
      tableData.verticalAlign ? 'vertical-align-top' : '',
      isAction ? 'table-zebra-action' : ''
    ]"
  >
    <a-table
      size="small"
      sticky
      v-bind="{ ...tableData, ...otherAttrs }"
      :columns="columns"
      :scroll="scroll"
      :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-zebra-crossing-striped' : null)"
      @change="pageChange"
      @expand="expand"
      :expand-column-width="32"
      bordered
      @resizeColumn="handleResizeColumn"
      :show-header="showHeader"
      :sortDirections="['descend','ascend']"
    >
      <template v-slot:[item]="scope" v-for="item in Object.keys(slots)">
        <slot :name="item" :scope="scope"></slot>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, useSlots, watch } from 'vue'
  import { useApp } from '@/hooks'
  import type { TableProps, ColumnType } from 'ant-design-vue'
  const { isMobile } = useApp()
  const props = defineProps({
    data: {
      type: Array
    },
    rowSelection: {
      type: Object
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  })
  const emits = defineEmits(['change', 'expand'])
  const slots = useSlots()

  const pageChange = (pagination: any, filters: any, sorter: any, { currentDataSource }: any) => {
    emits('change', pagination, filters, sorter, { currentDataSource })
  }
  const expand = (expanded, record) => {
    emits('expand', expanded, record)
  }
  const otherAttrs = computed(() => {
    if (props.rowSelection) {
      return { rowSelection: props.rowSelection }
    } else {
      return {}
    }
  })
  const isAction = computed(() => {
    if (props.data?.columns?.length) {
      const index = props.data.columns.findIndex((v: any) => ['action', 'operation'].includes(v.key))
      return index >= 0
    }
    return false
  })
  const isFixed = (item: any) => {
    if (isMobile.value) {
      return false
    }

    if (['action', 'handle', 'operation', 'actions'].includes(item.dataIndex)) {
      return 'right'
    }

    return item.fixed
  }
  // 使用 ref 让 columns 变成响应式 不能直接放到tableData中
  const columns = ref(
    props.data.columns.map((col) => ({
      ...col,
      resizable: props.data.hasOwnProperty('isResizable') ? col.isResizable : true, // ✅ 启用列宽调整
      width: col.width || 150, // ✅ 必须设置默认宽度
      fixed: isFixed(col)
    }))
  )
  const scroll = ref(props.data.scroll)
  const tableData = computed(() => {
    return {
      ...props.data,
      verticalAlign: true
    }
  })

  function handleResizeColumn(newWidth: number | string, col: ColumnType) {
    columns.value = columns.value.map((c) => {
      if (c.key === col.key) {
        return { ...c, width: newWidth }
      }
      return c
    })
  }
  watch(
    () => props.data.columns,
    (newColumns) => {
      columns.value = newColumns.map((col) => ({
        ...col,
        resizable: props.data.hasOwnProperty('isResizable') ? col.isResizable : true,
        width: col.width || 150,
        fixed: isFixed(col)
      }))
      if (isMobile.value) {
        if (!scroll.value) {
          scroll.value = { scrollToFirstRowOnChange: true, x: 'max-content' }
        } else if (!tableData.value?.scroll?.x) {
          scroll.value = { scrollToFirstRowOnChange: true, x: 'max-content' }
        }
      }
    },
    { deep: true, immediate: true }
  )
</script>

<style scoped lang="scss">
  .table-zebra-crossing {
    :deep(.ant-table-column-sorter) {
      color: #cccccc;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #cccccc;
    }

    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      // overflow: hidden;
      border-color: #e7e7e7 !important;
      .ant-table-header.ant-table-sticky-holder {
        top: -16px !important;
      }
      .ant-table-summary.ant-table-sticky-holder {
        bottom: -24px !important;
      }
      .ant-table-sticky-scroll {
        display: none;
      }
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 500;
      background: #f7f7f7;
      color: #535353;
      border-color: #e7e7e7 !important;
      // border-bottom: none;
      height: 38px;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
    :deep(.ant-table-column-sorters) {
      .ant-table-column-title {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .ant-table-column-sorter .ant-table-column-sorter-full {
        display: block;
        width: 12px;
      }
    }
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: middle;
      color: #2b2b2b;
      border-color: #e7e7e7 !important;
    }
    :deep(.ant-table-body) {
      overflow-y: hidden !important;
    }
    :deep(.ant-table-header) {
      border-right: 1px solid #f0f0f0;
    }

    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e7e7e7 !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e7e7e7 !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e7e7e7 !important;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
  .vertical-align-top {
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: top !important;
    }
  }
  .table-zebra-action {
    :deep(.ant-table-tbody) {
      .ant-table-row {
        .ant-table-cell:last-child {
          .ant-btn-link {
            padding: 5px 0;
          }
        }
      }
    }
    //   :deep(.ant-table-thead > tr > th) {
    //     background: transparent !important;
    //     border-color: transparent !important;
    //   }
    //   :deep(.ant-table-thead > tr > th::before) {
    //     background-color: transparent !important;
    //   }
    //   :deep(.ant-table-tbody > tr > td) {
    //     border-color: transparent !important;
    //     background: transparent;
    //   }
    //   :deep(
    //       .ant-table-tbody > tr.ant-table-row:hover > td,
    //       .ant-table-tbody > tr > td.ant-table-cell-row-hover,
    //       .ant-table-tbody > tr.table-zebra-crossing-striped > td.ant-table-cell-fix-right
    //     ) {
    //     background: #f4fafc !important;
    //   }
    //   :deep(.table-zebra-crossing-striped) td,
    //   :deep(.ant-table-tbody > tr.table-zebra-crossing-striped > td.ant-table-cell-fix-right) {
    //     background-color: #fafcfe !important;
    //   }

    //   :deep(.ant-table-thead > tr > th.ant-table-cell-fix-right) {
    //     background-color: #ffffff !important;
    //   }
    //   :deep(.ant-table-tbody > tr > td.ant-table-cell-fix-right) {
    //     background-color: #ffffff !important;
    //   }
    // }
    // .vertical-align-top {
    //   :deep(.ant-table-tbody > tr > td) {
    //     vertical-align: top;
    //   }
  }
</style>
