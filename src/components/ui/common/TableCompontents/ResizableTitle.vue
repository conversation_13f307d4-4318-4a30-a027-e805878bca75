<!-- ResizableTitle.vue -->
<template>
  <th :style="{ position: 'relative', width: width + 'px' }" ref="thRef">
    <slot></slot>

    <div class="resize-handle" @mousedown="onMouseDown"></div>
  </th>
</template>

<script setup>
  import { ref, watch, onBeforeUnmount } from 'vue'

  const props = defineProps({
    width: Number,
    onResize: Function
  })

  const thRef = ref(null)
  const width = ref(props.width) || 150

  watch(
    () => props.width,
    (val) => {
      width.value = val
    }
  )

  let startX = 0
  let startWidth = 0

  const onMouseDown = (e) => {
    e.preventDefault()
    e.stopPropagation()

    startX = e.clientX
    startWidth = thRef.value.offsetWidth

    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
  }

  const onMouseMove = (e) => {
    const delta = e.clientX - startX

    const newWidth = Math.max(60, startWidth + delta)
    console.log(newWidth, '**********', startWidth)

    width.value = newWidth
    props.onResize?.(newWidth)
  }

  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  onBeforeUnmount(() => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  })
</script>

<style scoped>
  .resize-handle {
    position: absolute;
    right: -5px;
    top: 0;
    bottom: 0;
    width: 14px;
    cursor: col-resize;
    z-index: 10;
    user-select: none;
    /* background-color: bisque; */
  }
</style>
