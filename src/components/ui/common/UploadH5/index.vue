<template>
  <div class="page_upload" ref="uploadRef">
    <div v-if="type !== 'button'" class="upload_imgs">
      <div v-for="(item, index) in modelValue" :key="item.url" class="item_box">
        <template v-if="isVideo(item)">
          <a-image
            class="upload_img"
            :src="
              item.url.includes('watermark') && Boolean(item.add)
                ? item.url.replace('watermark/', 'watermark/artwork/').replace(/\.jpg$/, '')
                : item.url
            "
            alt=""
            fit="cover"
            :initial-index="state.previewImgIndex"
            :preview-src-list="disabled ? [] : modelValue.map((v) => v.url)"
            :preview-teleported="props.previewTeleported"
            :preview="preview"
          />
          <div v-if="item.name" class="bottom_txt flex_center">
            {{ item.name }}
          </div>
          <div v-if="!disabled" class="img_mask flex_center">
            <!-- <img class="preview_icon" src="./images/preview.png" alt="" @click.stop="showPreview(index)" /> -->
            <img class="close_icon" src="./images/close.png" alt="" @click="close(index)" />
          </div>
        </template>
        <template v-else>
          <div class="upload_img video-item-warp">
            <a-image
              class="upload_img"
              :src="`${item.url}?x-oss-process=video/snapshot,t_0,f_jpg`"
              alt=""
              fit="cover"
              :preview="false"
            />
            <div v-if="!disabled" class="img_mask flex_center">
              <!-- <img class="preview_icon" src="./images/preview.png" alt="" @click.stop="showPreview(index)" /> -->
              <img class="close_icon" src="./images/close.png" alt="" @click="close(index)" />
            </div>
            <div class="video-icon" @click="() => (state.videoUrl = item)">
              <PlayCircleFilled />
            </div>
          </div>
        </template>
      </div>
    </div>

    <a-upload
      v-if="!modelValue || modelValue.length < state.fileMax"
      class="uploader"
      :customRequest="() => {}"
      :multiple="props.multiple"
      :showUploadList="false"
      :http-request="request"
      :beforeUpload="beforeUpload"
      :disabled="disabled"
      :maxCount="Number(props.max)"
      :accept="accept"
      @dragover="drag = true"
      :directory="drag && props.directory"
      @click="drag = false"
    >
      <div v-show="type !== 'button'" class="upload_content flex_center" @click="drag = false">
        <div v-if="state.fileStatus.type === 'ing'" class="progress_content">
          <a-progress
            type="circle"
            :width="60"
            size="small"
            :percent="state.fileStatus.content"
            :status="state.fileStatus.content === 100 ? 'success' : ''"
          />
        </div>
        <div v-else class="progress_content">
          <PlusOutlined class="icon" />
          <div class="txt">上传图片</div>
        </div>
      </div>

      <div v-show="type === 'button'" class="uplodText" @click="drag = false">
        <slot v-if="$slots.default"></slot>
        <AButton type="primary" v-else>
          <span>上传图片</span>
        </AButton>
      </div>
    </a-upload>
    <div class="video-fixe-warp" v-if="state.videoUrl?.url">
      <div class="video-fixe-content">
        <div class="video-item-play">
          <div class="close-btn" @click="() => (state.videoUrl = null)">关闭</div>
          <video controls class="video-src" :src="state.videoUrl.url"></video>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'Upload'
  }
</script>

<script setup name="Upload" lang="ts">
  import { reactive, ref, watch, computed } from 'vue'
  import Cos from './axios.ts'
  import { uploadProps } from './types'
  import { message } from 'ant-design-vue'
  import { PlusOutlined, PlayCircleFilled } from '@ant-design/icons-vue'
  import { isArray, isObject } from 'lodash-es'
  import moment from 'moment'

  const uploadRef = ref(null)
  const props = defineProps(uploadProps)
  const drag = ref(false)

  const emit = defineEmits(['update:modelValue', 'change', 'uploadSuccess', 'complete'])

  const state = reactive<any>({
    previewImgIndex: 0,
    fileStatus: {},
    acceptStr: '',
    fileSize: Number(props.size),
    fileMax: Number(props.max),
    currentNum: isArray(props.modelValue) ? props.modelValue.length : 0,
    videoUrl: ''
  })
  watch(
    () => props.accept,
    (newVal) => {
      init(newVal)
    },
    {
      immediate: true,
      deep: true
    }
  )
  function init(accept) {
    if (accept !== '*') {
      const acceptStr = accept.replace(/,,/g, ',').replace(/(^,|,$)/g, '')
      if (!/^\.[a-zA-Z0-9]{2,5}(,\.[a-zA-Z0-9]{2,5})*$/.test(acceptStr.toLowerCase())) {
        throw new Error(`accept格式错误, 参考'.jpg,.png,.mp4'`)
      }
      state.acceptStr = acceptStr
    }

    if (props.wh && !props.wh.includes('*') && !props.wh.includes('/')) {
      throw new Error(`图片宽高限制格式错误, 参考'宽*高 或者 宽/高'`)
    }
  }

  // 检查文件是否执行文件宽高限制
  async function constraintFileInfo(file: File) {
    let fileInfo = await getImageDimensions(file)
    // 检查文件类型
    const reg = file.name.match(/\.[a-zA-Z0-9]{2,5}$/)[0].toLowerCase()
    if (state.acceptStr && !new RegExp(state.acceptStr.replace(/\./g, '\\.').replace(/,/g, '|'), 'g').test(reg)) {
      throw new Error('请上传' + props.accept + '类型的文件')
    }
    if (props.wh) {
      let text = getFileTypeByMimeType(file) == 'image' ? '图片' : '视频'
      let splitStr = props.wh.includes('*') ? '*' : '/'
      let w = props.wh.split(splitStr)[0]
      let h = props.wh.split(splitStr)[1]

      // 限制图片宽高
      if (splitStr === '*' && (w != fileInfo.width || h != fileInfo.height)) {
        throw new Error(`请上传宽${w} 高${h}的${text}`)
      }
      // 限制图片宽高比
      if (splitStr === '/' && !(Math.abs(fileInfo.width / fileInfo.height - w / h) < 0.01)) {
        throw new Error(`请上传宽高比为 ${props.wh} 的${text}`)
      }
    }
    return fileInfo
  }

  /**
   * 同类型的提示 只显示一个值
   */
  let safetyMessage = new Map()
  function ensureMessage(text: string) {
    let keys = []
    safetyMessage.forEach((value, key) => {
      if (moment(+new Date()).diff(moment(value), 'seconds') > 3) {
        keys.push(key)
      }
    })
    keys.forEach((v) => safetyMessage.delete(v))
    if (!safetyMessage.has(text)) {
      message.error(text)
      safetyMessage.set(text, +new Date())
    }
  }
  // 是否是混合类型
  const isMixedType = computed(() => {
    return Object.keys(props.mixedType).length > 0
  })
  // 用于全部完成
  let completeFileList: any[] = []
  let completeFileNum = 0
  async function beforeUpload(file: File) {
    console.log('file', file)
    try {
      let fileInfo = await constraintFileInfo(file)
      // 检查多选时候文件上传个数限制
      // const modelValueLength = isArray(props.modelValue) ? props.modelValue.length : 0
      // const currentFilesLength = state.currentNum + modelValueLength
      // if (props.multiple && currentFilesLength >= state.fileMax) return false
      if (props.multiple && state.currentNum >= state.fileMax) return false
      if (!isNaN(state.fileSize) && state.fileSize != '-1' && file.size / 1024 / 1024 > state.fileSize) {
        return message.warning('请上传' + state.fileSize + 'M以内的文件')
      }

      state.currentNum++
      let disponeFile = await props.beforeUpload(file)
      if (!disponeFile) return false
      file = disponeFile

      // 用于全部完成判断
      completeFileNum++
      completeFileList.push(file)
      console.log('fileInfo', fileInfo)
      let use = !isMixedType.value
        ? isObject(props.use)
          ? props.use[fileInfo.type]
          : props.use || fileInfo.type
        : fileInfo.type === 'image'
          ? props.mixedType?.image?.use
          : props.mixedType?.video?.use
      console.log('eiueiuweiorioeriooeri==3=3=3=3=', use, props.mixedType?.video?.use)
      return await Cos.upload(file, use, props.water, (res) => {
        switch (res.type) {
          case 'ing':
            state.fileStatus = res
            break

          case 'success':
            state.fileStatus = {}
            let successFileInfo = null
            if (props.type !== 'button') {
              props.modelValue &&
                props.modelValue.push({
                  url: res.content,
                  fileName: file.name,
                  file: file,
                  video: res.video,
                  fileInfo,
                  add: true
                })
              emit('update:modelValue', props.modelValue)
              successFileInfo = {
                type: 'success',
                content: res.content,
                fileName: file.name,
                file: file,
                video: res.video,
                fileInfo
              }
              emit('change', successFileInfo)
            } else {
              successFileInfo = {
                type: 'success',
                content: res.content,
                fileName: file.name,
                file: file,
                video: res.video,
                fileInfo
              }
              emit('change', successFileInfo)
            }
            // 多选的情况下不使用默认提示
            if (props.multiple) {
              emit('uploadSuccess', file)
            } else {
              message.success('上传成功！')
              successFileInfo = {
                type: 'success',
                content: res.content,
                fileName: file.name,
                file: file,
                video: res.video,
                fileInfo
              }
              emit('change', successFileInfo)
            }

            // 全部完成赋值
            if (successFileInfo) {
              let index = completeFileList.findIndex((file) => file == successFileInfo.file)
              if (index != -1) completeFileList.splice(index, 1, successFileInfo)
            }

            // state.currentNum--
            break

          case 'err':
            state.currentNum--
            state.fileStatus = {}
            message.error('上传失败，请重新上传')
            console.log('err', file, res)
            emit('change', null)
            // 出错的时候删除掉不需要的file
            let index = completeFileList.findIndex((file) => file == file)
            if (index != -1) completeFileList.splice(index, 1)
            break
          case 'finally':
            completeFileNum--
            // 成功失败都会走到这是事件里面 需要按照顺序获取的可以在这个里面处理
            if (!completeFileNum) {
              console.log('上传完成', res, completeFileList)
              emit('complete', completeFileList)
              completeFileList = []
              state.currentNum = isArray(props.modelValue) ? props.modelValue.length : 0
            } else {
              console.log(`正在逐个上传${completeFileNum}`, res)
            }
            break
        }
      })
    } catch (error) {
      console.error('上传文件失败：', error.message)
      // message.error()
      ensureMessage(error.message)
    }
  }

  function close(i: number) {
    state.currentNum--
    props.modelValue.splice(i, 1)
    emit('update:modelValue', props.modelValue)
  }

  const isVideo = (item) => {
    const arr = (item.fileName || item.file_name || item.url).split('.')
    if (['mp4'].includes(arr[arr.length - 1])) {
      return false
    } else {
      return true
    }
  }

  /**
   * 获取文件信息
   * @param file
   */
  function getImageDimensions(file: File): Promise<{
    width: number
    height: number
    size: string
    duration?: number
    ratio: number
    type: 'image' | 'video'
  }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      const url = URL.createObjectURL(file)

      reader.onload = function () {
        if (getFileTypeByMimeType(file) === 'image') {
          const img = new Image()
          img.onload = function () {
            resolve({
              type: 'image',
              width: img.width,
              height: img.height,
              size: Number(file.size / 1024 / 1024).toFixed(2) + 'm',
              ratio: simplifyFraction(img.width, img.height)
            })
          }
          img.onerror = function () {
            reject(new Error('Failed to load image'))
          }
          img.src = url
        } else if (getFileTypeByMimeType(file) === 'video') {
          const video = document.createElement('video')
          video.src = url
          video.addEventListener('loadeddata', () => {
            const width = video.videoWidth
            const height = video.videoHeight
            const duration = video.duration
            console.log({ width, height, duration, size: Number(file.size / 1024 / 1024).toFixed(2) })
            resolve({
              type: 'video',
              width,
              height,
              duration,
              size: Number(file.size / 1024 / 1024).toFixed(2) + 'm',
              ratio: simplifyFraction(width, height)
            })
          })
        } else {
          resolve({})
        }
      }

      reader.onerror = function () {
        reject(new Error('Failed to read file'))
      }

      reader.readAsDataURL(file)
    })
  }

  function gcd(a: number, b: number) {
    return b ? gcd(b, a % b) : a
  }
  /**
   * 获取宽高比
   */
  function simplifyFraction(numerator: number, denominator: number) {
    const sign = numerator * denominator < 0 ? -1 : 1
    const absoluteNumerator = Math.abs(numerator)
    const absoluteDenominator = Math.abs(denominator)
    const greatestCommonDivisor = gcd(absoluteNumerator, absoluteDenominator)

    let obj = {
      numerator: (absoluteNumerator / greatestCommonDivisor) * sign,
      denominator: (absoluteDenominator / greatestCommonDivisor) * sign
    }
    return obj.numerator + ' / ' + obj.denominator
  }

  /**
   * 获取文件类型
   * @param file
   */
  function getFileTypeByMimeType(file: File) {
    var mime = file.type
    if (mime.startsWith('image/')) {
      return 'image'
    } else if (mime.startsWith('video/')) {
      return 'video'
    } else {
      return 'unknown'
    }
  }

  function request() {}
</script>

<style lang="scss" scoped>
  .uplodText {
    color: var(--primary-color);
  }
  .page_upload {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    line-height: 1;

    .progress_content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .upload_imgs {
      display: flex;
      flex-wrap: wrap;

      .item_box {
        position: relative;
        width: 101px;
        height: 101px;
        border-radius: 4px;
        margin-right: 20px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        justify-content: center;

        .bottom_txt {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 101px;
          height: 30px;
          background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
          border-radius: 0px 0px 4px 4px;
          font-size: 14px;
          color: #ffffff;
        }

        &:hover {
          .img_mask {
            opacity: 1;
            z-index: 1;
          }
        }
      }

      .img_mask {
        opacity: 0;
        cursor: pointer;

        // .preview_icon {
        //   width: 18px;
        // }

        .close_icon {
          position: absolute;
          top: -6px;
          right: -6px;
          width: 14px;
          background-color: #fff;
          border-radius: 50%;
          z-index: 10;
        }
      }
      :deep {
        .ant-image {
          height: 100%;
        }
        .upload_img {
          width: 100%;
          height: 100%;
          border-radius: 4px;
        }
      }
    }

    .upload_content {
      position: relative;
      width: 101px;
      height: 101px;
      background: #f3f6fc;
      border: 1px dashed #d8dde8;
      flex-direction: column;
      border-radius: 4px;
      transition: border 0.3s;

      &:hover {
        border-color: var(--el-color-primary);
      }

      .icon {
        font-size: 26px;
        color: rgba(122, 134, 159, 1);
      }

      .txt {
        font-size: 14px;
        color: #999999;
        margin-top: 9px;
        text-align: center;
      }
    }
  }
  .video-item-warp {
    // overflow: hidden;
    position: relative;
    .video-icon {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      top: 0px;
      left: 0px;
      text-align: center;
      font-size: 24px;
      color: #ffffff;
      padding-top: 38px;
      cursor: pointer;
    }
  }
  .video-fixe-warp {
    position: fixed;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    left: 0px;
    top: 0px;
    z-index: 100;
    .video-fixe-content {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .video-item-play {
        position: relative;
        .video-src {
          width: 500px;
          height: 400px;
          background-color: #000000;
        }
        .close-btn {
          position: absolute;
          top: 0px;
          right: 0px;
          background-color: #ffffff;
          z-index: 200;
          font-size: 14px;
          padding: 5px 10px;
        }
      }
    }
  }
</style>
