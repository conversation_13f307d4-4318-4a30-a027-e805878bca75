<template>
  <div class="comp_select_time" :style="{ width: table.width + 'px' }">
    <div
      class="table_box"
      :style="{ width: table.width + 'px', height: table.height + 'px' }"
      @mousedown="mousedown"
      @mousemove="mousemove"
      @mouseup="mouseup"
      ref="selectTimeEl"
    >
      <div class="table_header">
        <div
          class="chunk flex-center chunk_position c-#64666A"
          v-for="item in [
            ...config.dateRowHeader,
            ...config.tableBodyOneCol,
            ...config.dateRow,
            ...config.tableBodyTwoCol
          ]"
          :style="{
            width: item.style.width + 'px',
            height: item.style.height + 'px',
            top: item.location.top + 'px',
            bottom: item.location.bottom + 'px',
            left: item.location.left + 'px',
            right: item.location.right + 'px'
          }"
          :class="item.class || {}"
          @click="item.click"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="table_body">
        <div v-for="(itemParent, g) in config.tableBody">
          <div
            class="chunk flex-center chunk_position"
            v-for="(item, i) in itemParent"
            :style="{
              width: item.style.width + 'px',
              height: item.style.height + 'px',
              top: item.location.top + 'px',
              bottom: item.location.bottom + 'px',
              left: item.location.left + 'px',
              right: item.location.right + 'px'
            }"
            :class="{
              active: selectItems[g][i],
              ...item.class
            }"
            @click="item.click"
          >
            {{ item.text }}
          </div>
        </div>
      </div>
    </div>
    <div class="table_bottom">
      <span>上线时间段</span>
      <a-button type="link" class="clear_btn" @click="clear">
        <SvgIcon class="icon" icon="delete1"></SvgIcon>
        <span>清空全部</span>
      </a-button>
    </div>
    <template v-for="(item, index) in showTimeList" :key="index">
      <div class="date_list" v-if="item.arr.length">
        <div class="item">
          <div class="label">{{ item.text }} :</div>
          <div class="content">
            <div class="time_list c-#313233" v-for="(t, i) in item.arr">
              {{ t }}{{ i == item.arr.length - 1 ? ';' : '、' }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
  import { getSelectTimeNode } from '@/utils'
  import { cloneDeep, isArray, isNumber, throttle } from 'lodash-es'
  import { onMounted, onUnmounted, ref } from 'vue'

  defineOptions({ name: 'SelectTime' })
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => []
    },
    /**
     * 默认时间
     */
    defaultTime: String
  })
  const emits = defineEmits(['update:modelValue', 'update:item', 'change'])
  // 选择的时间
  const selectItems = ref(
    isArray(props.modelValue) && props.modelValue.length
      ? cloneDeep(props.modelValue)
      : Array.from({ length: 7 }).map((_) => Array.from({ length: 48 }).map((_) => 0))
  )
  const selectTimeEl = ref<HTMLElement | null>(null)
  // 选择的时间的下表对应的时间
  const selectTimeList = Array.from({ length: 7 }).map((_) =>
    Array.from({ length: 49 }).map((_, i) => {
      return {
        num: i,
        text:
          i == 0
            ? '00:00'
            : `${i < 20 ? '0' + Math.ceil((i - 1) / 2) : Math.ceil((i - 1) / 2)}:${(i - 1) % 2 ? '0' : '3'}0`
      }
    })
  )
  // 显示时间
  const showTimeList = ref<any[]>([])

  console.log(selectTimeList, '-----------------')
  let domLeft = 0,
    domTop = 0
  // 表格区域内部高度
  let tableBodyRowHeight = 24
  // 表格区域内部宽度
  let tableBodyRowWidth = 20
  //   第一列表头宽度
  let tableHeaderRowWidth = 30
  //   第一行表格高度
  let tableHeaderHeight = 60
  // 表格区域大小分布
  let table = {
    // 内容区域
    body: {
      width: (tableBodyRowWidth / 2) * 48,
      height: tableBodyRowHeight * 7
    },
    // 表格总宽度
    width: (tableBodyRowWidth / 2) * 48 + tableHeaderRowWidth,
    // 表格总高度
    height: tableHeaderHeight + tableBodyRowHeight * 7
  }
  let config = {
    // 第一列 第一个元素
    dateRowHeader: [
      {
        text: '星期',
        click: () => {},
        location: {
          top: 0,
          left: 0,
          right: tableHeaderRowWidth,
          bottom: tableHeaderHeight
        },
        class: {
          text_col: true
        },
        style: {
          width: tableHeaderRowWidth,
          height: tableHeaderHeight
        }
      }
    ],
    // 第一列 其余内容
    dateRow: Array.from({ length: 7 }, (_, i) => {
      return {
        text: convertToChineseNumber(i),
        click: () => {
          let col = selectItems.value[i]
          multiSelect(col)
        },
        location: {
          top: tableHeaderHeight + tableBodyRowHeight * i,
          left: 0,
          right: tableHeaderRowWidth,
          bottom: tableBodyRowHeight * (i + 2)
        },
        class: {
          row_title: true,
          laset_col: i == 6
        },
        style: {
          width: tableHeaderRowWidth,
          height: tableBodyRowHeight
        }
      }
    }),
    // 表头内容第一行内容
    tableBodyOneCol: [
      {
        text: '00:00 - 12:00',
        click: () => {
          multiSelect(selectItems.value, 0, 24)
        },
        location: {
          top: 0,
          left: tableHeaderRowWidth,
          right: table.body.width / 2 + tableHeaderRowWidth,
          bottom: tableHeaderHeight / 2
        },
        class: { time: true },
        style: {
          width: table.body.width / 2,
          height: tableHeaderHeight / 2
        }
      },
      {
        text: '12:00 - 24:00',
        click: () => {
          multiSelect(selectItems.value, 24, 48)
        },
        location: {
          top: 0,
          left: tableHeaderRowWidth + table.body.width / 2,
          right: table.body.width + tableHeaderRowWidth,
          bottom: tableHeaderHeight / 2
        },
        class: { row_laset: true, time: true },
        style: {
          width: table.body.width / 2,
          height: tableHeaderHeight / 2
        }
      }
    ],
    //  表头内容第二行内容
    tableBodyTwoCol: Array.from({ length: 24 }).map((_, i) => {
      return {
        text: i,
        click: () => {
          let arr: Array<number[]> = []
          selectItems.value.forEach((_, r) => {
            arr.push(selectItems.value[r])
          })
          multiSelect(arr, i * 2, (i + 1) * 2)
        },
        location: {
          top: tableHeaderHeight / 2,
          left: tableHeaderRowWidth + (table.body.width / 24) * i,
          right: table.body.width / 2 + tableHeaderRowWidth,
          bottom: tableHeaderHeight / 2 + tableBodyRowHeight * (i + 1)
        },
        class: {
          row_laset: i === 23,
          num: true
        },
        style: {
          width: table.body.width / 24,
          height: tableHeaderHeight / 2
        }
      }
    }),
    // 表格其它内容
    tableBody: Array.from({ length: 7 }).map((_, g) => {
      return Array.from({ length: 48 }).map((_, i) => {
        return {
          text: '',
          click: () => {
            selectItems.value[g][i] = Number(!Boolean(selectItems.value[g][i]))
            change()
          },
          location: {
            col: i,
            row: g,
            top: tableHeaderHeight + tableBodyRowHeight * g,
            left: tableHeaderRowWidth + (table.body.width / 24 / 2) * i,
            right: tableHeaderRowWidth + (table.body.width / 24 / 2) * (i + 1),
            bottom: tableHeaderHeight + tableBodyRowHeight * (g + 1)
          },
          class: {
            chunk2: Boolean(i % 2),
            chunk1: !Boolean(i % 2),
            row_laset: i === 47
          },
          style: {
            width: table.body.width / 24 / 2,
            height: tableBodyRowHeight
          }
        }
      })
    })
  }
  // 每一个列 对应的left区间
  let rows = Array.from({ length: 48 }).map((_, r) => {
    return [
      tableHeaderRowWidth + (table.body.width / 24 / 2) * r,
      tableHeaderRowWidth + (table.body.width / 24 / 2) * (r + 1)
    ]
  })
  //   每一行对应的 top 区间
  let cols = Array.from({ length: 7 }).map((_, c) => {
    return [tableHeaderHeight + tableBodyRowHeight * c, tableHeaderHeight + tableBodyRowHeight * (c + 1)]
  })
  console.log('配置项：', rows, cols, config, selectItems)

  /**
   * 多选操作
   * @param arr 选择数组
   * @param beforeNum 多选限制第几列
   */
  function multiSelect(arr: Array<number> | Array<number[]>, beforeNum: number = 0, endNum = 48) {
    if (isNumber(arr[0])) arr = [arr] as Array<number[]>
    // 摊平可操作的二维数组
    let allArr: number[] = []
    ;(arr as Array<number[]>).forEach((row) => {
      allArr.push(...row.slice(beforeNum, endNum))
    })
    // 选择操作
    ;(arr as Array<number[]>).forEach((row) => {
      let choosableArr = row.slice(beforeNum, endNum)
      if (allArr.every((item) => item)) {
        choosableArr.forEach((_, i) => {
          row.splice(i + beforeNum, 1, 0)
        })
      } else {
        choosableArr.forEach((_, i) => {
          row.splice(i + beforeNum, 1, 1)
        })
      }
    })
    change()
  }

  /**
   * 更新值
   */
  function change() {
    console.log('change了')
    emits('update:modelValue', selectItems.value)
    disposeSelectTimtList()
    emits('update:item', showTimeList.value)
    emits('change', showTimeList.value)
  }
  /**
   * 处理回显时间
   */
  function disposeSelectTimtList() {
    let arr: any[] = Array.from({ length: 7 }).map((_) => [])

    selectItems.value.forEach((_, c) => {
      let index = 0
      for (let r = index; r < selectItems.value[c].length; r++) {
        if (Boolean(selectItems.value[c][r])) {
          let startIndex = r
          for (let t = r; t < selectItems.value[c].length; t++) {
            if (!Boolean(selectItems.value[c][t]) || t >= 47) {
              r = t
              arr[c].push(
                selectTimeList[c][startIndex].text +
                  '-' +
                  selectTimeList[c][
                    t == selectItems.value[c].length - 1
                      ? selectItems.value[c][t]
                        ? selectItems.value[c].length
                        : t
                      : t
                  ].text
              )
              break
            }
          }
        }
      }
    })
    showTimeList.value = arr.map((v, i) => {
      return {
        text: '周' + convertToChineseNumber(i),
        arr: v
      }
    })
  }

  /**
   * 阿拉伯数字 -> 中文数字
   * @param number 数字
   */
  function convertToChineseNumber(number: number) {
    var chineseNumber = ['一', '二', '三', '四', '五', '六', '日']
    return chineseNumber[number]
  }

  /**
   * 获取当前为第几列
   * 获取当前left数值对应的是多少列
   */
  function passLeftGetRow(left: number) {
    for (let i = 0; i < rows.length; i++) {
      if (left >= rows[i][0] && left <= rows[i][1]) {
        return i
      }
    }
    return -1
  }

  const clear = () => {
    selectItems.value = Array.from({ length: 7 }).map((_) => Array.from({ length: 48 }).map((_) => 0))
    change()
  }

  /**
   * 获取当前为第几
   * 获取当前 top 数值对应的是多少行
   */
  function passTopGetCol(top: number) {
    for (let i = 0; i < cols.length; i++) {
      if (top >= cols[i][0] && top <= cols[i][1]) {
        return i
      }
    }
    return -1
  }

  /***************************以下是鼠标按下滑动事件 *********************** */
  let isDragging = false
  let isJust = false
  let areaMap: Map<string, number> = new Map()
  let startX = 0,
    startY = 0,
    statrRow: number | undefined = void 0,
    startCol: number | undefined = void 0,
    endX = 0,
    endY = 0

  const mousedown = (e: MouseEvent) => {
    isDragging = true

    if (selectTimeEl.value) {
      let { left, top } = selectTimeEl.value.getBoundingClientRect()
      domTop = top
      domLeft = left
    }

    startX = e.clientX - domLeft
    startY = e.clientY - domTop
    statrRow = passLeftGetRow(startX)
    startCol = passTopGetCol(startY)
    areaMap = new Map()

    if (isNumber(statrRow) && isNumber(startCol)) {
      isJust = !Boolean(selectItems.value[startCol][statrRow])
    }

    console.log('初始点击的位置：', isJust, statrRow, startCol, e.clientX, e.clientY, domLeft, domTop, startX, startY)
    // 起始位置如果为负，那么正向移动就为负
  }
  const throttleMove = throttle((x, y) => {
    let thisRow = passLeftGetRow(x)
    let thisCol = passTopGetCol(y)

    // console.log('触发节流方法', x, y, statrRow, startCol)

    // 记录已滑动区域
    let cutMap: Map<string, number> = new Map()
    for (let i = Math.min(startCol || 0, thisCol); i <= Math.max(startCol || 0, thisCol); i++) {
      for (let g = Math.min(statrRow || 0, thisRow); g <= Math.max(statrRow || 0, thisRow); g++) {
        cutMap.set(i + '-' + g, Number(isJust))
        areaMap.set(i + '-' + g, Number(isJust))
      }
    }

    for (let [key, value] of areaMap) {
      let [c, r] = key.split('-')
      if (cutMap.has(key)) {
        selectItems.value[Number(c)].splice(Number(r), 1, value)
      } else {
        selectItems.value[Number(c)].splice(Number(r), 1, Number(!Boolean(value)))
      }
    }
    // console.log(isJust)

    // console.log(areaMap, thisCol, thisRow, passTopGetCol(startX), startX)
  }, 0)

  const mousemove = (e: MouseEvent) => {
    if (!isDragging) return
    endX = e.clientX - domLeft
    endY = e.clientY - domTop
    throttleMove(endX, endY)
  }
  const mouseup = () => {
    if (!isDragging) return
    isDragging = false
    startX = 0
    startY = 0
    endX = 0
    endY = 0

    statrRow = void 0
    startCol = void 0
    areaMap = new Map()
    change()
  }

  onMounted(() => {
    if (props.defaultTime) {
      let obj = getSelectTimeNode(props.defaultTime)
      console.log('ahahahha1', obj)
      obj.nodes.forEach((_, i) => multiSelect(selectItems.value[i], obj.startIndex, obj.endIndex))
    }
    // console.log('获取的数据1', props.defaultTime, getSelectTimeNode(props.defaultTime))
    // change()
    disposeSelectTimtList()
    document && document.addEventListener('mouseup', mouseup)
  })
  onUnmounted(() => {
    document && document.removeEventListener('mouseup', mouseup)
  })
</script>
<style lang="scss" scoped>
  .comp_select_time {
    border: 1px solid#e6e8ed;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .table_box {
      position: relative;
      font-size: 12px;
      user-select: none;

      .chunk_position {
        position: absolute;
      }
      .chunk {
        cursor: pointer;
        border-color: #e6e8ed;
        border-style: solid;
        border-right-width: 1px;
        border-bottom-width: 1px;
        &.active {
          background-color: var(--primary-color);
        }
        &.row_laset {
          border-right: none;
        }
        &.laset_col {
          border-bottom: 1px solid #e6e8ed !important;
        }
        &.chunk1 {
          border-right-color: #f7f7f7;
        }
        &.row_title {
          border-bottom: none;
        }
        &.num {
          background-color: #f7f7f7 !important;
        }
        &.time {
          background-color: #f7f9fc !important;
        }
      }
      .text_col {
        padding: 0px 10px;
        line-height: 15px;
      }
      .table_header {
        .chunk {
          background-color: #fff;
        }
      }
    }
    .table_bottom {
      font-size: 12px;
      padding: 0px 7px;

      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f4f4f5;
      .clear_btn {
        display: flex;
        align-items: center;
        font-size: 12px;
        padding: 0px;

        cursor: pointer;
        .icon {
          margin-right: 5px;
        }
      }
    }
    .date_list {
      padding: 4px 7px;
      font-size: 12px;
      .item {
        display: flex;
        line-height: 20px;
        .label {
          flex-grow: 0;
          color: #999999;
          width: 40px;
        }
        .content {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
  }
</style>
