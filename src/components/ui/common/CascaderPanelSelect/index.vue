<template>
  <div class="panel">
    <a-cascader
      v-model:value="value"
      :options="options"
      placeholder="Please select"
      :fieldNames="fieldNames"
      :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
      ref="cascaderPanel"
      :open="open"
      :multiple="multiple"
      @change="handleChange"
      :key="state.timestamp"
    />
  </div>
</template>

<script lang="tsx" setup>
  defineOptions({
    name: 'CascaderPanelSelect'
  })
  import { ref, watch, nextTick, watchEffect, reactive } from 'vue'

  import { flatten } from '@/utils'
  import { isArray } from 'lodash-es'

  const value = ref<string[]>([])
  const cascaderPanel = ref()
  const open = ref(false)

  const props = defineProps({
    modelValue: {
      type: [String, Array]
    },
    options: {
      type: Array,
      default: () => []
    },
    multiple: Boolean,
    changeRefresh: Boolean,
    fieldNames: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          children: 'children'
        }
      }
    }
  })

  const emits = defineEmits(['update:modelValue', 'change'])

  const state = reactive({
    flatData: [],
    flatDataById: {},
    timestamp: Date.now()
  })

  // 监听 change 事件
  const handleChange = (value: any, selectedOptions: any) => {
    emits('update:modelValue', value)
    emits('change', value, selectedOptions)
  }

  // 初始化数据
  const initOptions = (data: any, parent = null) => {
    if (isArray(data)) {
      data.forEach((v) => {
        v.parent = parent
        v.pathLabels = parent ? parent.pathLabels.concat([v.name]) : [v.name]
        if (v.children) {
          initOptions(v.children, v)
        }
      })
    }
  }

  // 获取选中的 node
  const getCheckedNodes = () => {
    if (isArray(props.modelValue)) {
      if (props.multiple) {
        let resArr = []
        props.modelValue.forEach((v) => {
          if (isArray(v)) {
            const arr = []
            v.forEach((va) => {
              arr.push(state.flatDataById[va])
            })
            resArr.push(arr)
          }
        })
        return resArr
      } else {
        const targetId = props.modelValue[props.modelValue.length - 1]
        return [state.flatDataById[targetId]]
      }
    }
    return []
  }

  // 扁平化处理Options数据
  const handleFlatData = () => {
    const flattenOptions = flatten(props.options, props.fieldNames)
    state.flatData = flattenOptions
    state.flatData.forEach((v) => {
      state.flatDataById[v.id] = v
    })
  }

  // 初始化 Panel
  const initPanel = () => {
    nextTick(() => {
      cascaderPanel.value.focus()
      open.value = true
    })
  }

  watchEffect(async () => {
    await initOptions(props.options)
    await handleFlatData()
    if (isArray(props.modelValue)) {
      value.value = props.modelValue

      initPanel()
    }
  })
  watch(
    () => props.modelValue,
    () => {
      value.value = props.modelValue
      if (props.changeRefresh) {
        state.timestamp = Date.now()
      }
    },
    { deep: true }
  )

  // onMounted(() => {
  //   console.log('cascaderPanel', cascaderPanel.value)
  // })

  // 向外暴露方法
  defineExpose({ getCheckedNodes })
</script>

<style lang="scss" scoped>
  .panel {
    :deep {
      .ant-cascader {
        width: auto;
      }
      .ant-select {
        width: 100%;
        .ant-select-selector,
        .ant-select-arrow,
        .ant-select-clear {
          display: none;
        }
        .ant-cascader-dropdown.ant-select-dropdown {
          position: static !important;
          box-shadow: none;
          border: 1px solid #e4e7ed;
          z-index: 1;
          .ant-cascader-menu {
            // max-width: 370px;
            height: 500px;
            flex-grow: unset;
            flex: 1;
          }
        }
      }
    }
  }
</style>
