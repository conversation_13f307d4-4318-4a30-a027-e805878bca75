<template>
  <div class="flex w-full flex-justify-end flex-items-center page">
    <a-config-provider :theme="{ token: themeOverrides.common }" :locale="zhCN">
      <a-pagination
        v-model:current="props.page"
        v-model:pageSize="props.pageSize"
        :total="props.total"
        :pageSizeOptions="props.pageSizeOptions"
        show-less-items
        size="small"
        :show-total="(total) => `共 ${total} 条数据`"
        show-size-changer
        showQuickJumper
        @change="handlePaginationChange"
      />
    </a-config-provider>
  </div>
</template>
<script setup>
  import { ConfigProvider } from 'ant-design-vue'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  import { useTheme } from '@/hooks'
  const { themeOverrides } = useTheme()
  const props = defineProps({
    page: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    },
    pageSizeOptions: {
      type: Array,
      default: () => ['10', '20', '50', '100']
    }
  })
  const emits = defineEmits(['update:page', 'update:pageSize', 'change'])
  const handlePaginationChange = (page, pageSize) => {
    emits('update:page', page)
    emits('update:pageSize', pageSize)
    emits('change', { page: page, page_size: pageSize })
  }
</script>
