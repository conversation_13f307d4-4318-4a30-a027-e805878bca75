<!-- @Auther: 杨鹏飞-->
<!-- @Date: 2023/10/9 -10 - 09 -6:06 下午-->
<!-- @Description: -->

<template>
  <div class="product-item">
    <div class="product-item-img"><img :src="data.image" alt="" /></div>
    <div class="product-item-warp">
      <div>
        <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
          <template #title>{{ data.name }}</template>
          <div class="product-item-name text_overflow_row2">
            {{ data.name }}
          </div>
        </a-tooltip>

        <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
          <template #title>{{ data.sku_name }}</template>
          <div class="text_overflow max-w-300px">
            {{ data.sku_name || '--' }}
          </div>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps(['data'])
</script>

<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn';
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .product-item {
    display: flex;
  }
  .product-item-warp {
    flex: 1;
    padding: 0 10px;
    box-sizing: border-box;
  }
  .product-item-img {
    @include set_node_whb(70px, 70px);
  }
  // .product-item-warp {
  //   @include text_overflow(2);
  // }
</style>
