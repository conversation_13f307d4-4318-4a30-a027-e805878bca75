import { MapEventsKey, MapPropsKey, MapSlotsKey, UI } from 'types'

const ButtonProps: MapPropsKey<UI.Button['props']> = {
  'attr-type': {
    map: 'attr-type'
  },
  disabled: 'disabled',
  type: {
    map: 'type',
    dispone: (params) => {
      if (params == 'error') {
        return 'error'
      }
      return params
    }
  }
}
const ButtonEvents: MapEventsKey<UI.Button['events']> = {
  click: {
    map: 'click',
    dispone: (event) => {
      console.log('点击事件处理逻辑', event)
      return '我是改变后的参数'
    }
  }
}
const ButtonSlots: MapSlotsKey<UI.Button['slots']> = {
  default: 'default',
  item: {
    map: 'item',
    dispone: (param) => {
      console.log('走slot替换值', param)
      return {
        a: param.a,
        b: 1
      }
    }
  }
}
const ButtonMap: UI.UiCompMap<UI.Button> = {
  props: ButtonProps,
  events: ButtonEvents,
  slots: ButtonSlots
}

export default ButtonMap
