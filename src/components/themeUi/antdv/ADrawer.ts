import { <PERSON>er, But<PERSON> } from 'ant-design-vue'
import { ComponentOptions, h } from 'vue'
import './scss/ADrawer.scss'

import { isFunction } from 'lodash-es'
import { CloseOutlined } from '@ant-design/icons-vue'
export const ThemeUiADrawer = {
  name: '<PERSON>rawer',
  render({ $slots, $props, $attrs }: ComponentOptions) {
    return h(
      Drawer,
      {
        ...$attrs,
        ...$props,
        class: $attrs.class,
        rootClassName: 'common_comp_drawer',
        // 监听 Drawer 的 close 事件
        onClose: () => {
          if (isFunction($attrs.onClose)) $attrs.onClose()
        }
      },
      {
        ...($slots.title ? { title: () => $slots.title() } : {}),
        default: () => [
          $slots.default(),
          h(Button, {
            type: 'primary',
            class: ['comp_close_btn'],
            icon: h(CloseOutlined),
            onClick: () => {
              console.log('$attrs', $attrs, $props)
              if (isFunction($attrs.onClose)) {
                $attrs.onClose()
              }
            }
          })
        ]
      }
    )
  }
}
