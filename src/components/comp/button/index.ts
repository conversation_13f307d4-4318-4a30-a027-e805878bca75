// 结合event,props,和组件库的组件生成的新的自定义组件

import { SlotsType, defineComponent, h } from 'vue'
import { NButton } from '@/components/ui/naive'
import { useComponentMap } from '@/hooks'
import { ButtonAttrs } from './attrs'
import { UI } from 'types'

const Button = defineComponent({
  name: 'CButton',
  props: ButtonAttrs['props'] || {},
  emits: ButtonAttrs['events'],
  slots: Object as SlotsType<UI.Button['slots']>,
  render: () => h(useComponentMap(NButton))
})
export default Button
