import { UI } from 'types'
import { PropType } from 'vue'

// 组件需要接受的传参
export const ButtonAttrs: Partial<UI.Button> = {
  props: {
    'attr-type': {
      type: String as PropType<'button' | 'submit' | 'reset'>,
      default: 'button'
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    type: {
      type: String as PropType<'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error'>,
      default: 'default'
    }
  },
  events: {
    click: ($event: Event) => $event
  }
}
