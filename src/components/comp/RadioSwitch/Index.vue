<template>
  <div class="comp_radio">
    <div
      v-for="(item, index) in list"
      :key="item.name"
      class="item"
      :style="{
        width: props.w + 'px'
      }"
      :class="{ active: state.activeValue === item.value }"
      @click="switchTab(item, index)"
    >
      <span class="font_color">{{ item.name }}</span>
    </div>
    <div
      class="bg"
      :style="{ width: props.w + 'px', transform: ` translateX(${state.activeIndex * props.w}px)` }"
    ></div>
  </div>
</template>

<script setup>
  import { reactive, watch, ref } from 'vue'
  defineOptions({ name: 'RadioSwitch' })
  const emit = defineEmits(['update:modelValue'])
  const props = defineProps({
    modelValue: {
      type: Number,
      deflaut: null
    },
    list: {
      type: Array,
      default: () => []
    },
    w: {
      type: [String, Number],
      default: '70'
    }
  })

  const state = reactive({
    activeIndex: 0,
    activeValue: null
  })
  initData()

  function initData() {
    state.activeValue = props.modelValue
    state.activeIndex = props.list.findIndex((item) => item.value === props.modelValue) || 0
  }

  // 切换
  const switchTab = (item, index) => {
    state.activeIndex = index
    state.activeValue = item.value
    emit('update:modelValue', item.value)
  }

  watch(
    () => props.modelValue,
    (vld) => {
      state.activeValue = vld
      state.activeIndex = vld - 1
    }
  )
</script>

<style lang="scss" scoped>
  .comp_radio {
    position: relative;
    display: inline-block;
    border-radius: 6px;
    background: #f5f7f9;
    overflow: hidden;
    height: 40px;

    .item {
      display: inline-block;
      padding: 8px 0;
      text-align: center;
      font-size: 14px;
      color: #404040;
      cursor: pointer;
      transition: all 0.3s;

      &.active {
        color: white;
        line-height: 26px;
        .font_color {
          color: #080f1e;

          font-weight: 500;
        }
      }

      span {
        position: relative;
        z-index: 5;
      }
    }

    .bg {
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #118bce;
      border-radius: 6px;
      transition: all 0.3s;
    }
  }
</style>
