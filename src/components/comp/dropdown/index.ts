// 结合event,props,和组件库的组件生成的新的自定义组件

import { SlotsType, defineComponent, h } from 'vue'
import { NButton } from '@/components/ui/naive'
import { useComponentMap } from '@/hooks'
import { DropdownAttrs } from './attrs'
import { UI } from 'types'

const Button = defineComponent({
  name: 'CButton',
  props: DropdownAttrs['props'] || {},
  emits: DropdownAttrs['events'],
  slots: Object as SlotsType<UI.Button['slots']>,
  render: () => h(useComponentMap(NButton, DropdownAttrs as unknown as UI.UiComp))
})
export default Button
