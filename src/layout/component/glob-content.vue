<template>
  <router-view v-slot="{ Component }">
    <Transition :appear="true" :name="themeConfig.pageAnimation" mode="out-in">
      <KeepAlive :include="Array.from(new Set(...keepAliveList))" :max="10">
        <component v-if="reloadFlag" :is="Component" class="fu-bg" />
      </KeepAlive>
    </Transition>
  </router-view>
</template>

<script lang="ts" setup>
  import { useApp, useTheme } from '@/hooks'
  const { reloadFlag, keepAliveList } = useApp()
  const { themeConfig } = useTheme()
  defineOptions({ name: 'GlobalContent' })
</script>

<style scoped></style>
