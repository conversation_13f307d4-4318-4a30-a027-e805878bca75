<template>
  <div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">主题色</span>
      <n-color-picker
        v-model:value="themeConfig.colors.primary"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { primary: value } })"
      />
    </div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">字体色彩</span>
      <n-color-picker
        v-model:value="themeConfig.colors.textColor"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { textColor: value } })"
      />
    </div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">警告</span>
      <n-color-picker
        v-model:value="themeConfig.colors.warning"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { warning: value } })"
      />
    </div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">成功</span>
      <n-color-picker
        v-model:value="themeConfig.colors.success"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { success: value } })"
      />
    </div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">信息</span>
      <n-color-picker
        v-model:value="themeConfig.colors.info"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { info: value } })"
      />
    </div>
    <div class="flex flex-center m-b-10px">
      <span class="w-60px">错误</span>
      <n-color-picker
        v-model:value="themeConfig.colors.error"
        class="flex-1"
        @update:value="(value: string) => setTheme({ colors: { error: value } })"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'SetDeawerColor' })
  import { useTheme } from '@/hooks'
  const { themeConfig, setTheme } = useTheme()
</script>
