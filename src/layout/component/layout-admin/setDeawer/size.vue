<template>
  <div>
    <div class="flex flex-y-center justify-between m-b-10px">
      <span class="w-60px">字体大小</span>
      <a-input-number class="w-130px" :min="10" :max="20" v-model:value="themeConfig.fontSize">
        <template #addonAfter>px</template>
      </a-input-number>
    </div>
    <div class="flex flex-y-center justify-between m-b-10px">
      <span class="w-60px">行高</span>
      <a-input-number class="w-130px" :step="0.01" :min="0" :max="3" v-model:value="themeConfig.lineHeight">
        <template #addonAfter>倍</template>
      </a-input-number>
    </div>
    <div class="flex flex-y-center justify-between m-b-10px">
      <span class="w-60px">组件高度</span>
      <a-input-number class="w-130px" :min="30" :max="100" v-model:value="themeConfig.height">
        <template #addonAfter>px</template>
      </a-input-number>
    </div>
    <div class="flex flex-y-center justify-between m-b-10px">
      <span class="w-60px">圆角大小</span>
      <a-input-number class="w-130px" :min="0" :max="10" v-model:value="themeConfig.borderRadius">
        <template #addonAfter>px</template>
      </a-input-number>
    </div>
    <div class="flex flex-y-center justify-between m-b-10px">
      <span class="w-60px">间距</span>
      <a-input-number class="w-130px" :min="10" :max="40" v-model:value="themeConfig.space">
        <template #addonAfter>px</template>
      </a-input-number>
    </div>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'SetDeawerSize' })
  import { useTheme } from '@/hooks'
  const { themeConfig } = useTheme()
</script>
<style lang="scss" scoped></style>
