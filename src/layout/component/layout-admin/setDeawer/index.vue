<template>
  <div class="set_deawer">
    <a-collapse v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <a-collapse-panel key="1" header="色彩配置" :style="customStyle">
        <SetDeawerColor></SetDeawerColor>
      </a-collapse-panel>

      <a-collapse-panel key="2" header="尺寸" :style="customStyle">
        <SetDeawerSize></SetDeawerSize>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="布局" :style="customStyle">
        <SetDeawerLayout></SetDeawerLayout>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'SetDeawer' })
  import { ref } from 'vue'
  import SetDeawerColor from './color.vue'
  import SetDeawerSize from './size.vue'
  import SetDeawerLayout from './layout.vue'
  import { CaretRightOutlined } from '@ant-design/icons-vue'
  const activeKey = ref(['1', '3'])
  const customStyle = 'background: #fff;border-radius: 4px;border: 0;overflow: hidden'
</script>
<style lang="scss" scoped>
  .set_deawer {
    color: var(--text-color-base);
  }
</style>
