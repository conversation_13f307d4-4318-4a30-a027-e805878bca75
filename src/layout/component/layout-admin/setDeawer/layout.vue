<template>
  <div>
    <div class="text-size-16px">layout布局</div>
    <div class="flex justify-around">
      <!-- 左右布局 -->
      <div class="item_box" :class="{ active: mode == 'around' }" @click="checkMode('around')">
        <div class="item flex">
          <div class="black w-10px h-100%"></div>
          <div class="white w-2px h-100%"></div>
          <div class="flex-col flex-1">
            <div class="black w-100% h-10px"></div>
            <div class="white h-2px w-100%"></div>
            <div class="gray flex-1"></div>
          </div>
        </div>
      </div>
      <!-- 一级侧边栏在顶部 -->
      <div class="item_box" :class="{ active: mode == 'minSidebar' }" @click="setMinSidebarFixed(true)">
        <div class="item flex">
          <div class="black w-10px h-100%"></div>
          <div class="white w-2px h-100%"></div>
          <div class="flex-col flex-1">
            <div class="black w-100% h-10px flex-y-center">
              <div class="gray w-20px m-l-3px h-6px"></div>
            </div>
            <div class="white h-2px w-100%"></div>
            <div class="gray flex-1"></div>
          </div>
        </div>
      </div>
      <!-- 上下布局 -->
      <div class="item_box" :class="{ active: mode == 'upAndDown' }" @click="checkMode('upAndDown')">
        <div class="item flex-col">
          <div class="black w-100% h-10px"></div>
          <div class="white h-2px w-100%"></div>
          <div class="flex flex-1">
            <div class="black w-10px h-100%"></div>
            <div class="white w-2px h-100%"></div>
            <div class="gray flex-1"></div>
          </div>
        </div>
      </div>
      <!-- 侧边栏在顶部 -->
      <div class="item_box" :class="{ active: mode == 'top' }" @click="checkMode('top')">
        <div class="item flex-col">
          <div class="black w-100% h-10px"></div>
          <div class="white h-2px w-100%"></div>
          <div class="flex flex-1">
            <div class="gray flex-1"></div>
          </div>
        </div>
      </div>
      <!-- 侧边栏分为两部分 -->
      <div class="item_box" :class="{ active: mode == 'sidebarFullHeader' }" @click="checkMode('sidebarFullHeader')">
        <div class="item flex">
          <div class="black w-5px h-100%"></div>
          <div class="white w-1px h-100%"></div>
          <div class="black w-5px h-100%"></div>
          <div class="white w-2px h-100%"></div>
          <div class="flex-col flex-1">
            <div class="black w-100% h-10px"></div>
            <div class="white h-2px w-100%"></div>
            <div class="gray flex-1"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'SetDeawerLayout' })
  import { useTheme } from '@/hooks'
  const { mode, checkMode, setMinSidebarFixed } = useTheme()
</script>
<style lang="scss" scoped>
  .item_box {
    padding: 2px;
    cursor: pointer;
    border-radius: 4px;
    &.active {
      border: 1px solid var(--primary-color);
    }
  }
  .item {
    width: 50px;
    height: 40px;
    border-radius: 4px;
    font-size: 40px;
    color: red;
    overflow: hidden;
  }
  .gray {
    background-color: #e0e0e6;
  }
  .black {
    background-color: #18181c;
  }
  .white {
    background-color: #fff;
  }
</style>
