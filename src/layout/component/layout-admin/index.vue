<template>
  <!-- 左右布局,侧边栏在顶部,小型侧边栏 -->
  <div class="wh-full flex-col common_layout_adimn_container">
    <div class="header header-h w-full flex flex-fixed" v-if="!isHide">
      <div
        class="logo header-h flex-fixed"
        :class="[(isSidebarFold || mode == 'upAndDown') && !isMobile ? 'sidebar-w' : '']"
        :style="{
          width: !isSidebarFold && mode != 'upAndDown' ? `${themeConfig.layout.foldSidebarWidth}px` : '',
          zIndex: 3,
          boxShadow: ['top', 'upAndDown'].includes(mode)
            ? '-2px 1px 4px 0 rgba(0,21,41,.12)'
            : '1px 0 1px 0 rgba(29, 35, 41, 0.05)'
        }"
      >
        <slot name="logo"></slot>
      </div>
      <div class="header_box flex-grow flex-y-center" style="box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12); z-index: 2">
        <SvgIcon v-if="isMobile" icon="open" class="mr-10px" @click="checkSidebarFold(true)"></SvgIcon>
        <div class="flex flex-items-center w-100% grid-justify-between">
          <slot name="header"></slot>
          <SvgIcon
            v-if="getConfig('NODE_ENV') == 'dev'"
            icon="set"
            class="text-18px color-primary mr-10px cursor-pointer"
            @click="isOpenSet = true"
          ></SvgIcon>
        </div>
      </div>
    </div>
    <div class="sidebar_box flex flex-grow overflow-hidden">
      <!-- 正常侧边栏 -->
      <div
        class="sidebar flex-fixed overflow-hidden"
        :class="[mode != 'top' ? 'sidebar-w' : 'w-0px']"
        v-if="!isHide"
        :style="[
          !isSidebarFold
            ? { width: isMobile ? '0px !important' : themeConfig.layout.foldSidebarWidth + 'px' }
            : { width: isMobile ? '0px !important' : '' },
          {
            zIndex: 2,
            boxShadow: '2px 0 8px 0 rgba(29, 35, 41, 0.05)'
          }
        ]"
      >
        <slot name="sidebar"></slot>
      </div>
      <!-- h5显示侧边栏 -->
      <a-drawer
        :width="themeConfig.layout.sidebarWidth"
        :rootClassName="'menu_drawer'"
        placement="left"
        :closable="false"
        :open="isMobileSidebarFold && isMobile"
        :body-style="{ padding: '0px' }"
        @close="checkSidebarFold(false)"
        v-if="!isHide"
      >
        <slot name="sidebar"></slot>
      </a-drawer>
      <div class="flex-1 flex-col common_scroll_y scroll_bg">
        <div v-if="themeConfig.layout.isTag && !isHide" class="flex-fixed w-full bg-color">
          <Transition name="slide-up">
            <Tag v-if="pointData.alert" :tag-data="pointData" @close="state.closeTag = false" />
          </Transition>
        </div>

        <div
          class="main flex-grow"
          :style="{
            padding: isHide ? '0' : mainPadding
          }"
          id="scrollbar"
          :class="{ lock_main_loading: useGlobalLoadingStore.state.loading }"
          v-loading="useGlobalLoadingStore.state.loading"
        >
          <slot name="default"></slot>
        </div>
      </div>
    </div>

    <!-- 设置侧边栏 -->
    <a-drawer
      :width="350"
      placement="right"
      :closable="false"
      :open="isOpenSet"
      :body-style="{ padding: '0px' }"
      @close="isOpenSet = false"
    >
      <SetDeawer></SetDeawer>
    </a-drawer>
  </div>
</template>
<script setup lang="ts">
  import SetDeawer from './setDeawer/index.vue'
  import { useApp, useTheme, useGoods, useGlobalLoading, usePoints, useMenu } from '@/hooks'
  import { getConfig } from '@/utils'
  import { defineSlots, onMounted, ref, reactive, computed } from 'vue'
  import { isNumber } from 'lodash-es'
  import Tag from '../../template1/components/tag.vue'
  const { isMobile } = useApp()
  const { mode, themeConfig, themeVar, checkSidebarFold, isSidebarFold, isMobileSidebarFold } = useTheme()
  const useGoodsStore = useGoods()
  const useGlobalLoadingStore = useGlobalLoading()
  const { breadcrumbsData } = useMenu()
  // 通过路由name判断是否隐藏header和sidebar以及tag
  const hideArr = ['QaChatEditor', 'LandingPageEditor']
  const isHide = computed(() => {
    return breadcrumbsData.value.findIndex((v) => hideArr.includes(v.name)) != -1
  })
  const isOpenSet = ref<boolean>(false)
  const { pointData } = usePoints()
  const state = reactive({
    closeTag: true
  })
  onMounted(() => {
    const scrollbar = document.getElementById('scrollbar')
    useGoodsStore.setScrollEl(scrollbar)
  })
  const mainPadding = computed(() => {
    if (isMobile.value) {
      return 0
    }
    if (state.closeTag && pointData.value.alert) {
      return '10px 24px 24px 0'
    } else {
      return isNumber(themeConfig.value.layout.mainPadding)
        ? (themeConfig.value.layout.mainPadding || 10) + 'px'
        : themeConfig.value.layout.mainPadding || '10px'
    }
  })
  defineSlots<{
    /**logo */
    logo?: (props: {}) => any
    /**头部 */
    header?: (props: {}) => any
    /**侧边栏 */
    sidebar?: (props: {}) => any
    /**主要视图显示区域 */
    default?: (props: {}) => any
  }>()
</script>
<style lang="scss" scoped>
  .header {
    background-color: v-bind('themeVar.headerBgColor');
  }
  .sidebar {
    background-color: v-bind('themeVar.menuBgColor');
    overflow: hidden;
  }
  .main {
    background-color: v-bind('themeVar.mainBgColor || themeVar.layoutBgColor');
    overflow-y: auto;
    // width: 100%;
    // height: 100%;
  }

  .logo {
    background-color: v-bind('themeVar.headerBgColor');
  }
  .scroll_bg {
    &::-webkit-scrollbar {
      background-color: v-bind('themeVar.layoutBgColor');
    }
  }
  .header,
  .sidebar,
  .main,
  .logo {
    transition: all 0.2s;
  }

  .lock_main_loading {
    height: calc(100vh - 56px);
    overflow: hidden;
  }
  .bg-color {
    background-color: v-bind('themeVar.mainBgColor || themeVar.layoutBgColor');
  }
</style>
