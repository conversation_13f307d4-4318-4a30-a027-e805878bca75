<template>
  <ConfigProvider :theme="{ token: themeOverrides.common }" :locale="zhCN">
    <a-watermark
      class="watermark"
      :content="watermark"
      :rotate="-10"
      :gap="[100, 100]"
      :font="{
        color: 'rgb(99,99,99,.20)',
        fontSize: 14
      }"
    >
      <LayoutAdmin>
        <template #logo>
          <Logo />
        </template>
        <template #header>
          <Header />
        </template>
        <template #sidebar>
          <Sidebar />
        </template>
        <NConfigProvider :theme-overrides="themeOverrides">
          <NGlobalStyle></NGlobalStyle>
          <GlobContent></GlobContent>
        </NConfigProvider>
      </LayoutAdmin>
    </a-watermark>
  </ConfigProvider>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Template2' })
  import { useApp, useTheme } from '@/hooks'
  import Logo from './logo.vue'
  import Header from './header.vue'
  import Sidebar from './sidebar.vue'
  import { localStg } from '@/utils'
  import LayoutAdmin from '../component/layout-admin/index.vue'
  import { NConfigProvider, NGlobalStyle } from 'naive-ui'
  import { ConfigProvider } from 'ant-design-vue'
  import GlobContent from '../component/glob-content.vue'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  import { locale } from 'dayjs'
  import 'dayjs/locale/zh-cn'
  import { computed } from 'vue'
  locale('en')
  const { themeOverrides } = useTheme()
  const { useInfo } = useApp()

  const watermark = computed(() => {
    return localStg.get('userInfo')?.realname + ' ' + (localStg.get('userInfo')?.phone || '').substr(-4)
  })
</script>
<style>
  .watermark {
    height: 100%;
  }
</style>
