<template>
  <div class="p-b-10px">
    <ul id="scrollableDiv" class="InfiniteScroll common_scroll">
      <li class="item" v-for="item in msgList" :key="item.id">
        <div class="msg_title">{{ item.msg_title }}</div>
        <div class="msg_content">
          <span>{{ item.msg_content }}</span>
          <span class="btn ml-2" @click="msgToPage(item)">详情 ></span>
        </div>
        <div class="msg_time">{{ item.created_at }}</div>
      </li>
      <a-empty v-if="msgList.length == 0" />
    </ul>
    <a-spin class="spin" :spinning="loading" />
  </div>
</template>
<script setup>
  import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import { useMsg } from '@/hooks'
  import { msgUpdateApi } from '@/api/common'
  import { msgList as getMsgListApi } from '@/views/shop/dataCenter/dataOverview/index.api'
  import { useRouter } from 'vue-router'

  const props = defineProps(['id'])
  const emit = defineEmits(['msgChange'])

  const router = useRouter()
  const { smgNum, upMsgList } = useMsg()

  const loading = ref(false)
  const msgList = ref([])
  const total = ref(0)
  const unread_count = ref(0)
  const query = ref({
    page: 1,
    page_size: 10,
    type: 0,
    is_read: 0 //0全部  1未读  2已读
  })

  let scrollableDiv = ref(null)

  onMounted(() => {
    getMsgList()
    scrollableDiv.value = document.getElementById('scrollableDiv')
    scrollableDiv.value && scrollableDiv.value.addEventListener('scroll', handleScroll)
  })

  onBeforeUnmount(() => {
    scrollableDiv.value = document.getElementById('scrollableDiv')
    scrollableDiv.value && scrollableDiv.value.removeEventListener('scroll', handleScroll)
  })

  const getMsgList = async () => {
    try {
      // if (msgList.value.length == total.value) return message.warning('暂无更多数据')
      let res = await getMsgListApi(query.value)
      loading.value = false
      msgList.value = query.value.page == 1 ? res.data?.list || [] : msgList.value.concat(res.data?.list)
      total.value = res.data?.total_num || 0
      query.value.page++
    } catch (error) {
    } finally {
      loading.value = false
    }
  }

  const loadMoreData = () => {
    loading.value = true
    setTimeout(() => {
      getMsgList()
    }, 1000)
  }

  const handleScroll = () => {
    const scrollHeight = scrollableDiv.value.scrollHeight
    const scrollTop = scrollableDiv.value.scrollTop
    const clientHeight = scrollableDiv.value.clientHeight
    if (scrollHeight - scrollTop - clientHeight < 1 && !loading.value) {
      loadMoreData()
    }
  }

  const msgUpdate = async (id) => {
    try {
      let res = await msgUpdateApi({ id })
      upMsgList()
    } catch (error) {}
  }

  // 跳转
  const msgToPage = (v) => {
    console.log(v.type, 'vvv', v.join_id)
    switch (v.type) {
      case 1:
        router.push({ name: 'OrderDetails', query: { id: v.join_id, type: 'order' } })
        msgUpdate(v.id)
        break
      case 2:
        if (v.subtype == 'shop_complain') {
          router.push({ name: 'ShopComplaint', query: { id: v.join_id } })
        } else {
          router.push({ name: 'WxHandle', query: { id: v.join_id } })
        }
        msgUpdate(v.id)
        break
      case 3:
        router.push({ name: 'sale_details', query: { id: v.join_id, type: 'sales' } })
        msgUpdate(v.id)
        break
      case 5:
        router.push({ name: 'ShopGoodsListDetail', query: { id: v.join_id } })
        msgUpdate(v.id)
        break
      case 11:
        router.push({ name: 'ShopOrderQuery', query: { status: 2 } })
        msgUpdate(v.id)
        break
      case 21:
        router.push({ name: 'ShopComplaint', query: { id: v.join_id, status: 2 } })
        msgUpdate(v.id)
        break
    }
  }

  const onGetMsgList = () => {
    query.value.is_read = props.id
    query.value.page = 1
    loading.value = true
    getMsgList()
    upMsgList()
  }

  watch(
    () => props.id,
    (newVal) => {
      onGetMsgList()
    },
    {
      immediate: true
    }
  )

  defineExpose({
    upDate: msgUpdate,
    onGetMsgList
  })
</script>
<style lang="scss" scoped>
  ul {
    margin-bottom: 10px;
  }

  ol,
  ul,
  dl {
    margin-bottom: 0;
  }
  .InfiniteScroll {
    max-height: 300px;
    width: 480px;
    overflow-y: auto;
    .msg_title {
      margin-bottom: 16px;
      color: rgb(36, 47, 87);
      font-weight: 500;
      font-size: 16px;
      cursor: pointer;
    }
    .msg_content {
      color: #242f57;
      .btn {
        color: var(--primary-color);
        cursor: pointer;
        user-select: none;
      }
    }
    .msg_time {
      color: rgb(151, 160, 195);
      cursor: unset;
    }
    .item {
      padding: 16px;
      border-bottom: 1px solid rgba(151, 160, 195, 0.3);
    }
  }
  .spin {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
</style>
