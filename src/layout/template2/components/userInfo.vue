<template>
  <div class="flex flex-items-center">
    <a-dropdown>
      <a class="ant-dropdown-link flex flex-y-center" @click.prevent>
        <SvgIcon style="font-size: 22px" icon="user" />
        <slot name="default"></slot>
      </a>
      <template #overlay>
        <a-menu>
          <a-menu-item key="0">
            <div class="flex-y-center flex-col" @click.stop="">
              <div>{{ useInfo?.realname }}</div>
              <!-- <div>{{ useInfo?.phone }}</div> -->
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="1">
            <div class="flex-x-center" @click="editPassword">
              <FormOutlined class="m-r-5px" />
              <span>修改密码</span>
            </div>
          </a-menu-item>
          <!-- <a-menu-item key="2"><div class="flex-x-center">个人中心</div></a-menu-item> -->
          <a-menu-item key="3">
            <div class="flex-x-center" @click.prevent="routerPush({ name: 'Store' })">
              <AppstoreOutlined class="m-r-5px" />
              <span>全部店铺</span>
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="4" @click="logout">
            <div class="flex-x-center">
              <LoginOutlined class="m-r-5px" />
              <span>退出登录</span>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-modal v-model:open="dialog.visible" title="修改密码" :footer="null">
      <EditPassWord :size="1" v-if="dialog.visible" type="image" @event="onEvent" />
    </a-modal>
    <a-modal v-model:open="dialog.dialogValid.visible" :footer="null" :closable="false" :maskClosable="false">
      <template #title>
        <span class="el-dialog__title"
          >尊敬的<span class="reset_password_title">{{ localStg.get('userInfo').realname }}</span
          >用户</span
        >
      </template>
      <ValidNewAccount :size="1" v-if="dialog.dialogValid.visible" type="image" @event="onValidEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import { SvgIcon } from '@/components'
  import { FormOutlined, LoginOutlined, AppstoreOutlined } from '@ant-design/icons-vue'
  import { useApp, useLogin, useRouter } from '@/hooks'
  import EditPassWord from '@/layout/template2/components/EditPassWord.vue'
  import ValidNewAccount from '@/layout/template2/components/ValidNewAccount.vue'
  import { computed, onMounted, reactive, ref } from 'vue'
  import { localStg } from '@/utils'
  const dialog = reactive({
    visible: false,
    dialogValid: {
      viseble: false
    }
  })
  const { logout } = useLogin()
  const { routerPush } = useRouter()
  const { useInfo } = useApp()
  const editPassword = () => {
    dialog.visible = true
  }
  const onEvent = (type) => {
    if (type.cmd === 'close') {
      dialog.visible = false
    } else if (type.cmd === 'edit') {
    }
  }
  dialog.dialogValid.visible = localStg.get('userInfo').reset_password
  const onValidEvent = () => {
    dialog.visible = true
  }
</script>
<style lang="scss" scoped>
  .reset_password_title {
    color: var(--primary-color);
    margin: 0 4px;
  }
</style>
