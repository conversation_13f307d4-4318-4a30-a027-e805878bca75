<template>
  <div class="edit_password">
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width: 94px' }">
      <div class="item flex">
        <div class="item_form">
          <a-form-item label="原始密码：" name="old_password">
            <a-input-password
              :visibility-toggle="false"
              v-model:value="data.form.old_password"
              maxlength="16"
              placeholder="请输入原始密码"
            />
          </a-form-item>
        </div>
        <div class="item_icon">
          <template v-if="data.form.old_password">
            <SuccessFilled class="success" v-if="validPsdStatus(data.form.old_password, 'old') && psdYes" />
            <InfoCircleFilled class="danger" v-else />
          </template>
        </div>
      </div>
      <div class="item flex">
        <div class="item_form">
          <a-form-item label="新密码：" name="password">
            <a-input-password
              :visibility-toggle="false"
              v-model:value="data.form.password"
              maxlength="16"
              placeholder="密码6-16位，同时包含大小写英文、数字的组合"
            />
          </a-form-item>
        </div>
        <div class="item_icon">
          <template v-if="data.form.password">
            <a-icon class="success" v-if="validPsdStatus(data.form.password, 'new')"><CheckCircleFilled /></a-icon>
            <a-icon class="danger" v-else><InfoCircleFilled /></a-icon>
          </template>
        </div>
      </div>
      <div class="item flex">
        <div class="item_form">
          <a-form-item label="密码强度：">
            <PasswordStrength :level="checkPasswordStrength(data.form.password)" />
          </a-form-item>
        </div>
        <div class="item_icon"></div>
      </div>
      <div class="item flex">
        <div class="item_form">
          <a-form-item label="确认密码：" name="newpassword">
            <a-input-password
              :visibility-toggle="false"
              v-model:value="data.form.newpassword"
              maxlength="16"
              placeholder="请再次输入登录密码"
            />
          </a-form-item>
        </div>
        <div class="item_icon">
          <template v-if="data.form.password">
            <template v-if="data.form.newpassword">
              <InfoCircleFilled class="danger" v-if="data.form.password !== data.form.newpassword" />
              <CheckCircleFilled class="success" v-else />
            </template>
          </template>
        </div>
      </div>
    </a-form>
    <div class="footer">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" :loading="data.loading" @click="submitForm(ruleForm)">保存</a-button>
    </div>
  </div>
</template>
<script setup>
  import { reactive, ref } from 'vue'
  import { checkReg, checkPasswordStrength, localStg } from '@/utils'
  import { resetByPassword, checkPasswordRepeat } from '@/views/login/index/index.api'
  import PasswordStrength from './PasswordStrength.vue'
  import { message } from 'ant-design-vue'
  import { useLogin } from '@/hooks'
  import { CheckCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'
  const { logout } = useLogin()
  const emit = defineEmits(['event'])
  const ruleForm = ref(null)
  const newPassWord = (rule, value) => {
    return new Promise((resolve, reject) => {
      if (value !== data.form?.password) {
        reject(new Error('两次密码输入不一致'))
      } else {
        resolve()
      }
    })
  }

  // 验证密码是否与后台相同
  const psdYes = ref(false)
  const checkPasswordIsRepeat = async (rule, value) => {
    try {
      const res = await checkPasswordRepeat({ username: localStg.get('account'), old_password: value })
      if (res.data?.same === 1) {
        psdYes.value = true
        return Promise.resolve()
      } else {
        psdYes.value = false
        return Promise.reject('密码错误')
      }
    } catch (error) {
      return Promise.reject('请输入原始密码')
    }
  }

  const rules = {
    old_password: [
      { required: true, message: '请输入原始密码', trigger: 'blur' },
      { pattern: checkReg.old_pwd, message: '设置6至16位英文/数字/符号组合，至少包含2种字符' },
      { validator: checkPasswordIsRepeat, trigger: 'blur' }
    ],
    password: [
      { required: true, message: '密码6-16位，同时包含大小写英文、数字的组合', trigger: 'blur' },
      { pattern: checkReg.pwd, message: '密码6-16位，同时包含大小写英文、数字的组合', trigger: ['change', 'blur'] }
    ],
    newpassword: [
      { required: true, message: '请再次输入登录密码', trigger: 'blur' },
      { validator: newPassWord, trigger: ['change', 'blur'] }
    ]
  }

  const data = reactive({
    loading: false,
    form: {
      reset: 2, // 1.重置密码， 2.修改密码，  0 或 不传 -> 忘记密码
      username: localStg.get('account'),
      old_password: '',
      password: '',
      newpassword: ''
    }
  })

  const submitForm = (formEl) => {
    formEl.validate().then((valid) => {
      if (!valid) return false
      edit()
    })
  }

  // 修改密码
  const edit = async () => {
    try {
      data.loading = true
      let res = await resetByPassword(data.form)
      message.success(res.msg)
      logout()
    } catch (error) {
      console.error(error)
    } finally {
      data.loading = false
    }
  }

  const close = () => {
    ruleForm.value.resetFields()
    emit('event', { cmd: 'close' })
  }

  // 校验密码验证状态
  const validPsdStatus = (val, type) => {
    const is_flag = type == 'old' ? checkReg.old_pwd.test(val) : checkReg.pwd.test(val)
    if (is_flag) {
      return true
    }
    return false
  }
</script>
<style lang="scss" scoped>
  :deep(.ant-form-item-label) {
    label {
      width: 94px;
      display: inline-flex;
      justify-content: flex-end;
      flex: 0 0 auto;
    }
  }
  .edit_password {
    .item {
      width: 100%;
      .item_form {
        flex: 1;
      }
      .item_icon {
        width: 40px;
        line-height: 40px;
        font-size: 18px;
        padding-left: 10px;
        .success {
          color: var(--success-color);
        }
        .danger {
          color: var(--error-color);
        }
      }
    }
    .footer {
      text-align: end;
    }
  }
</style>
