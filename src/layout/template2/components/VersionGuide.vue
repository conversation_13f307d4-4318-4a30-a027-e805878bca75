<template>
  <div>
    <a-modal
      v-model:open="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      :footer="null"
      :closable="state.activeIndex + 1 === state.guideInfo.content.length || !state.guideInfo.content.length"
      :keyboard="state.activeIndex + 1 === state.guideInfo.content.length || !state.guideInfo.content.length"
      :maskClosable="false"
      :bodyStyle="{
        margin: '-20px -24px'
      }"
      destroyOnClose
      centered
      @cancel="handleFinish(true)"
    >
      <div class="version-guide">
        <!-- <div class="v-v">版本号{{ state.guideInfo.version }}</div> -->
        <div class="v-t">
          <div class="v-title">{{ state.guideInfo.title }}</div>
          <div :gutter="32" class="v-sub-title flex flex-justify-between">
            <div>
              {{ state.guideInfo.subtitle }}
              <!-- 本次版本更新的具体细节您也可以移步到<span class="v-guide-page"
                >【管理】-【系统管理】-【版本配置】</span
              >页面中查看 -->
            </div>
            <div style="flex: 0 0 60px" class="pl-16px" v-if="state.guideInfo?.video">
              <div class="v-video cursor-pointer" @click="handleShowVideo">查看视频</div>
            </div>
          </div>
        </div>
        <div class="v-guide-bg">
          <div class="v-guide" v-if="state.guideInfo.content?.length">
            <div class="v-g-list">
              <div class="v-g-item">
                <div class="v-g-img">
                  <a-image class="v-middle" :src="currentGuide?.img" />
                </div>
                <div class="flex flex-items-center mt-16px">
                  <div class="v-g-step-num">{{ state.activeIndex + 1 }}/{{ state.guideInfo.content.length }}</div>
                  <div class="v-g-step ml-8px">
                    <div class="v-g-s-title">操作步骤</div>
                  </div>
                </div>
                <div class="v-g-s-text">{{ currentGuide.step }}</div>
              </div>
            </div>
          </div>
          <div class="v-step mt-22px">
            <a-button @click="onStep(-1)" v-if="state.activeIndex > 0">上一步</a-button>
            <a-button type="primary" @click="onStep(1)" v-if="state.activeIndex + 1 < state.guideInfo.content.length"
              >下一步</a-button
            >
            <a-button
              type="primary"
              @click="onStep(0)"
              v-if="state.activeIndex + 1 === state.guideInfo.content.length || !state.guideInfo.content.length"
              >完成</a-button
            >
          </div>
        </div>
      </div>
    </a-modal>
    <a-modal
      v-model:open="videoDialog.visible"
      :title="videoDialog.title"
      :width="videoDialog.width"
      :footer="null"
      :bodyStyle="{
        margin: '-20px -24px'
      }"
      style="top: 33%"
      wrapClassName="video-dialog-wrap"
      destroyOnClose
    >
      <div>
        <video class="w-720px h-405px" :src="state.guideInfo.video" controls />
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  console.log('VersionGuide')
  import { computed, reactive, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { get_version_guide_last, update_version_guide_record } from '@/api/common'

  const router = useRouter()

  const props = defineProps(['modelValue'])
  const emit = defineEmits(['update:modelValue'])

  const state = reactive({
    guideInfo: {
      version: '',
      title: '',
      subtitle: '',
      content: [],
      video: ''
    },
    activeIndex: 0
  })
  const dialog = reactive({
    title: '',
    visible: false,
    width: 770
  })
  const videoDialog = reactive({
    title: '',
    visible: false,
    width: 720
  })

  // 获取用户引导信息
  const getVersionGuideInfo = async () => {
    try {
      const { data } = await get_version_guide_last()
      state.guideInfo = data.data
      if (data.is_pop) {
        if (data.data.status === 2) {
          emit('update:modelValue', false)
          await handleFinish(false)
        } else {
          emit('update:modelValue', data.is_pop)
          showGuideDialog()
        }
      } else {
        emit('update:modelValue', data.is_pop)
      }
    } catch (error) {
      console.error(error)
    }
  }

  watch(
    () => router.currentRoute.value.path,
    () => {
      getVersionGuideInfo()
    },
    { immediate: true }
  )

  // 弹出引导页
  const showGuideDialog = async () => {
    try {
      dialog.visible = true
    } catch (error) {
      console.error(error)
    }
  }

  // 当前显示引导
  const currentGuide = computed(() => {
    return state.guideInfo.content?.length ? state.guideInfo.content[state.activeIndex] : {}
  })

  // 查看视频
  const handleShowVideo = () => {
    videoDialog.visible = true
  }

  // 操作步骤
  const onStep = (value: number) => {
    if (value === -1) {
      if (state.activeIndex) state.activeIndex--
    } else if (value === 1) {
      if (state.activeIndex + 1 < state.guideInfo.content.length) state.activeIndex++
    } else {
      handleFinish(true)
      dialog.visible = false
    }
  }

  // 指引操作完成
  const handleFinish = async (flag?: any) => {
    try {
      await update_version_guide_record({ id: state.guideInfo.id })
      if (flag) {
        window.location.reload()
      }
    } catch (error) {
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .version-guide {
    font-family:
      PingFangSC,
      PingFang SC;
    padding: 16px 24px;
    border-radius: 6px;
    position: relative;
    background: radial-gradient(47% 51% at 91% 92%, #ffece4 0%, #ebefff 100%);

    .v-v {
      height: 22px;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
    }
    .v-t {
      .v-title {
        // height: 24px;
        font-weight: 400;
        font-size: 24px;
        color: #313233;
        line-height: 33px;
        // margin-top: 20px;
        margin-bottom: 16px;
        text-align: center;
        padding: 0 36px;
      }
      .v-sub-title {
        font-weight: 400;
        font-size: 12px;
        color: #636e95;
        line-height: 17px;
        margin-bottom: 16px;
        .v-guide-page {
          color: #313233;
          font-weight: 600;
        }
        .v-video {
          width: 60px;
          height: 26px;
          background: #ffffff;
          border-radius: 4px;
          font-weight: 400;
          font-size: 12px;
          color: var(--primary-color);

          line-height: 26px;
          text-align: center;
        }
      }
    }
    .v-guide-bg {
      background: linear-gradient(135deg, #ffffff 70%, #fff4ef 100%);
      box-shadow: 0px 2px 10px 0px rgba(148, 165, 191, 0.21);
      border-radius: 8px;
      padding: 16px;
    }
    .v-guide {
      .v-g-list {
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        .v-g-img {
          height: 336px;
          background: #f9f9f9;
          border-radius: 8px;
          :deep {
            .ant-image {
              .ant-image-img {
                width: auto;
              }
              height: 100%;
              display: flex;
              justify-content: center;
            }
          }
        }
        .v-g-step-num {
          height: 19px;
          color: #c0c0c0;
          line-height: 19px;
        }
        .v-g-s-title {
          line-height: 19px;
          height: 19px;
          color: #313131;
        }
        .v-g-s-text {
          line-height: 18px;
          color: #636e95;
          font-weight: 300;
          margin-top: 10px;
        }
      }
    }
    .v-step {
      text-align: right;
    }
    .guide-bg-img {
      position: absolute;
      bottom: 0;
      right: 0;
      pointer-events: none;
    }
  }
</style>
<style>
  .video-dialog-wrap {
    .ant-modal-content {
      /* background-color: transparent; */
    }
  }
</style>
