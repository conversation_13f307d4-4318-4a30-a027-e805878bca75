<template>
  <a-menu
    style="min-width: 400px"
    :selected-keys="breadcrumbsData.map((v) => v.name)"
    mode="horizontal"
    @click="onClick"
  >
    <a-menu-item v-for="v in menuData" :key="v.name" :label="v.title">
      {{ v.title }}
    </a-menu-item>
  </a-menu>
</template>
<script setup lang="ts">
  import { useMenu } from '@/hooks'
  import { useRouter } from '@/hooks/use-router'
  import { nextTick } from 'vue'
  const { menuData, breadcrumbsData } = useMenu()
  const { routerPush } = useRouter()

  const onClick = (obj: any) => {
    nextTick(() => {
      routerPush({ name: obj.key })
    })
  }
</script>
<style lang="scss" scoped>
  .ant-menu {
    line-height: var(--header-height);
    font-size: 16px;
  }
  .ant-menu-horizontal {
    border-bottom: none;
  }
  :deep(.ant-menu-item) {
    &:hover::after {
      border-bottom-color: transparent;
    }
  }
</style>
