<template>
  <div class="strength flex">
    <div class="item" :class="{ danger: props.level === 1 }">弱</div>
    <div class="item" :class="{ warning: props.level === 2 }">中</div>
    <div class="item" :class="{ success: props.level === 3 }">强</div>
  </div>
</template>

<script setup name="PasswordStrength">
  const props = defineProps({
    level: {
      type: Number,
      deflaut: 0
    }
  })
  console.log('props.level', props.level)
</script>

<style lang="scss" scoped>
  .strength {
    height: 34px;
    line-height: 34px;
    .item {
      flex: 1;
      text-align: center;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      &.danger {
        background-color: var(--error-color);
        border-color: var(--error-color);
        color: #fff;
      }
      &.warning {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
        color: #fff;
      }
      &.success {
        background-color: var(--success-color);
        border-color: var(--success-color);
        color: #fff;
      }
    }
    .item + .item {
      border-left-width: 0;
    }
    .item:first-child {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }
    .item:last-child {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }
</style>
