<template>
  <template v-for="item in data">
    <!-- 渲染目录 -->
    <template v-if="item.children && item.children.length">
      <a-sub-menu :key="item.name" :title="item.title">
        <template #icon>
          <component class="text-16px!" :is="renderIcon(item.icon)"></component>
        </template>
        <ChildMenu :data="item.children"></ChildMenu>
      </a-sub-menu>
    </template>
    <!-- 渲染没有子级的菜单 -->
    <a-menu-item v-else :key="item.name" :label="item.title">
      <template #icon v-if="item.icon && item.icon != 'none'">
        <component class="text-16px!" :is="item.icon"></component>
      </template>
      <a :href="`/#${item.path}`" onclick="return false;" class="inline-block">
        <component
          :is="
            renderTitle(
              item.meta.titleComponent ? returnTitleComponent(item.title, item.meta.titleComponent) : item.title
            )
          "
        ></component>
      </a>
    </a-menu-item>
  </template>
</template>
<script setup lang="ts">
  import { MenuData } from 'types/router'
  import { type Component, PropType, h, VNode } from 'vue'
  import { isFunction, isString } from 'lodash-es'
  import { SvgIcon } from '@/components'

  defineOptions({ name: 'ChildMenu' })
  defineProps({
    data: {
      type: Array as PropType<MenuData[]>,
      default: () => []
    }
  })

  // 选择icon
  const renderIcon = (icon: string | Component): Component => {
    if (isString(icon)) {
      return h(SvgIcon, { icon })
    }
    return icon
  }
  // 渲染title
  const renderTitle = (title: string | Component) => {
    if (isString(title)) {
      return h('span', title)
    }
    return title
  }
  // 判断渲染title组件类型

  const returnTitleComponent = (title: string, titleComponent: any): VNode | Array<VNode> => {
    if (isFunction(titleComponent())) {
      return titleComponent(title)
    } else {
      return titleComponent
    }
  }
</script>
