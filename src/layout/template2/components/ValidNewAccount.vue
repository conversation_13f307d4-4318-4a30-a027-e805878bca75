<template>
  <div>
    <p class="mt-0">您好：</p>
    <p>
      欢迎您加入<span class="system_platform">{{ systemPlatform }}</span
      >系统平台，当前登录密码安全等级较低，为保障您的系统安全，请修改登录密码后使用！
    </p>
    <div class="footer">
      <a-button @click="logout">退出登录</a-button>
      <a-button type="primary" @click="resetPassword">修改密码</a-button>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useLogin } from '@/hooks'
  import { localStg, getConfig } from '@/utils'

  const { logout } = useLogin()
  const emit = defineEmits(['event'])

  const systemPlatform = ref(getConfig('TITLE'))

  const resetPassword = () => {
    emit('event')
  }
</script>

<style lang="scss" scoped>
  .mt-0 {
    margin-top: 0;
  }
  .system_platform {
    color: var(--primary-color);
    margin: 0 4px;
  }
  .footer {
    text-align: center;
  }
</style>
