<template>
  <div class="flex-grow flex justify-between flex-items-center flex-justify-between">
    <HeaderMenu v-if="!isMobile" />
    <AvatarUser class="flex-grow" />
  </div>
</template>
<script setup lang="ts">
  import HeaderMenu from './components/headerMenu.vue'
  import AvatarUser from './components/avatarUser.vue'
  import { useApp } from '@/hooks'

  const { isMobile } = useApp()
</script>
