<template>
  <div class="mobile-sidebar">
    <a-menu
      mode="inline"
      class="menu common_scroll_y_hover m-x-auto"
      :selected-keys="breadcrumbsData.map((v) => v.name)"
      v-model:open-keys="openKeys"
      :inline-collapsed="!isSidebarFold"
      @click="onRouter"
    >
      <ChildMenu :data="isMobile ? menuData : routerSublevel"></ChildMenu>
    </a-menu>
    <div class="open" v-if="!isMobile" @click="checkSidebarFold(!isSidebarFold)">
      <SvgIcon class="icon" icon="open"></SvgIcon>
    </div>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'Sidebar' })
  import ChildMenu from './components/childMenu.vue'
  import { SvgIcon } from '@/components'
  import { ref } from 'vue'
  import { useApp, useTheme, useMenu, useRouter } from '@/hooks'
  import { MenuData } from 'types/router'
  const { routerSublevel, menuData, breadcrumbsData } = useMenu()
  const { routerPush } = useRouter()
  const { isMobile, setKeepAlive, keepAliveList } = useApp()
  const { checkSidebarFold, isSidebarFold } = useTheme()

  // 获取所有子级在 一级侧边栏目在顶部的时候需要
  let allChildren: MenuData[] = []
  menuData.value.forEach((v) => allChildren.push(...(v.children || [])))

  // 当前打开的子级
  const openKeys = ref(allChildren.map((v) => v.name))

  const onRouter = (item: any) => {
    // 切换清理掉缓存的列表页
    let names = Array.from(new Set(...keepAliveList.value))
    if (!names.includes(item.key)) setKeepAlive([])
    routerPush({ name: item.key })
    if (isMobile.value) {
      checkSidebarFold(false)
    }
  }
</script>
<style lang="scss" scoped>
  .mobile-sidebar {
    width: 100%;
    background: #fff;
    height: calc(100vh - 10px);
    position: relative;
    .menu {
      max-height: calc(100vh - 20px);
    }

    :deep(.ant-menu-light.ant-menu-root.ant-menu-inline),
    :deep(.ant-menu-light.ant-menu-root.ant-menu-vertical) {
      border-inline-end: none;
    }
    :deep(.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline) {
      background: transparent;
    }
    .open {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 54px;
      padding: 0px 16px;
      border-top: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      cursor: pointer;
      .icon {
        font-size: 20px;
      }
      &:hover {
        .icon {
          color: #0080ff;
        }
      }
    }
  }
</style>
