import { ref, h } from 'vue'
let openDownLoadModal = ref(false)
const setOpen = (val: boolean = true) => {
  openDownLoadModal.value = val
}
export default function downLoaddatas() {
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1000
    },
    dataSource: [],
    columns: [
      {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        slot: true,
        width: 60
      },
      {
        title: '导出类型',
        dataIndex: 'type',
        key: 'type',
        slot: true,
        width: 220
      },
      {
        title: '文件名称',
        dataIndex: 'file_path',
        key: 'file_path',
        slot: true,
        width: 400
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        slot: true,
        width: 200
      },

      {
        title: '操作人',
        dataIndex: 'user_name',
        key: 'user_name',
        width: 160
      },
      {
        title: '导出时间',
        dataIndex: 'created_at',
        key: 'created_at',
        slot: true,
        width: 200
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 150,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const typeStatus = (type: any) => {
    let status = {
      wx_customer_export: '客户明细',
      wx_work_link: '获客链接',
      invest_report_cory_latitude: '企微报表'
    }
    return (status as any)[type]
  }

  const orderStatus = (type: any) => {
    let status = {
      1: {
        color: '#118BCE',
        text: '导出中'
      },
      2: {
        color: '#E77316',
        text: '导出中'
      },
      3: {
        color: '#404040',
        text: '导出成功'
      },
      4: {
        color: '#60A13B',
        text: '已下载'
      },
      5: {
        color: '#E63030',
        text: '导出失败'
      }
    }
    return (status as any)[type]
  }
  return { tableConfigOptions, typeStatus, orderStatus, setOpen, openDownLoadModal }
}
