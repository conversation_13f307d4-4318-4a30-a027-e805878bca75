<template>
  <div>
    <a-tooltip
      :overlayInnerStyle="{
        padding: '8px !important',
        marginTop: '-4px',
        background: 'rgba(0,0,0,0.55)'
      }"
      :arrow="false"
    >
      <template #title>下载中心</template>
      <div @click="setOpen()" class="btn">
        <img :src="requireImg('download.png')" />
      </div>
    </a-tooltip>

    <template v-if="openDownLoadModal">
      <a-modal
        v-model:open="openDownLoadModal"
        title="下载中心"
        :width="1000"
        :footer="null"
        destroyOnClose
        centered
        @cancel="setOpen(false)"
      >
        <div class="download-wrapper">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'index'">
                {{ scope.record.index }}
              </template>
              <template v-if="scope.column.key === 'type'">
                {{ typeStatus(scope.record.type) }}
              </template>
              <template v-if="scope.column.key === 'file_path'">
                <div class="handle_btns min-w-400px">
                  <a-button
                    class="p-0! h-auto"
                    :disabled="scope.record.status == 4"
                    type="link"
                    @click="edit(scope.record, 4)"
                  >
                    {{ scope.record.file_name }}
                  </a-button>
                </div>
              </template>
              <template v-if="scope.column.key === 'status'">
                <div class="flex_align_center">
                  <div :class="downloadStatusEnum[scope.record.status]?.className || ''">
                    {{ downloadStatusEnum[scope.record.status]?.text }}
                  </div>
                </div>
              </template>
              <template v-if="scope.column.key === 'created_at'">
                {{ formatDate(scope.record.created_at * 1000, 'YYYY-MM-DD HH:mm:ss') }}
              </template>
              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <!-- <span style="margin-right: 10px">下载</span> -->
                    <span v-if="scope.record.file_path && scope.record.status != 2" @click="edit(scope.record, 4)">
                      <span v-if="!scope.record.downloadLoading">下载</span>
                      <loading-outlined v-else />
                    </span>
                    <a-popconfirm title="确定进行此操作吗?" placement="topRight" @confirm="edit(scope.record, -1)">
                      <span>删除</span>
                    </a-popconfirm>
                  </div>
                </div>
                <!-- <div v-else>—</div> -->
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </a-modal>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed } from 'vue'
  import { message } from 'ant-design-vue'
  import { downloadFileByPath, formatDate, requireImg, downloadStatusEnum } from '@/utils'
  import datas from './data'
  import { LoadingOutlined } from '@ant-design/icons-vue'
  import { exportdataList, exportdataEdit } from '@/api/common'
  // 搜索配置
  const { tableConfigOptions, typeStatus, orderStatus, setOpen, openDownLoadModal } = datas()

  const data = reactive({
    visible: false,
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 10
    },
    exportTimer: null as null | ReturnType<typeof setTimeout>
  })

  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await exportdataList(data.params)
      data.tableConfigOptions.dataSource =
        res.data.list.map((it: any, index: number) => {
          return {
            ...it,
            index: index + 1
          }
        }) || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = res.data?.page || 1
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }
  // getList()

  const pageChange = (pagination: any) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  const cancel = () => {
    console.log('-=====-=-=-=-')
    setOpen(false)
  }
  const edit = async (row: any, type: String | Number) => {
    try {
      if (type == 4) {
        row.downloadLoading = true
        await downloadFileByPath(row.file_path, row.file_name)
      }
      if (type == 4 && row.status == 3) {
        await exportdataEdit({ id: row.id, status: type })
      }
      if (type != 4) {
        let res: any = await exportdataEdit({ id: row.id, status: type })
        message.success(res.msg)
      }
      setTimeout(() => {
        getList()
      }, 100)
    } catch (error) {
      console.error(error)
      if (type == 4) {
        message.warning('文件下载失败，或已过期')
      }
    } finally {
      row.downloadLoading = false
    }
  }
  // 标记下载列表中是否含有 待导出/导出中 的文件，如果有则定时刷新列表
  const refreshFlag = computed(() => {
    return (
      data.tableConfigOptions.dataSource.length &&
      data.tableConfigOptions.dataSource.some((v: { status?: number }) => v.status == 1 || v.status == 2)
    )
  })
  watch(refreshFlag, (newValue) => {
    console.log('------', newValue)
    if (newValue) {
      data.exportTimer = setInterval(() => {
        getList()
      }, 3000)
    } else {
      if (data.exportTimer) {
        clearInterval(data.exportTimer)
      }
    }
  })
  watch(
    () => openDownLoadModal.value,
    (newValue) => {
      if (newValue) {
        getList()
      } else {
        if (refreshFlag.value && data.exportTimer) clearInterval(data.exportTimer)
      }
    }
  )
</script>

<style lang="scss" scoped>
  .btn {
    height: 32px;
    width: 32px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    img {
      width: 16px;
      height: 16px;
    }
    &:hover {
      background: #fff;
    }
  }
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    margin-right: 5px;
  }
  .file_name {
    word-break: break-all;
    color: var(--primary-color);
  }
  .page_main_pagination {
    margin-top: 30px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
</style>
