<template>
  <div class="watermark-container" ref="container">
    <slot />
    <div class="watermark-mask" ref="mask" v-show="!disabled" />
  </div>
</template>

<script setup>
  import { ref, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'

  const props = defineProps({
    // 水印配置项
    items: {
      type: Array,
      default: () => [
        {
          type: 'text',
          content: '斗量智投',
          font: 'bold 21px Microsoft YaHei',
          color: 'rgba(0, 0, 0, 0.07)'
        }
      ]
    },

    // 水印整体配置
    width: {
      type: Number,
      default: 240
    },
    height: {
      type: Number,
      default: 100
    },
    rotate: {
      type: Number,
      default: -15
    },
    fullscreen: {
      type: Boolean,
      default: true
    },
    gap: {
      type: Number,
      default: 50
    },
    fixed: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },

    // 布局配置
    spacing: {
      type: Number,
      default: 15 // 元素之间的间距
    },
    padding: {
      type: Number,
      default: 10 // 边缘间距
    },
    vertical: {
      type: Boolean,
      default: false // 是否垂直排列
    }
  })

  const container = ref(null)
  const mask = ref(null)
  const canvas = ref(null)
  const ctx = ref(null)
  const watermarkUrl = ref('')
  const imageLoadedCount = ref(0)
  const totalImages = ref(0)

  // 初始化
  onMounted(() => {
    countImages()
    createWatermark()

    if (props.fullscreen) {
      updateFullscreenSize()
      window.addEventListener('resize', updateFullscreenSize)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateFullscreenSize)
  })

  // 监听属性变化
  watch(
    () => props.items,
    (newVal) => {
      countImages()
      createWatermark()
    },
    { deep: true }
  )

  watch(
    [
      () => props.width,
      () => props.height,
      () => props.rotate,
      () => props.disabled,
      () => props.spacing,
      () => props.padding,
      () => props.vertical
    ],
    () => {
      if (props.disabled) {
        applyWatermark()
      } else {
        createWatermark()
      }
    }
  )

  // 统计图片数量
  const countImages = () => {
    totalImages.value = props.items.filter((item) => item.type === 'image').length
    imageLoadedCount.value = 0
  }

  // 创建水印
  const createWatermark = () => {
    // 创建Canvas
    if (!canvas.value) {
      canvas.value = document.createElement('canvas')
      ctx.value = canvas.value.getContext('2d')
    }

    // 设置Canvas尺寸
    canvas.value.width = props.width
    canvas.value.height = props.height

    // 清空Canvas
    ctx.value.clearRect(0, 0, props.width, props.height)

    // 应用旋转
    ctx.value.translate(props.width / 2, props.height / 2)
    ctx.value.rotate((props.rotate * Math.PI) / 180)
    ctx.value.translate(-props.width / 2, -props.height / 2)

    // 计算布局
    const layoutInfo = calculateLayout()

    // 按布局绘制元素
    if (props.vertical) {
      drawVerticalItems(layoutInfo)
    } else {
      drawHorizontalItems(layoutInfo)
    }

    // 生成水印URL
    generateWatermarkUrl()
  }

  // 计算水平布局信息
  const calculateLayout = () => {
    const itemsInfo = []
    let totalWidth = 0
    let maxHeight = 0

    // 计算每个元素的尺寸
    props.items.forEach((item) => {
      if (item.type === 'text') {
        ctx.value.font = item.font || '16px Microsoft YaHei'
        const metrics = ctx.value.measureText(item.content)
        const width = metrics.width
        const height = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent

        itemsInfo.push({
          type: 'text',
          width,
          height,
          item
        })

        totalWidth += width
        maxHeight = Math.max(maxHeight, height)
      } else if (item.type === 'image') {
        const width = item.width || 40
        const height = item.height || 40

        itemsInfo.push({
          type: 'image',
          width,
          height,
          item
        })

        totalWidth += width
        maxHeight = Math.max(maxHeight, height)
      }
    })

    // 计算总间距
    const totalSpacing = (props.items.length - 1) * props.spacing
    totalWidth += totalSpacing

    // 计算起始位置（居中）
    const startX = (props.width - totalWidth) / 2
    const startY = (props.height - maxHeight) / 2

    // 添加位置信息
    let currentX = startX
    itemsInfo.forEach((info) => {
      info.x = currentX
      info.y = startY + (maxHeight - info.height) / 2 // 垂直居中
      currentX += info.width + props.spacing
    })

    return {
      itemsInfo,
      totalWidth,
      maxHeight
    }
  }

  // 水平绘制元素
  const drawHorizontalItems = (layoutInfo) => {
    const { itemsInfo } = layoutInfo

    // 分离图片和文字项
    const textItems = itemsInfo.filter((info) => info.type === 'text')
    const imageItems = itemsInfo.filter((info) => info.type === 'image')

    // 先绘制文字
    textItems.forEach((info) => {
      const { item, x, y, width, height } = info
      ctx.value.font = item.font || '16px Microsoft YaHei'
      ctx.value.fillStyle = item.color || 'rgba(0, 0, 0, 0.15)'
      ctx.value.textAlign = 'left'
      ctx.value.textBaseline = 'top'
      ctx.value.fillText(item.content, x, y)
    })

    // 如果有图片，加载并绘制
    if (imageItems.length > 0) {
      loadImages(imageItems)
    }
  }

  // 垂直绘制元素
  const drawVerticalItems = (layoutInfo) => {
    // 垂直布局实现
    // ... （类似水平布局的实现逻辑，只是方向不同）
    console.log('垂直布局暂未实现，使用水平布局替代')
    drawHorizontalItems(layoutInfo)
  }

  // 加载图片
  const loadImages = (imageItems) => {
    let loaded = 0

    imageItems.forEach((info) => {
      const { item, x, y } = info
      const img = new Image()
      img.crossOrigin = 'anonymous'

      img.onload = () => {
        drawImage(item, img, x, y)
        loaded++

        // 所有图片加载完成后生成水印
        if (loaded === imageItems.length) {
          generateWatermarkUrl()
        }
      }

      img.onerror = () => {
        console.error('Failed to load image:', item.src)
        loaded++

        // 即使有图片加载失败，也继续生成水印
        if (loaded === imageItems.length) {
          generateWatermarkUrl()
        }
      }

      img.src = item.src
    })
  }

  // 绘制图片
  const drawImage = (item, img, x, y) => {
    const width = item.width || img.width
    const height = item.height || img.height

    ctx.value.globalAlpha = item.opacity !== undefined ? item.opacity : 0.2
    ctx.value.drawImage(img, x, y, width, height)
    ctx.value.globalAlpha = 1
  }

  // 生成水印URL并应用
  const generateWatermarkUrl = () => {
    watermarkUrl.value = canvas.value.toDataURL('image/png')
    applyWatermark()
  }

  // 应用水印到DOM
  const applyWatermark = () => {
    if (!mask.value || props.disabled) return

    mask.value.style.pointerEvents = 'none'
    mask.value.style.position = props.fixed ? 'fixed' : 'absolute'
    mask.value.style.left = '0'
    mask.value.style.top = '0'
    mask.value.style.width = '100%'
    mask.value.style.height = '100%'
    mask.value.style.zIndex = '9999'
    mask.value.style.backgroundImage = `url(${watermarkUrl.value})`
    mask.value.style.backgroundRepeat = 'repeat'
    mask.value.style.backgroundSize = `${props.width + props.gap}px ${props.height + props.gap}px`
  }

  // 更新全屏水印尺寸
  const updateFullscreenSize = () => {
    if (!mask.value) return

    mask.value.style.width = `${window.innerWidth}px`
    mask.value.style.height = `${window.innerHeight}px`
  }
</script>

<style scoped>
  .watermark-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .watermark-mask {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
  }
</style>
