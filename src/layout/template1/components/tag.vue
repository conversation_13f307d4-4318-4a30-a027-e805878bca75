<template>
  <div class="tag-content">
    <div class="flex justify-between items-center tag">
      <div class="flex justify-between items-center flex-1">
        <div class="flex flex-items-center">
          <WarningFilled class="c-#fe0301" />
          <div class="desc ml-8px">
            <span>{{ tagData.alert }}</span>
          </div>
          <div
            v-if="breadcrumbsData.findIndex((v) => v.name == 'WalletOverview') == -1"
            class="recharge decoration-underline"
            @click="goPage"
          >
            去充值
          </div>
        </div>
      </div>
      <!-- <CloseOutlined
        class="font-size-16px cursor-pointer c-#999 mr-24px"
        v-if="tagData.balance > 0"
        @click="emit('close')"
      /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
  import { WarningFilled, CloseOutlined } from '@ant-design/icons-vue'
  import { useRouter } from 'vue-router'
  import { useTheme, useMenu } from '@/hooks'

  const props = defineProps(['tagData'])
  const emit = defineEmits(['close'])
  const { themeVar } = useTheme()
  const router = useRouter()
  const { breadcrumbsData } = useMenu()
  const goPage = () => {
    router.push({
      name: 'WalletOverview'
    })
  }
</script>
<style lang="scss" scoped>
  .tag-content {
    padding-top: 24px;
    // padding-left: 10px;
    padding-right: 24px;
    line-height: 36px;
    background-color: v-bind('themeVar.mainBgColor || themeVar.layoutBgColor');
  }
  .tag {
    padding-left: 16px;
    border-radius: 5px;
    background: #fff0f0;
    border: 1px solid #ffccc7;
  }
  .desc {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #141414;
  }
  .recharge {
    color: #1677ff;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;

    cursor: pointer;
  }
</style>
