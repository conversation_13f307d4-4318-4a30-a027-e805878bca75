<template>
  <div>
    <a-popover :trigger="['hover']">
      <div class="pt-2 pb-2 point_box flex flex-items-center cursor-pointer" @click="goWalletOverview">
        <img src="@/assets/images/points/wallet.png" alt="" class="w-14px h-14px mr-6px" />
        <span class="color-#1B2FFF">{{ pointData?.balance }}</span>
      </div>
      <template #content>
        <div class="w-152px mr-20px">
          <span class="color-#747474">积分余额(个)</span>
          <div class="mt-6px fw-bold text-24px mb-10px amount">{{ pointData?.balance }}</div>
          <div class="flex flex-items-center flex-justify-between cursor-pointer">
            <span class="color-#1677FF" @click="goWalletOverview">点击查看积分</span
            ><img src="@/assets/images/points/arrow_right.png" alt="" class="w-5px h-10px" />
          </div>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'Points' })

  import { reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePoints } from '@/hooks'
  const { getPointBalance, pointData } = usePoints()
  const router = useRouter()
  getPointBalance()
  const goWalletOverview = () => {
    router.push({
      name: 'WalletOverview'
    })
  }
</script>
<style lang="scss" scoped>
  .point_box {
    height: 24px;
    background: #e2e8ff;
    border-radius: 4px;
    border: 1px solid #bdccff;
    padding: 5px 8px;
  }
  :deep(.ant-popover-placement-bottom .ant-popover-content .ant-popover-arrow) {
    display: none;
  }
  .amount {
    border-bottom: 1px dashed #e2e2e2;
  }
</style>
