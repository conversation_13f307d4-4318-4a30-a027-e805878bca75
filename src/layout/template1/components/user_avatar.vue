<template>
  <div>
    <a-dropdown :trigger="['click']">
      <div class="pt-2px pb-2px">
        <div class="user flex flex-items-center" tabindex="0">
          <div class="name text_overflow">
            {{ localStg.get('loginType') == 1 ? useInfo?.phone : useInfo?.realname }}
          </div>
          <SvgIcon icon="user_down" class="m-l-4px"></SvgIcon>
        </div>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item key="0">
            <div class="flex-y-center flex-col" @click.stop="">
              <div>{{ localStg.get('loginType') == 1 ? useInfo?.phone : useInfo?.realname }}</div>
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="1" v-if="localStg.get('loginType') == 2">
            <div class="flex-x-center" @click="editPassword">
              <FormOutlined class="m-r-5px" />
              <span>修改密码</span>
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="4" @click="logout">
            <div class="flex-x-center">
              <LoginOutlined class="m-r-5px" />
              <span>退出登录</span>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-modal v-model:open="dialog.visible" title="修改密码" :footer="null">
      <EditPassWord :size="1" v-if="dialog.visible" type="image" @event="onEvent" />
    </a-modal>
    <a-modal
      v-model:open="dialog.dialogValid.visible"
      :footer="null"
      :closable="false"
      :maskClosable="false"
      :keyboard="false"
      v-if="localStg.get('loginType') == 2"
    >
      <template #title>
        <span class="el-dialog__title"
          >尊敬的<span class="reset_password_title">{{ localStg.get('userInfo').realname }}</span
          >用户</span
        >
      </template>
      <ValidNewAccount :size="1" v-if="dialog.dialogValid.visible" type="image" @event="onValidEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'UserAvatar' })
  import { FormOutlined, LoginOutlined } from '@ant-design/icons-vue'
  import { notification } from 'ant-design-vue'
  import EditPassWord from '@/layout/template2/components/EditPassWord.vue'
  import ValidNewAccount from '@/layout/template2/components/ValidNewAccount.vue'
  import { reactive, h, onBeforeUnmount } from 'vue'
  // import { fetchProductList } from '@/views/cid/goods/taobao/index.api'
  import { localStg } from '@/utils'
  import { useApp, useLogin } from '@/hooks'
  import { SvgIcon } from '@/components'
  const dialog = reactive({
    visible: false,
    dialogValid: {
      viseble: false
    },
    info: {
      viseble: false
    }
  })

  const { logout } = useLogin()
  const { useInfo } = useApp()

  const editPassword = () => {
    dialog.visible = true
  }
  const onEvent = (type) => {
    if (type.cmd === 'close') {
      dialog.visible = false
    } else if (type.cmd === 'edit') {
    }
  }
  dialog.dialogValid.visible = localStg.get('userInfo').reset_password
  const onValidEvent = () => {
    dialog.visible = true
  }

  onBeforeUnmount(() => {
    notification.destroy()
  })
</script>
<style lang="scss" scoped>
  .user {
    padding: 6px 8px;
    position: relative;
    .name {
      cursor: pointer;
      user-select: none;
      color: #747474;
      font-size: 14px;
      max-width: 150px;
    }
    &:hover {
      background: #fff;
      border-radius: 6px;
    }
  }
</style>
