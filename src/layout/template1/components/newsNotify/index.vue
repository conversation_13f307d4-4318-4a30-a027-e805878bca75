<template>
  <div>
    <a-tooltip
      :overlayInnerStyle="{
        padding: '8px !important',
        marginTop: '-4px',
        background: 'rgba(0,0,0,0.55)'
      }"
      :arrow="false"
    >
      <template #title>消息中心</template>

      <div @click="setOpen()" class="btn">
        <a-badge :count="state.statsNum">
          <img :src="requireImg('news.png')" />
        </a-badge>
      </div>
    </a-tooltip>

    <template v-if="openDownLoadModal">
      <a-drawer
        v-model:open="openDownLoadModal"
        title="消息中心"
        :width="isMobile ? '100%' : 700"
        :footer="null"
        destroyOnClose
        placement="right"
        @close="setOpen(false)"
      >
        <div class="download-wrapper pl-24px pr-24px">
          <div class="flex justify-between items-center">
            <a-tabs class="flex-1" v-model:activeKey="state.activeKey" @change="handleChange">
              <a-tab-pane key="1">
                <template #tab>
                  <a-badge :count="state.alertsNum">
                    <div class="w-70px tab-text" :class="{ 'tab-active': state.activeKey == '1' }">消息提醒</div>
                  </a-badge>
                </template>
              </a-tab-pane>
              <a-tab-pane key="2">
                <template #tab>
                  <a-badge :count="state.noticesNum">
                    <div class="w-70px tab-text" :class="{ 'tab-active': state.activeKey == '2' }">更新通知</div>
                  </a-badge>
                </template>
              </a-tab-pane>
            </a-tabs>
            <div class="flex items-center cursor-pointer mb-16px" @click="changeReadStatus">
              <img
                style="width: 16px; height: 16px"
                :src="state.statsNum == 0 ? requireImg('news_icon_check.png') : requireImg('news_icon.png')"
                alt=""
              />
              <div class="ml-6px read_status" :class="{ all_read: state.statsNum == 0 }">全部已读</div>
            </div>
          </div>
          <div class="flex justify-center" v-if="state.loading">
            <a-spin />
          </div>
          <div v-else>
            <div class="flex mb-16px" v-if="state.activeKey == '1'">
              <div
                class="ad-item mr-8px flex justify-center"
                :class="{ active: state.curIndex == i }"
                @click="changeItem(item, i)"
                v-for="(item, i) in state.tabList"
                :key="i"
              >
                {{ item.name }}
              </div>
            </div>
            <div
              class="ad-content"
              :class="{ 'update-content': state.activeKey == '2' }"
              v-if="state.newsList.length > 0"
            >
              <div
                class="news_item mb-16px cursor-pointer"
                :class="{ news_phone: isMobile }"
                v-for="(item, i) in state.newsList"
                :key="i"
                @click="clickItem(item, i)"
              >
                <div class="flex items-center justify-between mb-10px" :class="{ 'phone-style': isMobile }">
                  <div class="flex items-center">
                    <div class="mark mr-4px" v-if="item.is_read == 1"></div>
                    <img
                      class="mr-8px"
                      style="width: 10px; height: 18px"
                      v-if="item.category == 2"
                      :src="requireImg('rocket.png')"
                      alt=""
                    />
                    <div class="ad-name mr-8px">{{ item.title }}</div>
                  </div>

                  <div class="c-#8F97A6 font-size-12px text-right">{{ item.created_at }}</div>
                </div>
                <div class="flex justify-between items-end" v-if="item.category == 1">
                  <div class="ad-desc">{{ item.content }}</div>
                  <a-button type="link" class="p0! ml-10px" size="small" @click="lookDetail(item)">点击查看>></a-button>
                </div>
                <!-- v-if="item.content.length > 32" -->
                <div class="flex justify-between items-start" v-if="item.category == 2">
                  <div
                    class="ad-desc flex-1 mr-10px"
                    :class="{ text_overflow_row1: !item.isExpand }"
                    v-html="item.content"
                    ref="adDesc"
                  ></div>

                  <div class="w-44px">
                    <div class="flex items-center" v-if="item.isShow">
                      <a-button type="link" class="p0!" size="small">{{ item.isExpand ? '收起' : '展开' }}</a-button>
                      <img class="ml-4px arrow" :class="{ up: item.isExpand }" :src="requireImg('arrow.png')" alt="" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="flex flex_column justify-center items-center mt-120px ad-content">
              <img style="width: 129px; height: 80px; opacity: 0.6" :src="requireImg('empty.png')" alt="" />
              <div class="c-#cdcdcd font-size-14px mt-20px">暂无数据</div>
            </div>
            <div class="flex justify-end pt-16px" v-if="state.newsList.length > 0">
              <a-pagination
                size="small"
                :show-total="(total: number) => `共 ${total} 条`"
                show-size-changer
                show-quick-jumper
                v-model:page="state.params.page"
                v-model:pageSize="state.params.page_size"
                :total="state.total"
                @change="pageChange"
              />
            </div>
          </div>
        </div>
      </a-drawer>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, watch, onMounted, nextTick } from 'vue'
  import { requireImg } from '@/utils'
  import { statsApi, messagegListApi, messagegReadApi, readAllApi } from '@/api/common'
  import { useRouter } from 'vue-router'
  import { useApp, useLogin, useNewsNotify } from '@/hooks'

  // 定义消息项的类型
  interface NewsItem {
    id: number
    title: string
    content: string
    created_at: string
    category: number
    is_read: number
    sub_type?: number
    related_data?: any
    isExpand?: boolean
    isShow?: boolean
  }

  const { isMobile } = useApp()
  const { isLogin } = useLogin()
  const { hasUnreadNotices, latestUnreadNotice, checkUnreadNotices } = useNewsNotify()
  const router = useRouter()
  let openDownLoadModal = ref(false)
  let isAutoExpanding = ref(false) // 添加标志来区分是否自动展开
  const state = reactive({
    activeKey: '1',
    curIndex: 0,
    curValue: '',
    visible: false,
    loading: false,
    total: 50,
    statsNum: 0,
    alertsNum: 0,
    noticesNum: 0,
    params: {
      page: 1,
      page_size: 20
    },
    tabList: [
      { name: '全部', value: '0' },
      { name: '广告账户未授权', value: '1' },
      { name: '获客余量不足', value: '2' },
      { name: '计划定向', value: '3' }
    ],
    newsList: [] as NewsItem[]
  })
  const adDesc = ref(null)

  // 自动展开未读更新通知
  const autoExpandUnreadNotices = async () => {
    if (hasUnreadNotices.value && latestUnreadNotice.value) {
      // 设置自动展开标志
      isAutoExpanding.value = true

      // 切换到更新通知tab
      state.activeKey = '2'
      state.curIndex = 0
      state.curValue = ''
      state.params.page = 1
      state.params.page_size = 20

      // 打开消息中心
      openDownLoadModal.value = true

      // 等待一下确保状态更新
      await nextTick()

      // 获取更新通知列表
      await getNewList()

      // 等待数据加载完成
      await nextTick()
      // 只检查第一条消息是否是未读的更新通知
      if (state.newsList.length > 0) {
        const firstItem = state.newsList[0]
        if (firstItem.is_read == 1 && firstItem.category == 2) {
          // 展开第一条消息
          if (state.newsList[0].isShow) {
            state.newsList[0].isShow = true
            state.newsList[0].isExpand = true
          }
          state.newsList[0].is_read = 2
          await messagegReadApi({ msg_id: state.newsList[0].id })
          getList()
        }
      }

      // // 找到最新的一条未读更新通知并展开
      // const unreadNoticesList = state.newsList.filter((item: NewsItem) => item.is_read == 1 && item.category == 2)
      // if (unreadNoticesList.length > 0) {
      //   // 按创建时间排序，找到最新的一条
      //   const latestUnread = unreadNoticesList.sort(
      //     (a: NewsItem, b: NewsItem) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      //   )[0]

      //   // 找到这条消息在列表中的索引
      //   const index = state.newsList.findIndex((item: NewsItem) => item.id === latestUnread.id)
      //   if (index !== -1) {
      //     // 展开这条消息
      //     state.newsList[index].isExpand = true
      //     // updateStatus(state.newsList[index], index)
      //   }
      // }

      // 重置自动展开标志
      setTimeout(() => {
        isAutoExpanding.value = false
      }, 1000)
    }
  }

  const setOpen = (val: boolean = true) => {
    openDownLoadModal.value = val
    getList()
  }
  const clickItem = (item: NewsItem, i?: number) => {
    if (item.category == 1) {
      return
    }
    updateStatus(item, i)
  }
  //单条已读
  const updateStatus = async (item: NewsItem, i?: number) => {
    try {
      if (item.category == 2) {
        expand(item, i)
      }
      if (item.is_read == 2) return
      await messagegReadApi({ msg_id: item.id })
      getList()
      item.is_read = 2
    } catch (error) {}
  }
  //查看详情
  const lookDetail = (item: NewsItem) => {
    updateStatus(item)
    setOpen(false)
    if (item.sub_type == 2) {
      router.replace({ name: 'EnterpriseWechatManagement', query: { corp_id: item?.related_data?.corp_id } })
    }
    if (item.sub_type == 1) {
      router.push({ name: 'oceanEngine' })
    }
  }
  //  全部已读
  const changeReadStatus = async () => {
    try {
      await readAllApi({})
      init()
    } catch (error) {}
  }
  //切换tab
  const changeItem = (item: any, i: number) => {
    state.curIndex = i
    state.curValue = item.value
    state.params.page = 1
    state.params.page_size = 20
    init()
  }
  //展开/收起
  const expand = (item: NewsItem, i?: number) => {
    if (i !== undefined && state.newsList[i]) {
      state.newsList[i].isExpand = !item.isExpand
    }
  }
  // 获取列表
  const getList = async () => {
    try {
      let res = await statsApi({})
      state.alertsNum = res.data.unread_alerts || 0
      state.noticesNum = res.data.unread_notices || 0
      state.statsNum = state.alertsNum + state.noticesNum
    } catch (error) {
      console.log(error)
    }
  }
  getList()
  const getNewList = async () => {
    try {
      let params = {
        ...state.params,
        category: state.activeKey,
        sub_type: state.curValue
      }
      let res = await messagegListApi(params)
      let result = res.data.list || []
      state.total = res.data.total_num || 0
      state.newsList = result.map((v: any) => {
        return {
          ...v,
          isExpand: false
        }
      })

      if (state.activeKey == '2') {
        nextTick(() => {
          state.newsList.forEach((item: NewsItem, index: number) => {
            const adDescElement = document.querySelectorAll('.ad-desc')[index]
            item.isShow = adDescElement && adDescElement.scrollHeight > adDescElement.clientHeight
          })
        })
      }
    } catch (error) {
      console.log(error)
    } finally {
      state.loading = false
    }
  }
  const pageChange = (page: number, pageSize: number) => {
    state.params.page = page
    state.params.page_size = pageSize
    init()
  }
  const init = () => {
    getList()
    getNewList()
  }

  // 监听未读更新通知状态变化
  watch(
    () => hasUnreadNotices.value,
    (newValue) => {
      if (newValue) {
        // 延迟执行，确保组件完全加载
        setTimeout(() => {
          autoExpandUnreadNotices()
        }, 500)
      }
    },
    { immediate: true }
  )

  // 组件挂载时检查未读更新通知
  onMounted(() => {
    if (isLogin.value) {
      setTimeout(() => {
        checkUnreadNotices()
      }, 100)
    }
  })

  watch(
    () => openDownLoadModal.value,
    (newValue) => {
      if (newValue) {
        // 只有在手动打开且不是自动展开时才重置为消息提醒tab
        if (!hasUnreadNotices.value && !isAutoExpanding.value) {
          state.activeKey = '1'
          state.curIndex = 0
          state.curValue = ''
          state.params.page = 1
          state.params.page_size = 20
          init()
        }
      }
    }
  )
  const handleChange = () => {
    state.loading = true
    if (state.activeKey == '1') {
      state.curIndex = 0
    }
    state.curValue = ''
    state.params.page = 1
    state.params.page_size = 20
    init()
    if (state.activeKey == '2') {
      setTimeout(() => {
        state.newsList.forEach((item: NewsItem, index: number) => {
          const adDescElement = document.querySelectorAll('.ad-desc')[index]
          item.isShow = adDescElement && adDescElement.scrollHeight > adDescElement.clientHeight
        })
      }, 500)
    }
  }
</script>

<style lang="scss" scoped>
  .phone-style {
    align-items: flex-start;
  }
  .ad-content {
    max-height: calc(100vh - 230px);
    overflow-y: auto;
  }
  .update-content {
    max-height: calc(100vh - 184px);
  }
  .ad-item {
    background: #f3f3f3;
    border-radius: 4px;
    padding: 5px 10px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.54);
    min-width: 60px;
    box-sizing: border-box;
    cursor: pointer;
  }
  .active {
    background: #ffeddb;
    color: #fe9d35;
  }
  .read_status {
    color: rgba(0, 0, 0, 0.66);
  }
  .all_read {
    color: #ffd7ac;
  }
  .news_item {
    background: #f7f9fc;
    border-radius: 8px;
    padding: 16px;
    box-sizing: border-box;
    .mark {
      width: 6px;
      height: 6px;
      background: #ff3d3d;
      border-radius: 50%;
    }
  }
  .news_item:last-child {
    margin-bottom: 0;
  }
  .news_phone {
    padding: 10px;
  }
  .ad-name {
    font-weight: bold;
    font-size: 16px;
    color: #313233;
    min-width: 162px;
  }
  .ad-desc {
    font-size: 14px;
    color: #313233;
    min-height: 24px;
    word-break: break-all;
  }
  .arrow {
    width: 10px;
    height: 10px;
  }
  .up {
    transform: rotate(180deg);
  }
  .btn {
    height: 32px;
    width: 32px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    img {
      width: 16px;
      height: 16px;
    }
    &:hover {
      background: #fff;
    }
  }
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    margin-right: 5px;
  }
  .file_name {
    word-break: break-all;
    color: var(--primary-color);
  }
  .page_main_pagination {
    margin-top: 30px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  :deep(.ant-tabs-top > .ant-tabs-nav::before) {
    border: none;
  }
  .tab-text {
    color: #5b5b5b;
    font-size: 16px;
  }
  .tab-active {
    color: #313233;
  }
  :deep(.ant-tabs) {
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #313233;
      text-shadow: none;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin: 0 0 0 34px;
    }
  }

  // :deep(.ant-badge) {
  //   color: #313233;
  //   font-size: 16px;
  // }
  :deep(.ant-badge .ant-badge-count) {
    min-width: 15px;
    height: 15px;
    line-height: 13px;
    padding: 0 4px;
    font-size: 12px;
    .ant-scroll-number .ant-scroll-number-only > p.ant-scroll-number-only-unit {
      height: 15px;
    }
  }
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5) {
    font-size: revert;
    font-weight: revert;
  }
</style>
