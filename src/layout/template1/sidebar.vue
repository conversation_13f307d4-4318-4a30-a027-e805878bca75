<template>
  <div class="sidebar common_scroll_y_hover">
    <a-collapse
      v-model:activeKey="openKeys"
      expand-icon-position="end"
      ghost
      :expandIcon="() => h(SvgIcon, { icon: 'b_arrow', class: ['right_arrow', 'c-#9C9CA9!'] })"
    >
      <a-collapse-panel
        class="collapse_panel"
        v-for="item in isMobile ? menuData : routerSublevel"
        :key="item.name"
        :class="{ item_active: expandedKeys.includes(item.name) }"
      >
        <template #header>
          <div class="flex items-center p-l-10px">
            <component class="text-16px! icon" :is="renderIcon(item.icon)"></component>
            <span class="m-l-8px"> {{ item.title }} </span>
          </div>
        </template>

        <template #default>
          <div class="flex flex-wrap p-l-34px" :class="{ 'flex-col': item.name == 'SystemManage' }">
            <div
              v-for="(c, i) in item.children"
              :key="c.name"
              class="item_childer min-w-62px c-#656D7D p-t-8px p-b-8px"
              :class="{
                'm-r-18px': !(i % 2),
                'c-primary': expandedKeys.includes(c.name)
              }"
              @click="onRouter(c)"
            >
              <a :href="`/#${c.path}`" onclick="return false;" class="flex items-center">
                <span class="vertical_lint bg-primary" :class="{ active: expandedKeys.includes(c.name) }"></span>
                <component
                  style="display: flex"
                  :is="
                    renderTitle(c.meta.titleComponent ? returnTitleComponent(c.title, c.meta.titleComponent) : c.title)
                  "
                ></component>
              </a>
            </div>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'Sidebar' })
  import { SvgIcon } from '@/components'
  import { type Component, VNode, h, ref, computed } from 'vue'
  import { useApp, useMenu, useRouter } from '@/hooks'
  import { MenuData } from 'types/router'
  import { isFunction, isString } from 'lodash-es'
  const { routerSublevel, menuData, breadcrumbsData } = useMenu()

  const { routerPush } = useRouter()
  const { isMobile } = useApp()

  const expandedKeys = computed(() => {
    return breadcrumbsData.value.map((v) => v.name)
  })

  // 获取所有子级在 一级侧边栏目在顶部的时候需要
  let allChildren: MenuData[] = []
  menuData.value.forEach((v) => allChildren.push(...(v.children || [])))

  // 当前打开的子级
  const openKeys = ref(allChildren.map((v) => v.name))

  const onRouter = (item: MenuData) => {
    routerPush({ name: item.name })
  }

  // 选择icon
  const renderIcon = (icon: string | Component): Component => {
    if (isString(icon)) {
      return h(SvgIcon, { icon })
    }
    return icon
  }

  // 渲染title
  const renderTitle = (title: string | Component) => {
    if (isString(title)) {
      return h('span', title)
    }
    return title
  }
  // 判断渲染title组件类型
  const returnTitleComponent = (title: string, titleComponent: any): VNode | Array<VNode> => {
    if (isFunction(titleComponent())) {
      return titleComponent(title)
    } else {
      return titleComponent
    }
  }
</script>
<style lang="scss" scoped>
  .sidebar {
    width: 100%;
    max-height: 100%;

    :deep(.right_arrow) {
      display: inline-block;
      transition: all 0.2s;
      transform-origin: center;
      transform: rotateX(180deg);
    }
    :deep(.ant-collapse-item) {
      &.ant-collapse-item-active {
        .right_arrow {
          transform: rotateX(0deg);
        }
      }
    }
    .item_active {
      .icon {
        color: var(--primary-color);
      }
    }
    .collapse_panel {
      :deep(.ant-collapse-content-box) {
        padding-top: 0px;
      }
    }

    .item_childer {
      cursor: pointer;
      display: flex;
      align-items: center;

      .vertical_lint {
        display: inline-block;
        height: 12px;
        width: 0px;
        margin-right: 0px;
        transition: all 0.2s;

        &.active {
          width: 2px;
          margin-right: 4px;
        }
      }
    }
  }
  :deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
    padding: 8px 16px;
  }
</style>
