<template>
  <a-config-provider :theme="{ token: themeOverrides.common }" :locale="zhCN">
    <!-- <a-watermark
      class="watermark"
      :content="watermark"
      image="https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*lkAoRbywo0oAAAAAAAAAAAAADrJ8AQ/original"
      :rotate="-15"
      :gap="[80, 120]"
      :font="{
        color: 'rgb(128,128,128,.1)',
        fontSize: 20
      }"
    > -->
    <!-- <myWaterMark :items="watermarkItems" :width="400" :height="240" :rotate="-15"> -->
    <LayoutAdmin>
      <template #logo>
        <Logo />
      </template>
      <template #header>
        <Header />
      </template>
      <template #sidebar>
        <component :is="isMobile ? MSidebar : Sidebar" class="m-t-10px"></component>
      </template>
      <NConfigProvider :theme-overrides="themeOverrides">
        <NGlobalStyle></NGlobalStyle>
        <GlobContent></GlobContent>
      </NConfigProvider>
    </LayoutAdmin>
    <!-- </myWaterMark> -->
    <!-- </a-watermark> -->
  </a-config-provider>
</template>

<script setup>
  defineOptions({ name: 'Template1' })
  import { reactive, computed } from 'vue'
  import { useApp, useTheme, usePoints } from '@/hooks'
  import Logo from './logo.vue'
  import Header from './header.vue'
  import Sidebar from './sidebar.vue' //展开侧边栏样式-量多多
  import MSidebar from '../template2/sidebar.vue'
  import LayoutAdmin from '../component/layout-admin/index.vue'
  import { NConfigProvider, NGlobalStyle } from 'naive-ui'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  import GlobContent from '../component/glob-content.vue'
  import { useRoute } from 'vue-router'
  import { locale } from 'dayjs'
  import { getConfig } from '@/utils'
  import myWaterMark from './components/waterMark.vue'
  import waterLogo from '@/assets/images/water_logo2.png'
  import 'dayjs/locale/zh-cn'
  locale('zh-cn')

  const { themeOverrides, mode } = useTheme()
  const { isMobile } = useApp()
  const route = useRoute()
  const { useInfo } = useApp()
  const watermark = computed(() => {
    return useInfo.value?.realname + ' ' + (useInfo.value?.phone || '').substr(-4)
  })
  const watermarkItems = [
    {
      type: 'text',
      content: watermark.value,
      font: 'bold 19px Microsoft YaHei',
      color: 'rgba(0, 0, 0, 0.07)',
      x: 100,
      y: 30
    },
    {
      type: 'image',
      src: waterLogo, // 替换为实际图片URL
      x: 30,
      y: 30,
      width: 105,
      height: 24,
      opacity: 1
    }
  ]
</script>
<style lang="scss" scoped>
  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: all 0.4s ease;
  }

  .slide-up-enter-from,
  .slide-up-leave-to {
    transform: translateY(0);
    opacity: 1;
  }

  .slide-up-leave-to {
    transform: translateY(-100%);
    opacity: 0;
  }
  .watermark {
    height: 100%;
  }
</style>
