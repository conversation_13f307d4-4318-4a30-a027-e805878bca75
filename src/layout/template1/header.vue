<template>
  <div class="flex flex-items-center flex-justify-between header w-100%" :class="isMobile && 'justify-end!'">
    <a-menu
      :selected-keys="breadcrumbsData.map((v) => v.name)"
      mode="horizontal"
      @click="onClick"
      v-if="!isMobile"
      style="min-width: 400px"
    >
      <a-menu-item v-for="v in menuData" :key="v.name" :label="v.title" class="c-#313233 font-500">
        {{ v.title }}
      </a-menu-item>
    </a-menu>
    <div class="flex flex-items-center">
      <NewsNotify />
      <div class="line ml-8px! mr-8px!"></div>
      <Download />
      <div class="line ml-8px!"></div>
      <Point />
      <div class="line"></div>
      <UserAvatar />
    </div>
  </div>
</template>
<script setup lang="ts">
  import UserAvatar from './components/user_avatar.vue'
  import Download from './components/downLoad/index.vue'
  import Point from './components/points.vue'
  import NewsNotify from './components/newsNotify/index.vue'
  import { useMenu, useApp } from '@/hooks'
  import { useRouter } from '@/hooks/use-router'
  import { nextTick } from 'vue'
  const { menuData, breadcrumbsData } = useMenu()
  const { routerPush, isRoute } = useRouter()
  const { isMobile } = useApp()

  const onClick = (obj: any) => {
    nextTick(() => {
      routerPush({ name: obj.key })
    })
  }
</script>
<style lang="scss" scoped>
  .header {
    .icon {
      position: relative;
      text-align: center;
      padding: 0px 10px;
      border-radius: 3px;
      display: inline-block;
      border: 1px solid var(--primary-color);
      font-size: var(--font-size);
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: var(--primary-color);
      &::after {
        position: absolute;
        bottom: -1px;
        right: -1px;
        display: block;
        content: '';
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: rgba(254, 157, 53, 0.16);
      }
    }
    .tips {
      line-height: inherit;
    }

    .line {
      width: 1px;
      height: 20px;
      background: #d9d9d9;
      margin: 0 16px;
    }
  }

  .ant-menu {
    line-height: var(--header-height);
    font-size: 16px;
  }
  .ant-menu-horizontal {
    border-bottom: none;
  }
  :deep(.ant-menu-item) {
    &:hover::after {
      border-bottom-color: transparent;
    }
  }
  .ant-menu-light {
    background: transparent;
  }
  :deep(.ant-menu-light.ant-menu-horizontal > .ant-menu-item-selected) {
    color: var(--primary-color);
  }
  :deep(.ant-menu-light.ant-menu-horizontal > .ant-menu-item-selected::after),
  :deep(.ant-menu-light.ant-menu-horizontal > .ant-menu-item:hover::after) {
    border-bottom-color: var(--primary-color);
    border-radius: 1px;
  }

  .wechat {
    padding: 6px 8px;
    &:hover {
      background: #fff;
      border-radius: 6px;
    }
  }
</style>
