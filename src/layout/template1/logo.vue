<template>
  <div class="logo h-full flex flex-y-center">
    <router-link to="/">
      <template v-if="showMiniLogo">
        <div class="wh-full flex-center"><img class="h-26px" :src="getSystemFile('h5_logo')" alt="" /></div>
      </template>
      <template v-else>
        <!-- 左右布局还是折叠状态的时候显示的 -->
        <div class="logo_box flex-items-start" v-if="!isMobile">
          <img class="img" :src="getSystemFile('logo')" alt="" />
          <div class="logo_text">获客通</div>
        </div>
        <!-- h5显示 -->
        <div class="logo_box" v-else>
          <img class="h-26px" :src="getSystemFile('h5_logo')" alt="" style="width: 24px; height: 26px" />
        </div>
      </template>
    </router-link>
  </div>
</template>
<script setup lang="ts">
  import { useApp, useTheme } from '@/hooks'
  import { getSystemFile } from '@/utils'
  import { computed } from 'vue'
  const { isMobile } = useApp()
  const { mode, isSidebarFold, themeVar } = useTheme()

  const showMiniLogo = computed(() => {
    let show = false
    if (['around', 'minSidebar', 'sidebarFullHeader'].includes(mode.value)) {
      if (!isSidebarFold.value) {
        show = true
      }
    }
    return show
  })

  // logo左右间距
  const logoSpace = computed(() => {
    return isMobile.value ? themeVar.value.paddingMedium : themeVar.value.paddingHuge
  })
</script>
<style lang="scss" scoped>
  .logo {
    margin: 0 v-bind('logoSpace');
    // .img {
    //   max-width: 100px;
    // }
    .img {
      color: var(--primary-color);
      min-width: 119px;
    }
  }
  .logo_box {
    display: flex;
    align-items: flex-end;
    height: 100%;
    .logo_text {
      margin-left: 6px;
      font-size: 12px;
      color: #606060;
      line-height: 28px;
      width: 64px;
      height: 28px;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex: none;
    }
  }
</style>
