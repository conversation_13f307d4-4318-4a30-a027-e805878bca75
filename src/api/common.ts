// 导出请求封装方法
import http from '@/utils/request'

/**
 * 获取用户菜单
 */
export const getRoleMenuList = (params?: any) => {
  return http('get', `/common/role/role_menu`, params)
}

// 获取素材库数据
export const getFileList = (data?: any) => {
  return http('get', `/common/file/list`, data)
}
/**
 * 编辑素材库文件名称
 */
export const updateNameFile = (data?: any) => {
  return http('post', `/common/file/update_name`, data)
}
/**
 * 删除素材库文件
 */
export const deleteFile = (data?: any) => {
  return http('post', `/common/file/delete`, data)
}

// 上传素材库图片
export const fileSave = (data?: any) => {
  return http('post', `/common/file/save`, data)
}
// 素材库文件/修改文件或分组名称
export const fileEdit = (data?: any) => {
  return http('post', `/common/file/edit`, data)
}

/**
 * 重置token
 * @param {String} shop_id 店铺id
 * @returns
 */
export const cutShopUpdateTokenApi = (shop_id: string) => {
  return http('post', `/common/token/reset`, {
    shop_id
  })
}

/**
 * 导出
 * @param {String} params： 搜索条件 转为json字符串
 * @returns
 */
export const exportCreate = (data?: any) => {
  return http('post', `/common/exportdata/create`, data)
}
/**
 * 获取对应导出数据表头
 * @param {String}
 * @returns
 */
export const getExportDataHeader = (data?: any) => {
  return http('post', `/common/exportdata/header`, data)
}

/**
 * 消息已读
 */
export const msgUpdateApi = (data?: any) => {
  return http('post', `/common/msg/update`, data)
}

/**
 * 获取服务数据页面
 */
export const getServiceStatData = () => {
  return http('get', `/admin/shop/service_stat_data`)
}

/**
 * 负责人数据
 */
export const getUserListApi = () => {
  return http('get', `/common/user/user_list`)
}

export const get_area_list = () => {
  return http('get', `/admin/area/list`)
}
/**
 * erp列表
 */
export const authList = (data) => {
  return http('post', `/shop-erp/auth-list`, data)
}

// 账号/发送验证码
export const set_Captcha = (data: any) => {
  return http('post', `/common/sms/code`, data)
}

export const change_phone = (data) => {
  return http('get', `/admin/order/get_phone`, data)
}

/**
 * 获取广点通授权链接
 */
export const setAuthUrl = (data) => {
  return http('get', `/admin/ad/generate_auth_url`, data)
}

/**
 * 获取报表商品搜索列表
 */
export const get_search_goods_list = (data: any) => {
  return http('get', `/admin/goods_log/search_list`, data)
}

/**
 * 获取报表账户搜索列表
 */
export const get_search_advertiser_list = (data: any) => {
  return http('get', `/admin/advertiserLog/list`, data)
}

// 保存和编辑标题排序
export const save_report_column = (data: any) => {
  return http('post', `/admin/tk_user_report_sort/add`, data)
}
// 获取标题排序列表
export const get_report_column_list = (data) => {
  return http('get', `/admin/tk_user_report_sort/list`, data)
}

/**
 * ocr识别
 * https://www.apifox.cn/link/project/2014698/apis/api-66580697
 */
export const setOcrInfo = (data: any) => {
  return http('get', `/common/ocr/info`, data)
}

export const setOceanAuthUrl = (data) => {
  return http('get', `/admin/ad/oceanengine_generate_auth_url`, data)
}

// 获取积分管理余额
export const getPointBalance = (data?: any) => {
  return http('get', `/admin/point/balance`, data)
}
//企业微信列表
export const workListApi = (data?: any) => {
  return http('get', `/common/work/list`, data)
}
//获取企业获客使用情况-提醒
export const checkQuotaApi = (data?: any) => {
  return http('get', `/admin/work/check_quota`, data)
}

/**
 * 导出列表
 */
export const exportdataList = (data) => {
  return http('get', `/common/exportdata/list`, data)
}

/**
 * 下载/删除
 */
export const exportdataEdit = (data) => {
  return http('post', `/common/exportdata/edit`, data)
}
//保存欢迎语

export const save_welcome_txt = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save_welcome_txt`, data)
}
// 获取获客链接详情
export const getLinkInfo = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_link_info`, data)
}
// 获取加粉数据列表
export const getFansList = (data: any) => {
  return http('post', `/admin/adConversion/get_list`, data)
}
/**
 * 产品库列表
 *
 */
export const product_list = (data: any) => {
  return http('post', `/admin/product/list`, data)
}

//十分钟内加粉流速详情
export const fans_flow = (data: any) => {
  return http('get', `/common/fans_flow`, data)
}
//获取未读消息统计
export const statsApi = (data: any) => {
  return http('get', `/admin/message/stats`, data)
}
//获取消息列表
export const messagegListApi = (data: any) => {
  return http('get', `/admin/message/list`, data)
}
// 标记单条消息已读
export const messagegReadApi = (data: any) => {
  return http('post', `/admin/message/read`, data)
}
//标记所有消息为已读
export const readAllApi = (data: any) => {
  return http('post', `/admin/message/read_all`, data)
}
