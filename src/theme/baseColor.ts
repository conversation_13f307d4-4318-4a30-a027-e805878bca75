import { BaseCssVarAction, ThemeConfig } from 'types'
import { addColorAlpha, getColorPalette } from './color'

export const baseCssVarAction: BaseCssVarAction = (themeConfig: ThemeConfig) => {
  return {
    fontSize: {
      value: themeConfig.fontSize,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number + 'px' },
        { scene: 'Mini', handler: (number: number) => number - 2 + 'px' },
        { scene: 'Tiny', handler: (number: number) => number - 2 + 'px' },
        { scene: 'Small', handler: (number: number) => number + 'px' },
        { scene: 'Medium', handler: (number: number) => number + 'px' },
        { scene: 'Large', handler: (number: number) => number + 1 + 'px' },
        { scene: 'Huge', handler: (number: number) => number + 2 + 'px' }
      ]
    },
    lineHeight: {
      value: themeConfig.lineHeight,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number },
        { scene: 'Small', handler: (number: number) => number - 0.3 },
        { scene: 'Medium', handler: (number: number) => number },
        { scene: 'Large', handler: (number: number) => number + 0.3 },
        { scene: 'Huge', handler: (number: number) => number + 0.6 }
      ]
    },
    margin: {
      value: themeConfig.space,
      type: 'size',
      action: [
        { scene: 'Small', handler: (number: number) => number + (number - 18) * 4 + 'px' },
        { scene: 'Medium', handler: (number: number) => number + 'px' },
        { scene: 'Large', handler: (number: number) => number + (number - 16) * 4 + 'px' },
        { scene: 'Huge', handler: (number: number) => number + (number - 14) * 4 + 'px' }
      ]
    },
    padding: {
      value: themeConfig.space,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number + 'px' },
        { scene: 'Small', handler: (number: number) => number + (number - 17) * 4 + 'px' },
        { scene: 'Medium', handler: (number: number) => number + 'px' },
        { scene: 'Large', handler: (number: number) => number + (number - 15) * 4 + 'px' },
        { scene: 'Huge', handler: (number: number) => number + (number - 14) * 4 + 'px' }
      ]
    },
    borderRadius: {
      value: themeConfig.borderRadius,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number + 'px' },
        { scene: 'Small', handler: (number: number) => number - 1 + 'px' },
        { scene: 'Medium', handler: (number: number) => number + 'px' },
        { scene: 'Large', handler: (number: number) => number + 1 + 'px' },
        { scene: 'Huge', handler: (number: number) => number + 2 + 'px' }
      ]
    },
    height: {
      value: themeConfig.height,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number + 'px' },
        { scene: 'Mini', handler: (number: number) => number - 18 + 'px' },
        { scene: 'Tiny', handler: (number: number) => number - 12 + 'px' },
        { scene: 'Small', handler: (number: number) => number - 6 + 'px' },
        { scene: 'Medium', handler: (number: number) => number + 'px' },
        { scene: 'Large', handler: (number: number) => number + 6 + 'px' },
        { scene: 'Huge', handler: (number: number) => number + 12 + 'px' }
      ]
    },
    text: {
      value: themeConfig.colors.textColor,
      type: 'color',
      action: [
        { scene: 'Base', handler: (color: string) => color },
        { scene: 'Gray', handler: (color: string) => getColorPalette(color, 2) },
        { scene: '1', handler: (color: string) => getColorPalette(getColorPalette(getColorPalette(color, 1), 1), 1) },
        { scene: '2', handler: (color: string) => getColorPalette(getColorPalette(getColorPalette(color, 1), 1), 2) },
        { scene: '3', handler: (color: string) => getColorPalette(getColorPalette(getColorPalette(color, 1), 1), 3) },
        { scene: '4', handler: (color: string) => getColorPalette(getColorPalette(getColorPalette(color, 1), 1), 4) },
        { scene: '5', handler: (color: string) => getColorPalette(getColorPalette(getColorPalette(color, 1), 1), 5) }
      ]
    },
    primary: {
      value: themeConfig.colors.primary,
      type: 'color',
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Suppl', handler: (color: string) => color },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Pressed', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Active', handler: (color: string) => addColorAlpha(color, 0.1) }
      ]
    },
    info: {
      value: themeConfig.colors.info,
      type: 'color',
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Suppl', handler: (color: string) => color },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Pressed', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Active', handler: (color: string) => addColorAlpha(color, 0.1) }
      ]
    },
    success: {
      value: themeConfig.colors.success,
      type: 'color',
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Suppl', handler: (color: string) => color },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Pressed', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Active', handler: (color: string) => addColorAlpha(color, 0.1) }
      ]
    },
    warning: {
      value: themeConfig.colors.warning,
      type: 'color',
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Suppl', handler: (color: string) => color },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Pressed', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Active', handler: (color: string) => addColorAlpha(color, 0.1) }
      ]
    },
    error: {
      value: themeConfig.colors.error,
      type: 'color',
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Suppl', handler: (color: string) => color },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Pressed', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Active', handler: (color: string) => addColorAlpha(color, 0.1) }
      ]
    },
    headerHeight: {
      value: themeConfig.layout.headerHeight,
      type: 'size',
      action: [{ scene: '', handler: (number: number) => number + 'px' }]
    },
    sidebarWidth: {
      value: themeConfig.layout.sidebarWidth,
      type: 'size',
      action: [{ scene: '', handler: (number: number) => number + 'px' }]
    },
    tagHeight: {
      value: themeConfig.layout.tagHeight,
      type: 'size',
      action: [{ scene: '', handler: (number: number) => number + 'px' }]
    },
    minSidebarWidth: {
      value: themeConfig.layout.minSidebarWidth,
      type: 'size',
      action: [{ scene: '', handler: (number: number) => number + 'px' }]
    },
    foldSidebarWidth: {
      value: themeConfig.layout.foldSidebarWidth,
      type: 'size',
      action: [{ scene: '', handler: (number: number) => number + 'px' }]
    },

    layoutBgColor: {
      value: themeConfig.layout.layoutBgColor,
      type: 'color',
      action: [{ scene: '', handler: (color: string) => color }]
    },
    menuBgColor: {
      value: themeConfig.layout.menuBgColor,
      type: 'color',
      action: [{ scene: '', handler: (color: string) => color }]
    },
    headerBgColor: {
      value: themeConfig.layout.headerBgColor,
      type: 'color',
      action: [{ scene: '', handler: (color: string) => color }]
    },
    mainBgColor: {
      value: themeConfig.layout.mainBgColor,
      type: 'color',
      action: [{ scene: '', handler: (color: string) => color }]
    }
  }
}
