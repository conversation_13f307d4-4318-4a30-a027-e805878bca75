import { BaseCssVarAction, ThemeConfig } from 'types'
import { naiveCssVarAction } from './naiveColor'
import { antdCssVarAction } from './antdColor'

export * from './helpers'
export * from './naiveColor'
export * from './baseColor'
export * from './color'

/**
 * 当前使用的是那个组件库的配置
 * @param themeConfig 配置文件
 * @returns
 */
export const cssVarAction: BaseCssVarAction = (themeConfig: ThemeConfig) => {
  return themeConfig.uiComponents == 'antd' ? antdCssVarAction(themeConfig) : naiveCssVarAction(themeConfig)
}
