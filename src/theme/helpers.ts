import { kebabCase } from 'lodash-es'
import { NaiveCssVar } from 'types'
import { useTheme } from '@/hooks'
import { getSetting } from '@/utils'

/**
 * 初始化设置当前使用的模板
 */
export async function setupTheme() {
  const { initTheme } = useTheme()
  initTheme(await getSetting())
}

/** 设置全局css变量 */
export function setCssVariables(varCss: { [s: string]: unknown }) {
  const styleElement = document.createElement('style')
  let styleStr = ':root {\n'
  Object.entries(varCss).forEach(([key, value]) => {
    styleStr += `  --${kebabCase(key)}: ${value};\n`
  })
  styleStr += '}'
  styleElement.innerHTML = styleStr
  document.head.appendChild(styleElement)
}

/**
 * 合并两个ui库的action
 * @param baseThemeVar 全局配置
 * @param uiThemeVar 需要合并的数组
 * @returns
 */
export function processCssVarAction(
  baseThemeVar: Partial<NaiveCssVar>,
  uiThemeVar: Partial<NaiveCssVar>
): Partial<NaiveCssVar> {
  ;(Object.keys(uiThemeVar) as Array<keyof NaiveCssVar>).forEach((key) => {
    if (baseThemeVar.hasOwnProperty(key) && baseThemeVar[key]) {
      ;(baseThemeVar[key] || { action: [] }).action =
        baseThemeVar[key]?.action.concat((uiThemeVar[key] || { action: [] }).action) || []
    } else {
      baseThemeVar[key] = uiThemeVar[key]
    }
  })
  return baseThemeVar
}

/**
 * 获取全部css变量
 */
export function getThemeVar(uiAction: Partial<NaiveCssVar>) {
  const themeColor: Record<string, any> = {}
  Object.entries(uiAction).forEach(([key, value]) => {
    if (value) {
      value.action.forEach(({ scene, handler }) => {
        let cssVarKey = ''
        if (value.type == 'color' && !key.includes('Color')) {
          cssVarKey = `${key}Color${scene}`
        } else if (value.type == 'size') {
          cssVarKey = `${key}${scene}`
        } else {
          cssVarKey = `${key}${scene}`
        }
        themeColor[cssVarKey] = handler(value.value)
      })
    }
  })
  return {
    ...themeColor
  }
}
