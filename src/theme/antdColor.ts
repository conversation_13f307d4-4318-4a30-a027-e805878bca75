import { BaseCssVarAction, ThemeConfig } from 'types'
import { baseCssVarAction } from './baseColor'
import { processCssVarAction } from './helpers'
import { getColorPalette } from './color'

export const antdCssVarAction: BaseCssVarAction = (themeConfig: ThemeConfig) => {
  return processCssVarAction(baseCssVarAction(themeConfig), {
    // 主题色
    colorPrimary: {
      value: themeConfig.colors.primary,
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Bg', handler: (color: string) => getColorPalette(color, 1) },
        { scene: 'BgHover', handler: (color: string) => getColorPalette(color, 2) },
        { scene: 'Border', handler: (color: string) => getColorPalette(color, 3) },
        { scene: 'BorderHover', handler: (color: string) => getColorPalette(color, 4) },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Active', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Text', handler: (color: string) => color },
        { scene: 'TextHover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'TextActive', handler: (color: string) => getColorPalette(color, 7) }
      ]
    },
    colorSuccess: {
      value: themeConfig.colors.success,
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Bg', handler: (color: string) => getColorPalette(color, 1) },
        { scene: 'BgHover', handler: (color: string) => getColorPalette(color, 2) },
        { scene: 'Border', handler: (color: string) => getColorPalette(color, 3) },
        { scene: 'BorderHover', handler: (color: string) => getColorPalette(color, 4) },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Active', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Text', handler: (color: string) => color },
        { scene: 'TextHover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'TextActive', handler: (color: string) => getColorPalette(color, 7) }
      ]
    },
    colorWarning: {
      value: themeConfig.colors.warning,
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Bg', handler: (color: string) => getColorPalette(color, 1) },
        { scene: 'BgHover', handler: (color: string) => getColorPalette(color, 2) },
        { scene: 'Border', handler: (color: string) => getColorPalette(color, 3) },
        { scene: 'BorderHover', handler: (color: string) => getColorPalette(color, 4) },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Active', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Text', handler: (color: string) => color },
        { scene: 'TextHover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'TextActive', handler: (color: string) => getColorPalette(color, 7) }
      ]
    },
    colorError: {
      value: themeConfig.colors.error,
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Bg', handler: (color: string) => getColorPalette(color, 1) },
        { scene: 'BgHover', handler: (color: string) => getColorPalette(color, 2) },
        { scene: 'Border', handler: (color: string) => getColorPalette(color, 3) },
        { scene: 'BorderHover', handler: (color: string) => getColorPalette(color, 4) },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Active', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Text', handler: (color: string) => color },
        { scene: 'TextHover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'TextActive', handler: (color: string) => getColorPalette(color, 7) }
      ]
    },
    colorInfo: {
      value: themeConfig.colors.info,
      action: [
        { scene: '', handler: (color: string) => color },
        { scene: 'Bg', handler: (color: string) => getColorPalette(color, 1) },
        { scene: 'BgHover', handler: (color: string) => getColorPalette(color, 2) },
        { scene: 'Border', handler: (color: string) => getColorPalette(color, 3) },
        { scene: 'BorderHover', handler: (color: string) => getColorPalette(color, 4) },
        { scene: 'Hover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'Active', handler: (color: string) => getColorPalette(color, 7) },
        { scene: 'Text', handler: (color: string) => color },
        { scene: 'TextHover', handler: (color: string) => getColorPalette(color, 5) },
        { scene: 'TextActive', handler: (color: string) => getColorPalette(color, 7) }
      ]
    },
    // 尺寸
    fontSize: {
      value: themeConfig.fontSize,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number },
        { scene: 'SM', handler: (number: number) => number - 2 },
        { scene: 'LG', handler: (number: number) => number + 2 },
        { scene: 'XL', handler: (number: number) => number + 4 },
        { scene: 'Heading1', handler: (number: number) => 32 + (number - 12) * 2 },
        { scene: 'Heading2', handler: (number: number) => 26 + (number - 12) * 2 },
        { scene: 'Heading3', handler: (number: number) => 20 + (number - 12) * 2 },
        { scene: 'Heading4', handler: (number: number) => 16 + (number - 12) * 2 },
        { scene: 'Heading5', handler: (number: number) => 14 + (number - 12) * 2 }
      ]
    },
    lineHeight: {
      value: themeConfig.lineHeight,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => 1.57 },
        { scene: 'SM', handler: (number: number) => 1.66666 },
        { scene: 'Medium', handler: (number: number) => number },
        { scene: 'LG', handler: (number: number) => 1.5 },
        { scene: 'XL', handler: (number: number) => number + 0.6 }
      ]
    },
    // 组件高度
    controlHeight: {
      value: themeConfig.height,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number },
        { scene: 'SM', handler: (number: number) => number - 10 },
        { scene: 'XS', handler: (number: number) => number - 24 },
        { scene: 'LG', handler: (number: number) => number + 8 }
      ]
    },
    // 外间距
    margin: {
      value: themeConfig.space,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number },
        { scene: 'XXS', handler: (number: number) => number + (number - 19) * 4 },
        { scene: 'XS', handler: (number: number) => number + (number - 18) * 4 },
        { scene: 'SM', handler: (number: number) => number + (number - 17) * 4 },
        { scene: 'MD', handler: (number: number) => number + (number - 15) * 4 },
        { scene: 'LG', handler: (number: number) => number + (number - 14) * 4 },
        { scene: 'XL', handler: (number: number) => number + (number - 12) * 4 },
        { scene: 'XXL', handler: (number: number) => number + (number - 8) * 4 }
      ]
    },
    // 内间距
    padding: {
      value: themeConfig.space,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => number },
        { scene: 'XXS', handler: (number: number) => number + (number - 19) * 4 },
        { scene: 'XS', handler: (number: number) => number + (number - 18) * 4 },
        { scene: 'SM', handler: (number: number) => number + (number - 17) * 4 },
        { scene: 'MD', handler: (number: number) => number + (number - 15) * 4 },
        { scene: 'LG', handler: (number: number) => number + (number - 14) * 4 },
        { scene: 'XL', handler: (number: number) => number + (number - 12) * 4 },
        { scene: 'XXL', handler: (number: number) => number + (number - 8) * 4 }
      ]
    },
    // 圆角
    borderRadius: {
      value: themeConfig.borderRadius,
      type: 'size',
      action: [
        { scene: '', handler: (number: number) => Math.ceil(number / 1) },
        { scene: 'XS', handler: (number: number) => number - 2 },
        { scene: 'SM', handler: (number: number) => number },
        { scene: 'LG', handler: (number: number) => number + 2 }
      ]
    }
  })
}
