.common_layout_adimn_container>.header>.logo,.common_layout_adimn_container>.header>.header_box{box-shadow:none !important;border-bottom:1px solid #f3f3f3}.common_layout_adimn_container>.sidebar_box>.sidebar{box-shadow:none !important}.common_page_warp{border:none;box-shadow:none !important;border-radius:16px}.common_page_warp .mt_24px{margin-top:24px}.common_page_warp>.ant-card-head{padding-top:16px !important;padding-bottom:0px !important;font-weight:600;font-size:16px;color:#313232;line-height:22px}.common_page_warp>.ant-card-body{background-color:#fff;border-radius:16px;padding:16px 24px}.search_base_layout{background-color:var(--layout-bg-color);padding:8px 0px;border-radius:6px;margin-left:0px !important;margin-right:0px !important}.search_base_layout .ml_10px{margin-left:10px}.common_page_style{background:none !important}.common_page_style .common_page_card{background:none !important}.common_page_style .common_page_card_body{background:#fff;border-radius:16px}.common_page_style .common_page_card_title{min-height:56px;padding:0 24px;align-items:center;padding-top:18px !important;padding-bottom:16px !important}.common_page_table .ant-table-thead>tr>th,.common_page_table .ant-table-tbody>tr>td{padding:8px 16px !important}.common_page_table .ant-table-thead>tr>th.ant-table-cell-fix-right,.common_page_table .ant-table-tbody>tr>td.ant-table-cell-fix-right{background-color:#fff}.common_page_table .desc-table-layout-list-item-desc{background-color:var(--header-bg-color) !important;padding:12px 16px !important}.common_page_table .ant-table-wrapper .ant-table-footer{padding:8px 16px !important}.cellos-item_img img,.ant-image img{border-radius:4px}.cellos-item_title,.product-item-name,.goods_info_data_name{word-break:break-all;text-overflow:ellipsis;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1 !important;-webkit-box-orient:vertical}.ant-table-wrapper .ant-table-thead>tr>th{background-color:var(--header-bg-color) !important;color:var(--text-color-gray);font-weight:normal}.common_page_table .ant-table-thead>tr>th{background-color:rgba(0,0,0,0) !important}.common_page_table .table-base-layout-desc{background-color:var(--header-bg-color) !important;font-weight:normal}.common_layout_table .ant-table-container>.ant-table-content>table>tbody>tr>td,.common_layout_table .ant-table.ant-table-small .ant-table-tbody>tr>td{vertical-align:inherit}.notification-custom-class{padding:8px}.notification-custom-class .ant-notification-notice-close{top:8px;right:8px}