// 设置文本 字体大小 颜色
@mixin set_font_config($fs, $color) {
  font-size: var($fs);
  color: var($color);
}
// 文本溢出隐藏
@mixin text_overflow($line: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
// 设置 边框
@mixin set_border_config($width, $color) {
  border: $width solid $color;
}
// 设置圆角
@mixin set_border_radius($radius) {
  border-radius: $radius;
  border-radius: var($radius);
}
// 设置 元素 width height bg
@mixin set_node_whb($w: 100%, $h: auto, $c: transparent) {
  width: $w;
  height: $h;
  background: $c;
  background: var($c);
}
