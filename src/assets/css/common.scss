html,
body,
#app {
  height: 100%;
  color: var(--text-color-base);
}

body {
  color: #242f57;
  font-size: 14px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-variant: tabular-nums;
  font-feature-settings: 'tnum', 'tnum';
}

.text_overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.text_overflow_row1 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.text_overflow_row2 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text_overflow_row3 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// .page_main {
//   min-height: calc(100vh - 76px);
//   &.ant-card {
//     .ant-card-head {
//       padding: 32px 24px 0;
//       border-bottom: none;
//       background-color: red;
//     }
//   }
//   // .page_table_toolbar {
//   //   margin-top: 20px;
//   // }
//   .ant-table-wrapper {
//     .ant-table-thead > tr > th {
//       // background-color: var(--header-bg-color);
//       background-color: #f7f7f7;
//       &::before {
//         display: none;
//       }
//     }
//   }
//   .ant-table-wrapper.is-striped {
//     .ant-table-tbody {
//       .ant-table-row:nth-child(2n) {
//         background-color: #fafcfe;
//       }
//       .ant-table-row:hover {
//         td {
//           background-color: #f4fafc;
//         }
//       }
//     }
//   }
// }

.modalWrapClassName .ant-modal-content {
  padding: 0;
  .close {
    background: rgba(255, 255, 255, 0.69);
    position: absolute;
    top: 16px;
    right: 16px;
    height: 22px;
    width: 47px;
    text-align: center;
    line-height: 22px;
    cursor: pointer;
    color: #313233;
    z-index: 10;
    border-radius: 4px;
    backdrop-filter: blur(2px);
  }
}

/* 滚动条样式 */
.common_scroll {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
  }
  &::-webkit-scrollbar-track {
    border-radius: 2px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    display: block;
    background-color: #cccccc;
  }
}
.common_scroll_y {
  @extend .common_scroll;
  overflow-y: auto;
}
.common_scroll_x {
  @extend .common_scroll;
  overflow-x: auto;
}
.common_scroll_y_hover {
  @extend .common_scroll_y;
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #cccccc;
    }
  }
}
.common_scroll_x_hover {
  @extend .common_scroll_x;
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #cccccc;
    }
  }
}

.flex {
  display: flex;
}

.flex_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_align_center {
  display: flex;
  align-items: center;
}
.items-end {
  display: flex;
  align-items: end;
}

.flex_justify_end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex_ju_sp {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_column {
  flex-direction: column;
}

.flex_1 {
  flex: 1;
}

.relative {
  position: relative;
}

.ellipsis {
  white-space: pre;
  text-overflow: ellipsis;
  overflow: hidden;
}

.f-flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-align {
  display: flex;
  align-items: center;
}

.flex-justify {
  display: flex;
  justify-content: center;
}

.space-between {
  justify-content: space-between;
}

.upf {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ups {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ant-btn + .ant-btn {
  margin-left: 10px;
}
.text_overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.card {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #fff;
  border-radius: 4px;
  .card_title {
    padding: 32px 24px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
  }
  .card_body {
    display: block;
    box-sizing: border-box;
    height: 100%;
    padding: 24px;
  }
}
.ant-popover-title {
  padding: 12px 16px 0px;
  border: none;
}

// 全屏弹窗
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
  }
}

.color-opacity-6 {
  color: rgba(36, 47, 87, 0.6) !important;
}

.number-id {
  color: rgba(36, 47, 87, 0.6) !important;
  font-size: var(--font-size-mini) !important;
}

// 操作样式
.action-group {
  .ant-btn-link {
    padding-left: 0;
    padding-right: 0;
  }
}

.el-color-dropdown__link-btn {
  & > span {
    display: none !important;
  }
  &::after {
    content: '取消';
    display: inline-flex;
  }
}
.el-color-dropdown__btn {
  & > span {
    display: none !important;
  }
  &::after {
    content: '确定';
    display: inline-flex;
  }
}
.custom-msg {
  .ant-message-custom-content.ant-message-warning {
    display: flex;
  }
}

.table_overflow_initial {
  .ant-table-content {
    overflow: initial !important;
  }
}

.ant-table-container > .ant-table-content > table > tbody > tr > td,
.ant-table.ant-table-small .ant-table-tbody > tr > td {
  vertical-align: top;
}

// 顶出登录
.force-logout {
  .ant-modal-confirm-title {
    font-size: 16px !important;
    line-height: 24px !important;
  }
  .ant-modal-confirm-btns {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    .ant-btn {
      height: 40px;
      width: 128px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 9px 36px;
      line-height: 22px;
    }
  }
  .ant-modal-confirm-content {
    max-width: 100% !important;
  }
}

.ant-modal .ant-modal-content {
  padding: 16px 24px 24px;
}
.ant-form-item {
  margin-bottom: 18px;
}
.ant-form-item-with-help .ant-form-item-explain {
  opacity: 1;
  height: 18px;
  line-height: 18px;
}
.form-scroll-wrapper {
  margin-right: -20px;
  .form-scroll-box {
    max-height: 550px;
    overflow: auto;
    padding-right: 20px;
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      /**/
    }
    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 2px;
    }
    &::-webkit-scrollbar-thumb {
      background: #c5c5c5;
      border-radius: 10px;
      display: block;
      padding-left: 30px;
    }
  }
  .form-scroll-btn {
    margin-right: 20px;
  }
}

.sens-picture {
  font-size: 0;
  img {
    width: 100%;
    height: auto;
  }
}

.sensbg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.rounds {
  width: 6px;
  height: 6px;
  background: #404040;
  border-radius: 50%;
  margin-right: 5px;
}
.disable-tag {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #b6afaf;
  line-height: 20px;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #b6afaf;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.success-tag {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #52c41a;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #52c41a;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.setup-tag {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #fe9d35;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #fe9d35;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.error-tag {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #ff4d4f;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #ff4d4f;
    border-radius: 50%;
    margin-right: 4px;
  }
}

.yellow-tag {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #fe9d35;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #fe9d35;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.blue-tag {
  font-weight: 400;
  font-size: 14px;
  color: #1677ff;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #1677ff;
    border-radius: 50%;
    margin-right: 4px;
  }
}

.normal-tag {
  font-weight: 400;
  font-size: 14px;
  color: #2b2b2b;

  &::before {
    content: ''; /* 必须设置 content */
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #2b2b2b;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.cursor-pointer {
  cursor: pointer;
}
.ant-modal .ant-modal-title {
  margin-bottom: 20px !important;
}
// 表单都左对齐
.ant-form-item-label {
  text-align: left !important;
}
.ant-form-item-control {
  text-align: left !important;
}

.ant-modal .table-zebra-crossing .ant-table {
  .ant-table-body {
    overflow-y: auto !important;
  }
}
.body_pad_none {
  .ant-modal-content {
    .ant-modal-body {
      .ant-card-body {
        padding: 0 !important;
      }
    }
  }
}
