// layout布局样式
.common_layout_adimn_container {
  & > .header {
    & > .logo,
    & > .header_box {
      box-shadow: none !important;
      border-bottom: 1px solid #f3f3f3;
    }
  }
  & > .sidebar_box {
    & > .sidebar {
      box-shadow: none !important;
    }
  }
}
// 主要显示区域 列表 外层样式
.common_page_warp {
  border: none;
  // background-color: transparent;
  box-shadow: none !important;
  border-radius: 16px;
  .mt_24px {
    margin-top: 24px;
  }

  & > .ant-card-head {
    padding-top: 10px !important;
    padding-bottom: 0px !important;
    font-weight: 600;
    font-size: 16px;
    color: #313232;
    line-height: 22px;
  }
  & > .ant-card-body {
    background-color: #fff;
    border-radius: 16px;
    padding: 10px 24px;
  }
}
// 筛选框样式
.search_base_layout {
  background-color: var(--layout-bg-color);
  padding: 8px 0px;
  border-radius: 6px;
  margin-left: 0px !important;
  margin-right: 0px !important;
  .ml_10px {
    margin-left: 10px;
  }
}
// 搜索上面的标题
.common_page_style {
  background: none !important;
  .common_page_card {
    background: none !important;
  }
  .common_page_card_body {
    background: #fff;
    border-radius: 16px;
  }
  .common_page_card_title {
    min-height: 56px;
    padding: 0 24px;
    align-items: center;
    padding-top: 18px !important;
    padding-bottom: 16px !important;
  }
}

//表格内容样式
.common_page_table .ant-table-thead > tr > th,
.common_page_table .ant-table-tbody > tr > td {
  padding: 8px 16px !important;
  &.ant-table-cell-fix-right {
    background-color: #fff;
  }
}
//表格内容描述样式
.common_page_table .desc-table-layout-list-item-desc {
  background-color: var(--header-bg-color) !important;
  padding: 12px 16px !important;
}
.common_page_table .ant-table-wrapper .ant-table-footer {
  padding: 8px 16px !important;
}
//表格内容里图片统一样式
.cellos-item_img,
.ant-image {
  img {
    border-radius: 4px;
  }
}
//表格内容里名称统一样式
.cellos-item_title,
.product-item-name,
.goods_info_data_name {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1 !important;
  -webkit-box-orient: vertical;
}
//表格统一头部样式
.ant-table-wrapper .ant-table-thead > tr > th {
  // background-color: var(--header-bg-color) !important;
  background-color: #f7f7f7;
  color: var(--text-color-gray);
  font-weight: normal;
}
//特殊表格有描述和头部
.common_page_table .ant-table-thead > tr > th {
  background-color: transparent !important;
}
//特殊表格有描述
.common_page_table .table-base-layout-desc {
  background-color: var(--header-bg-color) !important;
  font-weight: normal;
}

// 表格垂直居中
.common_layout_table {
  .ant-table-container > .ant-table-content > table > tbody > tr > td,
  .ant-table.ant-table-small .ant-table-tbody > tr > td {
    vertical-align: inherit;
  }
}
.notification-custom-class {
  padding: 8px;
  .ant-notification-notice-close {
    top: 8px;
    right: 8px;
  }
}
