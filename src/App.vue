<template>
  <a-config-provider :theme="{ token: themeOverrides.common }" :locale="zhCN">
    <a-modal
      class="open-details-warp"
      v-model:open="open"
      :title="options?.title"
      width="80%"
      :footer="null"
      centered
      @cancel="cancel"
      :bodyStyle="bodyStyle"
    >
      <component :is="component"></component>
    </a-modal>
    <router-view></router-view>
  </a-config-provider>
</template>
<script setup lang="ts">
  import zhCN from 'ant-design-vue/es/locale/zh_CN'
  // console.log('%c当前项目环境变量getConfig获取：', 'color: blue', import.meta.env.VITE_VAR)

  import { computed, ref, watch, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  const route = useRoute()
  const router = useRouter()
  import { useTheme, useApp, usePageDialog, useNewsNotify } from '@/hooks'
  const { setThemeVarColor, themeOverrides } = useTheme()
  const { reloadPage, setKeepAlive } = useApp()
  const { open, component, options } = usePageDialog()
  const { checkUnreadNotices } = useNewsNotify()
  import { locale } from 'dayjs'
  import 'dayjs/locale/zh-cn'
  locale('en')
  const bodyStyle = computed(() => {
    return {
      height: `calc(100vh - 200px)`,
      overflow: 'auto'
    }
  })
  const cancel = () => {
    router.replace({ path: route.path })
  }

  reloadPage()
  setThemeVarColor()

  // 应用启动时检查未读更新通知
  onMounted(() => {
    // 延迟检查，确保应用完全初始化
    setTimeout(() => {
      checkUnreadNotices()
    }, 500)
  })

  watch(
    () => router.currentRoute.value,
    () => {
      // 设置缓存
      const route = router.currentRoute.value
      if (route && route.meta.keepAlive && route.name) {
        setKeepAlive(route.matched.map((v) => v.name as string))
      }
    },
    {
      immediate: true
    }
  )
</script>

<style scoped lang="scss"></style>
