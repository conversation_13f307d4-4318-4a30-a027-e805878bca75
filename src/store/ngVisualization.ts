import { defineStore } from 'pinia'
import { parseTime } from '@/utils'

export const useNgVisualizationStore = defineStore('ngVisualization', {
  state: () => ({
    canvasSize: {
      // 画布大小
      width: 375,
      height: 585
    },
    canvasPosition: {
      top: null,
      left: null
    },
    allNoToolbar: ['NgFirstScreenVerify'],
    currMenusIndex: 'Image', // 当前选择的菜单Key
    activityData: {} as any, // 页面保存数据
    editingComponent: {} as any, // 当前操作中组件
    curPageData: [] as any, // 当前页
    // posterItems: [], // 组件列表
    activeItems: {} as any, // 当前操作中组件
    brickTempl: [], // 当前菜单对应的组件
    assistWidgets: [], // 辅助组件
    referenceLineOpened: true, // 是否打开参考线
    copiedWidgets: null, // 当前复制的组件 WidgetItem[]
    referenceLine: {
      // 参考线,用户定义的参考线
      row: [],
      col: []
    },
    matchedLine: null, // 匹配到的参考线 {row:[],col:[]}
    updateTime: null as null | string, // 上次修改时间
    dragType: null, // 拖拽类型
    dragIsPush: false, // 是否已添加组件
    dragIndex: null, // 当前组件的索引
    currStyleClass: null, // 当前拖拽组件类型
    isDragLeave: false, // 默认没有脱离拖拽区
    isWeixin: navigator.userAgent.toLowerCase().indexOf('micromessenger') != -1,
    landingPageType: null, // 新建落地页类型
    errTip: {}, // 保存错误提示
    qaChatType: 1, // 问答聊天组件left侧边 1基础配置 2内容配置 3问答结束
    isAllowedDrag: false, // true不可拖拽、置灰
    qaActive: true, //
    qaIsShowVerify: false,
    qaItemInfo: {},
    qaCurrInfo: {},
    qaLists: [],
    qaCurrIndex: 0,
    defaultVerTips:
      '亲，如果您是为了做任务，到这一步说明您已经完成任务，请不要再点了，把机会留给真正需要的人，谢谢！！！如果您真的需要，请输入验证码，联系客服，添加客服注册免费领取奖品，名额有限，仅限前200！'
  }),
  getters: {
    getActivityData(): any {
      return this.activityData
    },
    getCurPageData(): any {
      return this.curPageData
    },
    getActiveItems(): any {
      return this.activeItems || {}
    },
    getBrickTempl(): any {
      return this.brickTempl
    },
    getIsDragLeave(): any {
      return this.isDragLeave
    },
    getCurrMenusIndex(): any {
      return this.currMenusIndex
    },
    getCurrStyleClass(): any {
      return this.currStyleClass
    },
    getDragIndex(): any {
      return this.dragIndex
    },
    getUpdateTime(): any {
      return this.updateTime
    },
    getErrTip(): any {
      return this.errTip
    },
    getDragType(): any {
      return this.dragType
    },
    getLandingPageType(): any {
      return this.landingPageType
    },
    getDragIsPush(): any {
      return this.dragIsPush
    },
    getCanvasSize(): any {
      return this.canvasSize
    },
    getQaItemInfo(): any {
      return this.qaItemInfo
    },
    getQaActive(): any {
      return this.qaActive
    },
    getQaChatType(): any {
      return this.qaChatType
    }
  },
  actions: {
    modifyQaData(type: any, data: any) {
      if (type == 'reset') {
        this.qaCurrIndex = 0
        this.qaCurrInfo = {}
        this.qaLists = []
      } else {
        if (data?.list) this.qaLists = data.list
        if (data?.index >= 0) this.qaCurrIndex = data.index
        if (data.info?.id) this.qaCurrInfo = data.info
      }
    },
    modifyErrTip(data: any) {
      this.errTip = data
    },
    modifyQaItemInfo(data: any) {
      this.qaItemInfo = data
    },
    modifyCurrMenusIndex(key: string) {
      // 改变当前选择的菜单Key
      this.currMenusIndex = key
    },
    modifyBrickTempl(data: any) {
      // 改变当前菜单对应的组件
      this.brickTempl = data || []
    },
    modifyActiveItems(data: any) {
      // 当前操作中组件
      this.activeItems = data || {}
    },
    modifyCurPageData(data: any) {
      // 当前页数据
      this.curPageData = data || []
    },
    modifyActivityData(data: any) {
      // 页面保存数据总体数据
      this.activityData = data || {}
    },
    modifyDragType(data: any) {
      // 拖拽类型
      this.dragType = data
    },
    modifyDragIsPush(data: any) {
      // 是否已添加组件
      this.dragIsPush = data
    },
    modifyDragIndex(data: any) {
      // 当前组件的索引
      this.dragIndex = data
    },
    modifyCurrStyleClass(data: any) {
      // 当前拖拽组件类型
      this.currStyleClass = data
    },
    dragEnd(data: any) {
      // 拖拽结束修改
      this.modifyCurPageData(data.currData)
      this.modifyDragIsPush(data.isPush)
      this.modifyDragType(data.type)
    },
    modifyResize(data: any) {
      // console.log(state.activeItems, data)
      if (!this.activeItems.name) return
      if (data.x == undefined) return
      this.activeItems.eboxValue.x = data.x
      this.activeItems.eboxValue.y = data.y
      this.activeItems.styles.x = data.x / 100 + 'rem'
      this.activeItems.styles.y = data.y / 100 + 'rem'

      if (data.type == 'resizing') {
        this.activeItems.eboxValue.width = data.w
        this.activeItems.eboxValue.height = data.h
        this.activeItems.styles.width = data.w / 100 + 'rem'
        this.activeItems.styles.height = data.h / 100 + 'rem'
      }
    },
    modifyRotate(data: any) {
      this.activeItems.rotation = data.rotate || 0
    },
    modifyUpdatetime() {
      // commit('SET_UPDATETIME', time)
      this.updateTime = parseTime(new Date(), '{m}月{d}日 {h}:{i}')
    },
    modifyIsDragLeave(data: any) {
      this.isDragLeave = data
    },
    modifyQaChatType(data: any) {
      this.qaChatType = data
    },
    modifyQaActive(data: any) {
      this.qaActive = data
    },
    setLandingPageType(data: any) {
      this.landingPageType = data || ''
    },
    modifyIsAllowedDrag() {
      if (this.activityData && Array.isArray(this.activityData.pages)) {
        if (this.activityData.pages.length) {
          const index = this.activityData.pages[0].elements.findIndex((v) => v.name === 'NgQaChat')
          this.isAllowedDrag = index >= 0
        }
      }
    }
  }
})
