import axios from 'axios'
/**
 * 通过线上文件地址下载文件
 * @param {String} url 文件线上链接
 * @param {String} fileName 文件名称
 */
export const downloadFileByPath = async (url, fileName) => {
  const response = await axios({
    method: 'get',
    url: url,
    responseType: 'blob'
  })
  const linkUrl = window.URL.createObjectURL(new Blob([response.data]))
  const link = document.createElement('a')
  link.href = linkUrl
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  link.remove()
}
