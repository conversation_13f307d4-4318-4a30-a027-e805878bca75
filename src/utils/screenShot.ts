import html2canvas from 'html2canvas'
import { message } from 'ant-design-vue'
export class ScreenshotTool {
  constructor(id) {
    this.screenshotArea = document.getElementById(id)
  }

  async captureScreenshot() {
    try {
      let canvas = null
      // 使用html2canvas库截屏
      if (typeof html2canvas === 'undefined') {
        // 如果没有html2canvas库，使用原生方法
        canvas = await this.captureWithNativeMethod()
      } else {
        canvas = await this.captureWithHtml2Canvas()
      }
      // this.downloadImage(canvas)
      await this.copyToClipboard(canvas)
      return Promise.resolve('success')
    } catch (error) {
      console.error('截屏失败:', error)
      message.error('截屏失败')
    }
  }

  async captureWithNativeMethod() {
    // 检查是否支持现代截屏API
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      await this.captureWithDisplayMedia()
    } else {
      throw new Error('您的浏览器不支持截屏功能，请使用Chrome、Firefox或Edge浏览器')
    }
  }

  async captureWithDisplayMedia() {
    try {
      // 请求屏幕共享
      const stream = await navigator.mediaDevices.getDisplayMedia({
        preferCurrentTab: true,
        video: {
          mediaSource: 'screen'
        }
      })

      // 创建视频元素来捕获流
      const video = document.createElement('video')
      video.srcObject = stream
      video.play()

      // 等待视频加载
      await new Promise((resolve) => {
        video.onloadedmetadata = resolve
      })

      // 创建canvas
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      // 设置canvas尺寸
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // 绘制视频帧到canvas
      ctx.drawImage(video, 0, 0)

      // 停止流
      stream.getTracks().forEach((track) => track.stop())

      console.log('====captureWithNativeMethod====', canvas)
      return canvas
    } catch (error) {
      if (error.name === 'NotAllowedError') {
        throw new Error('用户取消了截屏权限')
      } else {
        throw error
      }
    }
  }

  async captureWithHtml2Canvas() {
    // 使用html2canvas库截屏
    const canvas = await html2canvas(this.screenshotArea, {
      backgroundColor: null,
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: true
    })

    console.log('====captureWithHtml2Canvas====', canvas)
    return canvas
  }
  async copyToClipboard(canvas) {
    try {
      const blob = await new Promise((resolve) => {
        canvas.toBlob(resolve, 'image/png')
      })
      const clipboardItem = new ClipboardItem({
        'image/png': blob
      })
      await navigator.clipboard.write([clipboardItem])

      // message.success('截屏成功，已复制到剪切板')
    } catch (error) {
      // 如果现代API不可用，尝试传统方法
      await this.fallbackCopyMethod(canvas)
    }
  }

  async fallbackCopyMethod(canvas) {
    try {
      const dataUrl = canvas.toDataURL('image/png')
      const input = document.createElement('input')
      input.value = dataUrl
      document.body.appendChild(input)
      input.select()
      const success = document.execCommand('copy')
      document.body.removeChild(input)

      if (success) {
        message.success('截屏成功，已复制到剪切板')
      } else {
        message.error('复制失败')
        throw new Error('复制失败')
      }
    } catch (error) {
      // 最后的备选方案：下载图片
      // this.downloadImage(canvas)
    }
  }

  downloadImage(canvas) {
    const link = document.createElement('a')
    link.download = 'screenshot.png'
    link.href = canvas.toDataURL('image/png')
    link.click()
    message.success('下载成功')
  }
}
