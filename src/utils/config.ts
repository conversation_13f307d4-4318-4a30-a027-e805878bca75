import Axios from 'axios'
import { getConfig, setConfig, setSystemFile } from '.'
import { useTheme } from '@/hooks'
// import faviconIco from "@/utils/assets/favicon.ico"

// 页面全局配置
let isRequest = false // 是否请求过

export const baseConfig = {
  icon: '',
  logo: '',
  title: '', // 网站名称
  h5_logo: ''
}
// 设置浏览器加载的icon
function setIcon(iconSrc: string) {
  iconSrc = iconSrc || 'favicon.ico'

  let favicon = document.querySelector('link[rel="icon"]')
  if (favicon !== null) {
    favicon.href = iconSrc
  } else {
    favicon = document.createElement('link')
    favicon.rel = 'icon'
    favicon.href = iconSrc
    document.head.appendChild(favicon)
  }
}
// 获取全局配置
export async function getBaseConfig() {
  try {
    if (isRequest) return
    const params = {
      domain: window.location.host
    }
    const data = {}
    // const { data = {} } = await Axios({
    //   method: 'post',
    //   url: getConfig('API_URL') + '/boss/company/domain',
    //   data: params
    // })
    console.log('data', data)
    if (data.code != 0) return
    console.log('服务配置')
    const res = data.data || {}
    res.domain = 'http://localhost:3010'
    baseConfig.icon = res.icon || '-'
    baseConfig.logo = res.logo || '-'
    baseConfig.title = res.name || '-'
    baseConfig.h5_logo = res.h5_logo || '-'
    setIcon(baseConfig.icon)
    setConfig('TITLE', baseConfig.title)
    console.log(baseConfig)
    setSystemFile('kf_logo', baseConfig.logo)
    setSystemFile('favicon', baseConfig.icon)
    setSystemFile('logo', baseConfig.logo)
    setSystemFile('h5_logo', baseConfig.h5_logo)
    setSystemFile('logo_orange', baseConfig.logo)
    isRequest = true
  } catch (error) {
    console.log('默认配置')
    baseConfig.icon = '-'
    baseConfig.logo = '-'
    baseConfig.title = '-'
    console.error('获取全局配置失败', error)
    setConfig('TITLE', baseConfig.title)
    setIcon(baseConfig.icon)
    setSystemFile('kf_logo', baseConfig.logo)
    setSystemFile('favicon', baseConfig.icon)
    setSystemFile('logo', baseConfig.logo)
    setSystemFile('h5_logo', baseConfig.logo)
    setSystemFile('logo_orange', baseConfig.logo)
  }
}
