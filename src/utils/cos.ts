import axios, { Canceler } from 'axios'
import http from '@/utils/request'
import { getConfig } from './common'
import { isFunction, isUndefined } from 'lodash-es'

const CancelToken = axios.CancelToken
let cancel: Canceler
export type UploadCallback<T = 'ing' | 'err' | 'success' | 'finally'> = (
  param: T extends 'ing'
    ? { type: T; content: number }
    : T extends 'err'
      ? { type: T; content: Error }
      : T extends 'finally'
        ? { type: T; content: null }
        : { type: T; content: string; video?: { isVideo: boolean; src: string; cover: string } }
) => void

// 生成随机字符串
function generateRandomString(length) {
  let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''

  for (let i = 0; i < length; i++) {
    let randomIndex = Math.floor(Math.random() * characters.length)
    result += characters.charAt(randomIndex)
  }

  return result
}

// 获取文件后缀
function getFileExtension(filename) {
  // 将文件名分割成数组
  var parts = filename.split('.')
  // 获取数组最后一个元素，即文件后缀
  var extension = parts[parts.length - 1]
  return extension
}

const initOption = {
  oss: {
    axios: axios,
    previewUrl: getConfig('COS').url,
    path: getConfig('COS').path,
    auth: async (fileName, file) => {
      console.log(fileName, file)
      let { data = {} } = await http('get', '/admin/file/update', { file_name: fileName, method: 'put' })
      return data
    }
  }
}

class Cos {
  /**
   * 上传文件
   * @param {File} file 单文件
   * @param {use} use 图片用途
   * @param {water} water 水印
   * @param {UploadCallback} cb 回调函数
   */
  static async upload(file: File, use: String, water: Boolean, cb: UploadCallback) {
    try {
      let fileName = `${use ? use : 'goods'}/${water ? 'watermark/' : ''}${Number(new Date())}_${generateRandomString(
        12
      )}.${getFileExtension(file.name)}`
      console.log(fileName, 'waterwaterwater')

      const { url } = await initOption.oss.auth(fileName, file)

      console.log(initOption.oss.axios)
      await initOption.oss.axios({
        url,
        method: 'PUT',
        data: file,
        headers: {
          'Content-Type': ''
        },
        cancelToken: new CancelToken((c) => {
          cancel = c
        }),
        onUploadProgress(progressEvent) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          cb({
            type: 'ing',
            content: percentCompleted
          })
        }
      })
      let urlString = url.split('?')[0]
      let domainWithProtocol = new URL(urlString).origin
      let content = urlString.replace(domainWithProtocol, initOption.oss.previewUrl || domainWithProtocol)
      console.log('上传成功：', domainWithProtocol)

      if (water) {
        content = `${content}.jpg`
        setTimeout(() => {
          cb({
            type: 'success',
            content: content
          })
        }, 500)
      } else {
        cb({
          type: 'success',
          content: content
        })
      }
    } catch (error) {
      console.error('上传错误', error)
      cb({
        type: 'err',
        content: error
      })
    } finally {
      cb({
        type: 'finally',
        content: null
      })
    }
  }

  static hash = (function () {
    const hashMap: string[] = []
    function beforeHash() {
      const lowerCase = []
      const upperCase = []
      for (let i = 97; i < 123; i++) {
        lowerCase.push(String.fromCharCode(i))
      }
      for (let i = 65; i < 91; i++) {
        upperCase.push(String.fromCharCode(i))
      }
      const strs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0].concat(
        lowerCase,
        upperCase,
        new Date()
          .getTime()
          .toString()
          .split('')
          .map((v) => Number(v))
      )
      function calculte(len) {
        return new Array(len)
          .fill(1)
          .map(() => strs[Math.floor(Math.random() * 63)])
          .join('')
      }

      return function (len) {
        let res = calculte(len)
        while (hashMap.includes(res)) {
          res = calculte(len)
        }
        hashMap.push(res)
        return res
      }
    }
    return beforeHash()
  })()
}

export const cosUploadCancel = () => {
  if (isFunction(cancel)) {
    console.log('用户手动取消上传')
    cancel('用户手动取消上传')
  }
}
export default Cos
