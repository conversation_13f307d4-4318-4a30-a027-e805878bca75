/**
 * 校验正则
 */
export const checkReg = {
  /**
   * 账号
   */
  account: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
  /**
   * 密码
   *
   */
  old_pwd: /(?!^(\d+|[a-zA-Z]+|[~!@#$%^&*()_.]+)$)^[\w~!@#$%^&*()_.]{6,16}$/,

  pwd: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?!^(\d+|[a-zA-Z]+|[~!@#$%^&*()_.]+)$)[\w~!@#$%^&*()_.]{6,16}$/
}

/**
 * 检查手机号码格式
 * @param str
 * @returns
 */
export function validateMobile(str: string) {
  //
  return /^1\d{10}$/.test(str)
}
/*
 * @description 密码强度校验
 * @param {String} val 待校验强度的密码
 * @return {Number} 密码强度等级，数字越大强度越高
 */
export function checkPasswordStrength(val) {
  const leng = val.trim().length
  if (leng === 0) {
    return 0
  }
  // 1 弱密码  2 中密码  3 强密码
  if (/^[0-9]*$/.test(val) || leng < 6) {
    return 1
  }
  if (/[A-Za-z]+/.test(val) && /[0-9]+/.test(val) && !/[~!@#$%^&*()_.]+/.test(val)) {
    return 2
  }
  if (/[A-Za-z]+/.test(val) && /[0-9]+/.test(val) && /[~!@#$%^&*()_.]+/.test(val)) {
    return 3
  }
}
