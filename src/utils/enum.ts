// 回话状态
export const chatEnum = (type: any) => {
  const status = {
    1: '客户已发送消息',
    2: '客户未发送消息',
    3: '客户待通过申请'
  }
  return (status as any)[type] || '--'
}
// 回话状态样式
export const chatEnumCls = (type: any) => {
  const status = {
    1: '#52C41A',
    2: '#B6AFAF',
    3: '#FE9D35'
  }
  return (status as any)[type] || '--'
}

// 巨量引擎-授权状态
export const empowerStatusClsEnum = (type: any): any => {
  const status = {
    1: 'success-tag',
    2: 'disable-tag'
  }
  return (status as any)[type] || 'disable-tag'
}

// 巨量引擎-敏感物料授权
export const materialAuthClsEnum = (type: any): any => {
  const status = {
    1: 'disable-tag',
    2: 'success-tag'
  }
  return (status as any)[type] || 'disable-tag'
}

// 企微管理-授权状态
export const qaAuthStatusEnum = {
  1: {
    text: '授权成功',
    className: 'success-tag'
  },
  2: {
    text: '取消授权',
    className: 'disable-tag'
  },
  '11': {
    text: '授权成功',
    className: 'success-tag'
  },
  '12': {
    text: '配置中',
    className: 'setup-tag'
  },
  '21': {
    text: '取消授权',
    className: 'disable-tag'
  },
  '22': {
    text: '取消授权',
    className: 'disable-tag'
  }
}

// 线索列表-状态
export const clueIsReplyEnum = {
  1: {
    text: '已处理',
    className: 'success-tag'
  },
  2: {
    text: '未处理',
    className: 'disable-tag'
  }
}

// 线索列表-画像标签
export const clueUserTagEnum = {
  1: {
    text: '已加粉',
    className: 'success-tag'
  },
  2: {
    text: '未加粉',
    className: 'disable-tag'
  }
}

// 客服分组-关联客服-获客链接异常监测
export const kfLinkExceptionEnum = {
  1: {
    text: '正常',
    className: 'success-tag'
  },
  2: {
    text: '异常',
    className: 'error-tag'
  },
  3: {
    text: '链接异常',
    className: 'error-tag'
  },
  4: {
    text: '成员异常',
    className: 'error-tag'
  }
} as any

// 角色管理-状态
export const roleStatusEnum = {
  1: {
    text: '启用',
    className: 'success-tag'
  },
  2: {
    text: '禁用',
    className: 'error-tag'
  }
} as any

// 下载中心-状态
export const downloadStatusEnum = {
  1: {
    text: '导出中',
    className: 'yellow-tag'
  },
  2: {
    text: '导出中',
    className: 'yellow-tag'
  },
  3: {
    text: '导出成功',
    className: 'success-tag'
  },
  4: {
    text: '已下载',
    className: 'disable-tag'
  },
  5: {
    text: '导出失败',
    className: 'error-tag'
  }
} as any
