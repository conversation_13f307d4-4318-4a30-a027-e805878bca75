// 公用文件
import { useTheme, useGoods } from '@/hooks'
import dayjs from 'dayjs'
import { Setting, SettingEnv, SettingFileNames } from 'types'
import setting from '@setting/setting.json'
import { createVNode } from 'vue'
import { Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { isUndefined } from 'lodash-es'
/**
 * 获取项目配置文件 异步
 */
export async function getSetting(): Promise<Setting> {
  return setting as Setting
}

let configMap: { [key in string]: string } = {}
/**
 * 获取当前项目的环境变量配置
 * @param envName
 * @returns
 */
export function getConfig<K extends keyof SettingEnv>(envName: K): SettingEnv[K]
export function getConfig(): SettingEnv
export function getConfig<K extends keyof SettingEnv>(envName?: K): SettingEnv[K] | SettingEnv {
  try {
    if (configMap[envName]) return configMap[envName]
  } catch (error) {}
  const env = (process.env as unknown as SettingEnv) || ((import.meta.env.VITE_VAR || {}) as SettingEnv)
  if (envName !== undefined) {
    return env[envName]
  } else {
    // 如果 envName 不存在，则返回整个环境配置对象
    return env
  }
}
/**
 * 设置环境变量
 * @param envName
 * @returns
 */
export function setConfig<K extends keyof SettingEnv>(envName: K, value: SettingEnv[K]): void {
  if (!configMap[envName]) configMap[envName] = ''
  configMap[envName] = value
}
/**
 * TODO这个后期需要去掉注释 去掉判断 等下面人熟悉后
 * 判断第一个参数 聚好卖，第二个参数悦铃
 * @param args
 * @returns
 */
export function getSchema<T = any>(...args: any): T {
  if (getConfig('NODE_ENV') == 'dev') {
    // 顺序和babel插件里面的对应
    let obj: { [key in SettingEnv['PROJECT']]: Array<number> } = {
      jhm: [0],
      ylyx: [1],
      ldd: [2, 1],
      hyd: [3, 0],
      jq: [4, 2],
      private: [5, 2],
      xy: [6, 3]
    }
    let numberArr = obj[getConfig('PROJECT')]
    // 做兼容 比如量多多是基于悦邻的，那么量多多要没有的时候向下找悦邻严选
    // 同样babel 也需要做处理
    if (args.length <= 1) return args[0]
    else {
      let existIndex = null
      function cycle(numbers: number[] = []) {
        let keys = Object.keys(obj)
        if (!isUndefined(args[numbers[0]])) {
          existIndex = numbers[0]
          return
        } else if (!isUndefined(numbers[1])) {
          cycle(obj[keys[numbers[1]]])
        } else {
          existIndex = args[numberArr[0]] ? 0 : 1
          return
        }
      }
      cycle(numberArr)
      return args[existIndex]
    }
  }
  if (args.length > 0) {
    return args[0]
  }
  return args[0]
}

let fileMap: { [key in string]: string } = {}
/**
 * 获取系统静态文件，需要打包替换的logo等图片
 * @returns
 */
export function getSystemFile(name: SettingFileNames): string {
  if (fileMap[name]) {
    return fileMap[name]
  }
  const { themeConfig } = useTheme()
  let files = themeConfig.value.files
  let item = files.find((v) => v.name.replace(v.baseDir + '/', '') == name)
  if (item) {
    return new URL(`${window.location.origin}/${item.name}.${item.type}`, import.meta.url).href
  }
  console.error(`文件资源未找到${name}，请前往配置文件里面添加`)
  return ''
}

export function setSystemFile(name: SettingFileNames, value: string) {
  if (!fileMap[name]) fileMap[name] = ''
  fileMap[name] = value
}

/**
 * 获取筛选项日期快捷配置
 */
export function getDatePresetsOptions(item?: any) {
  const today = dayjs()
  const yesterday = dayjs().subtract(1, 'day')
  const last7DaysStart = dayjs().subtract(6, 'days').startOf('day')
  const last30DaysStart = dayjs().subtract(29, 'days').startOf('day')

  const thisMonthStart = dayjs().startOf('month')
  const last6MonthsStart = dayjs().subtract(6, 'months').startOf('month')
  const lastYearStart = dayjs().subtract(1, 'year')

  // 如果item.range 等于NearlyThirty
  let options = []
  if (item?.range && item.range === 'NearlyThirty') {
    options = [
      { label: '今天', value: [today.startOf('day'), today.endOf('day')] },
      { label: '昨天', value: [yesterday.startOf('day'), yesterday.endOf('day')] },
      { label: '近7天', value: [last7DaysStart, today.endOf('day')] },
      { label: '近30天', value: [last30DaysStart, today.endOf('day')] },
      { label: '本月', value: [thisMonthStart, today.endOf('day')] }
    ]
  } else {
    options = [
      { label: '今天', value: [today.startOf('day'), today.endOf('day')] },
      { label: '昨天', value: [yesterday.startOf('day'), yesterday.endOf('day')] },
      { label: '近7天', value: [last7DaysStart, today.endOf('day')] },
      { label: '近30天', value: [last30DaysStart, today.endOf('day')] },
      { label: '本月', value: [thisMonthStart, today.endOf('day')] },
      { label: '近半年', value: [last6MonthsStart, today.endOf('day')] },
      { label: '近一年', value: [lastYearStart, today.endOf('day')] }
    ]
  }
  return options
}

// 不能输入表情
export function noEmoji() {
  return /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/g
}
/**
 * 通过传入时间获取当前选择的Node节点
 */
export function getSelectTimeNode(time: string) {
  const selectTimeRowList = Array.from({ length: 49 }).map((_, i) => {
    return {
      num: i,
      text:
        i == 0
          ? '00:00'
          : `${i < 20 ? '0' + Math.ceil((i - 1) / 2) : Math.ceil((i - 1) / 2)}:${(i - 1) % 2 ? '0' : '3'}0`
    }
  })
  let [startTime, endTime] = time.split('-')
  // if (!startTime || !endTime) throw new Error('传入格式错误')
  let startIndex = 0,
    endIndex = 0

  selectTimeRowList.forEach((item, i) => {
    if (item.text == startTime) {
      console.log('ahahahah', i)
      startIndex = i >= 47 ? 47 : i
    }
    if (item.text == endTime) {
      endIndex = i >= 47 ? 47 : i
    }
  })
  return {
    nodes: Array.from({ length: 7 }).map((_) =>
      Array.from({ length: 48 }).map((_, i) => Number(i >= startIndex && i <= endIndex))
    ),
    startIndex,
    endIndex
  }
}
// 深拷贝
export const copyData = function (obj) {
  let newobj = null
  if (typeof obj == 'object') {
    // 判断是否是引用类型
    newobj = obj instanceof Array ? [] : {}
    for (var i in obj) {
      newobj[i] = copyData(obj[i])
    }
  } else {
    newobj = obj
  }
  return newobj
}

// 保留2位小数
export const toFixed2 = (num) => {
  if (num === null || num === undefined || num === '') {
    return ''
  }
  return parseFloat(num).toFixed(2)
}

// 手机号脱敏
export const handlePhone = (phone: any, flag: boolean) => {
  if (flag) return phone
  if (!phone) return ''
  return (phone + '').replace(/(\d{3})\d*(\d{4})/, '$1****$2')
}

// 分转元单位
// export function centsToYuan(v: any) {
//   if (isNaN(v)) {
//     return ''
//   }
//   v = v / 100
//   if (/^\d+$/.test(v)) {
//     return v + '.00'
//   }
//   // if (v.toString().toFixed) {
//   //   return v.toString().toFixed(2);
//   // } else {
//   //   return String(v);
//   // }
//   if (v.toFixed) {
//     return v.toFixed(2)
//   } else {
//     return String(v)
//   }
// }

// export function formatDate(str: any, tpl = 'YYYY-MM-DD HH:mm:ss') {
//   if (!str) return ''
//   // 返回格式化后的字符串, str: Date类型数据
//   return moment(str).format(tpl)
// }

/**
 * 浏览器滚动到指定位置
 * @param {Element} element
 */
export function scrollToElement(element) {
  const counter = useGoods()

  if (counter.state.value.scrollEl) {
    counter.state.value.scrollEl.scrollTo({
      behavior: 'smooth',
      left: 0,
      top: element.offsetTop - 80
    })
  }
}
/**
 * 浏览器滚动到指定位置
 * @param {Element} element
 */
// export function scrollToElement(element) {
//   const dom = document.getElementById('main')
//   console.log('-------', dom)

//   dom.scrollTo({
//     behavior: 'smooth',
//     left: 0,
//     top: element.offsetTop - 173
//   })
// }

// 元转分
export function yuanToCents(v) {
  if (isNaN(v)) {
    return ''
  }
  return parseInt(v * 100 + 0.5)
}

// 自定义消息确认
export function messageConfirm(options) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: options.title || '',
      icon: createVNode(ExclamationCircleOutlined),
      content: options.content || '',
      ...options,
      onOk() {
        resolve('confirm')
      },
      onCancel() {
        reject('cancel')
      }
    })
  })
}
export const convertToImg = (mp4Url: any) => {
  const fileName = mp4Url.split('/').pop()
  const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)

  let upload_type = getConfig('UPLOAD_TYPE') || 'oss'
  let img
  if (upload_type == 'cos') {
    img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_0.jpg`
  } else if (upload_type == 'oss') {
    img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_1.jpg`
  } else {
    img = ''
  }

  return img
}
