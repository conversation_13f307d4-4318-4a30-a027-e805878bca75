/**
 * // 将秒转化为年月日时分秒
 * @param seconds 时间戳 单位秒
 * @returns xxxx-xx-xx xx:xx:xx
 */
export function secToTime(seconds) {
  if (!seconds) return '--'
  let date = new Date(seconds * 1000)
  let year = date.getFullYear()
  let month = date.getMonth() + 1
  let day = date.getDate()
  let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  if (month < 10) month = `0${month}`
  if (day < 10) day = `0${day}`
  let currentTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second
  return currentTime
}
