export const getFileMimeType = (file, typeArray) => {
  const reader = new FileReader()
  reader.readAsArrayBuffer(file)
  return new Promise((resolve, reject) => {
    reader.onload = (event) => {
      try {
        let buffer = [...Buffer.from(event.target.result)]
        buffer = buffer.splice(0, 4)
        buffer.forEach((num, i, arr) => {
          arr[i] = num.toString(16).padStart(2, '0')
        })
        let lowerCaseTypeArray = []
        for (let item of typeArray) {
          item = item.toLowerCase()
          lowerCaseTypeArray.push(item)
        }
        resolve(lowerCaseTypeArray.includes(buffer.join('').toLowerCase))
      } catch (e) {
        // 读取文件头出错 默认不是合法文件类型
        reject()
      }
    }
  })
}
