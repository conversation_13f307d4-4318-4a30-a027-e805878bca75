// 分转元单位
export function centsToYuan(v: any) {
  if (isNaN(v)) {
    return ''
  }
  v = v / 100
  if (/^\d+$/.test(v)) {
    return v + '.00'
  }
  // if (v.toString().toFixed) {
  //   return v.toString().toFixed(2);
  // } else {
  //   return String(v);
  // }
  if (v.toFixed) {
    return v.toFixed(2)
  } else {
    return String(v)
  }
}

/**
 * @param 金额元转千，保留2位小数
 */

export const convertAmountToThousand = (amount: number) => {
  var convertedAmountFloat = null
  if (amount > 100000) {
    var convertedAmount = (amount / 1000).toFixed(2) // 将原始金额除以1000并保留两位小数

    // 转换后的金额可以以浮点型（float）或者字符串型（string）进行存储
    convertedAmountFloat = parseFloat(convertedAmount) + 'K'
  } else {
    convertedAmountFloat = amount
  }

  return convertedAmountFloat // 返回转换后的金额（浮点型）
}

/**
 * 判断是否手机登录
 */
export function isMobileDevice() {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = ['android', 'iphone', 'ipad', 'windows phone']

  for (const keyword of mobileKeywords) {
    if (userAgent.includes(keyword)) {
      return true // 包含移动设备关键词，判断为手机登录
    }
  }

  return false
  // return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 超长文本中间截断...
 */
export function getShowTitle(title: any) {
  if (title.length <= 20) return title
  return `${title.slice(0, 3)}...${title.slice(-3)}`
}
