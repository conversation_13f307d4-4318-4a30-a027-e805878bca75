import { message } from 'ant-design-vue'
import { isArray, isObject } from 'lodash-es'
import moment from 'moment'
import useClipboard from 'vue-clipboard3'

const { toClipboard } = useClipboard()
export const urlRegex = /^https?:\/\/[^\s/$.?#].[^\s]*$/
/**
 * 将小驼峰转化为中划线模式
 * @param str
 * @returns
 */
export function camelCaseToDash(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
}
/**
 * 将中划线转为小驼峰模式
 * @param str
 * @returns
 */
export function dashToCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (match, char) => char.toUpperCase())
}
/**
 * 获取字符串数组重复部分
 * @param { string[] } arr
 * @returns
 */
export function findDuplicates(arr: string[]) {
  const duplicates = []
  const seen: any = {}

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (seen[item] !== undefined) {
      if (seen[item] === 1) {
        duplicates.push(item)
      }
      seen[item] = seen[item] + 1
    } else {
      seen[item] = 1
    }
  }

  return duplicates
}

/**
 * 延迟多久返回
 * @param ms 毫秒
 * @returns
 */
export function delay(ms: number) {
  return new Promise<void>((reslove) => {
    setTimeout(() => {
      reslove()
    }, ms || 500)
  })
}
/**
 * 判断是图片还是视频格式
 * @param {string} url
 * @returns {Object}
 */
export function checkFileFormat(fileValue: string, type: string | undefined) {
  var index = fileValue.lastIndexOf('.')
  const fileValueSuffix = fileValue.substring(index) // 截断"."之前的，得到后缀
  // /(.*)\.(mp4|rmvb|avi|ts)$/
  if (type == 'noGif') {
    if (/(.*)\.(jpg|jpeg|png|JPG|PNG)$/.test(fileValueSuffix)) {
      // 根据后缀，判断是否符合图片格式
      return 'noGif'
    }
    return
  }
  if (/(.*)\.(mp4)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合视频格式
    return 'video'
  }
  if (/(.*)\.(mp3|wma|wav|amr|m4a)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合视频格式
    return 'audio'
  }
  if (/(.*)\.(gif|jpg|jpeg|png|GIF|JPG|PNG)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合图片格式
    return 'image'
  }

  return false
}
export const debounce = function (fun: { (): void; apply?: any }, immediate: number, duration: number | undefined) {
  duration = duration || 1000
  let timer: number | null | undefined = null
  return () => {
    const that = this
    const args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    if (immediate) {
      const callNow = !timer
      if (callNow) {
        fun.apply(that, args)
      }
      timer = setTimeout(() => {
        timer = null
      }, duration)
    } else {
      timer = setTimeout(() => {
        fun.apply(that, args)
      }, duration)
    }
  }
}

export const throttle = function (fn: { call: (arg0: any, arg1: any) => void }, duration = 1000) {
  // 节流函数
  let last: number
  let timer: number | null | undefined = null
  let now
  return () => {
    if (timer === null) {
      last = new Date().getTime()
      timer = setTimeout(() => {
        timer = null
        fn.call(this, ...arguments)
      }, duration)
    } else {
      now = new Date().getTime()
      if (now - last <= duration) {
        clearTimeout(timer)
        last = now
        timer = setTimeout(() => {
          timer = null
          fn.call(this, ...arguments)
        }, duration)
      }
    }
  }
}

/**
 * 通过图片地址获取图片信息
 * @param {Array} list 图片地址
 */
export async function getImageInfoByPath(list: string | any[]) {
  const result = []
  for (let i = 0; i < list.length; i++) {
    let info = await new Promise((resolve, reject) => {
      const v = list[i]
      let imgDom = document.createElement('img')
      imgDom.src = v
      imgDom.onload = function (e) {
        const fileType = v.split('.')[v.split('.').length - 1]
        resolve({
          url: v,
          width: imgDom.width,
          height: imgDom.height,
          mph: Number(((imgDom.width / 750) * imgDom.height).toFixed(2)),
          type: fileType
        })
      }
      imgDom.onerror = function (e) {
        console.log('img load error', e)
        reject(new Error('请上传正确的图片'))
      }
    })
    result.push(info)
  }
  return result
}

// 动态引入图片
export const requireImg = (image: any) => {
  return new URL(`../assets/images/${image}`, import.meta.url).href
}
// 动态引入图片
export const requireNewImg = (image: any) => {
  return new URL(`../assets/lddImages/${image}`, import.meta.url).href
}
// 格式化时间
export function formatDate(str: any, tpl = 'YYYY-MM-DD HH:mm:ss') {
  if (!str) return ''
  // 返回格式化后的字符串, str: Date类型数据
  return moment(str).format(tpl)
}
// 去除对象空属性
export function cleanObject(data: any) {
  // 遍历对象的键值对
  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === undefined || value === '') {
      // 删除值为 null、undefined 或空字符串的属性
      delete data[key]
    } else if (typeof value === 'string') {
      // 对字符串类型的值进行去除首尾空格的操作
      data[key] = value.trim()
    }
  }
  return data
}

// 复制
export const copy = async (text: string, cb?: () => any) => {
  try {
    await toClipboard(text)
    cb ? cb() : message.success('复制成功！')
  } catch (error) {
    message.error('复制失败！')
  }
}

/**
 * 保留两位小数
 * @param {Number} num
 * @return number
 */
export const toDecimal = (num: any) => {
  let result = parseFloat(num)
  if (isNaN(result)) return result
  result = Math.floor(num * 100) / 100
  return result
}

//处理省市区参数
export const arrToAreas = (arr) => {
  let obj = {}
  ;[...new Set(arr.map((v) => v[0]))].forEach((v) => {
    obj[v] = {}
  })
  arr.forEach((v) => {
    let item = obj[v[0]]
    if (v[1]) {
      item[v[1]] = item[v[1]] || []
      if (v[2]) {
        item[v[1]].push(Number(v[2]))
      }
    }
  })
  return obj
}

/**
 * table表格排序 返回规则
 */
export function tablePropSort(row: any) {
  let text = ''
  switch (row.order) {
    case 'ascend':
      text = 'asc'
      break
    case 'descend':
      text = 'desc'
      break
    default:
      text = ''
      break
  }
  return text
}

//生成随机字符 randomFlag 是否任意长度 min 任意长度最小位[固定位数] max 任意长度最大位
export const randomWord = (randomFlag: boolean, min: number, max?: number) => {
  let str = '',
    range = min,
    arr = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z',
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'I',
      'J',
      'K',
      'L',
      'M',
      'N',
      'O',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z'
    ]

  if (randomFlag) {
    range = Math.round(Math.random() * (max - min)) + min // 任意长度
  }
  for (let i = 0; i < range; i++) {
    let pos = Math.round(Math.random() * (arr.length - 1))
    str += arr[pos]
  }
  return str
}

/**
 * 多维数组对象的扁平化
 * @param data 扁平化的数组
 * @param fieldNames 子节点配置项
 * @returns []
 */
export const flatten = (data: any[], fieldNames = { children: 'children' }) => {
  let result = []
  if (!isArray(data)) return result
  data.forEach((v) => {
    if (isArray(v[fieldNames.children])) {
      result.push(v)
      result = result.concat(flatten(v[fieldNames.children], { children: fieldNames.children }))
    } else {
      result.push(v)
    }
  })
  return result
}

// 过滤对象中的无效数据
export const filterValueField = (data) => {
  const params = {}
  if (isObject(data)) {
    Object.keys(data).forEach((key) => {
      if (data[key] !== undefined && data[key] !== null) {
        params[key] = data[key]
      }
    })
  }
  return params
}

// 获取路由中的query参数
export const getFullPathQuery = (fullPath: any) => {
  if (['null', 'undefined', null, undefined, ''].includes(fullPath) || fullPath.indexOf('?') === -1) {
    return false
  }
  const reArr = fullPath.split('?')
  const rePath = reArr[0]
  const reQueryArr = reArr[1].split('&')
  const reQuery = {}
  for (var i = 0; i < reQueryArr.length; i++) {
    ;(reQuery as any)[reQueryArr[i].split('=')[0]] = reQueryArr[i].split('=')[1]
  }
  return { path: rePath, query: reQuery }
}

// 获取路由中的query参数, 为解决对象对自身的循环引用
export const getJsonStringify = (data) => {
  let cache = []
  const json_str = JSON.stringify(data, function (key, value) {
    if (typeof value === 'object' && value !== null) {
      if (cache.indexOf(value) !== -1) {
        return
      }
      cache.push(value)
    }
    return value
  })
  cache = null
  return json_str
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time: any, cFormat: string | null) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

export function getPageNameFromUrl(url: any) {
  // 通过分割URL路径获取最后一段
  const pathParts = url.split('/')
  const lastSegment = pathParts[pathParts.length - 1]

  // 去除.html后缀（如果有的话）
  return lastSegment.split('.html')[0]
}

/**
 * 将字符串中的换行符替换为逗号，并去除多余换行和空行
 * @param str 输入字符串
 * @returns {string} 替换后的字符串
 */
export const replaceNewlineWithComma = (str: string): string => {
  return str?.replace(/\n+/g, ',').replace(/^,+|,+$/g, '')
}

/**
 * 将字符串中的逗号替换为换行符
 * @param str 输入字符串
 * @returns {string} 替换后的字符串
 */
export const replaceCommaWithNewline = (str: string): string => {
  return str?.replace(/,/g, '\n')
}

// 生成6位随机数字验证码
export function generateVerificationCode(): string {
  // 生成一个 0 到 999999 之间的随机数
  const code = Math.floor(Math.random() * 1000000)
  // 确保是 6 位数
  return code.toString().padStart(6, '0')
}

/**
 * 延迟执行  默认阻塞20ms
 * @param {*} value
 * @returns
 */
export const sleep = (value = 20) => {
  return new Promise((resolve) => {
    let timer = setTimeout(() => {
      resolve(1)
      clearTimeout(timer)
    }, value)
  })
}

// 秒转 x小时x分钟x秒
export const secondsToTime = (seconds: number) => {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    return '--'
  }
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const second = seconds % 60
  let result = ''
  if (hours > 0) {
    result += `${hours}小时`
  }
  if (minutes > 0) {
    result += `${minutes}分钟`
  }
  if (second > 0) {
    result += `${second}秒`
  }
  return result || '0秒'
}
