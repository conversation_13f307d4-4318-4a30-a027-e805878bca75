import { requireImg } from './method'

// 回传状态
export const callbackStatusParams = [
  {
    label: '未转化',
    value: 1,
    color: '#B6AFAF',
    cls: 'disable-tag'
  },
  {
    label: '已回传',
    value: 2,
    color: '#52C41A',
    cls: 'success-tag'
  },
  {
    label: '转化失败',
    value: 3,
    color: '#FF4D4F',
    cls: 'error-tag'
  },
  {
    label: '未回传',
    value: 4,
    color: '#B6AFAF',
    cls: 'yellow-tag'
  },
  {
    label: '手动上报',
    value: 5,
    color: '#52C41A',
    cls: 'disable-tag'
  },
  {
    label: '转化黑名单',
    value: 6,
    color: '#FF4D4F',
    cls: 'error-tag'
  },
  {
    label: '云盾',
    value: 7,
    color: '#1677FF',
    cls: 'blue-tag'
  }
]

export const callbackStatus2Content = (status: any): any => {
  let content = callbackStatusParams.find((it) => it.value === status)
  return content || {}
}

export const mediaType = [
  // {
  //   label: '全部',
  //   value: ''
  // },
  {
    label: '巨量引擎',
    value: 3,
    icon: requireImg('media/jl.png')
  },
  {
    label: '广点通',
    value: 1,
    icon: requireImg('media/gdt.png')
  }
  // {
  //   label: '磁力引擎',
  //   value: 2,
  //   icon: requireImg('media/cili.png')
  // },
]
export const mediaTypeSearch = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '巨量引擎',
    value: 3
  },
  {
    label: '广点通',
    value: 1
  }
  // {
  //   label: '磁力引擎',
  //   value: 2,
  //   icon: requireImg('media/cili.png')
  // },
]

export const shopType = [
  {
    label: '淘宝',
    value: 1,
    icon: requireImg('base/tb.png')
  },
  {
    label: '拼多多',
    value: 2,
    icon: requireImg('base/pdd.png')
  },
  {
    label: '京东',
    value: 3,
    icon: requireImg('base/jd.png')
  }
]
export const GdshopType = [
  {
    label: '拼多多',
    value: 2,
    icon: requireImg('base/pdd.png')
  },
  {
    label: '微信小程序商城',
    value: 4,
    icon: requireImg('base/we_chat.png')
  },
  {
    label: '美团',
    value: 5,
    icon: requireImg('base/mei_tuan.png')
  }
]

export const callbackLimit = [
  // {
  //   label: '不限制',
  //   value: 1
  // },
  {
    label: '访问时长',
    value: 2
  },
  {
    label: '答题时长',
    value: 3
  }
]

export const mediaType2Content = (status: any) => {
  let content = mediaType.find((it) => it.value === status)
  return content || {}
}

export const delete_statusList = [
  {
    label: '未删除',
    value: 1,
    color: '#52C41A'
  },
  {
    label: '客服删除客户',
    value: 2,
    color: '#FF4D4F'
  },
  {
    label: '客户删除客服',
    value: 3,
    color: '#FF4D4F'
  }
]
export const delStatusContent = (status: any): any => {
  let content = delete_statusList.find((it) => it.value === status)
  return content || {}
}
export const resTagList = [
  {
    label: '异常用户',
    value: 1,
    color: '#FF4D4F'
  },
  {
    label: '红包党',
    value: 2,
    color: '#FF4D4F'
  },
  {
    label: '羊毛党',
    value: 3,
    color: '#FF4D4F'
  },
  {
    label: '其他',
    value: 4,
    color: '#FF4D4F'
  }
]
export const resTagListContent = (status: any): any => {
  let content = resTagList.find((it) => it.value === status)
  return content || {}
}
