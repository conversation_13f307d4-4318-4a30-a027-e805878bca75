import axios, { AxiosRequestConfig, InternalAxiosRequestConfig, Method } from 'axios'
import { localStg } from './storage'
import { getConfig } from './common'
import { message } from 'ant-design-vue'
import { useLogin, useRouter } from '@/hooks'
import { Modal } from 'ant-design-vue'
import { cleanObject } from './method'

const instance = axios.create({
  timeout: 20000
})
let isUpdateModalShow = false
// 添加请求拦截器
instance.interceptors.request.use(
  function (config) {
    if (localStg.get('token') && !config.doNotToken) {
      config.headers['Authorization'] = 'Bearer ' + localStg.get('token')
    }
    config.timeout = config?.timeout || 20000
    // 代理添加
    if (getConfig('IS_PROXY')) {
      let proxyUrl = getConfig('PROXY_URI')
      if (proxyUrl instanceof String) {
        config.url = proxyUrl + (config.url || '/')
      } else if (proxyUrl instanceof Array && proxyUrl.every((substring) => !(config.url || '/').includes(substring))) {
        // 开启了代理 代理为列表，并且当前请求地址 不在代理列表里的时候应该默认加上数组的第一个值做为代理请求头
        config.url = proxyUrl[0] + (config.url || '/')
      }
    }
    // 正式 测试环境添加api前缀
    if (['test', 'prod', 'release'].includes(getConfig('NODE_ENV'))) {
      config.url = getConfig('API_URL') + config.url
    }

    // 添加权限页面需要的header
    config.headers['Code'] = localStg.get('code')
    // 添加版本号
    config.headers = {
      ...(config.headers || {}),
      'User-Version': import.meta.env.VITE_APP_VERSION
    }
    // if (config.method == 'get') {
    //   config.params = {
    //     ...config.params,
    //     version: import.meta.env.VITE_APP_VERSION
    //   }
    // } else {
    //   config.url = config.url?.includes('?')
    //     ? config.url + '&version=' + import.meta.env.VITE_APP_VERSION
    //     : config.url + '?version=' + import.meta.env.VITE_APP_VERSION
    // }

    config = disposeParams(config)

    return config
  },
  function (error) {
    // 对请求错误做些什么
    return Promise.reject({ type: 'error', msg: '请求错误,请稍后重试' })
  }
)
// 添加响应拦截器
instance.interceptors.response.use(
  async function (response) {
    // 对响应数据做点什么

    const errCode = [0, 200, 10013, 10015, 10000, 40003, 40002, 40004, 40102, 40004, 40005, 40006]
    // 强制更新弹框弹出
    if (response.data.code === 10002 && !isUpdateModalShow) {
      isUpdateModalShow = true
      Modal.warning({
        title: '提示',
        content: '系统有最新版本，请手动刷新网页',
        okText: '确认',
        async onOk() {
          window.location.reload(true)
        }
      })
    }
    if (!errCode.includes(response.data.code)) {
      // 全局提示
      console.warn(
        '公共网络请求：',
        response,
        response.data.msg,
        response.config.headers.get('Authorization'),
        '-----------\n',
        localStg.get('token')
      )
      if (
        response.config.headers.get('Authorization') == 'Bearer ' + localStg.get('token') ||
        !response.config.headers.get('Authorization')
      ) {
        const { forceLogout, logout } = useLogin()
        if ([401].includes(response.data.code)) {
          if (response.config.url?.indexOf('login_out') > -1) {
            return response.data
          } else {
            // 被顶下来
            if (response.data.data) {
              if (!localStg.get('isForceLogout')) {
                console.log('isForceLogout')
                localStg.set('isForceLogout', response.data.data || 'yes')

                await forceLogout(response.data.data)
                await logout()
              }
            } else {
              return await noAuth()
            }
          }
        }
        if ([405].includes(response.data.code)) {
          message.warning(response.data.msg)
          var { routerPush } = useRouter()
          localStg.clear()
          routerPush({ name: 'Login' })
          return
        }
        if (localStg.get('isForceLogout')) return false
        response.data.code != 10002 && !response.config.noTip && message.warning(response.data.msg)
        return Promise.reject({ type: 'warning', code: response.data.code, msg: response.data.msg })
      } else {
        return Promise.reject({ type: 'success', code: response.data.code, msg: response.data.msg })
      }
    }
    return response.data
  },
  async function (error) {
    // 401处理
    if (error.response.status == 4001) {
      return await noAuth()
    }

    // 401处理 退出登录返回登录页
    if (/401/.test(error.message)) {
      return await noAuth()
    }
    message.error('网络错误,请稍后重试')
    // 对响应错误做点什么
    return Promise.reject({ type: 'error', msg: '网络错误,请稍后重试' })
  }
)
/**
 * 接口请求
 * @param {*} method
 * @param {*} url
 * @param {*} data
 * @param {Object} extraConfig 额外配置 传入对象
 * @returns
 */
const http = (method: Method, url: string, data: any, extraConfig: object = {}) => {
  let params: AxiosRequestConfig<any> = {
    method,
    url,
    ...extraConfig
  }

  if (method === 'post') {
    params.data = data
  } else {
    params.params = data
  }
  return instance(params)
}

const noAuth = async () => {
  const { logout } = useLogin()
  // message.error('登录凭证过期')
  // token 失效时
  // 要跳转的页面
  const toPathData = localStg.get('current-route')
  if (toPathData) {
    const result = JSON.parse(toPathData).to
    localStg.set('redirect', encodeURIComponent(result.fullPath))
  }
  await logout()
  return await Promise.reject({ type: 'error', msg: '登录凭证过期' })
}

/**
 * 全局处理get post请求传参携带空格
 * @param config
 */
const disposeParams = (config: InternalAxiosRequestConfig<any>) => {
  // 处理所有请求参数的前后空格
  const params = config.params || {}
  cleanObject(params)

  // 处理所有请求体的前后空格
  const data = config.data || {}
  cleanObject(data)

  return config
}

export default http
