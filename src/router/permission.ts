import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useLogin, useMenu, usePageDialog } from '@/hooks'
import { TacticsAction } from 'types'
import { exeStrategyActions, localStg, getJsonStringify } from '@/utils'
import { defineAsyncComponent } from 'vue'

function recursionGet(setValues, valueId) {
  let value = null
  for (let index = 0; index < setValues.length; index += 1) {
    if (setValues[index].name === valueId) {
      value = setValues[index]
      break
    }
    if (setValues[index].children instanceof Array && setValues[index].children.length > 0) {
      const text = recursionGet(setValues[index].children, valueId)
      if (text) return text
    }
  }
  return value
}
const setModal = (component, data) => {
  const { setOpen, setComponent, setOptions } = usePageDialog()
  const com = defineAsyncComponent(component)
  setComponent(com)
  setOpen(true)
  setOptions(data)
}
/** 处理路由页面的权限 */
export async function createPermissionGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  if (!['LoginIndex'].includes(to.name)) {
    const { fullPath, href, name, params, path, query } = to
    const paramsData = { to: { fullPath, href, name, params, path, query } }
    const result = getJsonStringify(paramsData)
    localStg.set('current-route', result)
  }

  const { isLogin, login, logout } = useLogin()
  const { menuDataMap } = useMenu()

  const actions: Array<TacticsAction> = [
    // 首次进入，如果有token，并且前往登录页的情况下，返回到登录页
    {
      term: !isLogin.value && Boolean(localStg.get('token')) && to.path.includes('/login'),
      event: async () => {
        return next({ path: '/' })
      }
    },
    // 不需要任何权限的页面直接进入 不进行操作
    {
      term: to.meta.noAuth,
      event: async () => {
        next()
      }
    },
    {
      term: !isLogin.value && localStg.get('token'),
      event: async () => {
        await login({ token: localStg.get('token') })
        localStg.remove('isForceLogout')
        return next({ ...to, replace: true })
      }
    },
    // 没有登陆 并且不在登录页，返回登陆页
    {
      term: !isLogin.value && !to.path.includes('/login'),
      event: () => {
        const toPathData = localStg.get('current-route')
        if (toPathData) {
          const result = JSON.parse(toPathData).to
          localStg.set('redirect', encodeURIComponent(result.fullPath))
        }
        logout()
        return next({ path: '/login' })
      }
    },
    //登陆成功 进入没有权限的页面，或者不是指定路由的页面跳转到404页面
    {
      term: isLogin.value && !menuDataMap.has(to.name as string),
      event: () => next({ name: '404' })
    },
    {
      term: true,
      event: () => {
        if (to?.meta?.dialog === 2 || to?.meta?.dialog === '2') {
          to.meta.dia = true
        }
        if (to?.meta?.dia && !from.query?.dia) {
          console.log('默认1111')
          const curtt = to.matched.find((v) => v.path == to.path)
          setModal(curtt.components?.default, to.meta)
          next({ path: from.path, replace: true, query: { dia: true, cname: to.name, ...to.query } })
        } else if (to?.query?.dia) {
          //处理刷新
          const data = recursionGet(to.matched, to.query.cname)
          setModal(data.component, data.meta)
          next()
        } else {
          console.log('默认兜底')
          const { setOpen } = usePageDialog()
          setOpen(false)
          next()
        }
        // next()
      }
    }
  ]

  await exeStrategyActions(actions)
}
