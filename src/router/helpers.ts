import { useRouter } from '@/hooks'
import { MenuData, RouterMap, RouterMenu, RouterType } from 'types'
import { findDuplicates, getConfig } from '@/utils'
import { buildRouterTree, requestMapLocalRouter, sortRequestRouter } from './disponneRouter'
import Layout, { EmptyLayout } from '@/layout'
import { cloneDeep, isArray } from 'lodash-es'
import { RouteRecordRaw, Router, RouterScrollBehavior } from 'vue-router'
import { Component } from 'vue'
import routerList from '../../setting/router'

/**
 * 获取所有路由的树状图
 */
export const getRouters: () => Promise<Array<RouterMenu>> = async () => {
  console.log(cloneDeep(routerList))
  const list = routerList
  // 判断有没有重复的路由
  const multipleNames: string[] = findDuplicates(list.map((v) => v.content.name))
  if (multipleNames.length) console.error(`${multipleNames.join(' | ')}路由重复，请修改名称`)

  const allRouterList = buildRouterTree(list)
  // 添加默认的父级路由
  const mode: any = { Layout: getBaseRouter(allRouterList, Layout), Empty: [] }
  allRouterList.forEach((v) => {
    if (!v.mode || v.mode == 'Layout') {
      mode['Layout'].children.push(v)
    } else if (v.mode == 'Empty') {
      mode['Empty'] = [...mode['Empty'], v]
    }
  })
  let realList: Array<RouterMenu> = []
  Object.values(mode).forEach((v) => {
    if (isArray(v)) realList = [...realList, ...v]
    else if (v) realList.push(v as RouterMenu)
  })

  return realList
}

/**
 * 获取路由数据中所有默认路由，初始化时候添加到路由列表中
 */
export function getBaseRouterList(routerList: Array<RouterMenu>): Array<RouterMenu> {
  const list: Array<RouterMenu> = []
  function _cycel(arr: Array<RouterMenu>) {
    arr.forEach((v) => {
      if (v.isBase) {
        if (v.children && v.children.length) {
          v.children = v.children.filter((g) => g.isBase)
          _cycel(v.children)
        } else {
          v.children = []
        }
        list.push(v)
      }
    })
  }
  _cycel(cloneDeep(routerList))
  return list
}

/**
 * 设置根路由
 */
function getBaseRouter(routerList: Array<RouterMenu>, component: Component): RouterMenu {
  return {
    name: 'Index',
    path: '/',
    // redirect: getOnePage(routerList),
    component: component || Layout,
    isBase: true,
    meta: {
      title: '首页'
    },
    children: []
  }
}

/**
 * 获取默认第一个显示的页面
 * TODO 如果配置的有应该优先使用配置的，顺序 接口获取 > env > 匹配
 */
export function getOnePage(routerList: Array<RouterMenu>): RouterType['path'] {
  let path: RouterType['path'] = '/404'
  for (let i = 0; i < routerList.length; i++) {
    if (!routerList[i].isBase) {
      path = routerList[i].redirect || (routerList[i].path as RouterType['path'])
      break
    }
  }
  return path
}

/**
 * 根据路由返回的页面权限列表动态添加权限路由
 * TODO 菜单停用暂时未实现
 */
export function authRouter(names: Array<RouterType['name']>, router: Router, requestRouterMap: Map<string, any>) {
  const { routerList, setRouterMap } = useRouter()
  const list: Array<RouterMenu> = []
  let routerMap: RouterMap = new Map()
  function _cycel(arr: Array<RouterMenu>) {
    arr.forEach((v) => {
      if (v.children && v.children.length) {
        v.children = v.children
          .filter((g) => g.isBase || names.includes(g.name as RouterType['name']))
          .map((g) => requestMapLocalRouter(g, requestRouterMap))

        // 设置默认重定向以防重定向页面不存在
        let redirectItem = v.children.find((c) => c.path == v.redirect)
        if (!redirectItem || !redirectItem.meta.isHide) {
          v.redirect = sortRequestRouter(v.children).find((c) => !c.meta.isHide)?.path
        }

        _cycel(v.children)
      } else {
        v.children = []
      }
      // 处理本地路由信息是否要根据接口数据进行改变
      let item = requestMapLocalRouter(v, requestRouterMap)
      routerMap.set(v.name as RouterType['name'], item)
      list.push(item)
    })
  }
  _cycel(cloneDeep(routerList.value))
  resetRouter(routerMap, router)
  setRouterMap(routerMap)
  addRouter(cloneDeep(list), router)
  console.log('处理过后的路由：', cloneDeep(list))
  console.log('老的数据', cloneDeep(routerList.value))
  // TODO这个地方后期使用菜单权限的时候需要返回list 现在只是用于开发返回的时所有的路由权限
  // 也可以添加一个配置 在开发环境 返回所有变量
  return cloneDeep(list)
}

/**
 * 添加路由
 */
export function addRouter(list: Array<RouterMenu>, router: Router) {
  function _cycel(arr: Array<RouterMenu>) {
    arr.forEach((v) => {
      if (v.component == 'self') {
        v.component = EmptyLayout
      }
      if (v.children && v.children.length) {
        _cycel(v.children)
      } else {
        v.children = []
      }
      router.hasRoute(v.name) && router.removeRoute(v.name)
      router.addRoute(v as unknown as RouteRecordRaw)
    })
  }
  _cycel(cloneDeep(list))
}

/**
 * 重置路由保留固定路由
 */
const resetRouter = (routerMap: RouterMap, router: Router) => {
  const routes = router.getRoutes()
  routes.forEach((route) => {
    if (route.name) {
      let item = routerMap.get(route.name as RouterType['name'])
      if (!item || !item.isBase) {
        router.removeRoute(route.name)
      }
    }
  })
}

/**
 * 页面切换时候的处理
 * TODO后期需要加 可视区域滚动到顶部
 * @param to
 * @param from
 * @returns
 */
export const scrollBehavior: RouterScrollBehavior = (to, from, savedPosition) => {
  if (savedPosition) {
    return savedPosition
  } else {
    return { top: 0 }
  }
}
