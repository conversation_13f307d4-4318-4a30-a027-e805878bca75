import type { Router } from 'vue-router'
import { createPermissionGuard } from './permission'
import { useTitle } from '@vueuse/core'
import { getConfig, localStg } from '@/utils'
import { useRouter, useGlobalLoading } from '@/hooks'

const { setGlobalLoading } = useGlobalLoading()

/**
 * 路由守卫函数
 * @param router - 路由实例
 */
export function createRouterGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    window.scrollTo(0, 0)
    // 设置头部
    if (to.meta.title) {
      useTitle((to.meta.title as string) + ' - ' + getConfig('TITLE'))
    }

    // 切换路由时关闭全局 loading
    setGlobalLoading(false)

    // 页面跳转前添加请求所需要的权限
    const { routerMap } = useRouter()
    if (!to.meta.noAuth) {
      let item = routerMap.get(to.name as string)
      if (item && item.meta.code) {
        localStg.set('code', item.meta.code)
      } else {
        localStg.set('code', '')
      }
    }
    // 页面跳转权限处理
    await createPermissionGuard(to, from, next)
  })
}
