import { createRouter, createWebHashHistory } from 'vue-router'
import { addRouter, getBaseRouterList, getRouters, scrollBehavior } from './helpers'
import { App } from 'vue'
import { useRouter } from '@/hooks'
import { createRouterGuard } from './guard'
import { message } from 'ant-design-vue'
export const router = createRouter({
  history: createWebHashHistory(), // 本项目采用了哈希模式
  routes: [],
  scrollBehavior
})
// 保存原始的 router.push 方法
const originalPush = router.push

// 重写 router.push 方法
router.push = function push(location) {
  try {
    return originalPush.call(this, location).catch((err) => {
      return Promise.reject(err) // 继续抛出错误以便在调用处处理
    })
  } catch (error) {
    return message.warning('暂无权限，请联系管理员')
  }
}
/** setup vue router. - [安装vue路由] */
export async function setupRouter(app: App) {
  const { setRouterList, routerList } = useRouter()
  setRouterList(await getRouters())
  // 添加默认路由
  addRouter(getBaseRouterList(routerList.value), router)

  createRouterGuard(router)

  app.use(router)
  await router.isReady()
}
