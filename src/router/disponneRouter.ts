import { RouterMap, RouterMenu, RouterType } from 'types'
import { cloneDeep } from 'lodash-es'
/**
 * 摊平路由
 * @param {RouterMenu[]} data
 * @returns
 */
export function flattenRouterData(data: RouterMenu[]): RouterMenu[] {
  const flattenedData: RouterMenu[] = []

  data.forEach((item) => {
    const flattenedItem: RouterMenu = {
      name: item.name,
      path: item.path,
      component: item.component,
      meta: item.meta,
      redirect: item.redirect,
      mode: item.mode,
      isBase: Boolean(item.isBase)
    }

    flattenedData.push(flattenedItem)

    if (item.children) {
      flattenedData.push(...flattenRouterData(item.children))
    }
  })

  return flattenedData
}

type TreeNode = { filePath: string; content: RouterMenu; children?: Array<TreeNode> }

/**把获取的文件转化为树状图 */
export function buildRouterTree(data: Array<TreeNode>): RouterMenu[] {
  const _data = cloneDeep(data)
  const tree: TreeNode = {
    filePath: '/',
    content: {} as RouterMenu,
    children: []
  }

  _data.forEach((entry) => {
    const filePathLevels = entry.filePath.split('/').filter((level) => level !== '')

    let currentLevel = tree

    filePathLevels.forEach((level) => {
      if (!currentLevel.children) {
        currentLevel.children = []
      }
      const existingNode = currentLevel.children.find((node) => node.filePath === `/${level}`)
      if (!existingNode) {
        const newNode: TreeNode = {
          filePath: `/${level}`,
          content: {} as RouterMenu,
          children: []
        }
        currentLevel.children.push(newNode)
        currentLevel = newNode
      } else {
        currentLevel = existingNode
      }
    })

    currentLevel.content = entry.content
  })

  return processData(tree.children || [], '/')
}
/**处理树状图为路由模式 */
function processData(data: any[], parentPath: string): RouterMenu[] {
  const processedData: RouterMenu[] = []

  data.forEach((item) => {
    const content = item.content
    const processedItem: RouterMenu = {
      name: content.name,
      path: parentPath + content.path,
      component: content.component,
      meta: content.meta,
      mode: content.mode,
      beforeEnter: content.beforeEnter || null,
      isBase: Boolean(content.isBase)
    }
    if (content.redirect) {
      processedItem.redirect = content.redirect
    }

    if (item.children && item.children.length > 0) {
      processedItem.children = processData(item.children, processedItem.path + '/')
    }

    processedData.push(processedItem)
  })

  return processedData
}

/**
 * 将后台获取来的路由排序 按照sort
 */
export function sortRequestRouter(data: Array<any>) {
  return data.sort((a, b) => {
    return a.sort - b.sort
  })
}

/**
 * 获取后台传入 排序过后的name routerMap 对应的值
 * @param data
 * @returns
 */
export function getRequestReouter(data: Array<any>) {
  let routerMap = new Map<string, any>()
  let names: string[] = []
  let btnNames: Map<string, { title: string }> = new Map()

  function _cycel(arr: Array<any>) {
    arr = sortRequestRouter(arr)
    arr.forEach((v) => {
      if (v.children && v.children.length) {
        _cycel(v.children.filter((v: any) => v.node_type != 3))

        // 提取出btn按钮权限
        let btns = v.children.filter((v: any) => v.node_type == 3)
        btns.forEach((c: any) => {
          let meta = c.meta ? JSON.parse(c.meta) : {}
          btnNames.set(meta.name, { title: meta.title })
        })
      }
      v.meta = v.meta ? JSON.parse(v.meta) : {}
      let { name, flag, redirect, title, isLink, isHide, icon, dialog } = v.meta
      if (name) {
        names.push(name)
        routerMap.set(
          name as RouterType['name'],
          {
            sort: v.sort,
            name,
            path: flag,
            redirect,
            title,
            isLink,
            isHide,
            code: v.code,
            icon,
            dialog
          } as any
        )
      }
    })
  }
  _cycel(data)

  return {
    names,
    requestRouterMap: routerMap,
    btnNames
  }
}

/**
 * 网络请求的路由信息映射本地路由信息
 * @param locaRoute
 * @param requestRouterMap
 * @returns
 */
export function requestMapLocalRouter(locaRoute: RouterMenu, requestRouterMap: Map<string, any>) {
  let item: RouterMenu = locaRoute
  let requestItem = requestRouterMap.get(locaRoute.name)
  if (requestItem) {
    let { redirect, title, isLink, isHide, code, icon, dialog } = requestItem
    item = {
      ...locaRoute,
      redirect: (redirect ? '/' + redirect : locaRoute.redirect) as any,
      sort: requestItem.sort || locaRoute.sort,
      meta: {
        ...locaRoute.meta,
        title: title || locaRoute.meta.title,
        isLink: isLink || locaRoute.meta.isLink,
        isHide: !isHide || locaRoute.meta.isHide,
        icon: icon || locaRoute.meta.icon,
        code: code || locaRoute.meta.code,
        dialog: dialog || locaRoute.meta.dialog
      }
    }
  }
  return item
}
