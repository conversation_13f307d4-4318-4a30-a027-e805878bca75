<template>
  <div class="login-container">
    <div class="loginBox">
      <img v-if="!isMobile" :src="requireImg('login/loginnewbg.png')" class="bgg" alt="" />
      <div class="loginBoxLeft">
        <h1><span>找回密码</span><span class="s1"> FORGET PASSWORD</span></h1>
        <a-form
          :model="state.addForm"
          :rules="ruleData"
          ref="ruleForm"
          class="login-form"
          :class="[isMobile && 'w-320px!']"
        >
          <a-form-item name="username">
            <div class="logxt flex-y-center">
              <span class="svg-container">
                <SvgIcon icon="user"></SvgIcon>
              </span>
              <a-input v-model:value.trim="state.addForm.username" maxlength="16" placeholder="请输入登录账号" />
            </div>
          </a-form-item>
          <a-form-item name="phone">
            <div class="logxt flex-y-center">
              <span class="svg-container">
                <SvgIcon icon="phone"></SvgIcon>
              </span>
              <a-input
                v-model:value.trim="state.addForm.phone"
                maxlength="11"
                placeholder="请输入账号绑定的手机号"
                @input="(_) => (state.addForm.phone = state.addForm.phone.slice(0, 11))"
              />
            </div>
          </a-form-item>
          <a-form-item name="captcha">
            <div class="flex-y-center justify-between">
              <div class="w-65%">
                <span class="svg-container">
                  <SvgIcon icon="code" />
                </span>
                <a-input
                  v-model:value.trim="state.addForm.captcha"
                  type="text"
                  @input="(_) => (state.addForm.captcha = state.addForm.captcha.slice(0, 6))"
                  placeholder="短信验证码"
                />
              </div>
              <div class="w-34%">
                <a-button
                  class="loginCodeBtn"
                  :class="[isMobile && 'w-108px! p-5px']"
                  type="primary"
                  :disabled="!smsStatus"
                  @click.stop="getCatcha"
                  >{{ state.text }}</a-button
                >
              </div>
            </div>
          </a-form-item>
          <a-form-item name="password">
            <div class="logxt flex-y-center">
              <span class="svg-container">
                <SvgIcon icon="password"></SvgIcon>
              </span>
              <a-input-password
                :visibility-toggle="false"
                v-model:value.trim="state.addForm.password"
                type="text"
                maxlength="16"
                placeholder="设置6至16位英文/数字/符号组合"
                class="no-autofill-pwd"
              />
            </div>
          </a-form-item>
          <a-form-item name="confirm_password">
            <div class="logxt flex-y-center">
              <span class="svg-container">
                <SvgIcon icon="password"></SvgIcon>
              </span>
              <a-input-password
                :visibility-toggle="false"
                v-model:value.trim="state.addForm.confirm_password"
                type="text"
                maxlength="16"
                placeholder="请再次输入登录密码"
                class="no-autofill-pwd"
              />
            </div>
          </a-form-item>

          <div class="mb-23px">
            <span class="font-size-12px c-primary cursor-pointer" @click="router.push({ name: 'Login' })">
              返回登录
            </span>
          </div>
          <div class="footer">
            <a-button class="loginbtn" type="primary" :loading="state.submitLoading" @click="submitForm(ruleForm)"
              >确认修改</a-button
            >
          </div>
        </a-form>
      </div>
    </div>
    <div class="bottomText">
      <p>{{ getConfig('PROPERTY_RIGHTS') }}</p>
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { validateMobile, getSystemFile, getConfig, requireImg } from '@/utils'
  import { resetByPassword, setCaptcha } from './index.api'
  import code from './src/code'
  import rules from './src/rules'
  import { LeftOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { isMobileDevice } from '@/utils'
  import { useApp } from '@/hooks'
  const router = useRouter()
  const ruleForm = ref(null)
  const logo_back = ref(import.meta.env.VITE_APP_LOGO_BACK)
  const { isMobile } = useApp()
  const state = reactive({
    loading: false,
    submitLoading: false,
    addForm: {
      reset: 0,
      loginType: 1
    },
    text: '获取验证码',
    timer: null,
    time: 60,
    status: false,
    smsDisabled: true
  })
  // 导入登录验证规则
  const { ruleData } = rules(state.addForm)

  // 导入图形验证
  const { getCode, codeData } = code(login, getCatcha, state, state.addForm)

  // 提交
  const submitForm = (formName) => {
    console.log(formName)
    formName.validate().then((valid) => {
      if (!valid) return false
      reset()
    })
  }

  async function reset() {
    try {
      state.submitLoading = true
      // delete state.addForm.loginType
      await resetByPassword(state.addForm)
      router.push({ name: 'Login' })
      message.success('重置密码成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.submitLoading = false
    }
  }

  async function login(formName) {
    formName.validateField('phone', (valid) => {
      if (!valid) return false
      state.loading = true
      if (!codeData.randstr) {
        getCode()
        return
      }
    })
  }

  // 验证码按钮验证
  const smsStatus = computed(() => {
    console.log(
      'state.addForm.phone',
      state.addForm.phone,
      state.status,
      validateMobile(state.addForm.phone) && !state.status
    )
    return validateMobile(state.addForm.phone) && !state.status
  })
  // 获取验证码
  async function getCatcha() {
    if (!codeData?.data?.randstr) {
      getCode()
      return
    }
    try {
      let res = await setCaptcha({
        phone: state.addForm.phone,
        tp: 'forget',
        ...codeData.data
      })

      state.status = true
      state.text = '获取验证码'
      state.time = 60
      state.timer = setInterval(() => {
        state.time--
        state.text = state.time + '秒后重试'
        if (state.time == 0) {
          clearInterval(state.timer)
          state.text = '重新获取'
          state.status = false
          codeData.data = {}
        }
      }, 1000)
    } catch (error) {
      codeData.data = {}
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  $bg: #2d3a4b;
  $dark_gray: #889aa4;
  $light_gray: #eee;

  .login-container {
    width: 100%;
    height: 100vh;
    background-color: #eceff4;
    background-image: url('https://oss.jfb.qidianbox.com/assets/u9671_state0.svg');
    background-repeat: no-repeat;
    background-position: center center;
    background-attachment: scroll;
    background-origin: border-box;
    .loginBox {
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translateX(-50%) translateY(-50%);
      display: flex;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
      border-radius: 5px;
      overflow: hidden;
      .bgg {
        width: 330px;
        flex: none;
      }
    }
    .loginBoxLeft {
      padding: 35px 25px;
      background: #fff;
      width: 490px;

      h1 {
        font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
        font-weight: 700;
        font-size: 18px;
        color: #666666;
        margin-bottom: 40px;
        .s1 {
          font-size: 14px;
          color: #999;
          margin-left: 5px;
        }
      }
      .logxt {
        position: relative;
      }
      :deep(.ant-form-item) {
        margin-bottom: 20px;
        .ant-input {
          height: 50px;
          line-height: 50px;
          padding-left: 50px;
        }
      }
      .password-wrapper {
        :deep(.ant-input) {
          height: 40px;
          line-height: 40px;
          padding-left: 40px;
        }
      }
    }

    .login-form {
      position: relative;
      // width: 440px;
      max-width: 100%;
      overflow: hidden;
    }
    .bottomText {
      width: 100%;
      position: absolute;
      bottom: 5%;
      text-align: center;
      line-height: 20px;
      p {
        margin: 0;
        font-size: 12px;
        color: #ccc;
      }
    }
    .svg-container {
      position: absolute;
      color: $dark_gray;
      vertical-align: middle;
      padding: 0;
      width: 50px;
      line-height: 50px;
      text-align: center;
      display: inline-block;
      z-index: 1;
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 5px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .loginbtn {
      height: 50px;
      width: 100%;
      font-size: 14px;
    }
    .loginCodeBtn {
      height: 50px;
      font-size: 14px;
      width: 150px;
    }
  }
  @media (max-width: 820px) {
    .bgg {
      display: none;
    }
    .loginBoxLeft {
      width: 100% !important;
    }
  }
</style>
