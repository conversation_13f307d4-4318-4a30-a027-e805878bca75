<template>
  <div class="flex flex-items-center flex-justify-center empty">
    <div class="flex flex-col flex-items-center">
      <img class="img" :src="requireImg('404.png')" alt="" />
      <div class="m-b-10px">暂无权限</div>
      <a-button type="primary" @click="routerToBase()">返回首页</a-button>
    </div>
    <!-- <a-empty
      :image=""
      :image-style="{
        height: '50%',
        width: '50%'
      }"
    >
      <template #description>
        <div>暂无权限</div>
        <a-button type="primary" @click="routerToBase()">返回首页</a-button>
      </template>
    </a-empty> -->
  </div>
</template>
<script setup lang="ts">
  import { useRouter } from '@/hooks'
  import { requireImg } from '@/utils'

  defineOptions({ name: '404' })
  const { routerToBase } = useRouter()
</script>
<style lang="scss" scoped>
  .empty {
    height: calc(100vh - 150px);
  }
  .img {
    width: 50%;
    height: 50%;
  }
</style>
