<template>
  <div class="page_main_page">
    <CardBaseLayout class="mb-16px">
      <template #title>积分管理</template>
      <template #content>
        <div class="points_statistics" :class="[isMobile && 'min-w-320px!']">
          <div class="flex flex-items-center lh-16px">
            <img src="@/assets/images/points/points_icon.png" alt="" class="w-16px h-16px mr-8px" />
            <span class="text-18px color-#404040 mr-8px">剩余积分（个）</span>
            <span class="text-12px color-#FF4D4F">{{ pointData?.alert }}</span>
          </div>
          <div class="mt-14px mb-24px ml-24px text-32px lh-32px fw-bold color-#141414">{{ pointData?.balance }}</div>

          <a-button type="primary" class="ml-21px" @click="onShowDialog('pay')">充值</a-button>
          <a-button @click="refresh">刷新</a-button>
        </div>
      </template>
    </CardBaseLayout>
    <CardBaseLayout class="shipping_templates">
      <template #content>
        <a-tabs v-model:activeKey="data.activeKey" @change="handleChange">
          <a-tab-pane key="1" tab="积分明细">
            <div class="page_main_search">
              <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
            </div>
            <div class="page_main_table">
              <a-row justify="space-between" align="center" class="mt-24px mb-12px">
                <a-col>
                  <span class="color-#656D7D">因广告平台数据会发生波动，次日趋于稳定，因此当日实时数据仅供参考</span>
                  <span class="color-#1677FF ml-16px">每小时按实际广告加粉量扣减；每1个加粉扣除1个积分:</span>
                </a-col>
                <!-- <a-col>
                  <a-checkbox v-model:checked="data.checked" @change="getList">仅查看有消耗数据</a-checkbox>
                </a-col> -->
              </a-row>

              <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
                <template #bodyCell="{ scope }">
                  <template v-if="scope.column.key === 'proof'">
                    <span v-if="!scope.record.proof">--</span>
                    <div v-else class="flex">
                      <div class="w-40px h-40px mr-6px" v-for="(item, index) in scope.record.proof.split(',')">
                        <a-image :src="item" />
                      </div>
                    </div>
                  </template>
                  <template v-if="scope.column.key === 'action'">
                    <a-button type="link" @click="onShowDialog('detail', scope.record)">查看账单明细</a-button>
                  </template>
                </template>
              </TableZebraCrossing>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="充值明细"><RechargeDetails /> </a-tab-pane>
        </a-tabs>
      </template>
    </CardBaseLayout>
    <a-modal
      v-model:open="data.model.open"
      :width="data.model.width"
      :title="data.model.title"
      :footer="null"
      centered
      @cancel="handleCancelPay"
    >
      <RechargeModel
        ref="RechargeModelRef"
        @paySuccess="handleRefresh"
        @event="onEvent"
        :minMoney="pointData?.balance"
        v-if="data.model.type == 'pay'"
      />
      <BillDetail v-if="data.model.type == 'detail'" :data="data.model.info" />
    </a-modal>
  </div>
</template>

<script setup>
  import datas from './data'
  import RechargeModel from './components/RechargeModel.vue'
  import BillDetail from './components/billDetail.vue'
  import RechargeDetails from './components/rechargeDetails.vue'
  import { usePoints } from '@/hooks'
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const { getPointBalance, pointData } = usePoints()

  const { pageChange, searchForm, onShowDialog, searchConfig, data, statusType, getList, onOpenPay, RechargeModelRef } =
    datas()
  getList()
  getPointBalance()
  const onEvent = (type) => {
    if (type.cmd === 'close') {
      data.model.open = false
      getPointBalance()
    }
  }
  const refresh = () => {
    getPointBalance()
    message.success('刷新成功')
  }
  const handleCancelPay = () => {
    RechargeModelRef.value.initModel()
  }
</script>

<style lang="scss" scoped>
  .page_main_page {
    // min-height: calc(100vh - 146px - 15px * 2);
    // background-color: #fff;
    // padding: 8px 0;
    // border-radius: 6px;
  }
  .points_statistics {
    display: inline-block;
    padding: 20px 16px;
    background: #f7f9fc;
    border-radius: 8px;
    min-width: 580px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  :deep(.ant-card .ant-card-head) {
    min-height: 0;
  }
  :deep(.ant-checkbox .ant-checkbox-inner) {
    border-color: var(--primary-color);
  }
  :deep(.ant-checkbox .ant-checkbox-inner) {
    width: 16px;
    height: 16px;
  }
  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }
</style>
