import { reactive, ref, createVNode } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getPoint, getDailyList } from './index.api'
import dayjs from 'dayjs'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const RechargeModelRef = ref(null)
  const searchConfig = reactive({
    data: [
      {
        field: 'only_cost',
        type: 'select',
        value: undefined,
        props: {
          options: [{ label: '有消耗', value: 1 }],
          placeholder: '请选择数据类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'creat_date',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        props: {},
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '日期',
        dataIndex: 'stat_date',
        key: 'stat_date'
      },
      {
        title: '积分共消耗(个)',
        dataIndex: 'cost',
        key: 'cost'
      },
      {
        title: '加粉数量(个)',
        dataIndex: 'fans',
        key: 'fans'
      },
      {
        title: '加粉扣除积分(个)',
        dataIndex: 'points',
        key: 'points'
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    model: {
      open: false,
      title: '',
      width: '472px',
      info: undefined,
      type: ''
    },
    securityData: 10,
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20,
      start_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD')
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    checked: false,
    pointInfo: undefined,
    activeKey: '1'
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#E63030',
        text: '禁用'
      },
      1: {
        color: '#60A13B',
        text: '启用'
      }
    }
    return (status as any)[val]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      data.params.only_cost = data.params.only_cost == 1 ? true : false
      let res = await getDailyList(data.params)
      data.tableConfigOptions.dataSource = res.data?.List || []
      data.tableConfigOptions.pagination.total = res.data.Total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      start_time: v.formData.creat_date?.length ? v.formData.creat_date[0] : undefined,
      end_time: v.formData.creat_date?.length ? v.formData.creat_date[1] : undefined
    }
    delete data.params.creat_date
    if (!v.status) {
      data.params.start_time = dayjs().format('YYYY-MM-DD')
      data.params.end_time = dayjs().format('YYYY-MM-DD')
      v.formData.creat_date = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type: string, item?: any) => {
    data.model.info = item
    data.model.type = type
    data.model.open = true
    switch (type) {
      case 'pay':
        onOpenPay()
        break
      case 'detail':
        data.model.title = '账单明细'
        data.model.width = 800
        break
    }
  }

  //充值弹框
  const onOpenPay = () => {
    data.model.title = createVNode('div', { class: 'flex-items-center flex mr-4px ' }, [
      '积分充值',

      createVNode(
        'span',
        { style: 'color:var(--primary-color);font-weight:400;margin-top:1px', class: 'text-12px' },
        '1'
      ),
      createVNode('span', { style: 'font-weight:400;margin-top:1px', class: 'text-12px color-#656D7D' }, '积分等于'),
      createVNode(
        'span',
        { style: 'color:var(--primary-color);font-weight:400;margin-top:1px', class: 'text-12px ' },
        '1'
      ),
      createVNode(
        'span',
        { style: 'font-weight:400;margin-top:1px', class: 'text-12px color-#656D7D' },
        '元；每1个加粉扣除1个积分'
      )
    ])
  }

  return {
    pageChange,
    searchForm,
    onShowDialog,
    RechargeModelRef,
    searchConfig,
    data,
    statusType,
    getList,
    onOpenPay
  }
}
