<template>
  <div>
    <div class="step-box flex flex-justify-between">
      <div class="flex flex-center" v-for="(item, index) in state.rechargeList" :key="index">
        <div class="text-center min-w-72px">
          <div
            :class="[
              'step-num flex flex-center text-10px',
              state.rechargeStep >= index ? 'step_default' : 'step_finish'
            ]"
          >
            {{ index + 1 }}
          </div>
          <div :class="['text-12px', state.rechargeStep >= index ? 'blue' : 'c-#313233']">{{ item.name }}</div>
        </div>
        <div v-if="index != 2" class="ml-13px">
          <img v-if="state.rechargeStep > index" src="@/assets/images/points/select_r.png" alt="" class="w-70px" />
          <img v-else src="@/assets/images/points/right.png" alt="" class="w-70px" />
        </div>
      </div>
    </div>
    <!-- 输入金额 -->
    <div class="mt-16px" v-if="state.rechargeStep == 0">
      <div class="flex flex-center mb-4px">
        <a-form ref="ruleFormRef">
          <a-form-item
            label="充值积分"
            name="money"
            class="flex"
            :rules="[
              {
                required: true,
                trigger: ['blur', 'change'],
                validator: () => {
                  return regular(state.form.money)
                }
              }
            ]"
          >
            <div class="flex flex-items-center">
              <a-input-number
                id="inputNumber"
                v-model:value="state.form.money"
                :min="0"
                :controls="false"
                :step="1"
                :precision="0"
                style="width: 280px; height: 34px; margin: 0 8px"
                placeholder="请输入充值积分数量"
              />

              <span
                ><span class="color-#313232">积分</span>
                <a-tooltip>
                  <template #title> 支付成功后将累加到积分余额中 </template>
                  <QuestionCircleFilled class="m-l-4px font-size-14px c-#C5C6CC" />
                </a-tooltip>
              </span>
            </div>

            <div class="c-#86888B ml-10px text-12px" v-if="props.minMoney < 0">
              最少充值<span class="c-#FF4D4F">{{ Math.abs(props.minMoney) + 1 }}</span
              >积分
            </div>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <!-- 展示二维码 -->
    <div class="mt-16px" v-if="state.rechargeStep == 1">
      <div class="flex flex-center">
        <img src="@/assets/images/points/w_pay.png" alt="" class="w-14px h-12px mr-4px" />
        微信支付
        <span class="c-#EC0F0F">{{ toFixed2(state.form.money) }}积分</span>
        <!-- （含官方手续费<span class="c-#EC0F0F">{{ toFixed2(state.pay.service_amount) }}元</span>） -->
      </div>
      <div class="img-box bg-#FAFBFC flex flex-center">
        <img v-if="state.pay.url" :src="state.pay.url" alt="" class="w-152px h-152px" />
        <a-spin v-else />
      </div>
    </div>
    <!-- 充值成功 -->
    <div class="mt-30px" v-if="state.rechargeStep == 2">
      <div class="text-18px font-600 mb-16px text-center">充值成功</div>
      <div class="c-#85878A text-center">
        您的<span class="c-#EC0F0F">{{ toFixed2(state.form.money) }}元</span>积分已充值成功
      </div>
    </div>
    <div class="step-footer flex flex-justify-end mt-16px">
      <a-button v-if="state.rechargeStep == 0" @click="handleClose"> 取消 </a-button>
      <a-button v-if="state.rechargeStep == 0" type="primary" @click="handleNext">下一步</a-button>
      <a-button v-else @click="handleClose" type="primary">关闭</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, watch, inject, onUnmounted } from 'vue'
  import { QuestionCircleFilled, ExclamationCircleFilled } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { getNativePayApi, getIsPayApi } from '../index.api'
  import { centsToYuan, yuanToCents, toFixed2 } from '@/utils'
  import { useTheme } from '@/hooks'
  const { themeVar } = useTheme()
  const emits = defineEmits(['paySuccess', 'event'])
  const props = defineProps({
    minMoney: {
      type: Object
    }
  })
  const state = reactive({
    rechargeList: [{ name: '输入充值积分' }, { name: '扫码支付' }, { name: '完成充值' }],
    rechargeStep: 0, //充值步骤0输入金额1扫码支付2完成充值
    form: {
      money: undefined
    },
    model: {
      open: false,
      title: '保费充值',
      width: '472px'
    },
    pay: {
      amount: 0,
      service_amount: 0,
      url: '',
      order: '',
      loading: false
    }, //支付金额、手续费、支付二维码、订单号、
    colorFlag: false
  })
  let timer = ref<any>(null)
  const handleNext = () => {
    if (!state.form.money) {
      message.warning('请输入充值积分')
      return
    }
    if (state.form.money <= Math.abs(props.minMoney) && props.minMoney < 0) return

    state.rechargeStep = 1
    getNativePay()
  }

  //获取支付金额、二维码
  const getNativePay = async () => {
    try {
      let res = await getNativePayApi({ money: +yuanToCents(state.form.money) })
      state.pay.amount = res.data.amount
      state.pay.service_amount = res.data.service_amount
      state.pay.url = res.data.image
      state.pay.order = res.data.out_trade_no
      //查询是否支付
      timer.value = setInterval(() => {
        paySuccess(res.data.out_trade_no)
      }, 2000)
    } catch (error) {}
  }
  //status1已支付0未支付
  const paySuccess = async (id) => {
    let res = await getIsPayApi({ order_no: id })
    if (res.data.is_pay) {
      state.rechargeStep = 2
      clearInterval(timer.value)
      emits('paySuccess')
    } else {
      console.log('未支付')
    }
  }

  const regular = (value: undefined | number) => {
    if (!value) return Promise.reject('充值积分数量不能为空')
    if (value <= Math.abs(props.minMoney) && props.minMoney < 0) return Promise.reject('充值积分须大于欠费积分！')
    // if (value == props.minMoney && props.minMoney == 0) return Promise.reject(`最少充值${props.minMoney + 1}`)
    return Promise.resolve()
  }
  const initModel = () => {
    clearInterval(timer.value)
    state.rechargeStep = 0
    state.form.money = undefined
  }
  onUnmounted(() => {
    clearInterval(timer.value)
  })
  //关闭充值弹框
  const handleClose = () => {
    state.form.money = undefined
    initModel()
    emits('event', { cmd: 'close' })
  }
  defineExpose({
    initModel
  })
</script>
<style scoped lang="scss">
  // @import '../../../../../assets/css/mixin_scss_fn';
  .step-box {
    background: #fafbfc;
    border-radius: 2px;
    padding: 8px 20px;
    .step-num {
      width: 30px;
      height: 30px;
      margin: 0 auto 7px;
      border-radius: 50%;
      color: #fff;
      // border: 6px solid #cde1fd;
    }
    .step_default {
      width: 30px;
      height: 30px;
      background: url('@/assets/images/points/step_default.png') no-repeat;
      background-size: 100% 100%;
    }
    .step_finish {
      width: 30px;
      height: 30px;
      background: url('@/assets/images/points/step_finish.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .img-box {
    width: 176px;
    height: 176px;
    margin: 16px auto;
  }
  .tips-box {
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    background: #fff3f3;
    border-radius: 5px;
    border: 1px solid #ffacac;
  }
  .blue {
    color: v-bind('themeVar.infoColor');
  }
  .bg-blue {
    background: v-bind('themeVar.infoColor');
  }
</style>
