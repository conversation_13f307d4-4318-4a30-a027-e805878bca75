<template>
  <!-- 充值明细 -->
  <div class="page_main_page">
    <div class="page_main_search">
      <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
    </div>
    <div class="mt-16px">
      <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'proof'">
            <span v-if="!scope.record.proof">--</span>
            <div v-else class="flex">
              <div class="w-40px h-40px mr-6px" v-for="(item, index) in scope.record.proof.split(',')">
                <a-image :src="item" width="40px" height="40px" />
              </div>
            </div>
          </template>
        </template>
      </TableZebraCrossing>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue'
  import { getPayList } from '../index.api'
  import dayjs from 'dayjs'
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '充值单号',
        dataIndex: 'OrderNo',
        key: 'OrderNo',
        slot: true
      },
      {
        title: '充值时间',
        dataIndex: 'pay_time',
        key: 'pay_time',
        slot: true
      },
      {
        title: '充值积分（个）',
        dataIndex: 'money',
        key: 'money',
        slot: true
      },
      {
        title: '账户积分(个)',
        dataIndex: 'account_point',
        key: 'account_point',
        slot: true
      },
      {
        title: '充值凭证',
        dataIndex: 'proof',
        key: 'proof',
        width: 250
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }
  const data = reactive({
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,

    params: {
      page: 1,
      page_size: 20,
      start_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD')
    }
  })
  const searchConfig = reactive({
    data: [
      {
        type: 'date',
        field: 'creat_date',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        props: {},
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })

  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getPayList(data.params)
      data.tableConfigOptions.dataSource = res.data?.List || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      start_time: v.formData.creat_date?.length ? v.formData.creat_date[0] : undefined,
      end_time: v.formData.creat_date?.length ? v.formData.creat_date[1] : undefined
    }
    delete data.params.creat_date
    if (!v.status) {
      data.params.start_time = dayjs().format('YYYY-MM-DD')
      data.params.end_time = dayjs().format('YYYY-MM-DD')
      v.formData.creat_date = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    data.params.page = 1
    getList()
  }
  getList()
</script>

<style lang="scss" scoped>
  .page_main_page {
    // min-height: calc(100vh - 146px - 15px * 2);
    // background-color: #fff;
    // padding: 8px 0;
    // border-radius: 6px;
  }
  .points_statistics {
    width: 30%;
    padding: 20px 16px;
    background: #f7f9fc;
    border-radius: 8px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  :deep(.ant-checkbox .ant-checkbox-inner) {
    border-color: var(--primary-color);
  }
</style>
