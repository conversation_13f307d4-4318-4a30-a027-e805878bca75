<template>
  <!-- 账单明细（小时统计） -->
  <div class="page_main_page">
    <div class="page_main_table">
      <a-row justify="space-between" align="center" class="mb-13px">
        <a-col>
          <div class="text-14px">数据日期：{{ data?.stat_date }}（为保证数据的准确性，系统每1小时刷新一次数据）</div>
        </a-col>
        <a-col>
          <a-checkbox v-model:checked="state.checked" @change="getList">仅查看有消耗数据</a-checkbox>
        </a-col>
      </a-row>

      <TableZebraCrossing :data="state.detailTableConfig">
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'action'">
            <a-button type="link">查看账单明细</a-button>
          </template>
        </template>
      </TableZebraCrossing>
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue'
  import { getHourlyList } from '../index.api'
  const props = defineProps({
    data: {
      type: Object
    }
  })
  const detailTableConfig = {
    bordered: true,
    loading: false,
    rowKey: 'id',

    dataSource: [],
    columns: [
      {
        title: '时段',
        dataIndex: 'hour_str',
        key: 'hour_str',
        slot: true
      },
      {
        title: '积分共消耗(个)',
        dataIndex: 'cost',
        key: 'cost',
        slot: true
      },
      {
        title: '加粉数量(个)',
        dataIndex: 'fans',
        key: 'fans',
        slot: true
      },
      {
        title: '加粉扣除积分(个)',
        dataIndex: 'points',
        key: 'points',
        slot: true
      }
    ],
    pagination: null
  }

  const state = reactive({
    model: {
      open: false,
      title: '',
      width: '472px',
      info: undefined,
      type: ''
    },
    securityData: 10,
    loading: false,
    tableData: [],
    total: 0,
    detailTableConfig,
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    checked: false
  })
  const getList = async () => {
    try {
      let res = await getHourlyList({ date: props.data.stat_date, only_cost: state.checked })
      state.detailTableConfig.dataSource = res.data.List
    } catch (error) {}
  }
  watch(
    () => props.data,
    () => {
      getList()
    },
    {
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  :deep(.ant-checkbox .ant-checkbox-inner) {
    border-color: var(--primary-color);
  }
  :deep(.ant-table-wrapper .ant-table-pagination.ant-pagination) {
    margin-bottom: 4px;
  }
</style>
