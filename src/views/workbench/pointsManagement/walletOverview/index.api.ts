import http from '@/utils/request'

/**
 * 积分余额
 */
export const getPoint = (data?: any) => {
  return http('get', `/admin/point/balance`, data)
}

/**
 * 日期统计
 */
export const getDailyList = (data?: any) => {
  return http('get', `/common/point_stat/daily_list`, data)
}

/**
 * 小时统计
 */
export const getHourlyList = (data?: any) => {
  return http('get', `/common/point_stat/hourly_list`, data)
}

/**
 * 积分充值
 */
export const getNativePayApi = (data?: any) => {
  return http('post', `/admin/point/native_pay`, data)
}

/**
 * 充值查询
 */
export const getIsPayApi = (data?: any) => {
  return http('get', `/admin/point/pay_status`, data)
}
/**
 * 充值列表
 */
export const getPayList = (data) => {
  return http('get', `/common/point/pay_list`, data)
}
