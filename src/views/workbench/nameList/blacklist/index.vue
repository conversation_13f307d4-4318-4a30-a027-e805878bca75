<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>黑名单</div>
      </template>
      <template #extra>
        <a-button v-auth="['blackListAdd']" type="primary" @click="onShowDialog('add')">新增黑名单</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options">
          <template #btns>
            <a-button
              type="primary"
              v-auth="['blackListDelete']"
              ghost
              :disabled="!data.selectedRowKeys.length"
              @click="handleChange()"
              class="ml-8px"
              >批量删除</a-button
            >
          </template>
        </SearchBaseLayout>
      </template>
      <template #tableWarp>
        <!-- <div class="btn-group mb-16px">
          <a-popconfirm
            :disabled="!data.selectedRowKeys.length"
            title="是否确认删除当前IP？"
            placement="topRight"
            @confirm="delItem(data.selectedRowKeys)"
          >
            <a-button type="primary" v-auth="['projectReport_feedback']" ghost :disabled="!data.selectedRowKeys.length"
              >批量删除</a-button
            >
          </a-popconfirm>
        </div> -->
        <TableZebraCrossing
          :data="data.tableConfigOptions"
          @change="pageChange"
          :row-selection="{ selectedRowKeys: data.selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'remarks'">
              <div class="flex items-center">
                <a-tooltip placement="top" v-if="scope.record.remarks">
                  <template #title>{{ scope.record.remarks }}</template>
                  <div class="text_overflow_row1">
                    {{ scope.record.remarks }}
                  </div>
                </a-tooltip>
                <span v-else class="text_overflow">--</span>
                <EditOutlined class="c-#FE9D35 ml4px" @click="remarkEdit(scope.record)" />
              </div>
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="handle_btns">
                <div class="flex_align_center">
                  <a-popconfirm title="是否确认删除当前IP？" placement="topRight" @confirm="delItem([scope.record.id])">
                    <a-button v-auth="['blackListDelete']" size="small" type="link" class="pa-0!">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal v-model:open="data.modalOpen" title="新增黑名单" @ok="handleOk" :footer="null">
      <a-form ref="ruleForm" :model="data.addForm" :rules="data.rules" :label-col="{ style: { width: '84px' } }">
        <a-form-item label="类型">
          <a-radio-group v-model:value="data.addForm.type">
            <a-radio :value="1">当前网络IP</a-radio>
            <a-radio :value="2">自定义IP</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="IP地址" v-if="data.addForm.type == 1">
          <div>{{ data.addForm.locaIp }}</div>
        </a-form-item>
        <a-form-item label="" name="ip" v-if="data.addForm.type == 2">
          <div :class="[!isMobile && 'pl-80px']" style="margin-top: -10px">
            <a-input placeholder="请输入IP地址" v-model:value="data.addForm.ip" />
            <div class="number-id c-#B6AFAF! mt-4px">示例：{{ data.addForm.locaIp }}</div>
          </div>
        </a-form-item>
        <a-form-item label="IP查询网址" v-if="data.addForm.type == 2">
          <div>
            <span>https://ip.cn</span>
            <CopyOutlined @click="copyUrl('https://ip.cn')" class="c-#fe9d36 ml-4px" />
          </div>
        </a-form-item>
        <div class="btn flex-y-center justify-end">
          <a-button @click="data.modalOpen = false">取消</a-button>
          <a-button type="primary" @click="handleOk(ruleForm)">确定</a-button>
        </div>
      </a-form>
    </a-modal>
    <a-modal v-model:open="data.remarkOpen" title="备注" @ok="editRemarks">
      <a-textarea
        class="mb-50px"
        v-model:value="data.remarkItem.remarks"
        placeholder="请输入备注"
        :autoSize="false"
        :rows="4"
        show-count
        :maxlength="50"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import datas from './data'
  import { CopyOutlined, EditOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    copyUrl,
    onSelectChange,
    batchDelete,
    remarkEdit,
    editRemarks,
    handleChange
  } = datas()
  getList()

  const ruleForm = ref(null)
</script>

<style lang="scss" scoped>
  :deep(.ant-form-item-explain-error) {
    margin-left: 80px;
  }
  .light {
    display: none;
  }
  .ant-btn-default:not(:disabled):hover {
    .light {
      display: block;
    }
    .default {
      display: none;
    }
  }
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: #fffaf4;
    color: var(--primary-color);
  }
</style>
