import http from '@/utils/request'

/**
 * 黑白名单列表
 */
export const ad_ip_list = (data:any) => {
  return http('get', `/common/ad_ip/list`, {
    ...data,
    type: 1
  })
}

/**
 * 新增黑白名单
 */
export const ad_ip_add = (data:any) => {
  return http('post', `/common/ad_ip/add`, data)
}

/**
 * 删除黑白名单
 */
export const ad_ip_del = (data:any) => {
  return http('post', `/common/ad_ip/del`, data)
}

/**
 * 获取本机ip
 */
export const ad_ip_ip = (data:any) => {
  return http('get', `/common/ad_ip/ip`, data)
}