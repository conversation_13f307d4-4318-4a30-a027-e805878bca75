import { createVNode, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { ad_ip_list, ad_ip_add, ad_ip_ip, ad_ip_del } from './index.api'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'ip',
        value: undefined,
        props: {
          placeholder: '请输入IP'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 12,
      //   md: 8,
      //   lg: 8,
      //   xl: 8,
      //   xxl: 6
      // }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    isResizable: false,
    columns: [
      {
        title: '用户IP',
        dataIndex: 'ip',
        key: 'ip',
        slot: true
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        slot: true
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        slot: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right',
        slot: true
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    dropOpen: false,
    modalOpen: false,
    modalType: '',
    selectedRowKeys: [],
    remarkOpen: false,
    remarkItem: {},
    remarkText: '',
    info: null,
    addForm: {
      type: 1,
      ip: '',
      locaIp: ''
    },
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    rules: {
      ip: [
        { required: true, message: '' },
        {
          pattern:
            /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$/,
          message: ''
        }
      ]
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await ad_ip_list(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    if (!v.status) {
      data.selectedRowKeys = []
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = async (type, item) => {
    data.info = item
    data.modalType = type
    data.modalOpen = true
    data.addForm.ip = ''
    try {
      const res = await ad_ip_ip()
      data.addForm.locaIp = res.data?.ip || ''
    } catch {}
  }
  const handleChange = () => {
    data.dropOpen = false
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否确认删除当前IP？'),
        async onOk() {
          delItem(data.selectedRowKeys)
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const delItem = async (ids) => {
    try {
      const res = await ad_ip_del({ ids: ids, type: 1 })
      message.success('删除成功')
      getList()
      data.selectedRowKeys = []
    } catch (error) {
      console.error(error)
    }
  }
  const handleOk = async (formName) => {
    try {
      await formName.validateFields()
      const res = await ad_ip_add({
        type: 1,
        ip: data.addForm.type == 1 ? data.addForm.locaIp : data.addForm.ip
      })
      message.success('添加成功')
      data.modalOpen = false
      getList()
    } catch {}
  }
  const copyUrl = (url: string) => {
    const input = document.createElement('input')
    input.value = url
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
    message.success('复制成功')
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    data.selectedRowKeys = selectedRowKeys
  }
  const batchDelete = async () => {
    try {
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  const remarkEdit = (item: any) => {
    data.remarkOpen = true
    data.remarkItem = JSON.parse(JSON.stringify(item || {}))
  }
  const editRemarks = async () => {
    try {
      const res = await ad_ip_add(data.remarkItem)
      message.success('修改成功')
      data.remarkOpen = false
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  return {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    copyUrl,
    onSelectChange,
    batchDelete,
    remarkEdit,
    editRemarks,
    handleChange
  }
}
