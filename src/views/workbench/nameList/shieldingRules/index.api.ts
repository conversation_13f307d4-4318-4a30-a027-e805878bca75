import http from '@/utils/request'

/**
 * 屏蔽规则列表
 */
export const ad_rules_list = (data:any) => {
  return http('post', `/admin/rules/list`, data)
}

/**
 * 新增屏蔽规则
 */
export const ad_rules_add = (data:any) => {
  return http('post', `/admin/rules/add`, data)
}

/**
 * 删除屏蔽规则
 */
export const ad_rules_del = (data:any) => {
  return http('get', `/admin/rules/delete`, data)
}

/**
 * 获取省市列表
 */
export const ad_rules_citys = () => {
  return http('get', `/admin/rules/citys`, {})
}