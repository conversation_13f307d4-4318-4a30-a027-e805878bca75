<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>屏蔽规则</div>
      </template>
      <template #extra>
        <a-button v-auth="['addBlockingRules']" type="primary" @click="onShowDialog('add')">新增屏蔽规则</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options"></SearchBaseLayout>
      </template>
      <template #tableWarp>
        <TableZebraCrossing
          :data="data.tableConfigOptions"
          @change="pageChange"
        >
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'name'">
              <div class="flex items-center">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.name }}</template>
                  <div class="text_overflow cursor-pointer w-100%">
                    {{ scope.record.name }}
                  </div>
                </a-tooltip>
              </div>
            </template>
            <template v-if="scope.column.key === 'block_area'">
              <div class="flex items-center">
                <a-tooltip placement="top" :overlayStyle="{maxWidth: '80%'}">
                  <template #title v-if="scope.record.block_area">{{ scope.record.block_area?.join('、') }}</template>
                  <div class="text_overflow c-#FE9D35 cursor-pointer" @click="onShowDialog('region', scope.record)">
                    {{ scope.record.block_area?.length||0 }}个区域
                  </div>
                </a-tooltip>
              </div>
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="handle_btns">
                <div class="flex_align_center">
                  <a-button v-auth="['editBlockingRules']" v-if="scope.record.name!='默认规则'" size="small" type="link" class="pa-0!" @click="onShowDialog('edit', scope.record)">编辑</a-button>
                  <a-button size="small" type="link" class="pa-0!" @click="onShowDialog('view', scope.record)">查看</a-button>
                  <a-popconfirm v-if="scope.record.name!='默认规则'" title="删除后，投放链接将不再使用该屏蔽规则" placement="topRight" @confirm="delItem(scope.record.id)">
                    <a-button v-auth="['delBlockingRules']" size="small" type="link" class="pa-0!">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-drawer
      v-model:open="data.modalOpen"
      :title="data.modalTitle"
      @ok="handleOk"
      @close="data.modalOpen = false"
      :width="isMobile ? '100%' : '750px'"
      :footer="null"
    >
      <a-form class="pa-20px overflow-auto form" ref="ruleForm" :model="data.addForm" :rules="data.rules" :label-col="{ style: { width: '108px' } }">
        <a-form-item label="规则名称" name="name" :key="data.modalOpen">
          <a-input placeholder="请输入规则名称" :disabled="data.addForm.id || data.modalType=='view'" v-model:value="data.addForm.name" maxLength="50" />
        </a-form-item>
        <a-form-item label="广告环境检测" name="ad_env">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.ad_env" :checkedValue="1" :unCheckedValue="2" />
          <div class="mt-4px c-#656d7e font-size-12px">非广告环境访问的用户，会进入配置的屏蔽页</div>
        </a-form-item>
        <a-form-item label="访问次数限制" name="visit_num">
          <a-input-number :disabled="data.modalType=='view'" class="w-150px h-34px " id="inputNumber" v-model:value="data.addForm.visit_num" :min="0" :controls="false">
            <template #addonBefore v-if="data.modalType!='view'">
              <div class="cursor-pointer select-none w-16px" @click="data.addForm.visit_num>0 && data.addForm.visit_num--">-</div>
            </template>
            <template #addonAfter v-if="data.modalType!='view'">
              <div class="cursor-pointer select-none w-16px" @click="!data.addForm.visit_num ? data.addForm.visit_num=1 : data.addForm.visit_num++">+</div>
            </template>
          </a-input-number>
          <div class="mt-4px c-#656d7e font-size-12px">用户单条链接的访问次数限制，如配置为6次，用户访问第6次的时候会进入配置的屏蔽页，0为不限制</div>
        </a-form-item>
        <a-form-item label="默认IP屏蔽包" name="default_block_package_ip">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.default_block_package_ip" :checkedValue="1" :unCheckedValue="2" />
          <div class="mt-4px c-#656d7e font-size-12px">系统内置的风险网络IP，其中包括异常用户、红包党、羊毛党等</div>
        </a-form-item>
        <a-form-item label="共享IP屏蔽包" name="block_package_ip">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.block_package_ip" :checkedValue="1" :unCheckedValue="2" />
          <div class="mt-4px c-#656d7e font-size-12px">系统用户自主配置的P屏蔽，系统遴选后，生成的屏蔽P列表</div>
        </a-form-item>
        <a-form-item label="默认屏蔽包" name="default_block_package">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.default_block_package" :checkedValue="1" :unCheckedValue="2" />
          <div class="mt-4px c-#656d7e font-size-12px">系统内置的风险用户，其中包含异常用户、红包党、羊毛党等</div>
        </a-form-item>
        <a-form-item label="共享屏蔽包" name="block_package">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.block_package" :checkedValue="1" :unCheckedValue="2" />
          <div class="mt-4px c-#656d7e font-size-12px">系统用户主动进行的拉黑操作，系统遴选后，生成的屏蔽包列表</div>
        </a-form-item>
        <a-form-item label="仅限手机访问" name="only_phone_visit">
          <a-switch class="mt-6px" :disabled="data.modalType=='view'" v-model:checked="data.addForm.only_phone_visit" :checkedValue="1" :unCheckedValue="2" />
        </a-form-item>
        <a-form-item label="屏蔽地域" name="block_area">
          <div>
            <a-form-item-rest>
              <a-checkbox :disabled="data.modalType=='view'" v-model:checked="data.checkAll" @change="handleCheckAllChange">全选</a-checkbox>
              <a-checkbox v-model:checked="data.expandedAll" @change="handleExpandedAllChange">展开</a-checkbox>
            </a-form-item-rest>
          </div>
          <div class="border border-dashed border-#E5E5E5 pa-16px mt-8px rounded-4px">
            <a-tree
              :disabled="data.modalType=='view'"
              v-model:expandedKeys="data.expandedKeys"
              v-model:checkedKeys="data.selectedKeys"
              :field-names="{ key: 'id' }"
              checkable
              :checkStrictly="true"
              :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
              :tree-data="data.citys"
              @check="handleCheckedChange"
              @expand="handleExpandedChange"
            ></a-tree>
          </div>
          <div class="mb--16px!"></div>
        </a-form-item>
      </a-form>
      <div class="flex-y-center justify-end h-64px pr-25px" style="box-sizing: border-box;">
        <a-button @click="data.modalOpen = false">取消</a-button>
        <a-button v-if="data.modalType!='view'" type="primary" @click="handleOk(ruleForm)">确定</a-button>
      </div>
    </a-drawer>
    <a-modal
      v-model:open="data.regionOpen"
      title="屏蔽区域"
      :width="isMobile ? '90%' : '657px'"
      destroy-on-close
      :footer="null"
      :zIndex="2000"
    >
      <a-form-item label="屏蔽地域" name="block_area">
        <div class="mt-6px">
          <a-form-item-rest>
            <a-checkbox :disabled="data.modalType=='view' || data.addForm.name=='默认规则'" v-model:checked="data.checkAll" @change="handleCheckAllChange">全选</a-checkbox>
            <a-checkbox v-model:checked="data.expandedAll" @change="handleExpandedAllChange">展开</a-checkbox>
          </a-form-item-rest>
        </div>
        <div class="border border-dashed border-#E5E5E5 pa-16px mt-16px rounded-4px">
          <a-tree
            :disabled="data.modalType=='view' || data.addForm.name=='默认规则'"
            v-model:expandedKeys="data.expandedKeys"
            v-model:checkedKeys="data.selectedKeys"
            :field-names="{ key: 'id' }"
            checkable
            :checkStrictly="true"
            :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
            :tree-data="data.citys"
            @check="handleCheckedChange"
            @expand="handleExpandedChange"
            :height="500"
          ></a-tree>
        </div>
      </a-form-item>
      <div class="flex-y-center justify-end" style="box-sizing: border-box;">
        <a-button @click="data.regionOpen = false">取消</a-button>
        <a-button v-auth="['editBlockingRules']" v-if="data.modalType!='view' && data.addForm.name!='默认规则'" type="primary" @click="handleOk(ruleForm)">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import datas from './data'
  import { CopyOutlined, EditOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue'
  import {useApp } from '@/hooks'
  const { isMobile } = useApp()
  const {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    handleCheckAllChange,
    handleExpandedAllChange,
    handleCheckedChange,
    handleExpandedChange
  } = datas()
  getList()

  const ruleForm = ref(null)
</script>

<style lang="scss" scoped>
  .form {
    height: calc(100% - 64px);
    border-bottom: 1px solid #f3f3f3;
  }
  .light {
    display: none;
  }
  .ant-btn-default:not(:disabled):hover {
    .light {
      display: block;
    }
    .default {
      display: none;
    }
  }
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: #fffaf4;
    color: var(--primary-color);
  }
  ::v-deep .ant-input-number-input {
    text-align: center;
  }
</style>
