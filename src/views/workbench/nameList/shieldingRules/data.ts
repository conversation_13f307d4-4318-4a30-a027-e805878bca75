import { createVNode, reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { ad_rules_list, ad_rules_add, ad_rules_del, ad_rules_citys } from './index.api'
import { throttle, debounce } from 'lodash-es'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const route = useRoute()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入规则名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1600
    },
    dataSource: [],
    isResizable: false,
    columns: [
      {
        title: '规则名称',
        dataIndex: 'name',
        key: 'name',
        slot: true,
        width: 800
      },
      {
        title: '屏蔽区域',
        dataIndex: 'block_area',
        key: 'block_area',
        slot: true,
        width: 250
      },
      {
        title: '创建人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        slot: true,
        width: 190
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        slot: true,
        width: 233
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 150,
        fixed: 'right',
        slot: true
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    dropOpen: false,
    modalOpen: false,
    regionOpen: false,
    modalType: '',
    modalTitle: '',
    info: null,
    addForm: {
      name: '',
      ad_env: 1,
      visit_num: 6,
      default_block_package_ip: 1,
      block_package_ip: 1,
      default_block_package: 1,
      block_package: 1,
      only_phone_visit: 2,
      block_area: []
    },
    citys: [],
    expandedKeys: [],
    selectedKeys: [],
    selectedNames: [],
    checkAll: false,
    expandedAll: false,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 10
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    rules: {
      name: [
        { required: true, message: '请输入规则名称', trigger: ['blur'] }
      ]
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await ad_rules_list(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
      data.tableConfigOptions.loading = false
    } catch (error) {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    if (!v.status) {
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = async (type, item) => {
    try {
      data.info = item
      data.modalType = type
      if (type == 'region') {
         data.regionOpen = true
      } else {
        data.modalOpen = true
      }
      data.selectedKeys = []
      data.expandedKeys = []
      data.addForm.block_area = []
      data.checkAll = false
      if (type === 'add') {
        data.modalTitle = '新增规则'
        data.addForm = {
          name: '',
          ad_env: 1,
          visit_num: 6,
          default_block_package_ip: 1,
          block_package_ip: 1,
          default_block_package: 1,
          block_package: 1,
          only_phone_visit: 2,
          block_area: []
        }
      } else if (['edit', 'view', 'region'].includes(type)) {
        data.modalTitle = (type === 'edit' ? '编辑规则' : '查看规则')
        data.addForm = {
          id: item.id,
          name: item.name,
          ad_env: item.ad_env,
          visit_num: item.visit_num,
          default_block_package_ip: item.default_block_package_ip,
          block_package_ip: item.block_package_ip,
          default_block_package: item.default_block_package,
          block_package: item.block_package,
          only_phone_visit: item.only_phone_visit,
          block_area: item.block_area
        }
      }
      const res = await ad_rules_citys()
      data.citys = res.data
      if (['edit', 'view', 'region'].includes(type)) {
        data.selectedKeys = item.block_area.map(name => {
          return findCityIdByName(data.citys, name)
        }).filter(id => id !== null)
        getCheckAll()
        handleExpandedChange()
      } 
    } catch {
    }
  }
  const findCityIdByName = (cityList, name) => {
    for (const city of cityList) {
      if (city.name === name) {
        return city.id
      }
      if (city.children) {
        const childId = findCityIdByName(city.children, name)
        if (childId) {
          return childId
        }
      }
    }
    return null
  }
  const delItem = async (id) => {
    try {
      const res = await ad_rules_del({ id })
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  const handleOk = debounce(async (formName) => {
    try {
      await formName?.validateFields()
      const res = await ad_rules_add(data.addForm)
      message.success(data.addForm?.id ? '编辑成功' : '添加成功')
      data.modalOpen = false
      data.regionOpen = false
      getList()
    } catch (error) {
      console.error(error)
    }
  }, 1500, { leading: true,  trailing: false })
  const handleCheckAllChange = (e: any) => {
    data.selectedKeys = []
    data.addForm.block_area = []
    if (e.target.checked) {
      for (const row of data.citys) {
        data.selectedKeys.push(row.id)
        data.addForm.block_area.push(row.name)
        if (row.children) {
          for (const child of row.children) {
            data.selectedKeys.push(child.id)
            data.addForm.block_area.push(child.name)
          }
        }
      }
    }
  }
  const handleExpandedAllChange = (e: any) => {
    if (e.target.checked) {
      data.expandedKeys = data.citys.map((item: any) => item.id)
    } else {
      data.expandedKeys = []
    }
  }
  const getCheckAll = () => {
    let state = true
    for (let item of data.citys) {
      if (data.addForm.block_area.indexOf(item.name) === -1) {
        state = false
        break
      }
    }
    data.checkAll = state
  }
  const handleCheckedChange = (_:number[], nodes: any) => {
    data.addForm.block_area = nodes.checkedNodes.map((item: any) => item.name)
    getCheckAll()
  }
  const handleExpandedChange = () => {
    data.expandedAll = data.expandedKeys.length === data.citys.length
  }
  if (route.query?.isAdd == 'true') {
    onShowDialog('add')
  }
  return {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    handleCheckAllChange,
    handleExpandedAllChange,
    handleCheckedChange,
    handleExpandedChange
  }
}
