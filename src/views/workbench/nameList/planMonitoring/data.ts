import { reactive } from 'vue'
import { message } from 'ant-design-vue'
import { rule_list, rule_del } from './index.api'
import { useApp } from '@/hooks'
export default function datas() {
  const { isMobile } = useApp()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入监测名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'account_name',
        value: undefined,
        props: {
          placeholder: '请输入账户名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    isResizable: false,
    columns: [
      {
        title: '监测名称',
        dataIndex: 'name',
        key: 'name',
        slot: true
      },
      {
        title: '应用账户',
        dataIndex: 'account_num',
        key: 'account_num',
        width: 100
      },
      {
        title: '屏蔽区域',
        dataIndex: 'area_json',
        key: 'area_json',
        width: 100
      },
      {
        title: '定向性别',
        dataIndex: 'gender',
        key: 'gender',
        width: 140
      },
      {
        title: '定向年龄',
        dataIndex: 'age',
        key: 'age',
        width: 200
      },
      {
        title: '定向手机价格',
        dataIndex: 'launch_price',
        key: 'launch_price',
        width: 140
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right',
        slot: true
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const state = reactive({
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: {}
    },
    drawer: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: {}
    }
  })

  // 获取列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let res = await rule_list(state.params)
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v: any) => {
    state.params = {
      ...state.params,
      ...v.formData
    }
    state.params.page = 1
    getList()
  }
  const type2Data: Record<string, any> = {
    add: {
      width: isMobile.value ? '100%' : 800,
      title: '新增规则'
    },
    edit: {
      width: isMobile.value ? '100%' : 800,
      title: '编辑规则'
    },
    areas: {
      width: 717,
      title: '屏蔽区域'
    },
    accounts: {
      width: 919,
      title: '应用账户'
    }
  }
  const handlerAction = async (type: string, item?: any) => {
    try {
      if (type === 'delete') {
        await rule_del({ id: item.id })
        message.success('删除成功')
        getList()
      } else {
        if (['add', 'edit'].includes(type)) {
          state.drawer.visible = true
          state.drawer.type = type
          state.drawer.width = type2Data[type]?.width
          state.drawer.title = type2Data[type]?.title
          state.drawer.data = item
        } else {
          state.dialog.visible = true
          state.dialog.type = type
          state.dialog.width = type2Data[type]?.width
          state.dialog.title = type2Data[type]?.title
          state.dialog.data = item
        }
      }
    } catch (e) {
      console.log(e)
    }
  }
  const onEvent = ({ cmd, data }: any) => {
    if (state.drawer.visible) {
      state.drawer.visible = false
    }
    if (state.dialog.visible) {
      state.dialog.visible = false
    }
    getList()
  }
  return {
    pageChange,
    searchForm,
    searchConfig,
    state,
    getList,
    handlerAction,
    onEvent
  }
}
