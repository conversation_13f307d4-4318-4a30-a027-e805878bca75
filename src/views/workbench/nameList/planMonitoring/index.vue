<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>计划监测</div>
      </template>
      <template #extra>
        <a-button type="primary" v-auth="['planMonitoring_add']" @click="handlerAction('add')">新增监测</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options">
        </SearchBaseLayout>
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
          <template #bodyCell="{ scope }">
            <template v-if="['name'].includes(scope.column.key)">
              <a-tooltip placement="topLeft">
                <template #title v-if="scope.record[scope.column.key]?.length > 10">{{
                  scope.record[scope.column.key]
                }}</template>
                <div class="text_overflow">
                  {{ scope.record[scope.column.key] }}
                </div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'age'">
              <a-tooltip placement="topLeft">
                <template
                  #title
                  v-if="
                    scope.record?.age
                      ?.map((it) => it.join('-').replace('60-100', '60+').replace('50-100', '50+'))
                      ?.join(',')?.length > 15
                  "
                  >{{
                    scope.record?.age
                      ?.map((it) => it.join('-').replace('60-100', '60+').replace('50-100', '50+'))
                      ?.join(',')
                  }}</template
                >
                <div class="text_overflow">
                  {{
                    scope.record?.age
                      ? scope.record?.age
                          ?.map((it) => it.join('-').replace('60-100', '60+').replace('50-100', '50+'))
                          ?.join(',')
                      : '不限'
                  }}
                </div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'launch_price'">
              {{
                scope.record?.launch_price
                  ? scope.record?.launch_price?.join('-').replace('7000-11000', '7000-不限')
                  : '不限'
              }}
            </template>
            <template v-if="scope.column.key === 'gender'">
              {{ !scope.record?.gender ? '不限' : scope.record?.gender === 'GENDER_MALE' ? '男' : '女' }}
            </template>
            <template v-if="scope.column.key === 'account_num'">
              <a-button
                v-if="scope.record.all === 1"
                type="link"
                class="p-0!"
                size="small"
                :disabeld="scope.record.account_num"
                @click="handlerAction('accounts', scope.record)"
                >{{ scope.record?.account_num || 0 }}</a-button
              >
              <span v-else>全部账户</span>
            </template>
            <template v-if="scope.column.key === 'area_json'">
              <a-button
                type="link"
                class="p-0!"
                size="small"
                :disabeld="scope.record.area"
                @click="handlerAction('areas', scope.record)"
              >
                <span v-if="scope.record.area?.length">{{ scope.record.area?.length }}个区域</span>
                <span v-else>不限</span>
              </a-button>
            </template>
            <template v-if="scope.column.key === 'action'">
              <a-button
                type="link"
                class="p-0!"
                size="small"
                v-auth="['planMonitoring_edit']"
                @click="handlerAction('edit', scope.record)"
                >编辑</a-button
              >
              <a-popconfirm
                title="是否确认删除当前数据？"
                placement="topRight"
                @confirm="handlerAction('delete', scope.record)"
              >
                <a-button size="small" v-auth="['planMonitoring_delete']" type="link" class="pa-0!">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      destroyOnClose
      :footer="null"
      :centered="true"
      @cancel="
        () => {
          getList()
        }
      "
    >
      <applicationAccountModal
        v-if="['accounts'].includes(state.dialog.type)"
        :data="state.dialog.data"
        @onEvent="onEvent"
      />

      <areasModal v-if="state.dialog.type === 'areas'" :item="state.dialog.data" @onEvent="onEvent" />
    </a-modal>
    <a-drawer
      v-model:open="state.drawer.visible"
      :title="state.drawer.title"
      :width="state.drawer.width"
      placement="right"
      destroyOnClose
      @close="
        () => {
          state.drawer.visible = false
          getList()
        }
      "
    >
      <rulesModal v-if="['add', 'edit'].includes(state.drawer.type)" :item="state.drawer.data" @onEvent="onEvent" />
    </a-drawer>
  </div>
</template>

<script setup>
  import applicationAccountModal from './components/applicationAccountModal.vue'
  import rulesModal from './components/rulesModal.vue'
  import areasModal from './components/areasModal.vue'
  import datas from './data'
  const { pageChange, searchForm, searchConfig, state, getList, handleChange, handlerAction, onEvent } = datas()
  getList()
</script>

<style lang="scss" scoped></style>
