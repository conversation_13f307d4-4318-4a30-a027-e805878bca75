<template>
  <div class="form-scroll-wrapper">
    <a-form
      class="form-scroll-box"
      :model="state.form"
      ref="ruleForm"
      :colon="true"
      labelAlign="left"
      :labelCol="{ style: { width: '110px' } }"
    >
      <areaItem :data="state.areaData" @change="handlerChange" />
    </a-form>
    <div class="footer form-scroll-btn">
      <a-button @click="emit('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" v-auth="['planMonitoring_edit']" :loading="state.loading" @click="submitForm(ruleForm)"
        >确定</a-button
      >
    </div>
  </div>
</template>
<script setup name="areaModal" lang="ts">
  import areaItem from './comps/area.vue'
  import { reactive, ref, onMounted, provide } from 'vue'
  import { rule_sale } from '../index.api'
  import { message } from 'ant-design-vue'
  const props = defineProps(['item', 'type'])
  const emit = defineEmits(['onEvent'])
  const ruleForm = ref(null)
  const state = reactive({
    loading: false,
    areaData: undefined as any,
    form: {}
  })
  const handlerChange = ({ data }: any) => {
    state.areaData = data
  }
  const initData = async () => {
    try {
      if (props.item?.id) {
        const { area_json } = props.item
        state.areaData = JSON.parse(area_json)
      }
    } catch (error) {
      message.error('area_json获取数据有误')
      console.error(error)
    }
  }
  initData()
  const handlerParams = (data: any) => {
    const { area, type, tag_ids, tag_lists } = data
    if (area == null) {
      return []
    } else {
      if (type === '1') {
        return tag_ids.map((it: any) => {
          return String(parseInt(it[it.length - 1]))
        })
      } else {
        // 发展划分
        return tag_lists.map((it: string) => String(it))
      }
    }
  }
  const submitForm = async (formEl: any) => {
    try {
      state.loading = true
      await formEl.validate()

      let params = {
        ...props.item,
        area: handlerParams(state.areaData),
        area_json: JSON.stringify(state.areaData),
        account_ids: props.item.account_list
      } as any
      console.log('params', params)
      await rule_sale(params)
      emit('onEvent', { cmd: 'submit' })
      message.success('保存成功')
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
  provide('ruleForm', ruleForm)
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
