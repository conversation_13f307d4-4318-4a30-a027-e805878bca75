<template>
  <div class="comp_wrapper rules-modal-wrapper">
    <div class="form-scroll-wrapper con">
      <a-form
        class="form-scroll-box"
        :model="state.form"
        ref="ruleForm"
        :rules="rules"
        :colon="true"
        labelAlign="left"
        :labelCol="{ style: { width: '110px' } }"
      >
        <a-form-item label="监测名称" name="name" class="pt-18px">
          <a-input :maxlength="50" show-count v-model:value.trim="state.form.name" placeholder="请输入监测名称" />
        </a-form-item>
        <areaItem v-if="state.areaData || state.isFirst" :data="state.areaData" @change="handlerChange" />
        <gender v-if="state.genderData || state.isFirst" :data="state.genderData" @change="handlerChange" />
        <age v-if="state.ageData || state.isFirst" :data="state.ageData" @change="handlerChange" />
        <phonePrice v-if="state.priceData || state.isFirst" :data="state.priceData" @change="handlerChange" />
        <a-form-item label="应用账户" name="all">
          <a-radio-group
            :disabled="item?.id"
            v-model:value="state.form.all"
            @change="
              () => {
                state.form.accountSelected = undefined
              }
            "
          >
            <a-radio :value="2">全部账户</a-radio>
            <a-radio :value="1">指定账户</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          name="accountSelected"
          v-if="[1].includes(Number(state.form.all))"
          class="mt--14px"
          :class="[!isMobile ? 'pl-110px' : '']"
        >
          <a-select
            :show-search="true"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            v-model:value="state.form.accountSelected"
            :filter-option="filterOption"
            placeholder="请选择广告账户"
            allowClear
            mode="multiple"
            max-tag-count="responsive"
            show-arrow
          >
            <a-select-option
              v-for="item in state.accountsList"
              :key="item.token_account_id"
              :label="item.account_name"
              :value="item.token_account_id"
            >
              <div>{{ item.account_name }}</div>
              <div class="font-size-12px c-#c1c1c1">ID:{{ item.token_account_id }}</div>
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
    <div class="comp_footer_btn">
      <a-button @click="emit('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="rulesModal" lang="ts">
  import areaItem from './comps/area.vue'
  import age from './comps/age.vue'
  import phonePrice from './comps/phonePrice.vue'
  import gender from './comps/gender.vue'
  import { useApp } from '@/hooks'
  import { reactive, ref, onMounted, provide, watch } from 'vue'
  import { rule_sale, rule_info } from '../index.api'
  import { getOrglist } from '@/views/workbench/adAccount/ocean/index.api'
  import { message } from 'ant-design-vue'
  const { isMobile } = useApp()
  const props = defineProps(['item', 'type'])
  const emit = defineEmits(['onEvent'])
  const ruleForm = ref<HTMLFormElement | null>(null)
  const rules = {
    name: [{ required: true, message: '请输入名称', trigger: ['change', 'blur'] }],
    all: [{ required: true, message: '请选择应用账户', trigger: ['change', 'blur'] }],
    accountSelected: [{ required: true, message: '请选择广告账户', trigger: ['change', 'blur'] }]
  }
  const filterOption = (input: string, option: any) => {
    return (
      (option?.label || option?.account_name || option?.name).toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
      String(option.value).indexOf(input) >= 0
    )
  }
  const state = reactive({
    isFirst: true,
    loading: false,
    accountsList: [] as any, //广告账户列表
    form: {
      name: undefined,
      gender: undefined,
      all: 2 as any,
      accountSelected: undefined
    },
    areaData: undefined as any,
    priceData: undefined as any,
    ageData: undefined as any,
    genderData: undefined as any
  })
  const reviewParams = (type: string, item: any) => {
    if (type === 'age') {
      if (item === null) {
        state.ageData = {
          age: null
        }
      } else {
        let ageList = item?.map((it: any) => {
          return it.map((k: string) => Number(k)).join('-')
        })
        state.ageData = {
          age: ageList
        }
      }
    } else if (type === 'price') {
      if (item === null) {
        state.priceData = {
          price: null
        }
      } else {
        let price = item?.map((k: string) => Number(k)).join('-')
        state.priceData = {
          price
        }
      }
    } else if (type === 'gender') {
      state.genderData = {
        gender: item ? item : null
      }
    }
  }

  const initData = async () => {
    try {
      if (props.item?.id) {
        state.isFirst = false
        let res = await rule_info({ id: props.item.id })
        const { age, launch_price, name, area_json, account_list, gender, all } = res.data
        if (area_json) {
          state.areaData = JSON.parse(area_json)
        } else {
          message.error('area_json获取数据有误')
          // 因为不可能出现这种情况，一旦出现这种情况，先回显默认值
          state.areaData = {
            area: undefined,
            type: '1',
            selected: [] as any,
            upStatus: false,
            tag_ids: [],
            tag_lists: []
          }
        }
        reviewParams('age', age)
        reviewParams('price', launch_price)
        reviewParams('gender', gender)
        state.form.name = name
        state.form.all = all
        if (account_list.length) {
          state.form.accountSelected = account_list
        }
      }
    } catch (error) {
      console.error(error)
      message.error('获取数据有误')
    }
  }
  const handlerChange = ({ type, data }: any) => {
    state[`${type}Data`] = data
    // if (type === 'area') {
    //   state.areaData = data
    // } else if (type === 'price') {
    //   state.priceData = data
    // } else if (type === 'age') {
    //   state.ageData = data
    // } else if (type === 'gender') {
    //   state.genderData = data
    // }
  }
  const handlerParams = (type: string, data: any) => {
    if (type === 'area') {
      const { area, type, tag_ids, tag_lists } = data || {
        area: null
      }
      if (area == null) {
        return []
      } else {
        if (type === '1') {
          return tag_ids.map((it: any) => {
            return String(parseInt(it[it.length - 1]))
          })
        } else {
          // 发展划分
          return tag_lists
        }
      }
    } else if (type === 'age') {
      const { age, selected } = data || { age: null }
      if (!age?.length || age == null) {
        return []
      } else if (Array.isArray(age) && age.length === 1 && age[0] === 'selfDefine') {
        return selected.map((item: any) => item.split('-'))
      } else {
        return age.map((it: any) => it.split('-'))
      }
    } else if (type === 'price') {
      const { price, start, end } = data || { price: null }
      if (price == null) {
        return []
      } else if (price === 'selfDefine') {
        return [start, end]
      } else {
        return price.split('-')
      }
    } else if (type === 'gender') {
      const { gender } = data || { gender: null }
      return gender
    }
  }

  const submitForm = async (formEl: any) => {
    try {
      state.loading = true
      await formEl.validate()
      let params = {
        name: state.form.name,
        launch_price: handlerParams('price', state.priceData),
        gender: handlerParams('gender', state.genderData),
        age: handlerParams('age', state.ageData),
        area: handlerParams('area', state.areaData),
        area_json: JSON.stringify(
          !state.areaData?.area
            ? {
                area: null,
                type: '1',
                selected: [] as any,
                upStatus: false,
                tag_ids: [],
                tag_lists: []
              }
            : state.areaData
        ),
        all: state.form.all,
        account_ids: state.form.all === 2 ? [] : state.form.accountSelected // 不传代表是选择全部
      } as any
      if (props.item?.id) {
        params.id = props.item.id
      }
      console.log('params', params)
      await rule_sale(params)
      emit('onEvent', { cmd: 'submit' })
      message.success('保存成功')
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
  const query_account_list = async () => {
    try {
      let res: any = await getOrglist({ page: 1, page_size: 9999, type: 2 })
      if (res.code === 0) {
        state.accountsList = res.data.list
      }
    } catch (e) {
      console.log(e)
    }
  }
  onMounted(() => {
    query_account_list()

    initData()
  })
  provide('ruleForm', ruleForm)
</script>
<style lang="scss" scoped>
  .rules-modal-wrapper,
  .comp_wrapper {
    padding-top: 6px !important;
    .form-scroll-wrapper {
      .form-scroll-box {
        max-height: 100%;
        padding-bottom: 40px;
      }
    }
    .con {
      overflow: auto;
      height: calc(100% - 64px);
    }
  }
</style>
