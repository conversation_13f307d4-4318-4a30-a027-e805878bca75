<template>
  <div>
    <SearchBaseLayout
      :data="searchConfig.data"
      @changeValue="searchForm"
      :actions="searchConfig.options"
      :batchSetData="state.batchSetData"
    >
    </SearchBaseLayout>
    <TableZebraCrossing
      class="mt-16px"
      :data="state.tableConfigOptions"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: (record: any) => ({
          disabled: data?.includes(record.token_account_id)
        })
      }"
      @change="pageChange"
    >
      <template #bodyCell="{ scope }">
        <template
          v-if="['name', 'account_name', 'admin_name', 'oceanengine_name', 'admin_name'].includes(scope.column.key)"
        >
          <a-tooltip placement="topLeft">
            <template #title v-if="scope.record[scope.column.key]?.length > 15">{{
              scope.record[scope.column.key]
            }}</template>
            <div class="text_overflow">
              {{ scope.record[scope.column.key] }}
            </div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.key === 'remarks'">
          <div class="flex items-center">
            <a-tooltip placement="top" v-if="scope.record.remarks">
              <template #title>{{ scope.record.remarks }}</template>
              <div class="text_overflow_row1">
                {{ scope.record.remarks }}
              </div>
            </a-tooltip>
            <span v-else>--</span>
            <EditOutlined class="c-#FE9D35 ml4px" @click="remarkEdit(scope.record)" />
          </div>
        </template>
        <template v-if="scope.column.key === 'empower_status'">
          <div class="flex-y-center">
            <div :class="empowerStatusClsEnum(scope.record.empower_status)">
              {{ scope.record.empower_status == 1 ? '有效' : '失效' }}
            </div>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal v-model:open="state.remarkOpen" title="备注" @ok="editRemarks">
      <a-textarea
        class="mb-50px"
        v-model:value="state.remarkItem.remarks"
        placeholder="请输入备注"
        :autoSize="false"
        :rows="4"
        show-count
        :maxlength="50"
      />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import { onMounted, reactive, ref, createVNode } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { EditOutlined } from '@ant-design/icons-vue'
  import { getOrglist, oceanengine_batchedit } from '@/views/workbench/adAccount/ocean/index.api'
  import { rule_change_account } from '../index.api'
  import { empowerStatusClsEnum } from '@/utils'
  const props = defineProps(['id', 'data'])
  const emit = defineEmits(['onEvent'])
  const columns = [
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 180
    },
    {
      title: '账户ID',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      width: 160
    },
    {
      title: '管家账号',
      dataIndex: 'oceanengine_name',
      key: 'oceanengine_name',
      width: 140
    },
    {
      title: '授权状态',
      dataIndex: 'empower_status',
      key: 'empower_status',
      width: 100
    },
    {
      title: '添加人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 160
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    }
  ]
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'account_name',
        value: undefined,
        props: {
          placeholder: '请输入账户名称'
        }
      },
      {
        type: 'input.text',
        field: 'token_account_id',
        value: undefined,
        props: {
          placeholder: '请输入账户ID'
        }
      },
      {
        type: 'input.text',
        field: 'oceanengine_name',
        value: undefined,
        props: {
          placeholder: '请输入管家账号'
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 12,
      //   md: 8,
      //   lg: 8,
      //   xl: 5,
      //   xxl: 5
      // }
    }
  })
  const batchSetCallback = (item: any) => {
    if (item.type === 'add') {
      try {
        Modal.confirm({
          title: '提示',
          content: createVNode('div', {}, `确认新增${state.selectedRowKeys.length}个应用账户？`),
          async onOk() {
            handleAction('add')
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      } catch (error) {
        console.error(error)
      }
    }
  }
  const state = reactive({
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'token_account_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 1100,
        y: 340
      },
      columns,
      dataSource: [],
      isResizable: false,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 20,
        size: 'small',
        showTotal: (total: String | Number) => `共${total}条数据`
      }
    },
    params: {
      page: 1,
      page_size: 20
    },
    remarkOpen: false,
    remarkItem: {
      remarks: undefined
    },
    batchSetData: {
      isShow: true,
      list: [
        {
          text: '新增',
          type: 'add'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let res = await getOrglist({ ...state.params, type: 2 })
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v: any) => {
    state.params = {
      ...state.params,
      ...v.formData
    }
    state.params.page = 1
    getList()
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRowKeys = selectedRowKeys
    state.batchSetData.isSelected = selectedRowKeys.length
  }
  const handleAction = async (type: string) => {
    try {
      if (type === 'add') {
        await rule_change_account({
          id: props.id,
          add_account_ids: state.selectedRowKeys
        })
        message.success('新增成功')
        state.selectedRowKeys = []
        emit('onEvent', { cmd: 'submit' })
      }
    } catch (err) {
      console.log(err)
    }
  }
  const remarkEdit = (item: any) => {
    state.remarkOpen = true
    state.remarkItem = JSON.parse(JSON.stringify(item || {}))
  }
  const editRemarks = async () => {
    try {
      await oceanengine_batchedit({
        ids: [state.remarkItem.id],
        type: 2,
        remark: state.remarkItem.remarks.trim()
      })
      message.success('修改成功')
      state.remarkOpen = false
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  onMounted(() => {
    getList()
  })
</script>
