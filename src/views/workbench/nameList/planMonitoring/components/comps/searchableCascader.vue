<template>
  <div class="searchable-cascader">
    <input v-model="searchText" placeholder="搜索行政区名称" class="search-input" @keydown.enter="handleSearch" />

    <div class="columns">
      <div class="column" v-for="(list, level) in columns" :key="level">
        <ul>
          <li
            v-for="item in list"
            :key="item.id"
            :class="{ active: isSelected(level, item), checked: isChecked(item) }"
            @click="selectItem(level, item)"
          >
            <span @click.stop="toggleCheck(item)">{{ item.name }}</span>
          </li>
        </ul>
      </div>
    </div>

    <div class="checked-result">
      <div v-for="path in checkedPaths" :key="path.map((i) => i.id).join('-')">
        {{ path.map((i) => i.name).join(' > ') }}
        <span @click="removePath(path)">❌</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'

  interface RegionItem {
    name: string
    id: number
    levels: number
    children?: RegionItem[]
  }

  // props: 传入 options 数据
  const props = defineProps<{
    options: RegionItem[]
  }>()

  // 状态
  const selectedPath = ref<RegionItem[]>([]) // 当前路径
  const checkedPaths = ref<RegionItem[][]>([]) // 勾选路径集合
  const searchText = ref('')

  // 获取每列要显示的数据
  const columns = computed(() => {
    const list: RegionItem[][] = []
    let cur = props.options
    list.push(cur)
    for (const node of selectedPath.value) {
      if (!node.children || node.children.length === 0) break
      cur = node.children
      list.push(cur)
    }
    return list
  })

  // 选择某一级
  function selectItem(level: number, item: RegionItem) {
    selectedPath.value = [...selectedPath.value.slice(0, level), item]
  }

  // 判断是否选中路径中某一级
  function isSelected(level: number, item: RegionItem) {
    return selectedPath.value[level]?.id === item.id
  }

  // 判断是否已勾选
  function isChecked(item: RegionItem) {
    return checkedPaths.value.some((path) => path.at(-1)?.id === item.id)
  }

  // 勾选当前完整路径
  function toggleCheck(item: RegionItem) {
    const fullPath = [...selectedPath.value.slice(0, item.levels - 1), item]
    const key = fullPath.map((i) => i.id).join('-')
    const existIndex = checkedPaths.value.findIndex((p) => p.map((i) => i.id).join('-') === key)
    if (existIndex > -1) {
      checkedPaths.value.splice(existIndex, 1)
    } else {
      checkedPaths.value.push(fullPath)
    }
  }

  // 删除某条路径
  function removePath(path: RegionItem[]) {
    checkedPaths.value = checkedPaths.value.filter(
      (p) => p.map((i) => i.id).join('-') !== path.map((i) => i.id).join('-')
    )
  }

  // 搜索路径并自动选中
  function handleSearch() {
    if (!searchText.value.trim()) return
    const matched = findPathByName(props.options, searchText.value.trim())
    if (matched.length > 0) {
      selectedPath.value = matched
      checkedPaths.value.push(matched)
      searchText.value = ''
    }
  }

  // 递归搜索路径
  function findPathByName(list: RegionItem[], keyword: string): RegionItem[] {
    for (const item of list) {
      if (item.name.includes(keyword)) return [item]
      if (item.children && item.children.length > 0) {
        const childPath = findPathByName(item.children, keyword)
        if (childPath.length > 0) return [item, ...childPath]
      }
    }
    return []
  }
</script>

<style scoped>
  .searchable-cascader {
    width: 100%;
  }
  .search-input {
    width: 300px;
    padding: 6px 8px;
    margin-bottom: 10px;
  }
  .columns {
    display: flex;
    gap: 12px;
  }
  .column {
    width: 160px;
    border: 1px solid #ddd;
    padding: 6px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
  }
  li {
    padding: 6px;
    cursor: pointer;
  }
  li.active {
    background-color: #409eff;
    color: #fff;
  }
  li.checked {
    font-weight: bold;
  }
  .checked-result {
    margin-top: 10px;
  }
  .checked-result span {
    color: red;
    margin-left: 8px;
    cursor: pointer;
  }
</style>
