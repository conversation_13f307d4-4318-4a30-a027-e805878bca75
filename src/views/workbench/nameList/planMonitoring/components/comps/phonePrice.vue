<template>
  <a-form-item
    label="定向手机价格"
    name="price"
    :rules="[
      {
        required: true,
        validator: (_: any, __: any, callback: Function) => {
          if (state.price === undefined) {
            return callback('请选择定向手机价格')
          } else {
            return callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ]"
    :class="[state.price === 'selfDefine' && 'mb-8px']"
  >
    <!-- <a-radio-group v-model:value="state.price" @change="radioChange">
      <a-radio-button value="-1">不限</a-radio-button>
      <a-radio-button value="2000-4000">2000-4000元</a-radio-button>
      <a-radio-button value="4000-6000">4000-6000元</a-radio-button>
      <a-radio-button value="6000-8000">6000-8000元</a-radio-button>
      <a-radio-button value="selfDefine">自定义</a-radio-button>
    </a-radio-group> -->

    <SelectItem v-model="state.price" :options="PriceTypes" :defaultValue="null" @update:item="radioChange" />
  </a-form-item>

  <div class="flex flex-y-center" v-if="state.price === 'selfDefine'" :class="[!isMobile ? 'pl-110px' : '']">
    <a-form-item
      name="start"
      :rules="[
        {
          required: true,
          validator: (_: any, __: any, callback: Function) => {
            if (!state.start) {
              return callback('请选择价格')
            } else if (state?.end && Number(state.start) >= Number(state?.end)) {
              return callback('请选择合理的区间')
            } else {
              return callback()
            }
          },
          trigger: ['blur', 'change']
        }
      ]"
    >
      <a-select
        :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
        v-model:value="state.start"
        :options="startList"
        :filter-option="filterOption"
        placeholder="请选择"
        class="w-118px!"
        @change="change"
      >
      </a-select>
    </a-form-item>
    <div class="ml-10px mr-10px mt--18px">-</div>
    <a-form-item
      name="end"
      :rules="[
        {
          required: true,
          validator: (_: any, __: any, callback: Function) => {
            if (!state.end) {
              return callback('请选择价格')
            } else if (state?.start && Number(state.end) <= Number(state?.start)) {
              return callback('请选择合理的区间')
            } else {
              return callback()
            }
          },
          trigger: ['blur', 'change']
        }
      ]"
    >
      <a-select
        :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
        v-model:value="state.end"
        :options="endList"
        :filter-option="filterOption"
        placeholder="请选择"
        class="w-118px!"
        @change="change"
      >
      </a-select>
    </a-form-item>
  </div>
</template>
<script setup lang="ts">
  import { useApp } from '@/hooks'
  import { ref, reactive, inject, Ref, watch } from 'vue'
  import { cloneDeep } from 'lodash-es'
  const ruleForm = inject<Ref<HTMLFormElement | null>>('ruleForm')
  const { isMobile } = useApp()
  const props = defineProps({
    data: {
      type: Object,
      deflaut: () => {}
    }
  })
  const filterOption = (input: string, option: any) => {
    return (option?.label).toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const list = [
    {
      label: '500元',
      value: '500'
    },
    {
      label: '1000元',
      value: '1000'
    },
    {
      label: '1500元',
      value: '1500'
    },
    {
      label: '2000元',
      value: '2000'
    },
    {
      label: '2500元',
      value: '2500'
    },
    {
      label: '3000元',
      value: '3000'
    },
    {
      label: '3500元',
      value: '3500'
    },
    {
      label: '4000元',
      value: '4000'
    },
    {
      label: '5000元',
      value: '5000'
    },
    {
      label: '6000元',
      value: '6000'
    },
    {
      label: '7000元',
      value: '7000'
    },
    {
      label: '8000元',
      value: '8000'
    },
    {
      label: '9000元',
      value: '9000'
    },
    {
      label: '10000元',
      value: '10000'
    }
  ]
  const PriceTypes = [
    { label: '不限', value: null },
    { label: '2000-4000元', value: '2000-4000' },
    { label: '4000-6000元', value: '4000-6000' },
    { label: '6000-8000元', value: '6000-8000' },
    { label: '自定义', value: 'selfDefine' }
  ]
  const startList = ref([{ label: '0元', value: '0' }, ...list])
  const endList = ref([...list, { label: '不限', value: '11000' }])
  const emit = defineEmits(['update:modelValue', 'change'])
  const state = reactive({
    price: null,
    start: undefined,
    end: undefined
  }) as any
  const radioChange = (v: any) => {
    ruleForm?.value?.validateFields(['price'])
    emit('change', { type: 'price', data: state })
  }
  const change = () => {
    ruleForm?.value?.validateFields(['start', 'end'])
    emit('change', { type: 'price', data: state })
  }

  const init = () => {
    if (props.data) {
      const { price } = props.data
      if (price === null) {
        state.price = null
      } else {
        let list = cloneDeep(PriceTypes)?.filter((k) => k.value !== null && k.value !== 'selfDefine') || []
        let flag = list.some((obj: any) => obj.value === price)
        if (flag) {
          state.price = price
        } else {
          state.price = 'selfDefine'
          state.start = price.split('-')?.[0]
          state.end = price.split('-')?.[1]
        }
        console.log('price', state)
      }
    }
  }
  init()
  // watch(() => props.data, init, { immediate: true })
</script>
