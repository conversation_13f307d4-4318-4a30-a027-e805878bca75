<template>
  <a-form-item
    label="屏蔽地域"
    name="area"
    :rules="[
      {
        required: true,
        validator: (_: any, __: any, callback: Function) => {
          console.log('===', state.area)
          if (state.area === undefined) {
            return callback('请选择屏蔽地域')
          } else {
            return callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ]"
    :class="[state.area === 1 && 'mb-8px']"
  >
    <!-- <a-radio-group v-model:value="state.area" @change="radioChange">
      <a-radio-button :value="-1">不限</a-radio-button>
      <a-radio-button :value="1">按行政区域划分</a-radio-button>
      <a-radio-button :value="2">按商圈</a-radio-button>
    </a-radio-group> -->
    <SelectItem
      v-model="state.area"
      :options="[
        { label: '不限', value: null },
        { label: '按行政区域划分', value: 1 }
      ]"
      :defaultValue="null"
      @update:item="radioChange"
    />
  </a-form-item>

  <div class="dashed-box" v-if="[1].includes(state.area)">
    <a-form-item
      name="areaSelected"
      class="mb-0"
      :rules="[
        {
          required: true,
          validator: (_: any, __: any, callback: Function) => {
            if (!state.tag_ids.length) {
              return callback(state.type == '1' ? '请选择行政区域' : '请选择城市')
            } else {
              return callback()
            }
          },
          trigger: ['blur', 'change']
        }
      ]"
    >
      <div class="top mb-8px">
        <div>
          <span class="font-bold">已选：</span>
          <span v-if="state.type === '1'">{{ state.tag_ids?.length }}个行政区域</span>
          <span v-else>{{ state.tag_lists?.length }}个城市</span>
        </div>
        <a-button
          type="link"
          class="p-0!"
          size="small"
          :disabled="!state.tag_ids.length"
          @click="
            () => {
              if (state.type === '1') {
                state.tag_ids = []
              } else {
                state.tag_ids = []
                state.tag_lists = []
              }
            }
          "
          >清空</a-button
        >
      </div>
      <div class="content">
        <div class="title">
          <a-tabs class="tab" v-model:activeKey="state.type" size="small" @change="tabChange">
            <a-tab-pane key="1" tab="地理划分"></a-tab-pane>
            <a-tab-pane key="2" tab="发展划分"></a-tab-pane>
          </a-tabs>
          <a-button class="up" size="small" type="link" @click="state.upStatus = !state.upStatus">
            <DoubleRightOutlined class="icon" :class="[!state.upStatus && 'active']" />
            <span>{{ !state.upStatus ? '收起' : '展开' }}</span>
          </a-button>
        </div>
        <div class="list-wrapper" :class="[state.upStatus && 'h-0! p-0! border-b-0!']">
          <div v-show="!state.upStatus" style="color: rgba(0, 0, 0, 0.88)" class="mb-8px">
            {{ state.type == '1' ? '地理' : '等级' }}
          </div>
          <a-form-item-rest>
            <a-auto-complete
              v-show="!state.upStatus"
              v-model:value="searchConfig.search"
              :options="state.type == '1' ? filteredOptions : filteredDivideOptions"
              @focus="onFocus"
              @select="onCommonSelect"
              class="mb-8px autocomplete"
            >
              <a-input placeholder="不支持按拼音、拼音首字母">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
              <template #option="item">
                {{ item.fullPath }}
              </template>
            </a-auto-complete>
          </a-form-item-rest>
          <CascaderPanelSelect
            v-show="!state.upStatus"
            ref="cascaderPanelRef"
            :type="state.type + ''"
            v-model="state.tag_ids"
            @change="cascaderPanelChange"
            multiple
          ></CascaderPanelSelect>
        </div>
      </div>
    </a-form-item>
  </div>
  <!-- <div class="dashed-box" v-if="['2'].includes(modelValue?.area)">
    <a-form-item
      name="start"
      class="mb-0"
      :rules="[
        {
          required: true,
          validator: (_: any, __: any, callback: Function) => {
            if (!state.tag_ids.length) {
              return callback('请选择商圈')
            } else {
              return callback()
            }
          },
          trigger: ['blur', 'change']
        }
      ]"
    >
      <div class="top mb-8px">
        <div>
          <span class="font-bold">已选：</span>
          <span>{{ state.tag_ids?.length }}个商圈</span>
        </div>
        <a-button type="link" class="p-0!" size="small" @click="state.selected = []">清空</a-button>
      </div>
      <div class="content">
        <div class="title justify-end! w-100%">
          <a-button class="up" size="small" type="link" @click="state.upStatus = !state.upStatus">
            <DoubleRightOutlined class="icon" :class="[!state.upStatus && 'active']" />
            <span>{{ !state.upStatus ? '收起' : '展开' }}</span>
          </a-button>
        </div>
        <div class="list-wrapper" :class="[state.upStatus && 'h-0! p-0! border-b-0!']">
          <LocationSearchInput placeholder="请输入商圈名称" @select-location="handleLocationSelect" />

          <GeoRadiusSelector
            :search-location="selectedLocation"
            :default-center="[116.6586, 40.1441]"
            :default-radius="6000"
            @confirm="onConfirm"
          />
        </div>
      </div>
    </a-form-item>
  </div> -->
</template>
<script setup lang="ts">
  import { SearchOutlined } from '@ant-design/icons-vue'
  import mockData from './mock.json'
  import ChinaCitysData from './ChinaCitys.json'
  import DevelopmentDivision from './DevelopmentDivision.json'
  import CascaderPanelSelect from './CascaderPanelSelect.vue'

  import { DoubleRightOutlined } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  import { ref, reactive, computed, watch, inject, Ref } from 'vue'
  const ruleForm = inject<Ref<HTMLFormElement | null>>('ruleForm')
  const { isMobile } = useApp()
  const props = defineProps({
    ruleForm: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  })
  const emit = defineEmits(['update:modelValue', 'change'])
  const state = reactive({
    area: null,
    type: '1',
    selected: [] as any,
    upStatus: false,
    tag_ids: [],

    tag_lists: []
  })
  const searchConfig = reactive({
    search: undefined as any,
    searchData: [] as any
  })
  const radioChange = (v: any) => {
    ruleForm?.value?.validateFields(['area'])
    state.type = '1'
    reset()
    emit('change', { type: 'area', data: state })
  }
  // 处理省市县的数据，方便搜索
  const flattenCities = (data, parentPath = '', parentIds = []) => {
    return data.reduce((acc, node) => {
      const fullPath = parentPath ? `${parentPath}/${node.name}` : node.name
      const fullIds = [...parentIds, node.id]
      const item = {
        label: node.name,
        value: node.name,
        fullPath,
        fullIds,
        currentId: node.id, // 当前节点ID
        raw: node
      }
      acc.push(item)
      if (node.children && node.children.length) {
        acc.push(...flattenCities(node.children, fullPath, fullIds))
      }
      return acc
    }, [])
  }
  const flattenDivision = (data, parentPath = '', parentIds = []) => {
    return data.reduce((acc, node) => {
      // 处理分组（如“一线城市”）
      if (node.children) {
        const groupPath = parentPath ? `${parentPath}/${node.name}` : node.name
        const groupIds = [...parentIds, node.id]
        acc.push({
          label: node.name,
          value: node.name,
          fullPath: groupPath,
          fullIds: groupIds,
          currentId: node.id,
          raw: node,
          isGroup: true // 标记为分组
        })
        // 递归处理子节点（城市）
        if (node.children && node.children.length) {
          acc.push(...flattenCities(node.children, groupPath, groupIds))
        }
      } else {
        // 处理城市节点（如“北京市”）
        const fullPath = parentPath ? `${parentPath}/${node.name}` : node.name
        const fullIds = [...parentIds, node.id]
        acc.push({
          label: node.name,
          value: node.name,
          fullPath,
          fullIds,
          currentId: node.id,
          raw: node,
          isGroup: false // 标记为非分组
        })
      }
      return acc
    }, [])
  }
  const cityies = ref(flattenCities(ChinaCitysData?.[0]?.children))
  const division = ref(flattenDivision(DevelopmentDivision))
  const filteredOptions = computed(() => {
    if (!searchConfig.search) return []
    return searchConfig.searchData.filter((item: any) =>
      item.fullPath.toLowerCase().includes(searchConfig.search.toLowerCase())
    )
  })
  const filteredDivideOptions = computed(() => {
    if (!searchConfig.search) return []
    return division.value.filter(
      (item: any) => !item.isGroup && item.label.includes(searchConfig.search.toLowerCase()) // 只匹配城市
    )
  })
  const onFocus = () => {
    searchConfig.searchData = state.type === '1' ? cityies.value : division.value
  }
  const onSelect = (_: any, options: any) => {
    const newIds = options.fullIds
    if (!Array.isArray(newIds) || newIds.length === 0) {
      console.error('无效的区域ID:', newIds)
      return
    }
    //  是否完全重复
    const isExactDuplicate = state.tag_ids.some(
      (existingIds) => existingIds.length === newIds.length && existingIds.every((id, index) => id === newIds[index])
    )
    if (isExactDuplicate) {
      console.warn('重复选择:', options.fullPath)
      return
    }
    // 检查父子关系
    let isParentSelected = false
    let shouldRemoveChildren = false
    // 先检查是否已有父级存在
    const hasParent = state.tag_ids.some((existingIds) => {
      if (existingIds.length < newIds.length) {
        return existingIds.every((id, index) => id === newIds[index])
      }
      return false
    })
    // 再检查是否要覆盖子级
    const hasChildren = state.tag_ids.filter((existingIds) => {
      if (existingIds.length > newIds.length) {
        return newIds.every((id, index) => id === existingIds[index])
      }
      return false
    })
    if (hasParent) {
      // 情况1：已存在父级，阻止添加子级
      console.warn('已存在更高级区域:', options.fullPath)
      return
    } else if (hasChildren.length > 0) {
      // 情况2：要添加父级，覆盖所有子级
      shouldRemoveChildren = true
      isParentSelected = true
    }
    // 执行添加/覆盖操作
    if (shouldRemoveChildren) {
      // 移除所有被覆盖的子级
      state.tag_ids = state.tag_ids.filter(
        (existingIds) => !(existingIds.length > newIds.length && newIds.every((id, index) => id === existingIds[index]))
      )
    }
    // 6. 添加新项（除非被父级阻止）
    if (!hasParent) {
      state.tag_ids.push(newIds)
      console.log('添加成功:', newIds, options.fullPath)
    }
    // 7. 特殊处理：如果添加的是省级，检查是否有重复的市级
    if (isParentSelected && newIds.length === 1) {
      state.tag_ids = state.tag_ids.filter(
        (item, index, self) => item.length !== 1 || self.findIndex((i) => i[0] === item[0]) === index
      )
    }
    console.log('添加成功', newIds, state.tag_ids)
  }
  const onDivideSelect = (val: any, options: any) => {
    console.log('val', val, options)
    const { fullIds, isGroup } = options

    // 如果是分组标题
    if (isGroup) return

    // 检查是否已存在完全相同的 fullIds
    const isAlreadySelected = state.tag_ids.some((ids) => JSON.stringify(ids) === JSON.stringify(fullIds))

    if (isAlreadySelected) {
      console.log('当前城市已选中，跳过添加')
      return
    }

    // 检查是否父级已选中（当前 fullIds 是某个已选 fullIds 的子集）
    const isParentSelected = state.tag_ids.some((ids) => {
      // 如果已选 fullIds 是当前 fullIds 的前缀，说明父级已选中
      return ids.length < fullIds.length && ids.every((id, index) => id === fullIds[index])
    })

    if (isParentSelected) {
      console.log('父级已选中，跳过添加')
      return
    }

    // 检查是否子级已选中（如果当前 fullIds 是某个已选 fullIds 的父级，需替换）
    const childIndex = state.tag_ids.findIndex((ids) => {
      return ids.length > fullIds.length && fullIds.every((id, index) => id === ids[index])
    })

    if (childIndex !== -1) {
      // 如果当前 fullIds 是某个已选 fullIds 的父级，替换子级
      state.tag_ids.splice(childIndex, 1)
    }

    // 添加当前 fullIds
    state.tag_ids.push([...fullIds])
    if (!state.tag_lists.includes(options.currentId)) {
      state.tag_lists.push(options.currentId)
    }

    console.log('添加成功:', fullIds, state.tag_ids, state.tag_lists)
  }
  const onCommonSelect = (val: any, options: any) => {
    if (state.type === '1') {
      onSelect(val, options)
    } else {
      onDivideSelect(val, options)
    }
  }
  const reset = () => {
    searchConfig.search = undefined
    searchConfig.searchData = []
    state.tag_ids = []
    state.tag_lists = []
  }
  const tabChange = () => {
    ruleForm?.value?.clearValidate(['areaSelected'])
    emit('change', { type: 'area', data: state })
    reset()
  }
  const cascaderPanelChange = (val: any, options: any) => {
    // 只有在发展划分的时候存储options
    // 因为JSON.stringify无法存储循环
    console.log('vvvvv', val, options)
    if (state.type == '2') {
      let r = options.map((it) => {
        if (it.length === 1) {
          return it[0].children.map((it) => it.id)
        } else {
          return it[it.length - 1].id
        }
      })
      state.tag_lists = [...new Set(r.flat(4))]
    } else {
      state.tag_lists = []
    }
  }
  const init = () => {
    if (props?.data) {
      const { area, tag_ids, type, tag_lists } = props.data
      state.area = area
      state.tag_ids = tag_ids
      state.type = type
      if (type === '2') {
        state.tag_lists = tag_lists
      }
    }
  }
  // const selectedLocation = ref<{ name: string; location: [number, number] } | null>(null)

  // const handleLocationSelect = (val) => {
  //   selectedLocation.value = val
  // }
  // const onConfirm = (data) => {
  //   console.log('确认选择：', data)
  // }
  init()
  // watch(() => props.data, init, { immediate: true })
  watch(
    () => state.tag_ids,
    () => {
      emit('change', { type: 'area', data: state })
    },
    {
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .dashed-box {
    padding: 16px;
    border-radius: 8px;
    border: 1px dashed #d8d8d8;
    margin-bottom: 18px;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #313233;
    }
    .content {
      .title {
        color: rgba(0, 0, 0, 0.88);
        border: 1px solid #e6e8ed;
        background: #fafafb;
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 6px 6px 0px 0px;
        padding: 0 16px;
        border-bottom: none;
      }
      .up {
        color: #313233;
        padding: 0;
        display: flex;
        align-items: center;

        .icon {
          transition: all 0.3s ease-in-out;
          transform: rotate(90deg);
          &.active {
            transform: rotate(270deg);
          }
        }
      }
      .list-wrapper {
        border: 1px solid #e6e8ed;
        border-radius: 0px 0px 6px 6px;
        padding: 9px 7px;
        height: 370px;
        transition: height 0.3s ease;
      }
      .tab {
        :deep(.ant-tabs-nav) {
          margin: 0;
        }
        :deep(.ant-tabs-tab) {
          padding: 0 0 4px 0;
        }
        :deep(.ant-tabs-tab-btn) {
          margin-top: 6px;
          font-weight: 400;
        }
        :deep(.ant-tabs-nav::before) {
          border-bottom: none;
        }
      }
      .list {
        border-radius: 4px;
        border: 1px solid #e6e8ed;
        overflow-y: auto;
        height: 100%;
        padding: 8px 0;
        .item {
          height: 36px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          padding-left: 15px;
          &:hover {
            background-color: #f2f4f7;
          }
        }
        .all-select {
          background-color: #f2f4f7;
          position: sticky;
          top: -8px;
          z-index: 2;
        }
      }
    }
    .autocomplete {
      :deep(.ant-input-affix-wrapper, .ant-select-selection-search-input) {
        border-color: #d9d9d9 !important;
      }
      :deep(.ant-input-affix-wrapper .ant-input-prefix) {
        color: rgba(0, 0, 0, 0.88);
      }
    }
  }
</style>
