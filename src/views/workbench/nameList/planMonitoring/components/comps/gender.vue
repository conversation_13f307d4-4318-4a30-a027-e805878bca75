<template>
  <a-form-item
    label="定向性别"
    name="gender"
    :rules="[
      {
        required: true,
        validator: (_: any, __: any, callback: Function) => {
          if (state.gender === undefined) {
            return callback('请选择性别')
          } else {
            return callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ]"
  >
    <SelectItem v-model="state.gender" :options="Types" :defaultValue="null" @update:item="radioChange" />
  </a-form-item>
</template>
<script setup lang="ts">
  import { reactive, inject, Ref } from 'vue'
  const ruleForm = inject<Ref<HTMLFormElement | null>>('ruleForm')
  const props = defineProps({
    data: {
      type: Object,
      deflaut: () => {}
    }
  })
  const Types = [
    { label: '不限', value: null },
    { label: '男', value: 'GENDER_MALE' },
    { label: '女', value: 'GENDER_FEMALE' }
  ]

  const emit = defineEmits(['update:modelValue', 'change'])
  const state = reactive({
    gender: null
  }) as any
  const radioChange = (v: any) => {
    ruleForm?.value?.validateFields(['gender'])
    emit('change', { type: 'gender', data: state })
  }

  const init = () => {
    if (props.data) {
      const { gender } = props.data
      state.gender = gender
    }
  }
  init()
</script>
