<template>
  <a-form-item
    label="定向年龄"
    name="age"
    :rules="[
      {
        required: true,
        validator: (_: any, __: any, callback: Function) => {
          if (state.age === undefined) {
            return callback('请选择定向年龄')
          } else {
            return callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ]"
    :class="[state?.age?.includes('selfDefine') && 'mb-8px']"
  >
    <SelectItem v-model="state.age" multiple :options="AgeTypes" :defaultValue="null" @update:item="radioChange" />
  </a-form-item>

  <div class="dashed-box" v-if="state?.age?.includes('selfDefine')">
    <a-form-item
      name="start"
      class="mb-0"
      :rules="[
        {
          required: true,
          validator: (_: any, __: any, callback: Function) => {
            if (!state.selected.length) {
              return callback('请选择年龄段')
            } else {
              return callback()
            }
          },
          trigger: ['blur', 'change']
        }
      ]"
    >
      <div class="top mb-8px">
        <div>
          <span class="font-bold">已选：</span>
          <span>{{ state.selected?.length }}个年龄段</span>
        </div>
        <a-button type="link" class="p-0!" size="small" :disabled="!state.selected.length" @click="state.selected = []"
          >清空</a-button
        >
      </div>
      <div class="content">
        <div class="title">
          <span>年龄段</span>
          <a-button class="up" size="small" type="link" @click="state.upStatus = !state.upStatus">
            <DoubleRightOutlined class="icon" :class="[!state.upStatus && 'active']" />
            <span>{{ !state.upStatus ? '收起' : '展开' }}</span>
          </a-button>
        </div>
        <div class="list-wrapper" :class="[state.upStatus && 'h-0! p-0! border-b-0!']">
          <div class="list" v-show="!state.upStatus">
            <div class="item all-select">
              <a-checkbox v-model:checked="checkAll" :indeterminate="indeterminate" @change="checkAllHandler">
                全选
              </a-checkbox>
            </div>
            <a-checkbox-group v-model:value="state.selected" style="width: 100%" @change="checkHandler">
              <a-row>
                <a-col :span="24" v-for="(item, index) in list" class="item">
                  <a-checkbox :value="item.value">{{ item.label }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
        </div>
      </div>
    </a-form-item>
  </div>
</template>
<script setup lang="ts">
  import { DoubleRightOutlined } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  import { cloneDeep } from 'lodash-es'
  import { ref, reactive, computed, watch, inject, Ref, onMounted } from 'vue'
  const ruleForm = inject<Ref<HTMLFormElement | null>>('ruleForm')
  const { isMobile } = useApp()
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const list = ref([
    {
      label: '18-19',
      value: '18-19'
    },
    {
      label: '20-23',
      value: '20-23'
    },
    {
      label: '24-30',
      value: '24-30'
    },
    {
      label: '31-35',
      value: '31-35'
    },
    {
      label: '36-40',
      value: '36-40'
    },
    {
      label: '41-45',
      value: '41-45'
    },
    {
      label: '46-50',
      value: '46-50'
    },
    {
      label: '51-55',
      value: '51-55'
    },
    {
      label: '56-59',
      value: '56-59'
    },
    {
      label: '60+',
      value: '60-100'
    }
  ])
  const emit = defineEmits(['update:modelValue', 'change'])
  const state = reactive({
    age: null as any,
    selected: [] as any,
    upStatus: false
  })
  const AgeTypes = [
    { label: '不限', value: null },
    { label: '18-23', value: '18-23' },
    { label: '24-30', value: '24-30' },
    { label: '31-40', value: '31-40' },
    { label: '41-49', value: '41-49' },
    { label: '50+', value: '50-100' },
    { label: '自定义', value: 'selfDefine' }
  ]
  const radioChange = (v: any) => {
    console.log('v', v, state.age)
    ruleForm?.value?.validateFields(['age'])
    emit('change', { type: 'age', data: state })
  }
  const indeterminate = computed(() => {
    return !!state.selected.length && list.value.length > state.selected.length
  })
  const checkAll = computed(() => {
    return !!state.selected.length && list.value.length === state.selected.length
  })
  const checkAllHandler = (val: any) => {
    if (val.target.checked) {
      state.selected = list.value.map((it) => it.value)
    } else {
      state.selected = []
    }
  }
  const checkHandler = () => {
    emit('change', { type: 'age', data: state })
  }
  const initData = () => {
    if (props.data) {
      console.log('=====')
      const { age } = props.data
      if (age === null) {
        state.age = null
      } else {
        let list = cloneDeep(AgeTypes)?.filter((k) => k.value !== null && k.value !== 'selfDefine') || []
        if (Array.isArray(age)) {
          let flag = age.every((item) => list.some((obj: any) => obj.value === item))
          if (flag) {
            state.age = age
          } else {
            state.age = ['selfDefine']
            state.selected = age
          }
        }
        console.log('age', state)
      }
    }
  }
  initData()
  // watch(() => props.data, initData, { immediate: true })
</script>
<style lang="scss" scoped>
  .dashed-box {
    padding: 16px;
    border-radius: 8px;
    border: 1px dashed #d8d8d8;
    margin-bottom: 18px;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #313233;
    }
    .content {
      .title {
        color: rgba(0, 0, 0, 0.88);
        border: 1px solid #e6e8ed;
        background: #fafafb;
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 6px 6px 0px 0px;
        padding: 0 16px;
        border-bottom: none;
      }
      .up {
        color: #313233;
        padding: 0;
        display: flex;
        align-items: center;

        .icon {
          transition: all 0.3s ease-in-out;
          transform: rotate(90deg);
          &.active {
            transform: rotate(270deg);
          }
        }
      }
      .list-wrapper {
        border: 1px solid #e6e8ed;
        border-radius: 0px 0px 6px 6px;
        padding: 9px 7px;
        height: 284px;
        transition: height 0.3s ease;
      }
      .list {
        border-radius: 4px;
        border: 1px solid #e6e8ed;
        overflow-y: auto;
        height: 100%;
        padding: 8px 0;
        .item {
          height: 36px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          padding-left: 15px;
          &:hover {
            background-color: #f2f4f7;
          }
        }
        .all-select {
          background-color: #f2f4f7;
          position: sticky;
          top: -8px;
          z-index: 2;
        }
      }
    }
  }
</style>
