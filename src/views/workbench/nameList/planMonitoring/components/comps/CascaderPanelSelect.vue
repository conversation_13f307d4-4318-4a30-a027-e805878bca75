<template>
  <div>
    <div class="panel" :class="[isMobile && 'area-cascader-wrapper']">
      <a-cascader
        show-search
        v-model:value="value"
        :options="options"
        :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
        ref="cascaderPanel"
        :open="open"
        :multiple="multiple"
        :fieldNames="{
          label: 'name',
          value: 'id',
          children: 'children'
        }"
        @change="handleChange"
        :key="state.timestamp"
        :loadData="type == '1' ? loadData : null"
        :dropdownRender="dropdownRender"
      >
      </a-cascader>
    </div>
  </div>
  <!--  -->
</template>
<script lang="tsx" setup>
  defineOptions({
    name: 'CascaderPanelSelect'
  })
  import { useApp } from '@/hooks'
  import ChinaCitysData from './ChinaCitys.json'
  import DevelopmentDivision from './DevelopmentDivision.json'
  import { Checkbox } from 'ant-design-vue'
  import { getRegion } from '../../index.api'
  import { ref, watch, nextTick, watchEffect, reactive, h, computed } from 'vue'
  import { isArray } from 'lodash-es'
  const { isMobile } = useApp()
  const value = ref<string[]>([])
  const cascaderPanel = ref()
  const open = ref(false)

  const props = defineProps({
    type: {
      type: String,
      default: 1
    },
    modelValue: {
      type: [String, Array]
    },
    multiple: Boolean,
    changeRefresh: Boolean,
    readonly: {
      type: Boolean,
      default: false
    }
  })
  const options = ref(props.type == '1' ? ChinaCitysData?.[0]?.children : DevelopmentDivision)
  const emits = defineEmits(['update:modelValue', 'change'])

  const state = reactive({
    flatData: [],
    flatDataById: {},
    timestamp: Date.now()
  })

  const dropdownRender = (o) => {
    const allChecked =
      !!value.value.length && value.value.filter((it) => it.length === 1).length == options.value.length
    const anyChecked = !!value.value.length && value.value.filter((it) => it.length === 1).length < options.value.length

    return h('div', { class: '_con' }, [
      h(
        'div',
        {
          style: {
            padding: '8px',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center'
          }
        },
        [
          h(Checkbox, {
            checked: allChecked,
            indeterminate: !allChecked && anyChecked,
            onChange: (e) => {
              if (e.target.checked) {
                value.value = []
                let v = []
                let vD = []
                options.value.forEach((it) => {
                  v.push([it.id])
                  vD.push([it])
                })
                handleChange(v, vD)
              } else {
                value.value = []
                // selectedValues.value = []
                handleChange([], [])
              }
            }
          }),
          h('span', { style: { marginLeft: '8px' } }, '全选')
        ]
      ),
      o.menuNode
    ])
  }
  // 监听 change 事件
  const handleChange = (value: any, selectedOptions: any) => {
    emits('update:modelValue', value)
    emits('change', value, selectedOptions)
    console.log('val', value, selectedOptions)
  }
  const loadData = async (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]
    // 只有当选择到第三级（县级）时才加载镇级数据
    if (selectedOptions.length === 3) {
      targetOption.loading = true
      console.log('targetOption', targetOption)
      try {
        const response: any = await getRegion({ codes: parseInt(targetOption.id) })
        targetOption.children = response.data[parseInt(targetOption.id)].map((town: any) => {
          return {
            ...town,
            id: town.id * 1,
            isLeaf: true
          }
        })
        nextTick(() => {
          options.value = [...options.value]
        })
      } catch (error) {
        console.error('加载镇级数据失败:', error)
        nextTick(() => {
          options.value = [...options.value]
        })
      } finally {
        targetOption.loading = false
      }
    }
  }
  // 初始化数据
  const initOptions = (data: any, parent = null) => {
    if (isArray(data)) {
      data.forEach((v) => {
        v.parent = parent
        v.pathLabels = parent ? parent.pathLabels.concat([v.name || v.label]) : [v.name || v.label]
        if (v.children) {
          initOptions(v.children, v)
        }
      })
    }
  }

  // 初始化 Panel
  const initPanel = () => {
    nextTick(() => {
      // cascaderPanel.value.focus()
      open.value = true
    })
  }

  watchEffect(async () => {
    await initOptions(options.value)
    if (isArray(props.modelValue)) {
      value.value = props.modelValue

      initPanel()
    }
  })
  watch(
    () => props.modelValue,
    () => {
      value.value = props.modelValue
      if (props.changeRefresh) {
        state.timestamp = Date.now()
      }
    },
    { deep: true }
  )
  watch(
    () => props.type,
    (val, oldVal) => {
      if (val !== oldVal) {
        options.value = val == '1' ? ChinaCitysData?.[0]?.children : DevelopmentDivision
      }
    }
  )
  // onMounted(() => {
  //   console.log('cascaderPanel', cascaderPanel.value)
  // })
</script>

<style lang="scss" scoped>
  .panel {
    :deep {
      .ant-cascader {
        width: auto;
      }
      .ant-select {
        width: 100%;
        .ant-select-selector,
        .ant-select-arrow,
        .ant-select-clear {
          display: none;
        }
        .ant-cascader-dropdown.ant-select-dropdown {
          position: static !important;
          box-shadow: none;
          border: 1px solid #e4e7ed;
          z-index: 1;
          .ant-cascader-menu {
            // max-width: 370px;
            // height: 500px;
            height: 240px;
            flex-grow: unset;
            flex: 1;
          }
        }
        .ant-cascader-dropdown.ant-select-dropdown::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }
      }
    }
  }
  .area-cascader-wrapper {
    :deep(._con) {
      overflow-x: auto;
      .ant-cascader-menus {
        width: 500px;
        // .ant-cascader-menu-item {
        //   width: 100%;
        // }
        .ant-cascader-menu-item-content {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
</style>
