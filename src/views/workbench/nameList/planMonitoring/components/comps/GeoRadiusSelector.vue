<template>
  <div class="geo-radius-selector">
    <div class="search-bar">
      <a-auto-complete
        v-model:value="searchText"
        :options="options"
        :placeholder="'请输入地点关键词'"
        @search="handleSearch"
        @select="handleSelect"
        allowClear
      />
    </div>

    <div ref="mapRef" class="map-container"></div>

    <div class="control-panel">
      <h3>{{ state.placeName }}</h3>
      <p>地址：{{ state.address }}</p>
      <a-slider
        :min="3000"
        :max="30000"
        :step="1000"
        v-model:value="state.radius"
        @change="updateCircle"
        tooltip-placement="right"
      />
      <div class="actions" v-if="showConfirm">
        <a-button type="primary" @click="emitConfirm">确定</a-button>
        <a-button @click="reset">取消</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, reactive } from 'vue'
  import AMapLoader from '@amap/amap-jsapi-loader'
  import { getConfig } from '@/utils'

  // 事件 & 属性
  interface ConfirmData {
    center: [number, number]
    radius: number
    address: string
    name: string
  }

  const props = defineProps<{
    defaultCenter?: [number, number]
    defaultRadius?: number
    showConfirm?: boolean
    searchLocation?: {
      name: string
      location: [number, number]
    } | null
  }>()

  const emit = defineEmits<{
    (e: 'confirm', data: ConfirmData): void
  }>()

  // 核心状态封装
  const state = reactive({
    mapRef: null as HTMLDivElement | null,
    map: null as any,
    marker: null as any,
    circle: null as any,
    geocoder: null as any,
    placeSearch: null as any,
    autocomplete: null as any,
    radius: props.defaultRadius || 6000,
    center: props.defaultCenter || [116.6586, 40.1441],
    address: '',
    placeName: ''
  })

  // 搜索框状态
  const searchText = ref('')
  const options = ref<{ value: string; label: string; location: { lng: number; lat: number } }[]>([])

  onMounted(async () => {
    try {
      const AMap = await AMapLoader.load({
        key: getConfig('AMAP_KEY'),
        version: '2.0',
        plugins: ['AMap.Geocoder', 'AMap.PlaceSearch', 'AMap.Autocomplete']
      })

      // 初始化
      state.map = new AMap.Map(state.mapRef!, {
        center: state.center,
        zoom: 13
      })

      state.marker = new AMap.Marker({
        position: state.center,
        map: state.map,
        draggable: true
      })

      state.circle = new AMap.Circle({
        center: state.center,
        radius: state.radius,
        strokeColor: '#409EFF',
        strokeWeight: 2,
        fillOpacity: 0.3,
        fillColor: '#409EFF',
        map: state.map
      })

      state.geocoder = new AMap.Geocoder()
      state.placeSearch = new AMap.PlaceSearch({ pageSize: 1, city: '全国', map: state.map })
      state.autocomplete = new AMap.Autocomplete({ city: '全国' })

      reverseGeocode(state.center)

      // 拖动标记
      state.marker.on('dragend', (e: any) => {
        const lnglat: [number, number] = [e.lnglat.lng, e.lnglat.lat]
        updateCenter(lnglat)
        reverseGeocode(lnglat)
      })

      // 点击地图
      state.map.on('click', (e: any) => {
        const lnglat: [number, number] = [e.lnglat.lng, e.lnglat.lat]
        updateCenter(lnglat)
        reverseGeocode(lnglat)
      })
    } catch (error) {
      console.error('加载高德地图失败', error)
    }
  })

  watch(
    () => props.searchLocation,
    (newVal) => {
      if (newVal && newVal.location) {
        updateCenter(newVal.location)
        reverseGeocode(newVal.location)
        state.map?.setZoomAndCenter(15, newVal.location)
      }
    }
  )

  // 更新中心位置
  const updateCenter = (lnglat: [number, number]) => {
    state.center = lnglat
    state.marker?.setPosition(lnglat)
    state.circle?.setCenter(lnglat)
    state.map?.setCenter(lnglat)
  }

  // 修改圆形半径
  const updateCircle = () => {
    state.circle?.setRadius(state.radius)
  }

  // 反向地理编码
  const reverseGeocode = (lnglat: [number, number]) => {
    if (!state.geocoder) return
    state.geocoder.getAddress(lnglat, (status: string, result: any) => {
      if (status === 'complete' && result.regeocode) {
        state.address = result.regeocode.formattedAddress
        state.placeName = result.regeocode.addressComponent.township || '未知区域'
      } else {
        state.address = '无法获取地址'
        state.placeName = ''
      }
    })
  }

  // 地址自动补全搜索
  const handleSearch = (query: string) => {
    if (!query || !state.autocomplete) {
      options.value = []
      return
    }
    state.autocomplete.search(query, (status: string, result: any) => {
      if (status === 'complete' && result.tips) {
        options.value = result.tips
          .filter((tip: any) => tip.location)
          .map((tip: any) => ({
            value: tip.name,
            label: tip.name,
            location: tip.location
          }))
      }
    })
  }

  // 地址选中事件
  const handleSelect = (value: string, option: any) => {
    const loc = option.location
    if (loc) {
      const lnglat: [number, number] = [loc.lng, loc.lat]
      updateCenter(lnglat)
      reverseGeocode(lnglat)
    }
  }

  // 提交确认
  const emitConfirm = () => {
    emit('confirm', {
      center: state.center,
      radius: state.radius,
      address: state.address,
      name: state.placeName
    })
  }

  // 重置按钮
  const reset = () => {
    const defCenter = props.defaultCenter || [116.6586, 40.1441]
    updateCenter(defCenter)
    state.radius = props.defaultRadius || 6000
    updateCircle()
    reverseGeocode(defCenter)
  }
</script>

<style lang="scss" scoped>
  .geo-radius-selector {
    position: relative;

    .search-bar {
      position: absolute;
      top: 12px;
      left: 12px;
      z-index: 1000;
      width: 300px;
    }

    .map-container {
      width: 100%;
      height: 500px;
    }

    .control-panel {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 280px;
      background: #fff;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

      h3 {
        margin: 0 0 6px;
        font-size: 16px;
      }

      p {
        margin: 4px 0 10px;
        color: #666;
        font-size: 14px;
      }

      .actions {
        margin-top: 10px;

        :deep(.ant-btn) {
          margin-right: 8px;
        }
      }
    }
  }
</style>
