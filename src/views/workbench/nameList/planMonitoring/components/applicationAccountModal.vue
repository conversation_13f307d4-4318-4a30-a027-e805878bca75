<template>
  <div>
    <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options">
      <template #btns>
        <a-button
          class="ml-8px"
          @click="handlerAction('delete')"
          v-auth="['planMonitoring_account_delete']"
          :disabled="!state.selectedRowKeys.length"
          >移除</a-button
        >
        <a-button class="ml-16px!" @click="handlerAction('add')" type="primary" v-auth="['planMonitoring_account_add']"
          >新增</a-button
        >
      </template>
    </SearchBaseLayout>
    <TableZebraCrossing
      class="mt-16px"
      :data="state.tableConfigOptions"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
      @change="pageChange"
    >
      <template #bodyCell="{ scope }">
        <template
          v-if="['name', 'account_name', 'admin_name', 'parent_account_name', 'admin_name'].includes(scope.column.key)"
        >
          <a-tooltip placement="topLeft">
            <template #title v-if="scope.record[scope.column.key]?.length > 15">{{
              scope.record[scope.column.key]
            }}</template>
            <div class="text_overflow">
              {{ scope.record[scope.column.key] }}
            </div>
          </a-tooltip>
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      destroyOnClose
      :footer="null"
      :centered="true"
    >
      <addAccountModal v-if="state.dialog.type === 'add'" :data="state.dialog.data" :id="data.id" @onEvent="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import { onMounted, reactive, ref, h } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { account_list, rule_change_account } from '../index.api'
  import addAccountModal from './addAccountModal.vue'
  const props = defineProps(['data'])
  const emit = defineEmits(['onEvent'])
  const columns = [
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name',
      slot: true
    },
    {
      title: '账户ID',
      dataIndex: 'account_id',
      key: 'account_id',
      width: 120
    },
    {
      title: '管家账号',
      dataIndex: 'parent_account_name',
      key: 'parent_account_name',
      width: 120
    },
    {
      title: '关联人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 140
    },
    {
      title: '关联时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140
    }
  ]
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'account_name',
        value: undefined,
        props: {
          placeholder: '请输入账户名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 6,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'account_id',
        value: undefined,
        props: {
          placeholder: '请输入账户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 6,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0
    }
  })
  const state = reactive({
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'account_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 1100,
        y: 340
      },
      columns,
      dataSource: [],
      isResizable: false,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 20,
        size: 'small',
        showTotal: (total: String | Number) => `共${total}条数据`
      }
    },
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: null
    } as any
  })
  // 获取列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let res = await account_list({ ...state.params, id: props.data.id })
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v: any) => {
    state.params = {
      ...state.params,
      ...v.formData
    }
    state.params.page = 1
    getList()
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRowKeys = selectedRowKeys
  }
  const handlerAction = async (type: string) => {
    try {
      if (type === 'delete') {
        try {
          Modal.confirm({
            title: '提示',
            content: h('div', {}, `确认移除${state.selectedRowKeys.length}个应用账户？`),
            async onOk() {
              console.log('delete')
              await rule_change_account({
                id: props.data.id,
                del_account_ids: state.selectedRowKeys
              })
              message.success('删除成功')
              getList()
              state.selectedRowKeys = []
            },
            onCancel() {
              console.log('Cancel')
            }
          })
        } catch (error) {
          console.error(error)
        }
        // emit('onEvent', { cmd: 'submit' })
      } else if (type === 'add') {
        console.log('add')
        state.dialog.visible = true
        state.dialog.title = '新增账户'
        state.dialog.width = 919
        state.dialog.type = type
        state.dialog.data = state.tableConfigOptions.dataSource.map((it: any) => it.account_id)
      }
    } catch (err) {
      console.log(err)
    }
  }
  const onEvent = ({ cmd, data }: any) => {
    state.dialog.visible = false
    if (cmd === 'submit') {
      getList()
    }
  }
  onMounted(() => {
    getList()
  })
</script>
