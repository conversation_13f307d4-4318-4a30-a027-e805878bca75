import http from '@/utils/request'

/**
 * 列表
 */
export const rule_list = (data: any) => {
  return http('get', `/common/ad/monitor/rule_list`, data)
}

/**
 * 获取省市县的四级数据
 */
export const getRegion = (data: any) => {
  return http('get', `/common/jl/region`, data)
}

/**
 * 保存/新增
 */
export const rule_sale = (data: any) => {
  return http('post', `/common/ad/monitor/rule_sale`, data)
}

/**
 * 获取账户列表
 */
export const account_list = (data: any) => {
  return http('get', `/common/ad/monitor/account_list`, data)
}

/**
 * 删除
 */
export const rule_del = (data: any) => {
  return http('post', `/common/ad/monitor/del`, data)
}

/**
 * 获取详情
 */
export const rule_info = (data: any) => {
  return http('get', `/common/ad/monitor/info`, data)
}

/**
 * 监控账号变更
 */
export const rule_change_account = (data: any) => {
  return http('post', `/common/ad/monitor/rule_change_account`, data)
}
