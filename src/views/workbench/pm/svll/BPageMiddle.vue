<template>
  <div>
    <a-button class="" v-if="tableData.dataSource.length < 5" type="primary" @click="emits('addBpage')"
      >新增页面</a-button
    >
    <TableZebraCrossing :data="tableData" class="mt-16px">
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'name'">
          <a-tooltip placement="topLeft">
            <template #title>{{ record.name }}</template>
            <div class="text_overflow">{{ record.name }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.key === 'title'">
          <a-tooltip placement="topLeft">
            <template #title>{{ record.title }}</template>
            <div class="text_overflow">{{ record.title }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'image'">
          <a-image :src="record.image" width="40px" height="40px" :preview="false" />
        </template>
        <template v-if="column.dataIndex === 'weight'">
          <a-select class="max-w-100px" v-model:value="record.weight" size="small" style="width: 100%">
            <a-select-option v-for="i in 10" :key="i" :value="i">{{ i }}</a-select-option>
          </a-select>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-popconfirm
            title="确定要删除该页面吗？"
            @confirm="() => handleDelete(record.id)"
            okText="确定"
            cancelText="取消"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </TableZebraCrossing>
    <!-- <div class="footer">
      <a-button type="primary" @click="emits('close')">确定</a-button>
    </div> -->
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch } from 'vue'
  import { message } from 'ant-design-vue'
  const props = defineProps(['item'])
  const emits = defineEmits(['event', 'addBpage', 'delItem', 'close'])
  const state = reactive({
    params: {
      page: 1,
      page_size: 10
    },
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[]
  })
  // 表格数据
  const tableData = reactive({
    bordered: true,
    dataSource: props.item?.id,
    loading: false,
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 700
    },
    columns: [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: '198px'
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        width: '160px'
      },
      {
        title: '落地页',
        dataIndex: 'image',
        key: 'image',
        width: '125px'
      },
      {
        title: '权重',
        dataIndex: 'weight',
        key: 'weight',
        width: '80px'
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: '60px'
      }
    ],
    rowKey: 'id',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })

  const handleDelete = (id) => {
    emits('delItem', id)
  }
  watch(
    () => props.item.id,
    (newVal) => {
      tableData.dataSource = newVal || []
    },
    {
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_name {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
  .footer {
    text-align: end;
  }
</style>
