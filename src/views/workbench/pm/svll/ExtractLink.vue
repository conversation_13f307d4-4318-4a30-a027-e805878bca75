<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :rules="rules" :labelCol="{ style: { width: '130px' } }">
      <a-form-item label="名称：" name="name">
        <a-input :maxlength="30" show-count v-model:value.trim="state.form.name" placeholder="请输入名称" />
      </a-form-item>

      <a-form-item label="选择域名：" name="domain_id" :rules="[{ required: true, message: '请选择域名' }]">
        <a-select
          placeholder="请选择域名"
          label-in-value
          :field-names="{ label: 'domain', value: 'id' }"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.domain_id"
          :options="state.domainNameOptions"
          @change="changeDomain"
        ></a-select>
      </a-form-item>
      <a-form-item label="落地页：" name="h5_url">
        <div class="flex items-center">
          <a-tooltip>
            <template #title>{{ state.form.h5_url }}</template>
            <a-input
              class="flex-1"
              :disabled="true"
              v-model:value.trim="state.form.h5_url"
              placeholder="请输入落地页"
            />
          </a-tooltip>
          <span class="ml8px c-#FE4D4F cursor-pointer" @click="copyClick">复制</span>
        </div>
      </a-form-item>
      <a-form-item labe name="detection_url">
        <template #label> <span class="span-label">必填</span> 有效触点</template>
        <div class="flex items-center">
          <a-tooltip>
            <template #title>{{ state.form.detection_url }}</template>
            <a-input
              class="flex-1"
              :disabled="true"
              v-model:value.trim="state.form.detection_url"
              placeholder="请输入有效触点"
            />
          </a-tooltip>

          <span class="ml8px c-#FE4D4F cursor-pointer" @click="copy(state.form.detection_url)">复制</span>
        </div>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="PMAddList" lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import { domain_list_by_select, ad_link_update, ad_copy_url } from './index.api' 
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  import { copy } from '@/utils'
  import { cloneDeep } from 'lodash-es'
  const { useInfo } = useApp()
  const props = defineProps(['item', 'type'])
  const emit = defineEmits(['event'])
  const ruleForm = ref(null)
  const rules = {
    name: [{ required: true, message: '请输入名称', trigger: ['change', 'blur'] }],
    domain_id: [{ required: true, message: '请选择域名', trigger: ['change', 'blur'] }],
    h5_url: [{ required: true, message: '请输入落地页', trigger: ['change', 'blur'] }]
  }

  const state = reactive({
    loading: false,
    form: {
      name: props.item.name || undefined,
      domain_id: undefined,
      domain_name: '',
      h5_url: '', //落地页
      detection_url: ''
    },
    domainNameOptions: [] // 域名列表
  })
  const ad_links = cloneDeep(props.item.ad_links)
  const initData = async () => {
    try {
      const { data } = await domain_list_by_select({ page: 1, page_size: 9999, company_id: useInfo.value?.company_id, media_type:props.item?.media_type }) 
      state.domainNameOptions = data.list || []
      let option
      if (props.item.domain_id) {
        option = state.domainNameOptions.find((item) => item.id === props.item.domain_id) || state.domainNameOptions[0]
      } else {
        option = state.domainNameOptions[0]
      }
      state.domainNameOptions = state.domainNameOptions.map((option:any) => ({
        ...option,
        disabled: option.status != 1
      }))
      state.form.domain_id = { value: option?.id, option, label: option?.domain }
      let record = { label: option?.domain, option, value: option?.id }
      changeDomain(record)
    } catch (error) {
      console.error(error)
    }
  }
  initData()

  const changeDomain = (e) => {
    const { option } = e
    state.form.h5_url = option.domain + ad_links.h5_url
    state.form.detection_url = option.domain + ad_links.detection_url
  }
  const close = () => {
    emit('event', { type: 'AddLink', status: true })
  }
  const copyClick = async () => {
    try {
      await copy(state.form.h5_url)
      let res: any = await ad_copy_url({ id: props.item.id })
      if (res.code === 0) {
        state.form.h5_url = state.form.domain_id?.label + res.data.h5_url
        state.form.detection_url = state.form.domain_id?.label + res.data.detection_url
      }
    } catch (err) {
      console.log(err)
    }
  }
  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const edit = async () => {
    try {
      state.loading = true
      let params = {
        id: props.item.id,
        name: state.form.name,
        domain_id: state.form.domain_id?.value
      }
      await ad_link_update(params)
      emit('event', { type: 'AddLink', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    margin-top: 30px;
    text-align: end;
  }
  .change_type {
    :deep(.ant-row) {
      display: flex;
      align-items: baseline;
    }
    .ad_list {
      :deep(.ant-row) {
        display: flex;
        align-items: start !important;
      }
    }
  }
  .dashed_box {
    border: 1px dashed #d9d9d9;
    padding: 8px;
    margin-top: 10px;
    border-radius: 4px;
  }
  .span-label {
    display: inline-block;
    padding: 1px 2px;
    color: red;
    border: 1px solid red;
    border-radius: 6px;
    margin-right: 4px;
  }
</style>
