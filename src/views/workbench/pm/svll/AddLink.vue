<template>
  <div class="form-scroll-wrapper">
    <a-form
      :model="state.form"
      class="form-scroll-box"
      ref="ruleForm"
      :rules="rules"
      :labelCol="{ style: { width: '135px' } }"
      :colon="true"
    >
      <a-form-item label="名称：" name="name">
        <a-input :maxlength="30" show-count v-model:value.trim="state.form.name" placeholder="请输入名称" />
      </a-form-item>
      <a-form-item label="媒体类型：" name="media_type">
        <a-radio-group
          :disabled="disabled"
          v-model:value="state.form.media_type"
          :options="mediaType"
          @change="onChangeMediaType"
        />
      </a-form-item>
      <a-form-item label="产品库：" name="product_id">
        <div class="flex items-center">
          <a-select
            show-search
            placeholder="请选择产品库"
            :field-names="{ label: 'label', value: 'id' }"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            v-model:value="state.form.product_id"
            :options="state.productList"
            :filter-option="filterOption"
            @change="appPlaceAnOrderSetup"
          >
            <template #dropdownRender="{ menuNode: menu }">
              <v-nodes :vnodes="menu" />
              <a-divider style="margin: 4px 0" />
              <a-space>
                <a-button class="ml8px pa-0!" size="small" type="link" @click="addProduct">新增</a-button>
                <a-button size="small" class="pa-0!" type="link" @click="get_product_list">刷新</a-button>
              </a-space>
            </template>
          </a-select>
        </div>
      </a-form-item>
      <a-form-item name="limit_type" class="mb-0">
        <template #label>
          <span>加粉限制</span>
          <a-tooltip placement="topLeft">
            <template #title>
              <div>当前公司后台去重：同一用户不支持重复添加当前公司多企微客服;</div>
              <div>全系统去重：同一用户不支持重复添加斗量系统多企微客服</div>
            </template>
            <QuestionCircleFilled class="ml-3px font-size-12px c-#939599" />
          </a-tooltip>
        </template>
        <!-- <a-checkbox v-model:checked="state.form.limit_type">同企微主体，同IP的用户半个月内添加一次</a-checkbox> -->
        <div
          class="flex"
          :class="{
            'pt-6px': [1, 2].includes(state.form.limit_type),
            'mb-12px': [1, 2].includes(state.form.limit_type)
          }"
        >
          <a-switch
            :checked="[1, 2].includes(state.form.limit_type)"
            @change="
              (e) => {
                if (e) {
                  state.form.limit_type = 1
                } else {
                  state.form.limit_type = 0
                }
              }
            "
          />
        </div>
        <div class="flex" v-if="state.form.limit_type != 0">
          <div class="mr-3px">去重限制：</div>
          <a-radio-group v-model:value="state.form.limit_type">
            <a-radio :value="1">当前公司后台去重</a-radio>
            <a-radio :value="2">全系统去重</a-radio>
          </a-radio-group>
        </div>
        <div class="flex mt-12px flex-items-center" v-if="state.form.limit_type != 0">
          <a-form-item name="" class="flex-1">
            <div class="flex flex-items-center">
              <div class="mr-3px">时长限制：</div>
              <div name="callback_limit" class="flex-1">
                <a-select
                  v-model:value="state.form.limit_type_visit"
                  :options="callbackLimit"
                  placeholder="请选择回传限制"
                ></a-select>
              </div>
            </div>
          </a-form-item>
          <a-form-item
            name="limit_type_visit_second"
            class="ml-12px"
            v-if="[2, 3].includes(state.form.limit_type_visit)"
          >
            <div name="limit_second" class="flex-1">
              <a-input-number
                v-model:value="state.form.limit_type_visit_second"
                :min="0"
                :max="99999"
                :precision="0"
                placeholder="请设置时长"
                class="w-380px"
              >
                <template #addonBefore>
                  <div>小于</div>
                </template>
                <template #addonAfter>秒不允许加企微</template>
              </a-input-number>
            </div>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="屏蔽规则">
        <div class="pt-4px mb-12px">
          <a-switch
            :checked="state.ruleIdCheck"
            @change="
              (e: any) => {
                state.ruleIdCheck = e
                if (e) {
                  state.form.rule_id = state.form.rule_id || 1
                } else {
                  state.form.rule_id = 0
                }
              }
            "
          />
        </div>
        <a-select
          v-if="state.ruleIdCheck && state.rulesList.length"
          v-model:value="state.form.rule_id"
          placeholder="请选择规则"
          :options="state.rulesList"
          :field-names="{ label: 'name', value: 'id' }"
        >
          <template #dropdownRender="{ menuNode: menu }">
            <v-nodes :vnodes="menu" />
            <a-divider style="margin: 4px 0" />
            <a-space>
              <a-button v-auth="['addBlockingRules']" class="ml8px pa-0!" size="small" type="link" @click="addRules"
                >新建</a-button
              >
              <a-button size="small" class="pa-0!" type="link" @click="getRulesList()">刷新</a-button>
            </a-space>
          </template>
        </a-select>
        <div class="font-size-13px c-#656D7D mt-4px">系统内置的风险规则，其中包含异常用户、红包党、羊毛党等</div>
      </a-form-item>
      <a-form-item label="展示页面" name="landing_page_id">
        <div class="flex flex-items-center">
          <div class="flex-y-center" v-if="state.form.landing_page_name">
            <a-tooltip placement="topLeft">
              <template #title v-if="state.form.landing_page_name?.length > 15">{{
                state.form.landing_page_name
              }}</template>
              <div class="flex c-primary cursor-pointer A-text">
                <div class="max-w-140px text_name">
                  {{ state.form.landing_page_name }}
                </div>
                <CloseOutlined class="c-primary font-size-12px" @click="deleteABPage" />
              </div>
            </a-tooltip>
            <div class="c-primary flex-y-center cursor-pointer" @click="onShowDialog(state.form.landing_page_id, 1)">
              <span class="ml-6px mr-4px">修改</span>
              <DownOutlined class="font-size-12px" />
            </div>
          </div>
          <div
            v-else
            class="c-primary cursor-pointer bg-#FFF5EA font-size-14px lh-14px pa-7px rounded-4px border-#ffE8CF border"
            @click="onShowDialog(state.form.landing_page_id, 1)"
          >
            链接到页面地址
          </div>
          <div class="flex-1"></div>
        </div>
      </a-form-item>
      <a-form-item name="question_page">
        <template #label>
          <span>启用B页面</span>
          <a-tooltip placement="topLeft">
            <template #title>开启后广告用户将会展示B页面，非广告来源客户将展示A页面</template>
            <QuestionCircleFilled class="ml-3px font-size-12px c-#939599" />
          </a-tooltip>
        </template>
        <div class="flex-y-center">
          <a-switch
            :checkedValue="1"
            :unCheckedValue="2"
            v-model:checked="state.form.question_page"
            @change="handleQuestion"
          />
          <a-form-item
            name="b_page_items"
            class="b_page_items"
            :class="['group_type ml-8px', state.form.question_page === 1 ? 'mb0' : '']"
            v-if="state.form.question_page === 1"
          >
            <div @click="onShowDialog(state.form.b_page_items, 3)">
              <!-- <a-tooltip placement="topLeft">
                <template #title v-if="state.form.qa_page_name?.length > 15">{{ state.form.qa_page_name }}</template>
                <div class="c-primary cursor-pointer flex-y-center">
                  <div class="max-w-140px text_name flex-none!">
                    {{ state.form.qa_page_name || '选择页面' }}
                  </div>
                  <DownOutlined v-if="state.form.qa_page_name" class="ml-4px font-size-12px" />
                </div>
              </a-tooltip> -->
              <div class="c-primary cursor-pointer flex-y-center">
                <div
                  class="max-w-140px text_name flex-none! bg-#FFF5EA font-size-14px lh-14px pa-7px rounded-4px border-#ffE8CF border"
                  v-if="state.form.b_page_items?.length == 0"
                >
                  选择页面
                </div>
                <a-tooltip placement="topLeft" v-if="state.form.b_page_items?.length == 1">
                  <template #title>{{ state.form.b_page_items[0]?.name }}</template>
                  <div
                    class="max-w-140px text_overflow text_name flex-none! bg-#FFF5EA font-size-14px lh-14px pa-7px rounded-4px border-#ffE8CF border"
                  >
                    {{ state.form.b_page_items[0]?.name }}
                  </div>
                </a-tooltip>

                <div
                  class="flex bg-#FFF5EA font-size-14px lh-14px pa-7px rounded-4px border-#ffE8CF border"
                  v-if="state.form.b_page_items?.length > 1"
                >
                  <a-tooltip placement="topLeft">
                    <template #title>{{ state.form.b_page_items[0]?.name }}</template>
                    <span class="max-w-140px text_overflow flex-none!">{{ state.form.b_page_items[0]?.name }}</span>
                  </a-tooltip>
                  <span>、等 {{ state.form.b_page_items.length }}个页面...</span>
                </div>
                <div class="c-primary flex-y-center cursor-pointer">
                  <span class="ml-6px mr-4px">编辑</span>
                  <DownOutlined class="font-size-12px" />
                </div>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="客服分组：" name="group_type" class="group_type">
        <a-radio-group v-model:value="state.form.group_type" @change="handleGroupType">
          <a-radio :value="1">通用分组</a-radio>
          <a-radio :value="2" v-if="state.form.question_page === 1">A/B页专用客服组</a-radio>
        </a-radio-group>
        <div class="dashed_box">
          <a-form-item
            :label="state.form.group_type == 1 ? '客服分组：' : 'A页面：'"
            name="wx_group_id"
            class="wx_group_id mt10px"
          >
            <a-select
              show-search
              placeholder="请选择客服分组"
              :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              v-model:value="state.form.wx_group_id"
              :filter-option="filterOption"
              mode="multiple"
              :max-tag-count="1"
            >
              <a-select-option v-for="item in state.wxGroupList" :key="item.id" :value="item.id" :label="item.name">
                <div class="flex-y-center">
                  <div class="max-w-315px ellipsis-text">{{ item.name }}</div>
                  (客服:{{ item.linkNum }}；<span class="item-tag">已上线 : {{ item.can_use_link_num }}</span
                  >)
                </div>
              </a-select-option>
              <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
                <a-divider style="margin: 4px 0" />
                <a-space>
                  <a-button class="ml8px pa-0!" size="small" type="link" @click="addKfPage">新增</a-button>
                  <a-button size="small" class="pa-0!" type="link" @click="upDateKFData('one')">刷新</a-button>
                </a-space>
              </template>
            </a-select>
          </a-form-item>
          <a-form-item label="B页面：" name="question_wx_group_id" v-if="state.form.group_type == 2">
            <a-select
              show-search
              placeholder="请选择客服分组"
              :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              v-model:value="state.form.question_wx_group_id"
              :filter-option="filterOption"
              mode="multiple"
              :max-tag-count="1"
            >
              <a-select-option v-for="item in state.wxGroupList" :key="item.id" :value="item.id" :label="item.name">
                <div>
                  {{ item.name }} (客服:{{ item.linkNum }}；<span class="item-tag"
                    >已上线 : {{ item.can_use_link_num }}</span
                  >)
                </div>
              </a-select-option>
              <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
                <a-divider style="margin: 4px 0" />
                <a-space>
                  <a-button class="ml8px pa-0!" size="small" type="link" @click="addKfPage">新增</a-button>
                  <a-button size="small" class="pa-0!" type="link" @click="upDateKFData('two')">刷新</a-button>
                </a-space>
              </template>
            </a-select>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item name="limit_sites" v-if="state.form.media_type == 3">
        <template #label>
          <span class="lh-16px">指定版位跳转A页</span>
          <a-tooltip placement="topLeft">
            <template #title>
              <div>当选择指定版本跳转后，通过该版位进入的用户只能访问A页面</div>
            </template>
            <QuestionCircleFilled class="ml-3px font-size-12px c-#939599" />
          </a-tooltip>
        </template>
        <a-select
          placeholder="请选择流量版位"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.limit_sites"
          :search-value="state.searchlimitSitesText"
          mode="multiple"
          allowClear
          :max-tag-count="2"
          :filter-option="filterOption"
          :options="state.limitSites"
          @search="(val: any) => (state.searchlimitSitesText = val)"
          @dropdownVisibleChange="() => (state.searchlimitSitesText = null)"
        ></a-select>
      </a-form-item>
      <a-form-item label="回传时机：" name="callback_action" class="mt-12px">
        <!-- :class="{ 'pt-6px': state.form.callback_action === 'ONLY_TAG' }" -->
        <div :class="{ 'pt-6px': ['ONLY_SESSION', 'ONLY_TAG'].includes(state.form.callback_action) }">
          <a-radio-group v-model:value="state.form.callback_action" @change="handleCallbackAction">
            <a-radio value="FULL">全部回传</a-radio>
            <a-radio value="ONLY_SESSION">
              回传已发送消息客户
              <a-tooltip>
                <template #title> 回传当天发送消息的客户 </template>
                <QuestionCircleFilled class="ml-3px c-#939599" />
              </a-tooltip>
            </a-radio>
            <a-radio value="ONLY_TAG">
              回传已打标签客户
              <a-tooltip>
                <template #title> 回传当天被打标签的客户 </template>
                <QuestionCircleFilled class="ml-3px c-#939599" />
              </a-tooltip>
            </a-radio>
          </a-radio-group>
          <div class="flex open-num mt-12px mb-5px" v-if="state.form.callback_action === 'ONLY_SESSION'">
            企微-第
            <a-select
              placeholder="请选择"
              :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              v-model:value="state.form.session_num"
              :options="openNumsOptions"
              class="ml-5px mr-5px"
            >
            </a-select>
            次收到用户消息后回传
          </div>

          <a-form-item
            class="mt-12px mb-5px"
            label="标签："
            name="tag"
            v-if="state.form.callback_action === 'ONLY_TAG'"
          >
            <a-textarea
              v-model:value="state.form.tag"
              placeholder="请输入标签内容，多个请使用回车进行换行"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            />
          </a-form-item>
        </div>
      </a-form-item>

      <a-form-item label="转化目标：" name="convert_target">
        <a-select
          placeholder="请选择转化目标"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.convert_target"
          :options="state.convert[state.form.media_type]"
          @change="appPlaceAnOrderSetup"
        ></a-select>
      </a-form-item>
      <template v-if="state.form.convert_target === 'AD_CONVERT_TYPE_APP_ORDER' && state.form.media_type == 3">
        <div class="flex-y-center">
          <a-form-item label="电商：" name="shop_type" class="flex-1">
            <a-select v-model:value="state.form.shop_type" placeholder="请选择电商">
              <a-select-option :value="v.value" v-for="v in shopOption" :key="v.value">
                <div class="flex-y-center">
                  <img :src="v.icon" class="w-14px h-14px mr-8px" />
                  <div>{{ v.label }}</div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="订单金额：" name="order_amount" class="flex-1">
            <a-input-number
              v-model:value="state.form.order_amount"
              :min="0.01"
              :max="99999999.99"
              :precision="2"
              placeholder="请输入订单金额"
              class="w-100%"
            >
            </a-input-number>
          </a-form-item>
        </div>
        <a-form-item
          label="店铺名称："
          :name="['product_info', 'shop_name']"
          :rules="[{ required: true, message: '请输入店铺名称', trigger: ['change', 'blur'] }]"
        >
          <a-input
            :maxlength="50"
            show-count
            v-model:value.trim="state.form.product_info.shop_name"
            placeholder="请输入店铺名称"
          />
        </a-form-item>
        <a-form-item
          label="商品名称："
          :name="['product_info', 'product_name']"
          :rules="[{ required: true, message: '请输入商品名称', trigger: ['change', 'blur'] }]"
        >
          <a-input
            :maxlength="100"
            show-count
            v-model:value.trim="state.form.product_info.product_name"
            placeholder="请输入商品名称"
          />
        </a-form-item>
        <div class="flex-y-center">
          <a-form-item
            label="商品ID："
            :name="['product_info', 'product_id']"
            class="flex-1"
            :rules="[{ required: true, message: '请输入商品ID', trigger: ['change', 'blur'] }]"
          >
            <a-input
              :maxlength="50"
              show-count
              v-model:value.trim="state.form.product_info.product_id"
              placeholder="请输入商品ID"
            />
          </a-form-item>
          <a-form-item
            label="商品金额："
            :name="['product_info', 'product_price']"
            class="flex-1"
            :rules="[
              { required: true, message: '请输入商品金额', trigger: ['change', 'blur'] },
              {
                type: 'number',
                min: 0.01,
                max: 99999999.99,
                message: '请输入正确的商品金额',
                trigger: ['change', 'blur']
              }
            ]"
          >
            <a-input-number
              v-model:value="state.form.product_info.product_price"
              :min="0.01"
              :max="99999999.99"
              :precision="2"
              placeholder="请输入商品金额"
              class="w-100%"
            >
            </a-input-number>
          </a-form-item>
        </div>
        <a-form-item
          label="商品分类："
          :name="['product_info', 'product_category']"
          :rules="[{ required: true, message: '请输入商品分类', trigger: ['change', 'blur'] }]"
        >
          <a-input
            :maxlength="50"
            show-count
            v-model:value.trim="state.form.product_info.product_category"
            placeholder="请输入商品分类"
          />
        </a-form-item>
        <a-form-item label="商品图片：" required>
          <a-form-item
            v-for="(v, index) in state.form.product_img"
            :key="v.key"
            :name="['product_img', index, 'value']"
            :rules="[
              { required: true, message: '请填写合规的商品图片链接 https或者http开头', trigger: ['change', 'blur'] },
              {
                pattern: /^https?:\/\//,
                message: '请填写合规的商品图片链接 https或者http开头',
                trigger: ['change', 'blur']
              },
              {
                pattern: /^[^\u4e00-\u9fa5]*$/,
                message: '不能输入汉字',
                trigger: ['change', 'blur']
              }
            ]"
          >
            <div class="flex-y-center">
              <a-input v-model:value.trim="v.value" placeholder="请输入商品图片链接，格式http/https开头" />
              <a-button type="link" danger :icon="h(DeleteOutlined)" @click="handleDelImg(index)"></a-button>
            </div>
          </a-form-item>
          <a-button type="dashed" class="w-full!" v-if="state.form.product_img?.length < 3" @click="handleAddImg">
            +添加
          </a-button>
        </a-form-item>
      </template>
      <template v-if="state.form.convert_target === 'COMPLETE_ORDER' && state.form.media_type == 1">
        <div class="flex-y-center">
          <a-form-item label="电商：" name="shop_type" class="flex-1">
            <a-select v-model:value="state.form.shop_type" placeholder="请选择电商">
              <a-select-option :value="v.value" v-for="v in shopOption" :key="v.value">
                <div class="flex-y-center">
                  <img :src="v.icon" class="w-14px h-14px mr-8px" />
                  <div>{{ v.label }}</div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="订单金额：" name="order_amount" class="flex-1">
            <a-input-number
              v-model:value="state.form.order_amount"
              :min="0.01"
              :max="99999999.99"
              :precision="2"
              placeholder="请输入订单金额"
              class="w-100%"
            >
            </a-input-number>
          </a-form-item>
        </div>
        <a-form-item
          label="商品名称："
          :name="['product_info', 'product_name']"
          :rules="[{ required: true, message: '请输入商品名称', trigger: ['change', 'blur'] }]"
        >
          <a-input
            :maxlength="100"
            show-count
            v-model:value.trim="state.form.product_info.product_name"
            placeholder="请输入商品名称"
          />
        </a-form-item>
      </template>
      <a-form-item label="回传限制：" required class="mb-0!">
        <div class="flex-y-center">
          <a-form-item name="callback_limit" class="flex-1">
            <a-select v-model:value="state.form.callback_limit" :options="callbackLimit" placeholder="请选择回传限制">
            </a-select>
          </a-form-item>
          <!-- <a-tooltip v-if="[2, 3].includes(state.form.callback_limit)">
            <template #title>
              {{
                state.form.callback_limit == 2
                  ? '访问时长（点击获客时间-进入页面时间）'
                  : '答题时长（完成问答时间-首次点击问答时间）'
              }}
            </template>
            <QuestionCircleFilled class="mt--18px ml-6px mr-6px c-#939599" />
          </a-tooltip> -->
          <a-form-item name="limit_second" class="flex-1 ml-12px" v-if="[2, 3].includes(state.form.callback_limit)">
            <a-input-number
              v-model:value="state.form.limit_second"
              :min="0"
              :max="99999"
              :precision="0"
              placeholder="请设置时长"
              class="w-420px"
            >
              <template #addonBefore>
                <div>小于</div>
              </template>
              <template #addonAfter>秒不回传</template>
            </a-input-number>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="备注：" name="remark">
        <a-textarea
          v-model:value.trim="state.form.remark"
          :maxlength="200"
          show-count
          placeholder="请输入备注"
          :rows="4"
        />
      </a-form-item>
    </a-form>
    <div class="footer form-scroll-btn">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      destroyOnClose
      centered
      :footer="null"
    >
      <ABPage
        :item="state.dialog.item"
        @event="onEvent"
        :hasLink="state.hasMyLink"
        :mediaType="state.form.media_type"
        :url="state.form.landing_page_name"
        :isTwo="state.dialog.isTwo"
      />
    </a-modal>
    <a-modal v-model:open="state.dialog.visibleB" title="选择页面" :width="900" destroyOnClose centered :footer="null">
      <BPageMiddle
        :item="state.dialog.item"
        @addBpage="addBpage"
        @delItem="delItem"
        @close="state.dialog.visibleB = false"
      ></BPageMiddle>
    </a-modal>
  </div>
</template>
<script setup name="PMAddList" lang="ts">
  import { h, reactive, ref, computed, onMounted, defineComponent, watch } from 'vue'
  import {
    wechat_group_list,
    product_list,
    convert_target,
    ad_link_create,
    ad_link_update,
    ad_rules_list,
    callback_position_list
  } from './index.api'
  import ABPage from './ABPage.vue'
  import BPageMiddle from './BPageMiddle.vue'
  import { message } from 'ant-design-vue'
  import { QuestionCircleFilled, CloseOutlined, DownOutlined, DeleteOutlined } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  import { cloneDeep, pick } from 'lodash-es'
  import { useRouter } from '@/hooks/use-router'
  import { mediaType, shopType, GdshopType, callbackLimit, toDecimal } from '@/utils'
  const { routerResolve } = useRouter()
  const { useInfo } = useApp()
  const props = defineProps(['item', 'type'])
  const emit = defineEmits(['event'])
  const ruleForm = ref(null)
  const VNodes = defineComponent({
    props: {
      vnodes: {
        type: Object,
        required: true
      }
    },
    render() {
      return this.vnodes
    }
  })
  const shopOption = computed(() => {
    if (state.form.media_type == 3) {
      return shopType
    }
    return GdshopType
  })
  const rules = {
    name: [{ required: true, message: '请输入名称', trigger: ['change', 'blur'] }],
    media_type: [{ required: true, message: '请选择媒体类型', trigger: ['change', 'blur'] }],
    product_id: [{ required: true, message: '请选择产品库', trigger: ['change', 'blur'] }],
    wx_group_id: [{ required: true, message: '请选择客服分组', trigger: ['change', 'blur'] }],
    question_wx_group_id: [{ required: true, message: '请选择客服分组', trigger: ['change', 'blur'] }],
    qa_page_id: [{ required: true, message: '请选择页面', trigger: ['change', 'blur'] }],
    b_page_items: [{ required: true, message: '请选择页面', trigger: ['change', 'blur'] }],
    landing_page_id: [{ required: true, message: '请选择页面', trigger: ['change', 'blur'] }],
    callback_action: [{ required: true, message: '请选择回传行为', trigger: ['change', 'blur'] }],
    pre_callback_num: [{ required: true, message: '请输入回传时机', trigger: ['change', 'blur'] }],
    convert_target: [{ required: true, message: '请选择转化目标', trigger: ['change', 'blur'] }],
    // limit_type: [{ required: true, message: '请配置加粉限制', trigger: ['change', 'blur'] }],
    order_amount: [
      { required: true, message: '请输入订单金额', trigger: ['change', 'blur'] },
      { type: 'number', min: 0.01, max: 99999999.99, message: '订单金额必须大于0', trigger: ['change', 'blur'] }
    ],
    shop_type: [{ required: true, message: '请选择电商类型', trigger: ['change', 'blur'] }],
    tag: [{ required: true, message: '请输入标签', trigger: ['change', 'blur'] }],
    callback_limit: [{ required: true, message: '请选择回传限制', trigger: ['change', 'blur'] }],
    limit_second: [{ required: true, message: '请输入时长', trigger: ['change', 'blur'] }],
    limit_type_visit_second: [{ required: true, message: '请输入时长', trigger: ['change', 'blur'] }]
  }

  const state = reactive({
    loading: false,
    searchlimitSitesText: null,
    form: {
      name: undefined,
      media_type: 3,
      session_num: 1,
      wx_group_id: [],
      question_wx_group_id: [],
      limit_sites: [],
      same_ip_limit: false,
      qa_page_id: undefined, //B页面
      qa_page_name: '', //B页面名称
      b_page_items: [], //B页面列表
      landing_page_id: undefined, //A页面
      landing_page_name: '', //A页面名称
      callback_action: 'FULL', //回传行为
      convert_target: undefined, //转化目标
      order_amount: undefined,
      remark: undefined,
      question_page: 2,
      tag: undefined,
      shop_type: 1,
      group_type: 1,
      product_id: undefined, //产品库
      limit_type: 1,
      callback_limit: 2,
      limit_type_visit: 2,
      limit_type_visit_second: 0,
      limit_second: 0,
      product_img: [{ key: 1, value: '' }],
      product_info: {
        shop_name: undefined,
        product_name: undefined,
        product_id: undefined,
        product_price: undefined,
        product_category: undefined
      },
      rule_id: 0
    } as any,
    hasMyLink: false,
    productList: [], //产品库
    wxGroupList: [], // 客服分组
    QwxGroupList: [], //问答客服分组
    limitSites: [], // 流量版位
    convert: {}, // 转化目标
    dialog: {
      visible: false,
      visibleB: false,
      title: '',
      width: 0,
      type: '',
      isTwo: false,
      item: null as any
    },
    ruleIdCheck: false,
    rulesList: [] as any[]
  })
  const openNumsOptions = [
    {
      label: '1',
      value: 1
    },
    {
      label: '3',
      value: 3
    },
    {
      label: '5',
      value: 5
    },
    {
      label: '10',
      value: 10
    }
  ]
  const handleCallbackAction = (e: any) => {
    // const { target } = e
    // if (target.value === 'ONLY_SESSION') {
    //   state.form.tag = undefined
    // } else if (target.value === 'ONLY_TAG') {
    //   state.form.session_num = 1
    // } else {
    //   state.form.tag = undefined
    //   state.form.session_num = 1
    // }
  }

  const disabled = computed(() => {
    return props.type === 'edit'
  })
  const filterOption = (input: string, option: any) => {
    return option?.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const getRulesList = async () => {
    try {
      const rules = await ad_rules_list({
        page: 1,
        page_size: 9999
      })
      state.rulesList = rules.data.list || []
      if (!state.form.rule_id) {
        state.form.rule_id = props.item?.rule_id ? props.item?.rule_id : props.item?.rule_id === 0 ? 0 : 1
      }
    } catch (error) {
      console.error(error)
    }
  }
  const addRules = () => {
    routerResolve({
      name: 'ShieldingRules',
      query: {
        isAdd: true
      }
    })
  }
  onMounted(() => {
    if (props.type === 'edit' || props.type === 'copy') {
      const neededFields = pick(props.item, [
        'name',
        'media_type',
        'wx_group_id',
        'question_page',
        'question_wx_group_id',
        'group_type',
        'same_ip_limit',
        'qa_page_id',
        'qa_page_name',
        'b_page_items',
        'landing_page_id',
        'landing_page_name',
        'callback_action',
        'convert_target',
        'shop_type',
        'order_amount',
        'remark',
        'pre_callback_num',
        'callback_limit',
        'limit_second',
        'product_id',
        'session_num',
        'tag',
        'limit_type_visit',
        'limit_type_visit_second',
        'limit_sites',
        'jump_tr_link'
      ])
      let product_img: any = (props.item?.product_info?.product_img || []).map((pic: any, index: number) => {
        return {
          key: index + 1,
          value: pic
        }
      })

      state.form = {
        ...neededFields,
        session_num: props.item.session_num || 1,
        same_ip_limit: neededFields.same_ip_limit === 1,
        limit_type: props.item.limit_type || 0,
        shop_type: props.item.shop_type || 1,
        product_img: product_img.length ? product_img : [{ key: 1, value: '' }],
        callback_limit: props.item.callback_limit || 1,
        limit_second: props.item.limit_second || undefined,
        tag: props.item.tag ? props.item.tag : undefined,
        limit_type_visit_second: props.item.limit_type_visit_second,
        limit_second: props.item.limit_second,
        limit_sites: props.item.limit_sites || [],
        product_info: {
          shop_name: props.item?.product_info?.shop_name,
          product_name: props.item?.product_info?.product_name,
          product_id: props.item?.product_info?.product_id,
          product_price: props.item?.product_info?.product_price
            ? props.item?.product_info?.product_price / 100
            : undefined,
          product_category: props.item?.product_info?.product_category
        }
      }
      if (state.form.jump_tr_link) {
        state.form.landing_page_id = 11
        state.form.landing_page_name = state.form.jump_tr_link
        state.hasMyLink = true
      }
      state.form.wx_group_id =
        props.item.wx_group_a?.length > 0 ? props.item.wx_group_a?.split(',')?.map((item) => Number(item)) : []
      state.form.question_wx_group_id =
        props.item.wx_group_b?.length > 0 ? props.item.wx_group_b?.split(',')?.map((item) => Number(item)) : []
    }
    get_callback_position_list()
  })
  const initData = async () => {
    try {
      state.ruleIdCheck = props.item?.rule_id != 0
      let params = {
        page: 1,
        page_size: 9999
      }
      const [res, convert, product] = await Promise.all([
        wechat_group_list({ ...params, status: 1, is_link: 1 }),
        convert_target(),
        product_list({ ...params, media_type: props.item?.media_type || state.form.media_type })
      ])
      state.wxGroupList = res.data.WechatGroups || []
      state.QwxGroupList = res.data.WechatGroups || []

      state.productList = (product.data.list || []).map((item) => {
        return {
          ...item,
          label: item.name_alias || item.name
        }
      })
      state.convert = convert.data || {}
      getRulesList()
    } catch (error) {
      console.error(error)
    }
  }

  // 选择媒体类型
  const onChangeMediaType = (val: any) => {
    state.form.convert_target = undefined
    // state.form.limit_sites = []
    get_product_list(true)
    // get_callback_position_list()
    state.form.product_id = undefined

    if (val?.target?.value === 1) {
      state.form.shop_type = 2
    } else {
      if (state.hasMyLink) {
        state.form.landing_page_id = undefined
        state.form.landing_page_name = undefined
      }
      state.form.shop_type = 1
    }
  }

  const upDateKFData = async (type: string) => {
    let params = {
      page: 1,
      page_size: 9999
    }
    try {
      const res = await wechat_group_list({ ...params, status: 1, is_link: 1 })
      if (type === 'one') {
        state.wxGroupList = res.data.WechatGroups || []
      } else {
        state.QwxGroupList = res.data.WechatGroups || []
      }

      message.success('客服分组数据更新成功')
    } catch (error) {
      console.error(error)
    }
  }

  initData()

  const get_callback_position_list = async () => {
    try {
      const resp = await callback_position_list({
        media_type: state.form.media_type
      })
      console.log(resp, state.limitSites, 'sss')
      state.limitSites = (resp.data || []).map((v: any) => {
        return {
          label: v.value,
          value: Number(v.key)
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  //获取产品库数据
  const get_product_list = async (noTips = false) => {
    try {
      let params = {
        page: 1,
        page_size: 9999,
        media_type: state.form.media_type
      }
      const { data } = await product_list(params)
      state.productList = (data.list || []).map((item) => {
        return {
          ...item,
          label: item.name_alias || item.name
        }
      })
      if (!noTips) {
        message.success('产品库更新成功')
      }
    } catch (error) {
      console.error(error)
    }
  }
  // watch(
  //   () => state.form.media_type,
  //   () => {
  //     get_product_list(true)
  //     state.form.product_id = undefined
  //   }
  // )
  //新页面打开产品库
  const addProduct = () => {
    routerResolve({
      name: 'ProductManagement',
      query: {
        isAdd: true
      }
    })
  }
  const addKfPage = () => {
    routerResolve({
      name: 'WechatGroupManagement',
      query: {
        isAdd: true
      }
    })
  }

  // app内下单-添加图片
  const handleAddImg = () => {
    console.log(state.form.product_img)
    if (state.form.product_img.length < 3) {
      state.form.product_img.push({
        key: state.form.product_img.length + 1,
        value: ''
      })
    }
  }
  const handleDelImg = (index: number) => {
    if (state.form.product_img.length <= 1) {
      return message.warning('至少保留一张图片')
    }
    state.form.product_img.splice(index, 1)
  }

  const handleQuestion = () => {
    state.form.group_type = 1
    state.form.qa_page_id = undefined
    handleGroupType()
  }
  const handleGroupType = () => {
    state.form.question_wx_group_id = []
  }
  const onShowDialog = async (id, type, level) => {
    if (type == 3) {
      state.dialog.visibleB = true
    } else {
      state.dialog.visible = true
    }
    state.dialog.isTwo = level ? true : false
    state.dialog.title = type == 3 ? '' : '选择页面'
    state.dialog.width = 700
    state.dialog.type = type == 3 ? 'BPageMiddle' : 'ABPage'
    state.dialog.item = { id, type, name_alias: type == 1 ? state.form.landing_page_name : state.form.qa_page_name }
  }
  const addBpage = () => {
    state.dialog.visible = true

    onShowDialog(state.form.b_page_items, 2, 'two')
  }
  const delItem = (id) => {
    state.form.b_page_items = state.form.b_page_items.filter((item) => item.id !== id)
    state.dialog.item.id = state.form.b_page_items
  }
  const close = () => {
    emit('event', { type: 'updataLink', status: true })
  }
  const onEvent = (data) => {
    console.log(data)
    if (data.cmd !== 'submit') {
      state.dialog.visible = false
      return
    }
    switch (data.type) {
      case 1:
        state.hasMyLink = false
        state.form.landing_page_id = data.selectedRows[0].id
        state.form.landing_page_name = data.selectedRows[0].name_alias
        ruleForm.value.validateFields(['landing_page_id'])
        break
      case 2:
        data.selectedRows.forEach((newItem: any) => {
          const existingItem = state.form.b_page_items.find((oldItem: any) => oldItem.id === newItem.id)
          if (!existingItem) {
            state.form.b_page_items.push({
              id: newItem.id,
              image: newItem.cover_url,
              name: newItem.name_alias,
              title: newItem.name,
              weight: 1
            })
          }
        })
        if (state.form.b_page_items.length > 5) {
          message.warning('最多只能选择5条数据')
          state.form.b_page_items = state.form.b_page_items.slice(0, 5)
          state.dialog.item.id = state.form.b_page_items
        }
        break
      case 55:
        state.form.landing_page_id = 11
        state.form.landing_page_name = data.jump_tr_link
        state.hasMyLink = true
        ruleForm.value.validateFields(['landing_page_id'])
        break
      default:
        break
    }
    state.dialog.visible = false
  }
  const submitForm = (formEl: any) => {
    console.log(state.form, 'sdsdsdsdsd')
    formEl
      .validate()
      .then(() => {
        edit()
      })
      .catch((error: any) => {
        formEl.scrollToField(error.errorFields[0].name)
      })
  }
  const edit = async () => {
    try {
      state.loading = true
      let params = {
        ...state.form,
        landing_page_id: state.hasMyLink ? undefined : state.form.landing_page_id,
        jump_tr_link: state.hasMyLink ? state.form.landing_page_name : undefined,
        landing_page_name: state.hasMyLink ? undefined : state.form.landing_page_name,
        wx_group_a: state.form.wx_group_id?.join(','),
        wx_group_b: state.form.question_wx_group_id?.join(','),
        same_ip_limit: state.form.same_ip_limit ? 1 : 2,
        tag: state.form.callback_action === 'ONLY_TAG' ? state.form.tag : undefined,
        product_info: {
          ...state.form.product_info,
          product_price: state.form.product_info.product_price
            ? Number((state.form.product_info.product_price * 100).toFixed(0))
            : 0,
          product_img: state.form.product_img.map((item: any) => item.value)
        },
        limit_sites: state.form.media_type == 3 ? state.form.limit_sites : []
      }
      delete params.wx_group_id
      delete params.question_wx_group_id
      delete params.product_img
      const hasShop = ['AD_CONVERT_TYPE_APP_ORDER', 'COMPLETE_ORDER']
      if (!hasShop.includes(state.form.convert_target)) {
        delete params.shop_type
        params.product_info = {}
      }
      console.log(params)
      if (props.type === 'edit') {
        params.id = props.item.id
        await ad_link_update(params)
      } else {
        await ad_link_create(params)
      }
      emit('event', { type: 'AddLink', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  const deleteABPage = () => {
    state.form.landing_page_id = undefined
    state.form.landing_page_name = ''
  }
  const appPlaceAnOrderSetup = () => {
    const product = state.productList.find(item => item.id === state.form.product_id)
    if (state.form.media_type==3 && state.form.convert_target=='AD_CONVERT_TYPE_APP_ORDER' && product) {
      state.form.shop_type = product.shop_type || 1
      state.form.order_amount = product.order_amt
      state.form.product_info = {
        ...product.product_info,
        product_price: product.product_info.product_price / 100
      }
      state.form.product_img = (product.product_info.product_img || []).map((pic: any, index:number) => {
        return {
          key: index+1,
          value: pic
        }
      })
    }
    if (state.form.media_type==1 && state.form.convert_target=='COMPLETE_ORDER' && product) {
      state.form.shop_type = product.shop_type || 2
      state.form.order_amount = product.order_amt
      state.form.product_info = {
        ...product.product_info,
        product_price: product.product_info.product_price / 100
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .footer {
    text-align: end;
  }
  .group_type {
    :deep(.ant-row) {
      display: flex;
      align-items: baseline;
    }
    .wx_group_id {
      :deep(.ant-row) {
        display: flex;
        align-items: start !important;
      }
    }
  }
  .open-num {
    line-height: 31px;
    margin-top: 12px;
    :deep(.ant-select.ant-select-in-form-item) {
      width: 75px;
    }
  }
  .dashed_box {
    border: 1px dashed #d9d9d9;
    padding: 8px;
    margin-top: 10px;
    border-radius: 4px;
  }

  .text_name {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
  .A-text {
    background: #fff5ea;
    border: 1px solid #fed9b2;
    color: #fe9d35;
    padding: 4px 8px;
    border-radius: 6px;
    flex: none;
  }
  .item-tag {
    padding: 0 4px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
    color: #52c41a;
  }
</style>
