import http from '@/utils/request'

/**
 * 微信组列表
 */
export const wechat_group_list = (data) => {
  return http('post', `/admin/wechat_group/list`, data)
}
/**
 * 问答/落地页
 */
export const ad_h5_list = (data) => {
  return http('get', `/common/ad_h5/list`, data)
}
/**
 * 转化目标
 */
export const convert_target = () => {
  return http('post', `/admin/ad_link/convert_target`, {})
}
/**
 * 创建广告链接
 */
export const ad_link_create = (data) => {
  return http('post', `/admin/ad_link/create`, data)
}
/**
 * 更新数据
 */
export const ad_link_update = (data) => {
  return http('post', `/admin/ad_link/update`, data)
}
/**
 * 列表
 */
export const ad_link_list = (data) => {
  return http('post', `/admin/ad_link/list`, data)
}
/**
 * 域名列表
 *
 */
export const domain_list = (data) => {
  return http('post', `/admin/domain/list`, data)
}
/**
 * 域名列表 导出巨量链接 后端让此处替换为  /admin/domain/list_by_select
 *
 */
export const domain_list_by_select = (data) => {
  return http('post', `/admin/domain/list_by_select`, data)
}
/**
 * 产品库列表
 *
 */
export const product_list = (data) => {
  return http('post', `/admin/product/list`, data)
}

/**
 * 复制后请求接口
 */
export const ad_copy_url = (data: any) => {
  return http('get', `/admin/ad_link/jump_url`, data)
}

/**
 * 修改客服组
 */
export const editKeFu = (data: any) => {
  return http('post', `/admin/ad_link/update_wx_group`, data)
}
/**
 * 屏蔽规则列表
 */
export const ad_rules_list = (data: any) => {
  return http('post', `/admin/rules/list`, data)
}

/**
 * 批量修改客服组
 */
export const batchEditKeFu = (data: any) => {
  return http('post', `/admin/ad_link/update_wx_group_batch`, data)
}
/* 版位回传
 *
 */
export const callback_position_list = (data: any) => {
  return http('post', `/admin/ad/callback_position_list`, data)
}

// 获取公司详情
export const company_info = (data: any) => {
  return http('get', `/admin/company/info`, data)
}
