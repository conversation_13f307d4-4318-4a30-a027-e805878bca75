<template>
  <div>
    <DesTablePage>
      <template #title> 投放链接库 </template>
      <template #extra>
        <a-button
          type="primary"
          v-auth="['pmSvllBatchAction']"
          :disabled="pointData.balance <= 0 || state.selectedRowKeys.length === 0"
          @click="batchKeFuAction()"
        >
          批量更换客服分组
        </a-button>
        <a-button type="primary" v-auth="['pmSvllAdd']" :disabled="pointData.balance <= 0" @click="handleChange('add')">
          新增
        </a-button>
      </template>

      <template #action>
        <SearchBaseLayout ref="searchFormDataRef" :data="searchList" @changeValue="changeValue" :actions="actions" />
      </template>

      <template #tableWarp>
        <!-- <div class="mb12px text-right">
          <a-checkbox v-model:checked="state.status" @change="changeCan_add"> 投放状态为开启 </a-checkbox>
        </div> -->
        <!-- 
              :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange
        }" -->
        <TableZebraCrossing
          :data="tableData"
          @change="pageChange"
          :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
        >
          <template #headerCell="{ scope }">
            <template v-if="scope.column.dataIndex == 'fans_flew'">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title>流速：当前时间往前推10分钟的加粉数据</template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.key === 'status'">
              <a-switch
                v-model:checked="record.status"
                :checkedValue="1"
                :unCheckedValue="2"
                :disabled="pointData.balance <= 0"
                @change="handleReturn($event, record)"
              />
            </template>
            <template v-if="column.key === 'name'">
              <div class="flex-y-center">
                <a-tooltip>
                  <template #title>{{ mediaType2Content(record.media_type)?.label }}</template>
                  <img
                    v-if="[1, 2, 3].includes(record.media_type)"
                    class="w-15px h-15px mr-4px"
                    :src="mediaType2Content(record.media_type)?.icon"
                  />
                </a-tooltip>
                <a-tooltip>
                  <template #title>{{ record.name }}</template>
                  <div class="text_overflow_row1">
                    {{ record.name }}
                  </div>
                </a-tooltip>
              </div>
            </template>
            <template v-if="column.key === 'media_type'">
              {{ mediaTypeName[record.media_type] }}
            </template>
            <template v-if="column.dataIndex === 'fans_flew'">
              <div>
                <span class="c-#52c41a">{{ record.fans_flew || 0 }}/</span>
                <span class="c-#B6AFAF">10min</span>
              </div>
            </template>
            <template v-if="column.key === 'wx_group_name'">
              <template v-if="record.group_type === 1">
                <!-- <a-tooltip placement="topLeft">
                <template #title>{{ record.wx_group_name }}</template>
                <div class="text_overflow">{{ record.wx_group_name }}</div>
              </a-tooltip> -->
                <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer(record, 'A')">
                  {{ record.wx_group_a?.split(',')?.length || 0 }}
                </a-button>
              </template>
              <template v-else>
                <div class="text_overflow_row1">
                  <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer(record, 'All')">
                    {{ record.wx_group_a?.split(',')?.length + record.wx_group_b?.split(',')?.length || 0 }}
                  </a-button>
                </div>
                <!-- <div class="text_overflow_row1">
                  <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer(record, 'B')">
                    {{ record.wx_group_b?.split(',')?.length }}
                  </a-button>
                </div> -->
              </template>
            </template>
            <template v-if="column.key === 'product_name'">
              <a-tooltip>
                <template #title v-if="record.product_name">{{ record.product_name }}</template>
                <div class="text_overflow_row1 cursor-pointer">{{ record.product_name }}</div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'remark'">
              <a-tooltip v-if="record.remark">
                <template #title>{{ record.remark }}</template>
                <div class="text_overflow_row1 cursor-pointer">{{ record.remark }}</div>
              </a-tooltip>
              <span v-else>--</span>
            </template>
            <template v-if="column.key === 'operate'">
              <a-button
                size="small"
                v-auth="['pmSvllExtractLink']"
                :disabled="pointData.balance <= 0"
                class="pa-0!"
                type="link"
                @click="handleChange('extract', record)"
                >提取链接</a-button
              >
              <a-button
                size="small"
                class="pa-0!"
                type="link"
                v-auth="['pmSvllEdit']"
                :disabled="pointData.balance <= 0"
                @click="handleChange('edit', record)"
                >编辑</a-button
              >
              <a-button
                size="small"
                v-auth="['pmSvllCopy']"
                :disabled="pointData.balance <= 0"
                class="pa-0!"
                type="link"
                @click="handleChange('copy', record)"
                >复制</a-button
              >
            </template>
          </template>
        </TableZebraCrossing>
        <a-modal
          v-model:open="state.dialog.visible"
          :title="state.dialog.title"
          :width="state.dialog.width"
          destroyOnClose
          :footer="null"
          :centered="true"
          :closable="!['extract'].includes(state.dialog.type)"
          @cancel="
            () => {
              ;['add', 'edit', 'copy'].includes(state.dialog.type) && onEvent({ type: 'updataLink', status: true })
            }
          "
        >
          <AddLink
            v-if="['add', 'edit', 'copy'].includes(state.dialog.type)"
            :type="state.dialog.type"
            :item="state.dialog.item"
            @event="onEvent"
          />
          <ExtractLink v-if="state.dialog.type === 'extract'" :item="state.dialog.item" @event="onEvent" />
        </a-modal>
      </template>
    </DesTablePage>
    <a-drawer
      destroyOnClose
      v-model:open="state.drawer.visible"
      :title="state.drawer.title"
      :width="state.drawer.width"
      placement="right"
      @close="
        () => {
          state.drawer.visible = false
          getList()
          clearBatchData()
        }
      "
    >
      <KeFuTem
        :AData="state.drawer.AData"
        :BData="state.drawer.BData"
        @cancel="state.drawer.visible = false"
        :AIds="state.drawer.AIds"
        :BIds="state.drawer.BIds"
        @sure="sureDraw"
        @batchSure="batchSureDraw"
      />
    </a-drawer>
  </div>
</template>
<script setup lang="ts">
  import AddLink from './AddLink.vue'
  import ExtractLink from './ExtractLink.vue'
  import KeFuTem from '@/views/workbench/privateDomainManagement/wechatGroupManagement/drawerTem.vue'
  import { ad_link_list, ad_link_update, editKeFu, batchEditKeFu } from './index.api'
  import { getUserListApi } from '@/api/common'
  import { ref, reactive, computed } from 'vue'
  import datas from './src/datas'
  import { message } from 'ant-design-vue'
  import { useTheme, usePoints, useApp } from '@/hooks'
  const { isMobile } = useApp()
  import { mediaType2Content } from '@/utils'
  const { pointData } = usePoints()
  const { themeVar } = useTheme()
  const { columns, searchList, callbackActionCls } = datas()
  const props = defineProps(['item', 'from'])
  const emits = defineEmits(['event'])
  const searchFormDataRef = ref()
  import dayjs from 'dayjs'
  const state = reactive({
    active: 1,
    status: false,
    params: {
      page: 1,
      page_size: 20,
      begin_date: dayjs().format('YYYY-MM-DD'),
      end_date: dayjs().format('YYYY-MM-DD')
    },
    drawer: {
      visible: false,
      title: '客服分组',
      width: isMobile.value ? '100%' : '90%',
      type: '',
      landTitleA: '',
      landTitleB: '',
      AData: [],
      BData: [],
      row: {},
      AIds: '',
      BIds: ''
    },
    selectedRowKeys: [],
    selectedRows: [],
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      item: null
    }
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  }
  const clearBatchData = () => {
    state.selectedRowKeys = []
    state.selectedRows = []
    state.drawer.AData = []
    state.drawer.BData = []
    state.drawer.AIds = ''
    state.drawer.BIds = ''
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 'max-content'
    },
    dataSource: [],
    loading: false,
    columns: columns,

    rowKey: 'id',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  const mediaTypeName = {
    3: '巨量引擎',
    2: '磁力引擎',
    1: '腾讯广告'
  }
  const onShowDrawer = (row: any, type: string) => {
    state.drawer.visible = true
    const selectAData = row.wx_group_a?.split(',')?.map((item) => Number(item)) || []
    const selectBdata = row.wx_group_b ? row.wx_group_b?.split(',')?.map((item) => Number(item)) : []
    console.log('selectBdata----', selectBdata)
    // const selectData = data?.split(',')?.map((item) => Number(item)) || []
    state.drawer.AData = selectAData
    state.drawer.BData = selectBdata
    state.drawer.row = row
    state.drawer.type = type

    if (type === 'tong') {
      state.drawer.landTitleA = row.landing_page_name
      state.drawer.landTitleB = row.qa_page_name
    } else if (type === 'A') {
      state.drawer.landTitleA = row.landing_page_name
      state.drawer.landTitleB = ''
    } else {
      state.drawer.landTitleA = ''
      state.drawer.landTitleB = row.qa_page_name
    }
  }
  const batchKeFuAction = () => {
    state.drawer.visible = true
    const onlyA = state.selectedRows?.filter((item: any) => item?.group_type === 1)
    const onlyB = state.selectedRows?.filter((item: any) => item?.group_type === 2)
    state.drawer.BIds = onlyB?.map((item) => item.id).join(',')
    state.drawer.AIds = onlyA?.map((item) => item.id).join(',')
    // console.log('onlyA---', state.selectedRows, state.drawer.AIds, state.drawer.BIds)
  }
  const changeCan_add = () => {
    getList()
  }
  const sureDraw = async (dataA: any, dataB: any) => {
    const params = {
      Id: state.drawer.row?.id
    }
    const aData = dataA.map((item) => item.id).join(',')
    params.wx_group_a = aData
    if (dataB?.length > 0) {
      const bData = dataB.map((item) => item.id).join(',')
      params.wx_group_b = bData
    }

    await editKeFu(params)
    clearBatchData()
    message.success('修改成功')

    state.drawer.visible = false
    getList()
  }
  const batchSureDraw = async (data: any) => {
    // console.log('data---', data)
    await batchEditKeFu(data)
    message.success('批量操作成功')
    clearBatchData()
    state.drawer.visible = false
    getList()
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    // console.log('selectedRowKeys', selectedRowKeys, selectedRows)

    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }

  const getList = async () => {
    try {
      tableData.loading = true
      const res = await ad_link_list({ ...state.params, status: state.status ? 1 : undefined })
      tableData.dataSource = res.data.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data?.page || 1
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }
  getList()
  const getUserList = async () => {
    try {
      const res = await getUserListApi()
      searchList.forEach((item) => {
        if (item.field == 'admin_id') {
          item.props.options = res.data.map((item) => {
            return {
              label: item.realname,
              value: item.id
            }
          })
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getUserList()
  const handleReturn = async (val: number, item: { id: number }) => {
    try {
      let params = {
        id: item.id,
        status: val
      }
      await ad_link_update(params)
      message.success('操作成功')
      getList()
    } catch (error) {}
  }
  //分页
  const pageChange = (pagination) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  //筛选
  const changeValue = (data) => {
    state.params = { ...state.params, ...data.formData, page: 1 }
    if (data.formData.updated_time?.length && data.formData.updated_time[0] && data.formData.updated_time[1]) {
      state.params.begin_date = data.formData.updated_time[0]
      state.params.end_date = data.formData.updated_time[1]
    } else {
      state.params.begin_date = undefined
      state.params.end_date = undefined
    }
    delete state.params.updated_time
    if (!data.status) {
      state.params.page = 1
      state.params.page_size = 20
      state.params.begin_date = dayjs().format('YYYY-MM-DD')
      state.params.end_date = dayjs().format('YYYY-MM-DD')
      searchFormDataRef.value.formData.updated_time = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    getList()
  }
  const DIALOG_NAME: any = {
    add: {
      title: '新增投放链接',
      width: 830
    },
    copy: {
      title: '新增投放链接',
      width: 830
    },
    edit: {
      title: '编辑投放链接',
      width: 830
    },
    extract: {
      title: '导出巨量链接',
      width: 620
    }
  }
  const handleChange = (type: string, record?: any) => {
    if (type == 'edit') {
      record.limit_type_visit==1 && (record.limit_type_visit=2)
      record.callback_limit==1 && (record.callback_limit=2)
    }
    console.log(record)
    state.dialog.type = type
    state.dialog.item = record ? record : null
    state.dialog.title = type === 'extract' && record.media_type === 1 ? '导出广点通链接' : DIALOG_NAME[type].title
    state.dialog.width = DIALOG_NAME[type].width
    state.dialog.visible = true
  }
  const onEvent = (data) => {
    state.dialog.visible = false
    if (data.status) {
      getList()
    }
  }
</script>

<style lang="scss" scoped>
  .timer_search {
    padding: 0 0 20px 0;
    box-sizing: border-box;
    justify-content: flex-end;
    flex-wrap: wrap;
    .search_picker {
      border-radius: 6px;
      height: 30px;
      background-color: #f0f2f6;
      margin-left: 20px;
    }
  }
  .product_id {
    margin-top: v-bind('themeVar.marginSmall');
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    font-family: PingFang SC;
    color: #404040;
    line-height: 20px;
  }
  .product_name {
    font-size: 14px;
    color: v-bind('themeVar.infoColor');
    line-height: 24px;
    cursor: pointer;
  }
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    @include set_font_config(--font-size-mini, --text-color-gray);
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    color: v-bind('themeVar.textColorGray');
    line-height: 16px;
  }
</style>
