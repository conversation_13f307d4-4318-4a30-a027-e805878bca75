<template>
  <div>
    <a-tabs v-if="mediaType == 1 && !isTwo && state.companyInfo?.gdt_domain_switch==1" v-model:activeKey="state.activeTab" @change="changeTab">
      <a-tab-pane key="1" tab="选择落地页"></a-tab-pane>
      <a-tab-pane key="2" tab="自定义链接"></a-tab-pane>
    </a-tabs>
    <template v-if="state.activeTab === '1'">
      <SearchBaseLayout ref="searchFormDataRef" :data="searchList" @changeValue="changeValue" :actions="actions" />
      <TableZebraCrossing
        :data="tableData"
        @change="pageChange"
        :row-selection="{
          type: item.type == 2 ? 'checkbox' : 'radio',
          selectedRowKeys: state.selectedRowKeys,
          selectedRows: state.selectedRows,
          onChange: (selectedKeys: any, selectedRows: any) => {
            console.log(selectedKeys)
            state.selectedRowKeys = selectedKeys
            state.selectedRows = selectedRows
          }
        }"
        class="mt24px"
      >
        <template #bodyCell="{ scope: { record, column } }">
          <template v-if="column.key === 'name_alias'">
            <a-tooltip placement="topLeft">
              <template #title>{{ record.name_alias }}</template>
              <div class="text_overflow_row1">{{ record.name_alias }}</div>
            </a-tooltip>
            <div class="number-id">ID：{{ record.code }}</div>
          </template>
          <template v-if="column.key === 'name'">
            <a-tooltip placement="topLeft">
              <template #title>{{ record.name }}</template>
              <div class="text_name">{{ record.name }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'cover_url'">
            <a-image
              v-if="record.cover_url"
              :preview="false"
              class="w-40px! h-40px!"
              :src="record.cover_url"
              alt=""
            ></a-image>
          </template>
        </template>
      </TableZebraCrossing>
    </template>
    <template v-else>
      <a-input v-model:value="state.jump_tr_link" class="mb-24px" placeholder="请输入链接" />
    </template>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm" :disabled="sureBtnDisabled">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ad_h5_list, company_info } from './index.api'
  import { ref, reactive, onMounted, computed } from 'vue'
  import { message } from 'ant-design-vue'
  const props = defineProps(['item', 'hasLink', 'url', 'mediaType', 'isTwo'])
  const emits = defineEmits(['event'])
  const searchFormDataRef = ref()
  const state = reactive({
    params: {
      page: 1,
      page_size: 20
    },
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
    activeTab: '1',
    jump_tr_link: undefined,
    companyInfo: {}
  })
  const isValidUrl = (url: any) => {
    // 基本的URL正则表达式，支持http/https协议，域名和路径
    const urlRegex = /^https?:\/\/(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+([\/?#].*)?$/

    // 检查是否为空或非字符串
    if (!url || typeof url !== 'string') {
      return false
    }

    // 测试正则表达式
    return urlRegex.test(url.trim())
  }
  const changeTab = (e) => {
    console.log('ee---', e)
    if (e === '2') {
      state.selectedRowKeys = []
      state.selectedRows = []
    } else {
      state.jump_tr_link = undefined
    }
  }
  const sureBtnDisabled = computed(() => {
    if (state.activeTab === '1') {
      return state.selectedRowKeys.length == 0
    }
    return !state.jump_tr_link
  })
  const searchList = reactive([
    {
      type: 'input.text',
      field: 'name_alias',
      value: undefined,
      props: {
        placeholder: '请输入名称',
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    },
    {
      type: 'input.text',
      field: 'code',
      value: undefined,
      props: {
        placeholder: '请输入ID',
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    }
  ])
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 500,
      y: 340
    },
    dataSource: [],
    loading: false,
    columns: [
      {
        title: '名称',
        dataIndex: 'name_alias',
        key: 'name_alias',
        width: '198px'
      },
      {
        title: '标题',
        dataIndex: 'name',
        key: 'name',
        width: '160px'
      },
      {
        title: '落地页',
        dataIndex: 'cover_url',
        key: 'cover_url',
        width: '125px'
      }
    ],
    rowKey: 'id',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  onMounted(() => {
    if (props.item?.id && props.item.type == 1 && !props.hasLink) {
      state.selectedRowKeys = [props.item.id]
      state.selectedRows = [{ id: props.item.id, name_alias: props.item.name_alias }]
    } else if (props.hasLink && !props.isTwo) {
      state.jump_tr_link = props.url
      state.activeTab = '2'
    }
    getList()
    getCompanyInfo()
  })
  // 获取公司详情
  const getCompanyInfo = async () => {
    try {
      const res = await company_info({})
      state.companyInfo = res.data
      if (res.data.gdt_domain_switch==2 && props.mediaType==1) {
        state.activeTab = '1'
      }
    } catch (error) {
      console.log(error)
    }
  }
    
  const getList = async () => {
    try {
      tableData.loading = true
      const res = await ad_h5_list(state.params)
      tableData.dataSource = res.data.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data?.page || 1
      if (tableData.dataSource.length > 3) {
        tableData.scroll.y = 240
      } else {
        delete tableData.scroll.y
      }
      // 回显
      // if (props.item.type == 2) {
      //   state.selectedRowKeys = props.item.id.map((item:any) => item.id)
      //   state.selectedRows = props.item.id.map(({ id }) => tableData.dataSource.find(data => data.id === id)).filter(Boolean)
      // }
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }
  //分页
  const pageChange = (pagination) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  //筛选
  const changeValue = (data) => {
    if (!data.status) {
      state.params.page = 1
    }
    state.params = { ...state.params, ...data.formData, page: 1 }
    getList()
  }

  const close = () => {
    emits('event', { cmd: 'close' })
  }
  const submitForm = () => {
    if (state.activeTab === '1') {
      if (state.selectedRowKeys.length === 0) {
        message.warning('请选择一条数据')
        return
      }
      console.log(state.selectedRows)
      emits('event', { cmd: 'submit', type: props.item.type, selectedRows: state.selectedRows })
    } else {
      if (isValidUrl(state.jump_tr_link)) {
        emits('event', { cmd: 'submit', type: 55, jump_tr_link: state.jump_tr_link })
      } else {
        message.warning('请输入正确的链接地址')
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_name {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
  .footer {
    text-align: end;
  }
</style>
