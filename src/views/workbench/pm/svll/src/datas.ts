import { reactive } from 'vue'
import { mediaTypeSearch } from '@/utils'
import dayjs from 'dayjs'
export default function datas() {
  const searchList = reactive([
    {
      type: 'input.text',
      field: 'name',
      value: undefined,
      props: {
        placeholder: '请输入名称',
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'remark',
      value: undefined,
      props: {
        placeholder: '请输入备注',
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'media_type',
      value: '',
      props: {
        options: mediaTypeSearch,
        placeholder: '请选择媒体类型'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'admin_id',
      value: undefined,
      props: {
        options: [],
        placeholder: '请选择创建人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'updated_time',
      value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      props: {
        placeholder: ['加粉开始时间', '加粉结束时间']
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])

  const columns = reactive([
    // {
    //   title: '投放状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   width: 80
    // },
    // {
    //   title: 'ID',
    //   dataIndex: 'code',
    //   key: 'code',
    //   width: 100
    // },
    {
      title: '媒体类型/名称',
      dataIndex: 'name',
      key: 'name',
      width: 160
    },
    // {
    //   title: '媒体类型',
    //   dataIndex: 'media_type',
    //   key: 'media_type',
    //   width: 100
    // },
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 100
    },
    {
      title: '关联客服分组',
      dataIndex: 'wx_group_name',
      key: 'wx_group_name',
      width: 140
    },

    {
      title: '加粉数',
      dataIndex: 'fans_num',
      key: 'fans_num',
      width: 100
    },
    {
      title: '流速',
      dataIndex: 'fans_flew',
      key: 'fans_flew',
      width: 100
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 140
    },
    {
      title: '回传限制',
      dataIndex: 'callback_action_name',
      key: 'callback_action_name',
      width: 140
    },
    {
      title: '转化目标',
      dataIndex: 'convert_target_name',
      key: 'convert_target_name',
      width: 120
    },
    {
      title: '创建人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 120
    },
    {
      title: '更新时间',
      dataIndex: 'updated_time',
      key: 'updated_time',
      width: 180
    },

    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      width: 160,
      fixed: 'right'
    }
  ])

  const callbackActionCls = (type: 'FULL' | 'ONLY_SESSION' | string) => {
    return (
      {
        FULL: 'success-tag',
        ONLY_SESSION: 'yellow-tag'
      }[type as 'FULL' | 'ONLY_SESSION'] || 'normal-tag'
    )
  }

  const statusData = {
    0: '未转化',
    1: '未转化',
    2: '已转化',
    3: '转化失败',
    4: '已转化未上报',
    5: '手动上报',
    6: '转化黑名单',
    7: '云盾'
  }
  return { columns, searchList, statusData, callbackActionCls }
}
