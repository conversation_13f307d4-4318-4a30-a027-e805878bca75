<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>产品管理</div>
      </template>
      <template #extra>
        <a-button
          v-auth="['productAdd']"
          type="primary"
          @click="onShowDialog('add', null)"
          :disabled="pointData.balance <= 0"
          >新增</a-button
        >
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #headerCell="{ scope }">
              <template v-if="['cost'].includes(scope.column.dataIndex)">
                <div>
                  <span>{{ scope.column.title }}</span>
                  <a-tooltip>
                    <template #title>{{ scope.column.text }}</template>
                    <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                  </a-tooltip>
                </div>
              </template>
            </template>
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'ad_name'">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.ad_name }}</template>
                  <div class="text_overflow">{{ scope.record.ad_name }}</div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'link_num'">
                <span class="cursor-pointer c-primary" @click="onShowDialog('link_num', scope.record)">{{
                  scope.record.link_num
                }}</span>
              </template>
              <template v-if="scope.column.key === 'action'">
                <a-button
                  v-auth="['productEdit']"
                  class="pa-0! m-r-5px"
                  type="link"
                  size="small"
                  @click="onShowDialog('edit', scope.record)"
                  :disabled="pointData.balance <= 0"
                  >编辑</a-button
                >
                <a-popconfirm title="确定进行此操作吗?" placement="topRight" @confirm="del(scope.record)">
                  <a-button v-auth="['productDelete']" class="pa-0! m-r-5px" type="link" size="small" @click=""
                    >删除</a-button
                  >
                </a-popconfirm>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      destroyOnClose
      :centered="true"
      :footer="null"
    >
      <AddProduct
        v-if="['add'].includes(data.dialog.type)"
        @event="onEvent"
        :datas="data.dialog.item"
        :type="data.dialog.type"
      />
      <Edit
        v-if="['edit'].includes(data.dialog.type)"
        @event="onEvent"
        :datas="data.dialog.item"
        :type="data.dialog.type"
      />
      <DeploymentLink v-if="['link_num'].includes(data.dialog.type)" :datas="data.dialog.item" />
    </a-modal>
  </div>
</template>

<script setup>
  import { onMounted } from 'vue'
  import { useRoute } from 'vue-router'

  import datas from './src/data'
  import AddProduct from './components/AddProduct.vue'
  import Edit from './components/Edit.vue'
  import DeploymentLink from './components/DeploymentLink.vue'
  import { usePoints } from '@/hooks'
  import { EditOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
  const { pointData } = usePoints()
  const route = useRoute()
  const { pageChange, searchForm, searchConfig, data, getList, onShowDialog, onEvent, del } = datas()
  getList()

  onMounted(() => {
    if (route.query.isAdd) {
      onShowDialog('add', null)
    }
  })
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
</style>
