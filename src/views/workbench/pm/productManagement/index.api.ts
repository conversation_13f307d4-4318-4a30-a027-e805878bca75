import http from '@/utils/request'

/**
 * 新增产品
 */
export const create = (data?: any) => {
  return http('post', `/admin/product/create`, data)
}
/**
 * 编辑产品
 */
export const update = (data?: any) => {
  return http('post', `/admin/product/update`, data)
}
/**
 * 线索列表
 */
export const product_list = (data?: any) => {
  return http('post', `/admin/product/list`, data)
}
/**
 * 删除列表
 */
export const product_delete = (data?: any) => {
  return http('post', `/admin/product/delete`, data)
}
/**
 * 投放链接列表
 *
 */
export const ad_link_list = (data?: any) => {
  return http('post', `/admin/ad_link/list`, data)
}
