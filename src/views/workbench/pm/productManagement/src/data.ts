import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { product_list, product_delete } from '../index.api'
import { useApp } from '@/hooks'
import { mediaTypeSearch } from '@/utils'
export default function datas() {
  const searchConfig = reactive({
    data: [
      {
        field: 'media_type',
        type: 'select',
        value: '',
        props: {
          options: mediaTypeSearch,
          placeholder: '请选择媒体类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入广告名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    isResizable: false,
    columns: [
      {
        title: '产品名称',
        dataIndex: 'name',
        key: 'name',
        slot: true,
        width: 200
      },
      {
        title: '媒体类型',
        dataIndex: 'media_name',
        key: 'media_name',
        slot: true,
        width: 200
      },
      {
        title: '成本限制',
        dataIndex: 'cost',
        key: 'cost',
        text: '当关联这个产品的广告链接对应计划实际成本高于设置成本时，系统标红提示',
        slot: true,
        width: 200
      },
      {
        title: '关联投放链接',
        dataIndex: 'link_num',
        key: 'link_num',
        width: 200,
        slot: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100,
        fixed: 'right',
        slot: true
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      item: null
    }
  })

  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await product_list(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      ad_ids: v.formData.ad_ids ? v.formData.ad_ids.join(',') : undefined,
      phone: v.formData.phone ? String(v.formData.phone) : undefined,
      ip: v.formData.ip ? String(v.formData.ip) : undefined,
      created_at: v.formData.created_at ? v.formData.created_at.join('_') : undefined,
      admin_ids: v.formData.admin_ids ? v.formData.admin_ids.join(',') : undefined
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type, item) => {
    data.dialog.item = item
    data.dialog.type = type

    data.dialog.visible = true
    switch (type) {
      case 'add':
        data.dialog.width = 800
        data.dialog.title = '新增产品'
        break
      case 'edit':
        data.dialog.width = 800
        data.dialog.title = '编辑产品'
        data.dialog.item = JSON.parse(JSON.stringify({
          ...item,
          product_info: {
            ...item.product_info,
            product_price: item.product_info.product_price / 100
          }
        }))
        try {
          if (data.dialog.item.shop_type===0) {
            delete data.dialog.item.shop_type
            delete data.dialog.item.order_amt
            delete data.dialog.item.product_info.product_price
          }
        } catch {}
        break
      case 'link_num':
        data.dialog.width = 850
        data.dialog.title = '关联投放链接'
        break
    }
  }
  const onEvent = (e) => {
    data.dialog.visible = false
    if (e.type !== 'close') {
      getList()
    }
  }
  //删除
  const del = async (item) => {
    try {
      await product_delete({ id: item.id })
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  return {
    pageChange,
    searchForm,
    searchConfig,
    data,
    getList,
    onShowDialog,
    onEvent,
    del
  }
}
