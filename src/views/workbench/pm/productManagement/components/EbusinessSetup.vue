<template>
  <div class="bg-#fdfaf4 py-10px px-10px rounded-4px">
    <div class="flex flex-items-center">
      <div class="c-#000 fw-700 font-size-14px">电商配置</div>
      <div class="c-#d94614 font-size-12px lh-12px ml-10px">配置电商表单数据质量会影响广告模型的精准度哦!</div>
    </div>
    <a-form v-if="media_type==3" :form="form" ref="ruleForm" class="mt-10px" :labelCol="{ style: { width: '90px' } }">
      <div class="flex">
        <a-form-item label="电商" class="flex-1">
          <a-select v-model:value="form.shop_type" placeholder="请选择电商">
            <a-select-option :value="v.value" v-for="v in shopType" :key="v.value">
              <div class="flex-y-center">
                <img :src="v.icon" class="w-14px h-14px mr-8px" />
                <div>{{ v.label }}</div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="订单金额" class="flex-1 ml-20px" :labelCol="{ style: { width: '70px' } }">
          <a-input-number
            v-model:value="form.order_amt"
            :min="0.01"
            :max="99999999.99"
            :precision="2"
            placeholder="请输入订单金额"
            class="w-100%"
          >
          </a-input-number>
        </a-form-item>
      </div>
      <a-form-item label="店铺名称" class="flex-1">
        <a-input
          :maxlength="50"
          show-count
          v-model:value.trim="form.product_info.shop_name"
          placeholder="请输入店铺名称"
        />
      </a-form-item>
      <a-form-item label="商品名称" class="flex-1">
        <a-input
          :maxlength="100"
          show-count
          v-model:value.trim="form.product_info.product_name"
          placeholder="请输入商品名称"
        />
      </a-form-item>
      <div class="flex">
        <a-form-item label="商品ID" class="flex-1">
          <a-input
            :maxlength="50"
            show-count
            v-model:value.trim="form.product_info.product_id"
            placeholder="请输入商品ID"
          />
        </a-form-item>
        <a-form-item label="商品金额" class="flex-1 ml-20px" :labelCol="{ style: { width: '70px' } }">
          <a-input-number
            v-model:value="form.product_info.product_price"
            :min="0.01"
            :max="99999999.99"
            :precision="2"
            placeholder="请输入商品金额"
            class="w-100%"
          >
          </a-input-number>
        </a-form-item>
      </div>
      <a-form-item label="商品分类" class="flex-1">
        <a-input
          :maxlength="50"
          show-count
          v-model:value.trim="form.product_info.product_category"
          placeholder="请输入商品分类"
        />
      </a-form-item>
      <a-form-item label="商品图片" class="flex-1">
        <a-form-item
          v-for="(item, index) in form.product_info.product_img"
          :key="index"
          :name="['product_img', index]"
          :rules="{
            validator: (rule, value, callback) => {
              if (form.product_info.product_img?.[index]) {
                if (!/^https?:\/\//.test(form.product_info.product_img?.[index])) {
                  callback('请填写合规的商品图片链接 https或者http开头')
                } else if (/[\u4e00-\u9fa5]/.test(form.product_info.product_img?.[index])) {
                  callback('不能输入汉字')
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: ['change', 'blur']
          }"
        >
          <div class="flex-y-center">
            <a-input 
              v-model:value.trim="form.product_info.product_img[index]" 
              placeholder="请输入商品图片链接，格式http/https开头" 
            />
            <a-button 
              type="link" 
              danger 
              :icon="h(DeleteOutlined)" 
              @click="handleDelImg(index)"
            ></a-button>
          </div>
        </a-form-item>
        <a-button 
          type="dashed" 
          class="w-full!" 
          v-if="form.product_info.product_img?.length < 3" 
          @click="handleAddImg"
        > 
          +添加 
        </a-button>
      </a-form-item>
    </a-form>
    <a-form v-if="media_type==1" :form="form" ref="ruleForm" class="mt-10px" :labelCol="{ style: { width: '90px' } }">
      <div class="flex">
        <a-form-item label="电商" class="flex-1">
          <a-select v-model:value="form.shop_type" placeholder="请选择电商">
            <a-select-option :value="v.value" v-for="v in GdshopType" :key="v.value">
              <div class="flex-y-center">
                <img :src="v.icon" class="w-14px h-14px mr-8px" />
                <div>{{ v.label }}</div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="订单金额" class="flex-1 ml-20px" :labelCol="{ style: { width: '70px' } }">
          <a-input-number
            v-model:value="form.order_amt"
            :min="0.01"
            :max="99999999.99"
            :precision="2"
            placeholder="请输入订单金额"
            class="w-100%"
          >
          </a-input-number>
        </a-form-item>
      </div>
      <a-form-item label="商品名称" class="flex-1">
        <a-input
          :maxlength="100"
          show-count
          v-model:value.trim="form.product_info.product_name"
          placeholder="请输入商品名称"
        />
      </a-form-item>
    </a-form>
  </div>
</template>
<script setup>
  import { h, ref, watch } from 'vue'
  import { mediaType, shopType, callbackLimit, GdshopType } from '@/utils'
  import { DeleteOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  const ruleForm = ref()
  const props = defineProps(['form', 'media_type'])
  const emits = defineEmits(['event'])
  const rulesData = ref({
  })
  const handleAddImg = () => {
    if (props.form.product_info.product_img.length < 3) {
      props.form.product_info.product_img.push('')
    }
  }
  const handleDelImg = (index) => {
    if (props.form.product_info.product_img.length <= 1) {
      return message.warning('至少保留一张图片')
    }
    props.form.product_info.product_img.splice(index, 1)
  }
  const validate = () => {
    for (const imgUrl of props.form.product_info?.product_img || []) {
      if (imgUrl) {
        if (!/^https?:\/\//.test(imgUrl)) {
          return false
        }
        if (/[\u4e00-\u9fa5]/.test(imgUrl)) {
          return false
        }
      }
    }
    return true
  }
  watch(() => props.media_type, (val) => {
    if (!props.form?.id) {
      if (val == 1) {
        delete props.form.product_info.shop_name
        delete props.form.product_info.product_id
        delete props.form.product_info.product_price
        delete props.form.product_info.product_category
        delete props.form.product_info.product_img
        props.form.product_info.product_name = ''
        props.form.order_amt = undefined
        props.form.shop_type = 2
      } else if (val == 3) {
        props.form.product_info = {
          product_img: [''],
          product_name: '',
        }
        props.form.order_amt = undefined
        props.form.shop_type = 1
      }
    }
  })
  defineExpose({
    validate
  })
</script>
<style lang="scss" scoped>
</style>