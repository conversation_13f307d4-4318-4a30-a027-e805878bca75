<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :rules="rulesData" :labelCol="{ style: { width: '100px' } }">
      <a-form-item label="产品名称" name="name">
        <a-input
          :disabled="disabled"
          v-model:value.trim="state.form.name"
          maxlength="30"
          showCount
          placeholder="请输入产品名称"
        />
      </a-form-item>
      <a-form-item label="媒体类型" name="media_type">
        <a-select
          :disabled="disabled"
          v-model:value="state.form.media_type"
          class="w-150px"
          :options="mediaType"
          placeholder="请选择媒体类型"
        >
        </a-select>
      </a-form-item>
      <a-form-item name="cost">
        <template #label>
          <div class="flex-align">
            <span>成本限制</span>
            <a-tooltip>
              <template #title> 当关联这个产品的广告链接对应计划实际成本高于设置成本时，系统标红提示 </template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-input-number
          v-model:value="state.form.cost"
          :min="0.01"
          :precision="2"
          placeholder="请输入成本限制"
          class="w-100%"
        >
        </a-input-number>
      </a-form-item>
      <EbusinessSetup ref="EbusinessSetupRef" :media_type="state.form.media_type" :form="state.form"></EbusinessSetup>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { reactive, ref, onMounted, computed } from 'vue'
  import { message } from 'ant-design-vue'
  import { update } from '../index.api'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { mediaType } from '@/utils'
  import EbusinessSetup from './EbusinessSetup.vue'
  const props = defineProps(['datas', 'type'])
  const emits = defineEmits(['event'])
  const ruleForm = ref(null)
  const disabled = computed(() => {
    return props.type === 'edit'
  })
  const state = reactive({
    loading: false,
    form: {
      ...props.datas,
      product_info: props.datas?.product_info || {}
    }
  })
  if (!state.form.product_info.product_img) {
    state.form.product_info.product_img = ['']
  }
  const rulesData = ref({
    name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    cost: [{ required: true, message: '请输入成本限制', trigger: 'blur' }],
    media_type: [{ required: true, message: '请选择媒体类型', trigger: 'change' }]
  })
  onMounted(() => {})

  const close = () => {
    emits('event', { type: 'close' })
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const EbusinessSetupRef = ref(null)
  const edit = async () => {
    try {
      state.loading = true
      if (state.form.product_info?.product_img) {
        state.form.product_info.product_img = state.form.product_info.product_img.filter(item => item !== '');
      }
      const params = { id: props.datas.id, ...JSON.parse(JSON.stringify(state.form)) }
      if (params.product_info.product_price) {
        params.product_info.product_price = Number((params.product_info.product_price * 100).toFixed(0))
      } else {
        delete params.product_info.product_price
      }
      if (!EbusinessSetupRef.value?.validate()) return
      await update(params)
      emits('event', { type: 'submit', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    margin-top: 30px;
    text-align: end;
  }
  :deep(.ant-descriptions-row) {
    display: flex;
    flex-direction: column;
    .ant-descriptions-item-label {
      max-width: 200px;
    }
  }
</style>
