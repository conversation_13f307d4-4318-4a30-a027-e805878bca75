<template>
  <div>
    <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
    <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange" class="mt16px">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'land_url'">
          <a-tooltip placement="topLeft">
            <template #title>{{ scope.record.domain }}{{ scope.record.ad_links.h5_url }}</template>
            <div class="url_overflow">{{ scope.record.domain }}{{ scope.record.ad_links.h5_url }}</div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.key === 'detection_url'">
          <a-tooltip placement="topLeft">
            <template #title>{{ scope.record.domain }}{{ scope.record.ad_links.detection_url }}</template>
            <div class="url_overflow">{{ scope.record.domain }}{{ scope.record.ad_links.detection_url }}</div>
          </a-tooltip>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>

<script setup>
  import datas from '../src/DeploymentLink'
  const props = defineProps(['datas'])
  const { pageChange, searchForm, searchConfig, state } = datas(props.datas)
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .url_overflow {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
</style>
