import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getClueList, ad_link_list } from '../index.api'
import { useApp } from '@/hooks'
import dayjs from 'dayjs'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const { useInfo } = useApp()
  const searchConfig = reactive({
    data: [
      {
        field: 'ad_ids',
        type: 'select',
        value: undefined,
        props: {
          options: [],
          mode: 'multiple',
          placeholder: '请选择所属项目'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'is_reply',
        props: {
          placeholder: '请选择处理状态',
          options: [
            { value: 1, label: '已处理' },
            { value: 2, label: '未处理' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'phone',
        value: undefined,
        props: {
          placeholder: '请输入手机号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'ip',
        value: undefined,
        props: {
          placeholder: '请输入IP'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        field: 'admin_ids',
        type: 'admin',
        value: undefined,
        span: 6,
        props: {
          mode: 'multiple',
          placeholder: '请选择负责人'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'created_at',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [{ created_at: '111' }, { created_at: '111' }],
    columns: [
      {
        title: '所属项目',
        dataIndex: 'ad_name',
        key: 'ad_name',
        slot: true,
        width: 200
      },
      {
        title: 'IP',
        dataIndex: 'ip',
        key: 'ip',
        slot: true,
        width: 150
      },
      {
        title: '地区',
        dataIndex: 'area',
        key: 'area',
        slot: true,
        width: 100
      },
      {
        title: '姓名',
        dataIndex: 'user_name',
        key: 'user_name',
        slot: true,
        width: 100
      },
      {
        title: '手机号码',
        dataIndex: 'phone',
        key: 'phone',
        slot: true,
        width: 150
      },
      {
        title: '提交次数',
        dataIndex: 'submit_num',
        key: 'submit_num',
        slot: true,
        width: 100
      },
      {
        title: '提交时间',
        dataIndex: 'created_at',
        key: 'created_at',
        slot: true,
        width: 180
      },
      {
        title: '处理时间',
        dataIndex: 'reply_at',
        key: 'reply_at',
        slot: true,
        width: 180
      },
      {
        title: '状态',
        dataIndex: 'is_reply',
        key: 'is_reply',
        slot: true,
        width: 100
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        slot: true,
        width: 180
      },
      {
        title: '画像标签',
        dataIndex: 'user_tag',
        key: 'user_tag',
        slot: true,
        width: 100
      },
      {
        title: '负责人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        slot: true,
        width: 100
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 200,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20,
      created_at: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')].join('_')
    },
    dialog: {
      visible: false,
      title: '',
      width: 500,
      type: '',
      item: null
    }
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#FF4D4F',
        text: '未处理'
      },
      1: {
        color: '#52C41A',
        text: '已处理'
      }
    }
    return (status as any)[val]
  }
  const tagType = (val: string | number) => {
    let status = {
      2: {
        color: '#FF4D4F',
        text: '未加粉'
      },
      1: {
        color: '#52C41A',
        text: '已加粉'
      }
    }
    return (status as any)[val]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getClueList(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      ad_ids: v.formData.ad_ids ? v.formData.ad_ids.join(',') : undefined,
      phone: v.formData.phone ? String(v.formData.phone) : undefined,
      ip: v.formData.ip ? String(v.formData.ip) : undefined,
      created_at: v.formData.created_at ? v.formData.created_at.join('_') : undefined,
      admin_ids: v.formData.admin_ids ? v.formData.admin_ids.join(',') : undefined
    }
    if (!v.status) {
      v.formData.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      data.params.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')].join('_')
    }
    data.params.page = 1
    getList()
  }
  // 选择所属项目：获取投放链接列表
  const get_ad_link_list = async () => {
    try {
      let res = await ad_link_list({ page: 1, page_size: 9999, company_id: useInfo.value?.company_id })
      const _options = (res.data?.list || []).map((v: any) => {
        return {
          value: v.id,
          label: v.name
        }
      })
      searchConfig.data.forEach((v) => {
        if (v.field === 'ad_ids') {
          v.props.options = [...v.props.options, ..._options]
        }
      })
    } catch (error) {}
  }
  const onShowDialog = (type, item) => {
    data.dialog.item = item
    data.dialog.type = type
    data.dialog.visible = true
    switch (type) {
      case 'handle':
        data.dialog.title = '处理线索'
        break
      case 'detail':
        data.dialog.title = '线索详情'
        break
    }
  }
  const onEvent = (e) => {
    data.dialog.visible = false
    if (e.type !== 'close') {
      getList()
    }
  }

  return {
    pageChange,
    searchForm,
    searchConfig,
    data,
    statusType,
    getList,
    get_ad_link_list,
    onShowDialog,
    onEvent,
    tagType
  }
}
