<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :labelCol="{ style: { width: '50px' } }" v-if="type == 'handle'">
      <a-form-item
        label="状态"
        name="is_reply"
        :rules="[
          {
            required: true,
            message: '请选择状态',
            trigger: ['change', 'blur']
          }
        ]"
      >
        <a-radio-group v-model:value="state.form.is_reply" button-style="solid">
          <a-radio-button value="1">已处理</a-radio-button>
          <a-radio-button value="2">未处理</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remarks">
        <a-textarea v-model:value="state.form.remarks" :rows="4" placeholder="请输入备注" :maxlength="100" showCount
      /></a-form-item>
    </a-form>
    <a-descriptions v-else :column="6">
      <a-descriptions-item v-for="(item, index) in state.detailData">
        <template #label>{{ item.title }}</template>
        <div>{{ item.form_value }}</div>
      </a-descriptions-item>
    </a-descriptions>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)" v-if="type == 'handle'"
        >确定</a-button
      >
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import { isArray } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import { handleClues } from '../index.api'
  const props = defineProps(['datas', 'type'])
  const emits = defineEmits(['event'])
  const ruleForm = ref(null)

  const state = reactive({
    loading: false,
    form: {
      is_reply: undefined,
      remarks: undefined
    },
    detailData: null
  })
  onMounted(() => {
    if (props.datas?.json_data) {
      let newArr = JSON.parse(props.datas.json_data)
      // newArr.forEach((item: any) => {
      //   if (item.key == 'radio') {
      //     item.form_value = item.subs.find((sub: any) => sub.kId == item.form_value)?.title || ''
      //   } else if (item.key == 'checkout') {
      //     item.form_value = item.subs
      //       .filter((sub: any) => item.form_value.includes(sub.kId))
      //       .map((sub: any) => sub.title)
      //       .join(',')
      //   } else if (item.key == 'gender"') {
      //     item.form_value = { 1: '男', 2: '女' }[item.form_value] || ''
      //   }
      // })
      state.detailData = newArr
      console.log(state.detailData, 'state.detailData')
    }
  })

  const close = () => {
    emits('event', { type: 'close' })
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const edit = async () => {
    try {
      state.loading = true
      console.log(state.form, '~~~~~~~~~~~~~~', props.datas)
      const params = {
        is_reply: state.form.is_reply ? Number(state.form.is_reply) : undefined
      }
      await handleClues({ ...state.form, ...params, id: props.datas.id })
      emits('event', { type: 'submit', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    margin-top: 20px;
    text-align: end;
  }
  :deep(.ant-descriptions-row) {
    display: flex;
    flex-direction: column;
    .ant-descriptions-item-label {
      max-width: 200px;
    }
  }
</style>
