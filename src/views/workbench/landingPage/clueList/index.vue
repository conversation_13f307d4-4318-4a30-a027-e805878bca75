<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>线索列表</div>
      </template>

      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'ad_name'">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.ad_name }}</template>
                  <div class="text_overflow">{{ scope.record.ad_name }}</div>
                </a-tooltip>
              </template>

              <template v-if="scope.column.key === 'remarks'">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.remarks }}</template>
                  <div class="text_overflow_row1">{{ scope.record.remarks || '--' }}</div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'area'">
                {{ scope.record.area || '--' }}
              </template>
              <template v-if="scope.column.key === 'user_name'">
                <a-tooltip>
                  <template #title>{{ scope.record.user_name }}</template>
                  <div class="text_overflow">{{ scope.record.user_name || '--' }}</div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'phone'">
                {{ scope.record.phone || '--' }}
              </template>
              <template v-if="scope.column.key === 'reply_at'">
                {{ scope.record.reply_at || '--' }}
              </template>
              <template v-if="scope.column.key === 'is_reply'">
                <div class="flex_align_center" style="min-width: 100px">
                  <div
                    :class="clueIsReplyEnum[scope.record.is_reply].className"
                    v-if="clueIsReplyEnum[scope.record.is_reply]"
                  >
                    {{ clueIsReplyEnum[scope.record.is_reply].text }}
                  </div>
                  <div v-else>--</div>
                </div>
              </template>
              <template v-if="scope.column.key === 'user_tag'">
                <div class="flex_align_center" style="min-width: 100px">
                  <div
                    :class="clueUserTagEnum[scope.record.user_tag].className"
                    v-if="clueUserTagEnum[scope.record.user_tag]"
                  >
                    {{ clueUserTagEnum[scope.record.user_tag].text }}
                  </div>
                  <div v-else>--</div>
                </div>
              </template>
              <template v-if="scope.column.key === 'action'">
                <a-button
                  v-auth="['clueHandle']"
                  class="pa-0 m-r-5px"
                  type="link"
                  @click="onShowDialog('handle', scope.record)"
                  :disabled="pointData.balance <= 0"
                  >处理</a-button
                >
                <a-button class="pa-0 m-r-5px" type="link" @click="onShowDialog('detail', scope.record)"
                  >线索详情</a-button
                >
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      destroyOnClose
      :footer="null"
    >
      <ModalHandle @event="onEvent" :datas="data.dialog.item" :type="data.dialog.type" />
    </a-modal>
  </div>
</template>

<script setup>
  import datas from './src/data'
  import ModalHandle from './components/modalHandle.vue'
  import { clueIsReplyEnum, clueUserTagEnum } from '@/utils'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  const {
    pageChange,
    searchForm,
    searchConfig,
    data,
    statusType,
    getList,
    get_ad_link_list,
    onShowDialog,
    onEvent,
    tagType
  } = datas()
  getList()
  get_ad_link_list()
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
</style>
