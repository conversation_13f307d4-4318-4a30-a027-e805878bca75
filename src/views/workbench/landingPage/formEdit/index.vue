<template>
  <div class="list-container">
    <BreadcrumbCom
      :data="[{ title: '表单管理', pathname: 'FormList' }, { title: state.form_id ? '修改表单' : '添加表单' }]"
    />
    <div :class="['flex', isMobile ? 'flex-col mobile-wrapper' : '']">
      <a-form
        ref="ruleForm"
        :model="state.addForm"
        :rules="state.rules"
        class="h5d-addForm flex-1 mr-20px"
        label-width="0px"
      >
        <div class="createdCommon">
          <div class="createCommon-section">
            <div class="common_section-title">表单内容</div>
            <div class="fieldList">
              <div class="fieldList-header vtf">
                <div class="fieldList_field-drag flex-align" />
                <div class="fieldList_field-type"><span class="required">*</span>字段类型</div>
                <div class="fieldList_field-title"><span class="required">*</span>字段标题</div>
                <div class="fieldList_field-bt flex-align">
                  必填
                  <a-popover placement="top" trigger="hover">
                    <template #content>开启后，用户必须填写该字段</template>
                    <QuestionCircleOutlined />
                  </a-popover>
                </div>
                <div class="fieldList_field-delete flex-align" />
              </div>
              <div v-if="state.jsonData.list.length" style="overflow: auto">
                <draggable :list="state.jsonData.list" itemKey="value" handle=".icon-drag" @end="onEnd">
                  <template #item="{ element: v, index }">
                    <div :key="index" class="fieldList_field-list-item vtf">
                      <div class="fieldList_field-drag flex-align">
                        <HolderOutlined class="el-icon-s-grid icon-drag" />
                      </div>
                      <div class="fieldList_field-type">
                        <a-cascader
                          ref="demoCascader"
                          v-model:value="v.types"
                          :options="state.optionsList"
                          placeholder="请选择字段类型"
                          :allowClear="false"
                          @change="(val, selectedOptions) => handleChange(val, index, selectedOptions)"
                        >
                          <template #tagRender="data">
                            <a-popover placement="right">
                              <template #content>
                                <img v-if="data.tipImg" :src="data.tipImg" :class="['claTipImg', data.key]" />
                              </template>
                              <div>{{ data.label }}</div>
                            </a-popover>
                          </template>
                        </a-cascader>
                      </div>
                      <div class="fieldList_field-title">
                        <a-input
                          v-model:value.trim="v.title"
                          class="field-input"
                          :maxlength="10"
                          placeholder="请输入标题"
                          @keydown.space.prevent
                          @blur="(e) => handleInputBlur(e, v)"
                        />
                        <div v-if="v.isErr" class="form-help">请输入字段标题</div>
                      </div>
                      <div class="fieldList_field-bt flex-align ptb5"><a-switch v-model:checked="v.must" /></div>
                      <div class="fieldList_field-delete flex-align">
                        <DeleteOutlined class="el-icon-delete" @click="handleDel(index, v)" />
                      </div>
                      <div
                        v-if="['inputNumber', 'select', 'radio', 'checkout'].includes(v.key)"
                        class="fieldList_field-other"
                        @click="handleOpen(v)"
                      >
                        <div class="other-operat">
                          <a-button type="link" :underline="false">
                            {{ v.isOpen ? '展开' : '收起' }}
                            <i :class="v.isOpen ? 'el-icon-caret-top' : 'el-icon-caret-bottom'" />
                          </a-button>
                        </div>
                        <div v-if="v.isOpen" class="other-hide">
                          <div class="flex-align">
                            <label for="" class="form-item_label right">{{ keyFilters(v.key) }}</label>
                            <span v-if="!v.isOptionsErr" class="overflow-omit">{{ v.values }}</span>
                            <div v-if="v.isOptionsErr" class="form-help" style="margin-top: 0">选项不能为空</div>
                          </div>
                        </div>
                        <div v-else class="other-more" @click.stop>
                          <div v-if="v.key == 'inputNumber'" class="other-form-container">
                            <div class="flex-align">
                              <label for="" class="form-item_label right require" style="width: 66px">默认值</label>
                              <a-input-number
                                v-model:value="v.values"
                                :min="0"
                                placeholder="请输入数字"
                                :controls="false"
                                class="common-btnText"
                              />
                            </div>
                          </div>
                          <div v-if="['select', 'radio', 'checkout'].includes(v.key)" class="other-option-list">
                            <draggable :list="v.subs" itemKey="value" handle=".icon-drag" @end="onEnd">
                              {{ v.subs }}
                              <template #item="{ element: item, index: i }">
                                <div :key="i" class="addListInput_option-item">
                                  <div class="addListInput_option-item-drag flex-align">
                                    <HolderOutlined class="el-icon-s-grid icon-drag" />
                                  </div>
                                  <div class="addListInput_option-item-name">
                                    <a-input
                                      v-model:value.trim="item.title"
                                      class="field-input"
                                      placeholder="请输入标题"
                                      :maxlength="20"
                                      @keydown.space.prevent
                                      @blur="(e) => handleSubInputBlur(e, item)"
                                    />
                                    <div v-if="item.isErr" class="form-help">请输入字段标题</div>
                                  </div>
                                  <div class="addListInput_option-item-default">
                                    <a-checkbox v-model:checked="item.checked" @click="handleChangeSub(item, index, i)">
                                      默认选中
                                    </a-checkbox>
                                    <!-- <label
                                      class="ant-checkbox-wrapper"
                                      :class="{ 'ant-checkbox-wrapper-checked': item.checked }"
                                      @click="handleChangeSub(item, index, i)"
                                    >
                                      <span class="ant-checkbox" :class="{ 'ant-checkbox-checked': item.checked }">
                                        <span class="ant-checkbox-inner" />
                                      </span>
                                      <span>默认选中</span>
                                    </label> -->
                                  </div>
                                  <div class="fieldList_field-delete flex-align">
                                    <DeleteOutlined
                                      class="el-icon-delete"
                                      :class="{ disabled: v.subs.length <= 2 }"
                                      @click="handleSubDel(index, i)"
                                    />
                                  </div>
                                </div>
                              </template>
                            </draggable>
                            <div class="addbtn-wrap">
                              <a-button :icon="h(PlusOutlined)" plain @click="handleAdd(2, v, index)"
                                >添加选项({{ v.subs.length }}/20)</a-button
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </draggable>
              </div>
              <div v-else class="form-container-help">请添加表单字段</div>
              <div class="addbtn-wrap mt-22px">
                <a-button type="dashed" :icon="h(PlusOutlined)" size="large" class="w-full" @click="handleAdd(1)">
                  添加表单字段({{ state.jsonData.list.length }}/20)
                </a-button>
              </div>
            </div>
            <a-form-item label="" name="btnText" class="btntexts">
              <div class="flex-align">
                <label for="name" class="form-item_label right require" style="width: 104px">提交按钮文案</label>
                <a-input
                  v-model:value.trim="state.addForm.btnText"
                  placeholder="仅用于在表单列表中展示，不对外显示"
                  class="common-btnText h-32px"
                  :maxlength="10"
                  @keydown.space.prevent
                />
              </div>
            </a-form-item>
          </div>
          <div class="createCommon-section mb-130px">
            <div class="common_section-title">表单通用设置</div>
            <a-form-item label="" name="name" class="names">
              <div class="flex-align">
                <label for="name" class="form-item_label right require" style="width: 76px">表单名称</label>
                <a-input
                  v-model:value.trim="state.addForm.name"
                  placeholder="仅用于在表单列表中展示，不对外显示"
                  class="common-name"
                  :maxlength="20"
                  @keydown.space.prevent
                />
              </div>
            </a-form-item>
          </div>
        </div>
        <div class="createCommon_section-footer" v-if="state.showFooter">
          <div class="btn-group flex-center">
            <a-button class="btn cancel" @click="onCancel">取消</a-button>
            <a-button type="primary" :loading="state.loading" class="btn comfrim" @click="submitForm(ruleForm)">
              {{ state.form_id && state.addForm.is_use != 1 ? '确认修改' : '完成创建' }}
            </a-button>
          </div>
        </div>
      </a-form>
      <!-- 预览区 -->
      <Preview :list="state.jsonData.list" :btn-text="state.addForm.btnText" />
    </div>
  </div>
</template>

<script setup>
  import { h, ref, reactive, onMounted, nextTick, watch, computed } from 'vue'
  import draggable from 'vuedraggable'
  import {
    DeleteOutlined,
    ExclamationCircleOutlined,
    QuestionCircleOutlined,
    HolderOutlined,
    PlusOutlined
  } from '@ant-design/icons-vue'
  import { message, Modal } from 'ant-design-vue'
  import Preview from './components/preview.vue'
  import { setFormInfo, setAddForm } from './index.api.ts'
  import { optionsl } from './src/data.js'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  import { useRoute, useRouter } from 'vue-router'
  const route = useRoute()
  const router = useRouter()

  const demoCascader = ref(null)
  const ruleForm = ref(null)
  const state = reactive({
    form_id: null,
    loading: false,
    showFooter: false,
    jsonData: {
      list: [
        {
          key: 'username',
          types: ['1', '1-1'],
          title: '您的姓名',
          must: true,
          isErr: false,
          isOpen: false,
          form_value: null,
          subs: []
        },
        {
          key: 'phone',
          types: ['1', '1-2'],
          title: '您的手机号',
          must: true,
          isErr: false,
          isOpen: false,
          form_value: null,
          subs: []
        }
      ],
      btnText: null
    },
    addForm: {
      name: null,
      btnText: '立即提交'
    },
    optionsList: null,
    rules: {
      name: [{ required: true, message: '请输入表单名称', trigger: 'blur' }],
      btnText: [{ required: true, message: '请输入按钮文案', trigger: 'blur' }]
    }
  })

  onMounted(() => {
    state.optionsList = JSON.parse(JSON.stringify(optionsl))
    state.form_id = route.query.id || null
    if (state.form_id) fetchDataHtmlInfo()
    const timer = setTimeout(() => {
      state.showFooter = true
      clearTimeout(timer)
    }, 400)
  })

  const fetchDataHtmlInfo = async () => {
    try {
      const resp = await setFormInfo({ id: state.form_id })
      const { data } = resp
      const jsn = JSON.parse(data.content) || {}
      state.addForm = {
        id: data.is_use == 1 ? null : data.id,
        name: data.is_use == 1 ? null : data.name,
        is_use: data.is_use,
        btnText: jsn.btnText
      }
      if (data.is_use == 1) tips()
      state.jsonData.list = Array.isArray(jsn?.list) ? jsn.list : []
      const uobj = state.jsonData.list.find((v) => v.key == 'username')
      if (!uobj) {
        state.optionsList[0].children[0].disabled = false
      }
      const pobj = state.jsonData.list.find((v) => v.key == 'phone')
      if (!pobj) {
        state.optionsList[0].children[1].disabled = false
      }
    } catch (error) {
      console.log(error)
      // message.error(error.message)
    }
  }

  const keyFilters = (type) => {
    const status = {
      inputNumber: '默认值',
      select: '选项设置',
      radio: '选项设置',
      checkout: '选项设置'
    }
    return status[type]
  }

  const onCancel = () => {
    router.back()
  }

  const handleAdd = (type, item, index) => {
    // 添加表单字段
    if (type === 1) {
      if (state.jsonData.list.length > 19) return
      state.jsonData.list.push({
        key: null, // 字段键值
        types: [], // 字段类型
        title: null, // 字段标题
        must: true, // 是否必填
        isErr: false, // 错误提示
        isOpen: false, // 是否展开
        form_value: null,
        subs: []
      })
    }
    if (type === 2) {
      if (item.subs.length > 19) return
      item.subs.push({
        kId: item.subs.length + 1,
        key: item.key,
        title: null,
        checked: false,
        isErr: false
      })
    }
  }
  const handleInputBlur = (e, item) => {
    if (item.types.length) item.isErr = !item.title
  }
  const handleSubInputBlur = (e, item) => {
    item.isErr = !item.title
  }
  const handleDel = (index, item) => {
    // 删除表单字段
    try {
      state.jsonData.list.splice(index, 1)
      const obj = state.optionsList[0].children.find((v) => v.key === item.key) || {}
      if (obj.key) obj.disabled = false
    } catch (error) {
      console.log(error)
    }
  }
  const handleSubDel = (index, subIndex) => {
    if (state.jsonData.list[index].subs.length <= 2) return
    state.jsonData.list[index].subs.splice(subIndex, 1)
  }
  const handleOpen = (item) => {
    item.isOpen = !item.isOpen
    if (['select', 'radio', 'checkout'].includes(item.key)) {
      const values = item.subs.map((v) => v.title)
      if (values && values.length) {
        const err = values.every((v) => v)
        item.isOptionsErr = !err
        item.values = values.join()
      }
    }
  }
  const handleChange = (val, index, selectedOptions) => {
    if (demoCascader.value) {
      const obj = selectedOptions.find((v) => v.value == val[val.length - 1])
      // const key = obj[0].data.key
      const key = obj.key
      const lst = state.jsonData.list[index]
      lst.key = key
      lst.subs = []
      lst.title = ['cityOne', 'cityTwo'].includes(key) ? '您的地址' : '您的' + obj.label
      if (key === 'inputNumber') lst.values = 0
      if (['select', 'radio', 'checkout'].includes(key)) {
        lst.subs = [
          { kId: 1, key: lst.key, title: null, checked: false, isErr: false },
          { kId: 2, key: lst.key, title: null, checked: false, isErr: false }
        ]
      }
      if (['checkout', 'cityOne', 'cityTwo'].includes(key)) lst.form_value = []
      if (['username', 'phone'].includes(key)) obj.disabled = true
      const uobj = state.jsonData.list.find((v) => v.key === 'username')
      if (!uobj) {
        state.optionsList[0].children[0].disabled = false
        return
      }
      const pobj = state.jsonData.list.find((v) => v.key === 'phone')
      if (!pobj) {
        state.optionsList[0].children[1].disabled = false
        return
      }
    }
  }
  const handleChangeSub = (item, index, i) => {
    state.jsonData.list[index].subs.forEach((v, si) => {
      if (v.kId === item.kId && item.checked) {
        v.checked = false
      } else {
        v.checked = false
        if (v.kId === item.kId) v.checked = true
      }
    })
  }
  const submitForm = (formName) => {
    const flag = state.jsonData.list.every((v) => {
      if (v.subs instanceof Array && v.subs.length) {
        if (['radio', 'select', 'checkout'].includes(v.key)) {
          const formVal = v.subs.find((item) => item.checked)
          v.form_value = formVal ? (v.key === 'checkout' ? [formVal.kId] : formVal.kId) : null
        }
        const values = v.subs.every((v) => v.title)
        if (!values) return false
      }
      if (v.key === 'inputNumber') v.form_value = v.values
      return v.isErr === false && v.key && v.title
    })
    if (!flag) {
      message.error('请完善表单内容')
      return
    }
    let check = false
    state.jsonData.list.filter((ele) => {
      if (ele.must) {
        check = true
      }
    })
    if (!check) {
      message.error('请完善表单内容,保证最少一项必填项')
      return
    }
    formName.validate().then((valid) => {
      if (!valid) return false
      state.loading = true
      state.jsonData.btnText = state.addForm.btnText
      state.addForm.jsonData = state.jsonData
      // state.form_id && state.addForm.is_use != 1 ? update() : save()
      update()
    })
  }
  const update = async () => {
    try {
      const params = { content: JSON.stringify(state.addForm.jsonData), name: state.addForm.name }
      if (state.form_id) {
        params.id = Number(state.form_id)
      }
      await setAddForm(params)
      message.success(state.form_id ? '修改表单成功' : '添加表单成功')
      onCancel()
      state.loading = false
    } catch (error) {
      state.loading = false
    }
  }
  const tips = () => {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '该表单正在使用中，如需修改将会新建表单并需要在对应落地页中手动替换，确认修改吗？'
    })
  }
  const setData = (dataTransfer) => {
    // to avoid Firefox bug
    // Detail see : https://github.com/RubaXa/Sortable/issues/1012
    dataTransfer.setData('Text', '')
  }

  const onEnd = (event) => {
    console.log(event)
  }
</script>

<style lang="scss" scoped>
  .h5d-addForm {
    border-radius: 4px;
    overflow: hidden;
    .createdCommon {
      max-height: calc(100vh - 160px);
      border-radius: 4px;
      overflow-y: auto;
    }
    .createCommon-section {
      position: relative;
      padding: 16px 24px;
      margin-top: 16px;
      border-radius: 4px;
      background-color: #fff;
      &:first-child {
        margin-top: 0;
      }
      .common_section-title {
        margin-bottom: 20px;
        font-weight: 700;
        font-size: 16px;
        color: #333;
      }
      .common-name {
        width: 340px;
        height: 32px;
        line-height: normal;
      }
      .common-btnText {
        width: 280px;
        line-height: normal;
      }
      ::v-deep {
        .a-cascader {
          width: 100%;
        }
        .names .ant-form-item-explain-error {
          position: absolute;
          left: 76px;
        }
        .btntexts .ant-form-item-explain-error {
          position: absolute;
          left: 104px;
        }
        .el-input-number .el-input__inner {
          text-align: left;
        }
        .addbtn-wrap {
          .el-button {
            width: 100%;
            height: 40px;
            margin-top: 22px;
            border-radius: 4px;
            border: 1px dashed #338aff;
            color: #338aff;
            padding: 10px 20px;
            font-size: 14px;
            &:hover {
              background: transparent;
              color: #409eff;
            }
            .el-icon-plus {
              font-size: 16px;
              font-weight: 600;
            }
          }
        }
      }
    }
    .fieldList {
      margin-bottom: 24px;
      .fieldList-header {
        height: 36px;
        padding-top: 4px;
        margin-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
        color: #333;
        font-size: 14px;
        .fieldList_field-type,
        .fieldList_field-bt {
          line-height: 21px;
        }
      }
      .fieldList_field-drag {
        width: 25px;
        height: 30px;
        color: #999;
        cursor: move;
        .el-icon-s-grid {
          color: #999;
        }
      }
      .addListInput_option-item-drag {
        height: 30px;
        margin-right: 12px;
        cursor: move;
        .el-icon-s-grid {
          color: #999;
        }
      }
      .fieldList_field-type,
      .fieldList_field-title {
        flex: 1 1;
        .required {
          display: inline-block;
          margin-right: 6px;
          border-radius: 2px;
          vertical-align: middle;
          color: #f45778;
        }
      }
      .fieldList_field-type {
        margin-right: 25px;
      }
      .fieldList_field-title {
        margin-right: 80px;
      }
      .fieldList_field-bt {
        justify-content: space-between;
        width: 50px;
        margin-right: 25px;
        // .el-icon-warning-outline { color: rgb(235, 235, 235); }
      }
      .fieldList_field-delete {
        width: 20px;
        height: 30px;
        .el-icon-delete {
          color: #338aff;
          cursor: pointer;
          &.disabled {
            color: #c1c1c1;
            cursor: no-drop;
          }
        }
      }
      .fieldList_field-list-item {
        padding: 8px 0;
        // &.vtf { align-items: center; }
      }
      .fieldList_field-other {
        position: relative;
        width: 100%;
        padding: 16px 20px 16px 26px;
        margin-top: 8px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 22px;
        background: #fafafa;
        .other-operat {
          position: absolute;
          z-index: 10;
          top: 16px;
          right: 16px;
          cursor: pointer;
          user-select: none;
        }
        .other-more {
          display: inline-block;
        }
        .other-form-container {
          display: flex;
          box-sizing: border-box;
          color: #333;
          vertical-align: top;
          position: relative;
          font-size: 14px;
          line-height: 22px;
        }
      }
      .addListInput_option-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        background: #fafafa;
      }
      .addListInput_option-item-name {
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        color: #333;
        vertical-align: top;
        position: relative;
        font-size: 14px;
        line-height: 22px;
        margin-right: 16px;
      }
      .addListInput_option-item-default {
        margin-right: 20px;
        line-height: 30px;
        height: 30px;
      }
      .vtf {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        background: #fff;
      }
    }

    .form-item_label {
      position: relative;
      vertical-align: middle;
      direction: ltr;
      font-size: 14px;
      line-height: 22px;
      box-sizing: border-box;
      width: 130px;
      word-wrap: break-word;
      padding: 6px 20px 6px 0;
      font-weight: normal;
      &.require::before {
        top: 8px;
        left: -12px;
      }
      &.right {
        text-align: left;
      }
      &::before {
        content: '*';
        display: inline-block;
        position: absolute;
        color: #f65656;
        // width: 4px;
        // height: 4px;
        margin-right: 18px;
        // margin-right: unset;
        // top: 50%;
        // transform: translateY(-50%);
      }
    }
    .other-hide {
      .form-item_label {
        width: 66px;
        color: rgba(0, 0, 0, 0.65);
        padding-right: 0;
        &::after {
          background: transparent;
        }
      }
    }
    .createCommon_section-footer {
      position: fixed;
      bottom: 0;
      width: calc(100% - var(--sidebar-width));
      padding: 16px 24px;
      margin-top: 16px;
      border-radius: 4px;
      background-color: #fff;
      .btn-group {
        width: calc(100% - 270px);
      }
      .comfrim {
        // background: #004fee;
        // border-color: #004fee;
        margin-left: 25px;
      }
      .btn {
        padding: 0px 32px;
      }
      // .cancel {
      //   border-color: #004fee;
      //   color: #004fee;
      // }
    }
    .ptb5 {
      padding: 5px 0;
    }
    .overflow-omit {
      max-width: 440px;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .form-help,
    .form-container-help {
      margin-top: 4px;
      color: #f65656;
      font-size: 12px;
      line-height: 20px;
    }
  }
  .mobile-wrapper {
    .createdCommon {
      max-height: max-content;
    }
    .createCommon-section {
      margin-bottom: 30px;
    }
    .fieldList {
      .fieldList_field-title {
        margin-right: 0;
      }
    }
    .createCommon_section-footer {
      width: 100%;
      z-index: 9;
      .btn-group {
        width: 100%;
      }
    }
    .form-preview {
      margin-bottom: 100px;
    }
  }
</style>

<style lang="scss">
  .bitianp {
    padding: 9px 16px 7px;
  }
  .claTipImg {
    width: 280px;
    height: 64px;
    &.textarea {
      height: 130px;
    }
  }
</style>
