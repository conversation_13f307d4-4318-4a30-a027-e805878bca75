/**
 * 自定义表单——字段类型
 */
import { getConfig } from '@/utils'
export const optionsl = [
  {
    value: '1',
    label: '预设字段',
    children: [{
      value: '1-1',
      label: '姓名',
      key: 'username',
      disabled: true, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/username.png'
      // children: []
    }, {
      value: '1-2',
      label: '手机号',
      key: 'phone',
      disabled: true, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/phone.png'
      // children: []
    }, {
      value: '1-3',
      label: '邮箱',
      key: 'email',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/email.png'
      // children: []
    }, {
      value: '1-4',
      label: '城市',
      key: 'city',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: '',
      children: [{
        value: '1-4-1',
        label: '省-市',
        key: 'cityOne',
        disabled: false, // 选项中只能有一个该类型 true 只能有一个
        tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/cityOne.png'
        // children: []
      }, {
        value: '1-4-2',
        label: '省-市-区',
        key: 'cityTwo',
        disabled: false, // 选项中只能有一个该类型 true 只能有一个
        tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/cityTwo.png'
        // children: []
      }]
    }, {
      value: '1-5',
      label: '性别',
      key: 'gender',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/gender.png'
      // children: []
    }, {
      value: '1-6',
      label: '日期',
      key: 'date',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/date.png'
      // children: []
    }]
  },
  {
    value: '2',
    label: '自定义字段',
    children: [{
      value: '2-1',
      label: '文本',
      key: 'text',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/text.png'
      // children: []
    }, {
      value: '2-2',
      label: '多文本',
      key: 'textarea',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/textarea.png'
      // children: []
    }, {
      value: '2-3',
      label: '数值',
      key: 'inputNumber',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/inputNumber.png'
      // children: []
    }, {
      value: '2-4',
      label: '下拉',
      key: 'select',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/select.png'
      // children: []
    }, {
      value: '2-5',
      label: '单选',
      key: 'radio',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/radio.png'
      // children: []
    }, {
      value: '2-6',
      label: '多选',
      key: 'checkout',
      disabled: false, // 选项中只能有一个该类型 true 只能有一个
      tipImg: getConfig('NODE_OSS_URL') + '/assets/h5/form/checkout.png'
      // children: []
    }]
  }
]
