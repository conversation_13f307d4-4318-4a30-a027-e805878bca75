<template>
  <div class="form-preview">
    <div class="preview-root">
      <div class="review_time">12:15</div>
      <div class="preview_page">
        <div class="clue-form-wrapper override-css svelte-2xx92">
          <form class="clue-form" style="color: rgb(200, 170, 170)">
            <template v-for="(v, index) in list">
              <div
                :key="index"
                class="input-wrapper layout1 svelte-mmrthh"
                :class="[v.key, { required: v.must }]"
                v-if="v.key"
              >
                <div class="label svelte-mmrthh">{{ v.title }}</div>
                <div class="gender-item-wrapper svelte-mmrthh" v-if="['gender'].includes(v.key)">
                  <a-radio-group v-model="radio">
                    <a-radio :value="1">男</a-radio>
                    <a-radio :value="2">女</a-radio>
                  </a-radio-group>
                </div>
                <div class="input-item-wrapper svelte-mmrthh" v-else>
                  <input
                    class="input-item svelte-mmrthh"
                    type="text"
                    placeholder="请输入"
                    :name="index"
                    autocomplete="off"
                    v-if="['username', 'email', 'date', 'text', 'cityOne', 'cityTwo', 'select'].includes(v.key)"
                  />

                  <input
                    class="input-item svelte-mmrthh"
                    type="number"
                    placeholder="请输入"
                    :name="index"
                    autocomplete="off"
                    v-if="['phone', 'inputNumber'].includes(v.key)"
                  />

                  <textarea
                    class="input-item svelte-mmrthh"
                    placeholder="请输入"
                    :name="index"
                    v-if="['textarea'].includes(v.key)"
                  ></textarea>

                  <a-radio-group v-model="state.radio" v-if="['radio'].includes(v.key)">
                    <a-radio v-for="(t, i) in v.subs" :key="i" :value="t.kId">{{ t.title }}</a-radio>
                  </a-radio-group>

                  <a-checkbox-group v-model="state.checkList" v-if="['checkout'].includes(v.key)">
                    <a-checkbox v-for="(t, i) in v.subs" :key="i" :value="t.kId">{{ t.title }}</a-checkbox>
                  </a-checkbox-group>
                  <!-- <template v-if="['checkout'].includes(v.key)">
                <a-checkbox v-for="(t, i) in v.subs" :key="i" v-model="v.form_value">{{t.title}}</a-checkbox>
              </template> -->

                  <DownOutlined class="el-icon-arrow-down" v-if="['cityOne', 'cityTwo', 'select'].includes(v.key)" />
                  <FieldTimeOutlined class="el-icon-date" v-if="['date'].includes(v.key)" />
                </div>
                <div class="error-msg svelte-mmrthh">错误原因：不能为空</div>
              </div>
            </template>

            <div class="phone_agreement_link_wrapper" v-if="flag">
              <label style="#999"><div class="icon"></div> </label>
              <a href="javascript:void(0);" class="phone_agreement_link" style="color: #1095fe"
                >《个人信息授权与保护声明》</a
              >
            </div>
            <button class="submitbtn">
              <span class="svelte-pgjnfl upf">{{ btnText ? btnText : '立即提交' }}</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, reactive } from 'vue'
  import { DownOutlined, FieldTimeOutlined } from '@ant-design/icons-vue'
  const props = defineProps({
    btnText: {
      type: [String, Number],
      default: null
    },
    list: {
      type: Array,
      default: () => []
    }
  })

  const state = reactive({
    submitText: '立即提交',
    radio: null,
    checkList: []
  })

  const flag = computed(() => {
    if (props.list.length) {
      return props.list.some((v) => v.key == 'phone')
    }
    return props
  })
</script>

<style lang="scss" scoped>
  .form-preview {
    width: 250px;
    .preview-root {
      position: relative;
      width: 250px;
      height: 532px;
      background: url($imgSrc + '/assets/h5/form_preview.png') 50%/100% no-repeat;
      transition: transform;
      &::before {
        z-index: 10;
        top: 0;
      }
      &::after,
      &::before {
        position: absolute;
        display: block;
        width: 100%;
        height: 150px;
        content: '';
      }
    }
    .review_time {
      position: absolute;
      top: 14px;
      left: 10px;
      font-weight: bolder;
      font-size: 12px;
      text-align: center;
      color: #979797;
      transform: scale(0.6);
      pointer-events: none;
    }
    .preview_page {
      position: absolute;
      top: -82.25px;
      left: -55.5px;
      width: 360px;
      height: 727.5px;
      border-bottom-right-radius: 35px;
      border-bottom-left-radius: 35px;
      transform: scale(0.66666667);
      user-select: none;
      overflow-y: auto;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        width: 0px; /*对垂直流动条有效*/
        height: 0px; /*对水平流动条有效*/
      }
    }
    .clue-form-wrapper {
      pointer-events: none;
      &.svelte-2xx92 {
        background-color: #fff;
      }
    }
    .clue-form {
      padding: 12px 24px;
      font-family: 'PingFangSC-Regular';
      font-size: 14px;
      line-height: 22px;
      color: #444444;
      * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
      }
      .input-wrapper {
        margin-top: 22px;
        &:first-child {
          margin-top: 0;
        }
        &.cityOne,
        &.cityTwo,
        &.date,
        &.select {
          position: relative;
          cursor: pointer;
          .el-icon-arrow-down,
          .el-icon-date {
            position: absolute;
            top: 57%;
            right: 0;
            // transform: translateY(-50%);
            color: #dcdcdc;
          }
        }
        &.textarea {
          .input-item.svelte-mmrthh {
            padding: 8px;
            height: 116px;
            border: 1px solid #dcdcdc;
            font-size: inherit;
            line-height: inherit;
            vertical-align: middle;
            resize: none;
          }
        }
      }
      .label {
        color: inherit;
        font-weight: 500;
      }
      .required .label::after {
        content: '*';
        color: #ff4444;
      }
      .input-item.svelte-mmrthh {
        margin-top: 4px;
        -webkit-appearance: none;
        padding: 6px 0 7px 0;
        width: 100%;
        background-color: transparent;
        border: 1px solid #dcdcdc;
        border-width: 0 0 1px 0;
        border-radius: 0;
        font-size: 14px;
        line-height: 22px;
        outline: none;
      }
      .error-msg {
        display: none;
        font-size: 12px;
        line-height: 20px;
        color: #ff4444;
      }
      .gender-item-wrapper {
        margin-top: 8px;
        display: flex;
        flex-wrap: wrap;
        color: rgba(68, 68, 68, 0.8);
      }
    }
    .phone_agreement_link_wrapper {
      margin-top: 22px;
      font-size: 12px;
      line-height: 18px;
      color: #999;
      text-align: left;
      .icon {
        display: inline-block;
        content: '';
        width: 16px;
        height: 16px;
        margin-top: -1px;
        margin-right: 8px;
        line-height: inherit;
        vertical-align: middle;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url($imgSrc + '/assets/h5/sele1.png');
        cursor: pointer;
        &.selected {
          background-image: url($imgSrc + '/assets/h5/sele2.png');
        }
      }
      .phone_agreement_link {
        color: #3b9aff;
        text-decoration: none;
      }
    }
    .submitbtn {
      margin: 24px auto 0;
      padding: 0;
      width: 100%;
      height: 48px;
      color: #fff;
      background-color: #3b9aff;
      border-radius: 28px;
      font-family: PingFangSC-Regular;
      font-weight: bold;
      font-size: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border: none;
      outline: none;
      -webkit-user-select: none;
      user-select: none;
      cursor: pointer;
      -webkit-tap-highlight-color: transparent;
      .svelte-pgjnfl {
        flex: 1 0 auto;
      }
      &.disagree {
        background-color: #e8eced !important;
        color: black !important;
      }
    }
  }
</style>

<style lang="scss">
  .form-preview .clue-form {
    .radio .ant-radio-group,
    .checkout .ant-checkbox-group {
      display: flex;
      flex-direction: column;
      margin-top: 6px;
      .ant-radio-wrapper,
      .ant-checkbox-wrapper {
        margin-bottom: 14px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
</style>
