<template>
  <div>
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:75px;' }">
      <a-form-item label="权重" name="sort" required>
        <a-input-number
          class="w-285px"
          v-model:value="data.form.sort"
          :controls="false"
          :precision="0"
          :max="100"
          @keydown.space.prevent
          placeholder="请输入权重"
        />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { set_sort } from '../index.api'
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['data'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      sort: props.data?.sort || 0
    }
  })
  const close = () => {
    ruleForm.value.clearValidate()
    emit('onEvent', { cmd: 'close' })
  }

  const submitForm = async (formEl) => {
    try {
      data.loading = true
      await formEl.validate()
      let params = {
        sort: data.form.sort,
        id: props.data.id,
        item_id: props.data.item_id
      }
      // try {
      //   await set_sort(params)
      //   message.success('操作成功')
      // } catch (error) {}
      emit('onEvent', { cmd: 'sort', data: params })
    } catch (err) {
      console.log(err)
    } finally {
      data.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
