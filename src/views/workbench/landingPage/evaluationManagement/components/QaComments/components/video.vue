<template>
  <div style="text-align: center">
    <video style="border-radius: 6px" :src="props.video.file_url" controls="controls" />
    <div class="footer_button">
      <a-button @click="close">取消</a-button>
    </div>
  </div>
</template>
<script setup>
  const props = defineProps({
    video: {
      type: Object,
      default: () => {}
    }
  })
  const emit = defineEmits(['event'])
  const close = () => {
    emit('event', { cmd: 'close' })
  }
</script>
<style lang="scss" scoped>
  .footer_button {
    text-align: end;
  }
</style>
