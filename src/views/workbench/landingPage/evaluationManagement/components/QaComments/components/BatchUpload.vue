<template>
  <a-space direction="vertical" class="w-full">
    <div>
      <span class="font-size-12px c-#85878A font-300">支持将该商品在其他平台的{{ pageTypeFilter() }}导入</span>
      <div class="flex justify-between w-100% mt-8px">
        <div
          class="item-radio"
          v-for="(item, index) in platformList"
          :class="{ active: state.currentIndex == index }"
          @click="changePlatform(index)"
        >
          <img :src="item.icon" class="w-16px h-16px" />
          <span class="font-size-14px ml-8px">{{ item.name }}</span>
          <div class="item-active" v-show="state.currentIndex == index">
            <span class="item-trangle"></span>
            <img :src="requireImg('qa/qa-active.png')" class="w-9px img-active" />
          </div>
        </div>
      </div>
      <a-upload
        class="w-100%"
        accept=".csv,.xls,.xlsx"
        :showUploadList="false"
        :customRequest="(e) => unploadFiles(e, 'add')"
      >
        <!-- <a-button type="primary" :loading="state.uploadStatus"> 导入文件 </a-button> -->
        <div class="upload-item flex-y-center flex-x-center">
          <img :src="requireImg('qa/qa-download.png')" class="w-24px" />
          <span class="mt-24px font-size-16px c-#313233"
            >将文件拖到此处，或<a-button type="link" class="font-size-16px p-0">点击上传</a-button></span
          >
          <div class="font-size-14px c-#85878A">支持上传.xls/.xlsx单个文件，且不超过20M的文件</div>
        </div>
      </a-upload>
    </div>
    <div class="file-list mb-8px flex justify-between flex-y-center" v-if="state.file_name">
      <div class="flex">
        <img :src="requireImg('qa/qa-xls.png')" class="w-18px h-20px mr-8px" />
        <span class="item-trangle">{{ state.file.name }}</span>
      </div>
      <span class="c-#FF4D4F btn-del cursor-pointer" @click="delFile">删除</span>
    </div>

    <div class="description bg-#ECF3FF w-100% p-8px border-rd-4px">
      <div class="description-text">
        <img :src="requireImg('qa/qa-tips.png')" class="w-9px img-active" /> 请严格按照模板规则填写导入数据；
        <a-button type="link" class="font-size-12px p-0 h-12px line-height-10px" @click="onDownloadTemplate"
          >下载模板</a-button
        >
      </div>
      <div class="description-text">
        <img :src="requireImg('qa/qa-tips.png')" class="w-9px img-active" />每次只允许导入一个文件；
      </div>
      <div class="description-text">
        <img
          :src="requireImg('qa/qa-tips.png')"
          class="w-9px img-active"
        />导入文件仅支持.xls/.xlsx单个文件，大小不超过20M
      </div>
    </div>
    <div class="footer_button">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">导入</a-button>
    </div>
  </a-space>
</template>
<script setup lang="ts">
  import axios from 'axios'
  import { onMounted, reactive, ref, createVNode } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { importComment, fetchImportCommentCancel } from '@/views/shop/goods/Comment/index.api'
  import { import_question } from '@/views/shop/goods/QandA/index.api'
  import { requireImg } from '@/utils'
  import Cos from '@/utils/cos'
  import { useRouter } from 'vue-router'
  import { LoadingOutlined } from '@ant-design/icons-vue'
  const router = useRouter()

  const props = defineProps({
    pageType: {
      type: String,
      default: 'QA' // 评论管理(comments) / 问答管理(QA)
    },
    item: Object
  })
  const emits = defineEmits(['event'])
  const state = reactive({
    uploadStatus: 'uploading',
    exportTimer: null,
    currentIndex: -1,
    uploadData: {
      data: [],
      error_num: 0,
      success_num: 0,
      url: ''
    },
    file: {},
    file_name: '',
    resData: undefined,
    form: {
      is_bind: 1
    },
    linkData: {
      open: false,
      title: '提示',
      content: '确定进行此操作吗',
      data: null
    },
    dialog: {
      title: '',
      visible: false,
      width: 460,
      type: ''
    },
    initParams: {
      page: 1,
      page_size: 10,
      created_at: undefined
    }
  })
  // 页面filters
  const pageTypeFilter = () => {
    return props.pageType === 'comments' ? '评论' : '问答'
  }
  const platformList = [
    {
      key: 'tbao',
      name: '淘宝',
      icon: requireImg('qa/qa-tbao.png'),
      value: 1
    },
    {
      key: 'jd',
      name: '京东',
      icon: requireImg('qa/qa-jd.png'),
      value: 2
    },
    {
      key: 'pdd',
      name: '拼多多',
      icon: requireImg('qa/qa-pdd.png'),
      value: 3
    },
    {
      key: 'other',
      name: '其他',
      icon: requireImg('qa/qa-other.png'),
      value: 4
    }
  ]
  const changePlatform = (index) => {
    state.currentIndex = index
    state.form.is_bind = platformList[index].value
  }

  // 下载批量发货模板

  const onDownloadTemplate = async () => {
    try {
      const file_url = props.pageType == 'comments' ? '评论导入模版.xlsx' : '问答导入模版.xlsx'
      let res = await axios({
        method: 'get',
        responseType: 'arraybuffer',
        url: file_url
      })
      let blob = new Blob([res.data], {
        type: 'application/x-msdownload;charset=UTF-8'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', file_url)
      document.body.appendChild(link)
      link.click()
    } catch (error) {
      message.warning('下载失败')
    }
  }

  const delFile = () => {
    state.resData = null
    state.file_name = ''
  }
  // 关闭页面
  const close = () => {
    emits('event', { cmd: 'close' })
  }

  const unploadFiles = (e, type) => {
    let size = e.file.size / 1024 / 1024
    if (size > 20) return message.error('大小不能超过20M')
    let fileLast = e.file.name.substring(e.file.name.lastIndexOf('.') + 1)
    if (['xls', 'xlsx'].includes(fileLast)) {
      Cos.upload(e.file, 'comment', '', (res) => {
        if (res.type === 'success') {
          message.success('上传成功')
          state.file_name = e.file.name
          state.file = e.file
          state.resData = res
        }
      })
    } else {
      message.warning('请选择正确的文件格式')
      return
    }
  }
  const submit = () => {
    if (state.currentIndex == -1) {
      return message.error('请先选择平台')
    }
    if (!state.file_name) {
      return message.error('请先上传文件')
    }
    emits('event', {
      cmd: 'import',
      data: {
        file: state.file,
        resData: state.resData,
        currentIndex: state.currentIndex
      }
    })
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 12px;
    margin-bottom: 8px;
    font-weight: 300;
    color: #85878a;
    &:last-child {
      margin-bottom: 0;
    }
    img {
      margin-right: 8px;
      width: 12px;
      height: 12px;
    }
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .upload-item {
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    height: 212px;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px dashed #e6e6e6;
  }
  .file-list {
    padding: 8px 16px 8px 8px;
    border-radius: 4px;
    .btn-del {
      display: none;
    }
    &:hover {
      background: #f3f3f3;
      .btn-del {
        display: block;
      }
    }
  }
  .item-radio {
    display: flex;
    align-items: center;
    width: 120px;
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    padding: 8px;
    border: 1px solid #e6e6e6;
    cursor: pointer;
    position: relative;
    &.active {
      border: 1px solid var(--primary-color);
    }
    .item-active {
      position: absolute;
      right: 0;
      top: 0;
      width: 20px;
      height: 20px;
      .img-active {
        position: absolute;
        right: 1px;
        top: 4px;
        z-index: 1;
      }
      .item-trangle {
        position: absolute;
        width: 0;
        height: 0;
        border-bottom: 20px solid transparent;
        border-right: 20px solid var(--primary-color);
      }
    }
  }
  :deep(.ant-upload.ant-upload-select) {
    width: 100%;
  }
  .primary-color {
    color: var(--primary-color);
  }
  .item-dialog {
    .upload-status {
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 25px;
    }
    .upload-data {
      font-size: 14px;
      color: rgba(133, 135, 138, 0.88);
      line-height: 18px;
    }
  }
  .fail-info {
    padding: 8px 13px;
  }
  .footer_button {
    margin-top: 16px;
    text-align: right;
  }
</style>
