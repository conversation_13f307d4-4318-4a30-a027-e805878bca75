<template>
  <div>
    <DesTablePage>
      <template #search>
        <SearchFilter :admin-list="state.admin_list" @event="onSearchEvent" />
      </template>
      <template #tableWarp>
        <TableZebraCrossing
          :data="state.tableConfig"
          @change="pageChange"
          :rowSelection="{
            selectedRowKeys: state.selectedRowKeys,
            getCheckboxProps: getCheckboxProps,
            onChange: selectionEvent,
            type: 'radio'
          }"
        >
          <template #bodyCell="{ scope, data }">
            <template v-if="scope.column.key === 'created_at'">
              {{ formatDate(scope.record.created_at * 1000) }}
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <div class="flex flex-justify-end m-t-2">
      <a-button @click="() => emits('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
    </div>
  </div>
</template>

<script setup>
  defineOptions({ name: 'OldList' })
  import { reactive, watch } from 'vue'
  import SearchFilter from '@/views/shop/goods/Comment/components/SearchFilter.vue'
  import { getCommentTempList } from '@/views/shop/goods/Comment/index.api'
  import { getQuestionTempList } from '@/views/shop/goods/QandA/index.api'
  import { get_temp_relation } from '../../../index.api.ts'
  import { useRouter, useRoute } from 'vue-router'
  import { formatDate } from '@/utils'
  import { isArray } from 'lodash-es'
  const props = defineProps(['pageType'])
  const emits = defineEmits(['event'])
  const route = useRoute()
  // 页面data
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10
    },
    loading: false,
    selectedRowKeys: [],
    selectedRows: [],
    tableConfig: {
      loading: false,
      size: 'small',
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        y: 500,
        x: 700
      },
      columns: [
        {
          dataIndex: 'name',
          key: 'name',
          title: props.pageType == 'comments' ? '评论组名称' : '问答组名称',
          ellipsis: true
        },
        { dataIndex: 'author_name', key: 'author_name', title: '负责人', width: 140 },
        { dataIndex: 'created_at', key: 'created_at', title: '创建时间', width: 180 }
      ],
      dataSource: [],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    admin_list: []
  })

  // 监听搜索filter
  const onSearchEvent = (v) => {
    if (!v.status) {
      state.initParams = { ...state.initParams, page: 1 }
      state.selectedRowKeys = []
    }
    console.log('onSearchEvent', v)
    state.initParams = { ...state.initParams, ...v.formData, page: 1 }
    getList()
  }

  // 获取列表
  const getList = async () => {
    try {
      state.tableConfig.loading = true
      const params = {
        ...state.initParams
      }
      // 请求接口
      const result = props.pageType == 'comments' ? await getCommentTempList(params) : await getQuestionTempList(params)

      state.tableConfig.dataSource = result.data.list || []
      state.tableConfig.pagination.current = result.data?.page || 1
      state.tableConfig.pagination.total = result.data?.total_num || 0
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const getTempIds = async () => {
    const params = {
      type: props.pageType == 'comments' ? 1 : 2,
      product_library_id: route.query.product_library_id
    }
    // 请求接口
    const { data } = await get_temp_relation(params)
    state.ids = data.ids.split(',').map((item) => +item)
  }
  const initData = async () => {
    await getTempIds()
    getList()
  }
  initData()
  const pageChange = (pagination) => {
    console.log(pagination, 'paginationpagination')

    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfig.pagination.pageSize = pagination.pageSize
    getList()
  }
  const submit = async () => {
    if (!state.selectedRows.length) {
      return message.warning('请选择评论组')
    }
    // const { data } = await getProductSku(state.selectedRows[0].id)

    // state.saveLoading = true
    emits('event', {
      cmd: 'importData',
      data: {
        id: state.selectedRowKeys[0]
      }
    })
  }

  const selectionEvent = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const getCheckboxProps = (record) => {
    return {
      disabled: state.ids.includes(record.id)
    }
  }
</script>

<style lang="scss" scoped>
  .handle_btn {
    padding: 0;
  }
  .page_main_pagination {
    margin-top: 24px;
  }
  .item-btn {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
  }
</style>
