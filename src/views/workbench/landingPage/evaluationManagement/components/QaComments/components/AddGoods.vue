<template>
  <div>
    <SearchBaseLayout
      :data="searchData"
      @changeValue="searchForm"
      :actions="{
        foldNum: 0
      }"
      class="m-b-5"
    />
    <TableZebraCrossing
      @change="pageChange"
      :data="state.tableConfigOptions"
      rowKey="code"
      :rowSelection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: selectionEvent,
        type: 'radio'
      }"
    >
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'title'">
          <div class="flex">
            <img :src="record.image" alt="" class="w-34px h-34px mr-8px" />
            <a-tooltip popper-class="toolt">
              <template #title>{{ record.title }}</template>
              <div class="flex justify-between">
                <div class="text_overflow_2 w-270px">
                  {{ record.title }}
                </div>
              </div>
            </a-tooltip>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
    <div class="flex flex-justify-end m-t-2">
      <a-button @click="() => emits('event', { cmd: 'close', data: state.selectedRowKeys })">取消</a-button>
      <a-button type="primary" :loading="state.saveLoading" @click="submit">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="tsx">
  import { reactive, ref, watch, onMounted } from 'vue'
  import { message } from 'ant-design-vue'
  import { isArray } from 'lodash-es'
  import { setProductList, getProductSku } from '@/views/shop/goods/goodList/index.api'
  const props = defineProps(['item', 'list', 'id'])
  const emits = defineEmits(['event'])

  const searchData = ref([
    {
      type: 'input.text',
      field: 'code',
      value: undefined,
      props: {
        placeholder: '请输入落地页ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    },
    {
      type: 'input.text',
      field: 'title',
      value: undefined,
      props: {
        placeholder: '请输入落地页名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    }
  ])

  const state = reactive({
    saveLoading: false,
    activeIds: [], //ids
    product_list: [],
    query: {
      product_library_list: [+props.id],
      page: 1,
      page_size: 10
    },
    selectedRowKeys: [],
    selectedRows: [],
    shop_list: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'code',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 540
      },
      dataSource: [],
      columns: [
        {
          title: '落地页ID',
          key: 'code',
          dataIndex: 'code',
          width: 136
        },
        {
          title: '落地页名称',
          key: 'title',
          dataIndex: 'title',
          width: 360
        }
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 5,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    // 选中的数据
    selectionRowsPlus: []
  })
  const selectionEvent = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  async function submit() {
    try {
      console.log(state.selectedRows, '333333')

      if (!state.selectedRows.length) {
        return message.warning('请选择落地页')
      }
      const { data } = await getProductSku(state.selectedRows[0].id)

      state.saveLoading = true
      emits('event', {
        cmd: 'addGoods',
        data: {
          info: state.selectedRows[0],
          sku_list: data || []
        }
      })
    } catch (error) {
      console.error(error)
    } finally {
      state.saveLoading = false
    }
  }

  async function getList() {
    try {
      state.tableConfigOptions.loading = true
      let res = await setProductList({ ...state.query })
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data?.total || 0
      state.tableConfigOptions.pagination.current = res.data?.page
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    state.query = {
      ...state.query,
      ...v.formData
    }
    state.query.page = 1
    getList()
  }
  onMounted(() => {
    if (props.item.code) {
      state.selectedRowKeys = [props.item.code]
      state.selectedRows = [props.item]
    }
    getList()
  })
</script>
<style lang="scss" scoped>
  .text_overflow_2 {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    line-height: 1.2;
  }
</style>
