<template>
  <a-form :labelCol="{ style: 'width: 80px' }" :model="data.form" :rules="rules" ref="ruleForm">
    <a-form-item label="商品" name="product_name">
      <a-input v-model:value="data.form.product_name" placeholder="请输入商品" />
      <template #extra> 示例：蓝牙筋膜枪 </template>
    </a-form-item>
    <a-form-item label="特点" name="feature">
      <a-input v-model:value="data.form.feature" placeholder="请输入商品特点" :maxlength="50" showCount />
      <template #extra> 示例：轻巧便捷、性价比高 </template>
    </a-form-item>
    <a-form-item label="评论字数" name="word_num">
      <div class="flex-y-center">
        每条评论最少
        <a-input-number
          v-model:value="data.form.word_num"
          class="ml-3px mr-3px w-150px"
          min="1"
          max="50"
          :controls="false"
          :precision="0"
          placeholder="请输入评论字数"
        />
        字
      </div>
    </a-form-item>
  </a-form>
  <div class="footer">
    <a-button :mr="20" @click="close">取消</a-button>
    <a-button type="primary" :loading="data.loading" @click="submitForm(ruleForm)">保存</a-button>
  </div>
</template>

<script setup name="AIForm">
  import { reactive, ref, watch } from 'vue'
  const props = defineProps(['loading'])
  const emit = defineEmits(['event', 'change'])
  const ruleForm = ref(null)

  const rules = {
    product_name: [{ required: true, message: '请输入商品', trigger: ['blur', 'change'] }],
    feature: [{ required: true, message: '请输入商品特点', trigger: ['blur', 'change'] }],
    word_num: [{ required: true, message: '请输入评论字数', trigger: ['blur', 'change'] }]
  }

  const data = reactive({
    loading: false,
    form: {
      product_name: ''
    }
  })

  const submitForm = (formName) => {
    formName
      .validate()
      .then(async () => {
        emit('event', { cmd: 'submit', data: data.form })
        data.loading = true
      })
      .catch((err) => {
        console.log('error', err)
      })
  }
  watch(
    () => props.loading,
    (val, old) => {
      if (!val) {
        data.loading = val
      }
    },
    {
      deep: true,
      immediate: true
    }
  )

  const close = () => {
    ruleForm.value.resetFields()
    emit('event', { cmd: 'close' })
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
