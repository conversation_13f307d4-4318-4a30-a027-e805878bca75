<template>
  <el-dialog v-model="modelValue">
    <el-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width: 94px' }">
      <el-form-item label="数量" prop="replayNum">
        <el-input-number
          v-model="data.form.replayNum"
          :min="1"
          :max="10000"
          :controls="false"
          step-strictly
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)">保存</a-button>
    </div>
  </el-dialog>
</template>

<script setup name="ReplayNumber">
  import { reactive, ref } from 'vue'
  const props = defineProps(['modelValue'])
  const emit = defineEmits(['update:modelValue', 'event', 'change'])
  const ruleForm = ref(null)

  const rules = {
    replayNum: [{ required: true, message: '请输入数量', trigger: 'blur' }]
  }

  const data = reactive({
    loading: false,
    form: {
      replayNum: ''
    }
  })

  const submitForm = (formEl) => {
    formEl.validate((valid) => {
      if (!valid) return false
      onChange()
    })
  }

  const onChange = () => {
    emit('change', data.form.replayNum)
  }

  const close = () => {
    ruleForm.value.resetFields()
    emit('event', { cmd: 'close' })
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
