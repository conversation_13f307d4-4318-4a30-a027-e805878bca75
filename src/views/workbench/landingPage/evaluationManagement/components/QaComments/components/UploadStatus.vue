<template>
  <!-- <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      @cancel="stopImport"
      destroyOnClose
    > -->
  <div class="flex-x-center item-dialog flex-col text-center mt-28px">
    <div v-if="uploadStatus == 'uploading'">
      <div class="flex-x-center">
        <LoadingOutlined class="font-size-40px primary-color" />
      </div>
      <div class="upload-status mt-24px mb-16px">数据导入中</div>
      <span class="upload-data">正在导入{{ platformList[currentIndex].name }}评论数据</span>
    </div>
    <div v-if="uploadStatus == 'success'">
      <div class="flex-x-center">
        <img :src="requireImg('qa/qa-success.png')" class="w-40px" />
      </div>
      <div class="upload-status mt-24px mb-16px">数据导入完成</div>
      <div v-if="item.error_num">
        <div class="upload-data">
          {{ platformList[currentIndex].name }}导入的
          <span class="c-#313233 font-600">{{ item.total_num }}</span> 条评论数据，导入成功
          <span class="c-#6fcd41">{{ item.success_num }}</span
          >条，导入失败<span class="c-#fe6466">{{ item.error_num }}</span
          >条
        </div>
        <span class="primary-color decoration-underline cursor-pointer mt-8px" @click="onLink">点击下载失败数据</span>
        <div class="flex-y-center flex bg-#ECF3FF fail-info mb-24px mt-16px border-rd-4px">
          <img :src="requireImg('qa/qa-tips.png')" class="w-12px mr-8px" />
          <span class="c-#555555 line-height-14px">下载失败数据，可以查看导入失败原因</span>
        </div>
      </div>
      <div v-else class="upload-data mb-40px">
        {{ platformList[currentIndex].name }}导入的{{ item.success_num }}条{{ pageTypeFilter() }}数据，已全部导入成功
      </div>
    </div>
    <div v-if="uploadStatus == 'success'">
      <a-button class="primary-color" @click="close">取消</a-button>
      <a-button :loading="state.btnLoading" type="primary" @click="okImport">确认导入</a-button>
    </div>
  </div>
  <!-- </a-modal> -->
</template>
<script setup lang="ts">
  import { onMounted, reactive, ref, createVNode } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { importComment, fetchImportCommentCancel } from '@/views/shop/goods/Comment/index.api'
  import { import_question } from '@/views/shop/goods/QandA/index.api'
  import { requireImg } from '@/utils'
  import Cos from '@/utils/cos'
  import { useRouter } from 'vue-router'
  import { LoadingOutlined } from '@ant-design/icons-vue'
  const router = useRouter()

  const props = defineProps({
    pageType: {
      type: String,
      default: 'QA' // 评论管理(comments) / 问答管理(QA)
    },
    item: Object,
    currentIndex: {
      type: Number,
      default: -1
    },
    uploadStatus: {
      type: String,
      default: 'uploading' // 评论管理(comments) / 问答管理(QA)
    }
  })
  const emits = defineEmits(['event'])
  const state = reactive({
    uploadStatus: 'uploading',
    exportTimer: null,
    btnLoading: false,
    uploadData: {
      data: [],
      error_num: 0,
      success_num: 0,
      url: ''
    },
    file: {},
    file_name: '',
    resData: undefined,
    form: {
      is_bind: 1
    },
    linkData: {
      open: false,
      title: '提示',
      content: '确定进行此操作吗',
      data: null
    },
    dialog: {
      title: '',
      visible: false,
      width: 460,
      type: ''
    },
    initParams: {
      page: 1,
      page_size: 10,
      created_at: undefined
    }
  })
  // 页面filters
  const pageTypeFilter = () => {
    return props.pageType === 'comments' ? '评论' : '问答'
  }
  const platformList = [
    {
      key: 'tbao',
      name: '淘宝',
      icon: requireImg('qa/qa-tbao.png'),
      value: 1
    },
    {
      key: 'jd',
      name: '京东',
      icon: requireImg('qa/qa-jd.png'),
      value: 2
    },
    {
      key: 'pdd',
      name: '拼多多',
      icon: requireImg('qa/qa-pdd.png'),
      value: 3
    },
    {
      key: 'other',
      name: '其他',
      icon: requireImg('qa/qa-other.png'),
      value: 4
    }
  ]
  // 下载失败详情
  const onLink = () => {
    if (!props.item.url) return message.warning('暂无下载链接')
    const elt = document.createElement('a')
    elt.setAttribute('href', props.item.url)
    elt.setAttribute('download', props.item.url)
    elt.style.display = 'none'
    document.body.appendChild(elt)
    elt.click()
    document.body.removeChild(elt)
  }
  // 关闭页面
  const close = () => {
    emits('event', { cmd: 'close' })
  }
  const okImport = () => {
    state.btnLoading = true
    console.log('ok import')
    emits('event', { cmd: 'sure', data: props.item.data || [] })
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 12px;
    margin-bottom: 8px;
    font-weight: 300;
    color: #85878a;
    &:last-child {
      margin-bottom: 0;
    }
    img {
      margin-right: 8px;
      width: 12px;
      height: 12px;
    }
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .upload-item {
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    height: 212px;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px dashed #e6e6e6;
  }
  .file-list {
    padding: 8px 16px 8px 8px;
    border-radius: 4px;
    .btn-del {
      display: none;
    }
    &:hover {
      background: #f3f3f3;
      .btn-del {
        display: block;
      }
    }
  }
  .item-radio {
    display: flex;
    align-items: center;
    width: 120px;
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    padding: 8px;
    border: 1px solid #e6e6e6;
    cursor: pointer;
    position: relative;
    &.active {
      border: 1px solid var(--primary-color);
    }
    .item-active {
      position: absolute;
      right: 0;
      top: 0;
      width: 20px;
      height: 20px;
      .img-active {
        position: absolute;
        right: 1px;
        top: 4px;
        z-index: 1;
      }
      .item-trangle {
        position: absolute;
        width: 0;
        height: 0;
        border-bottom: 20px solid transparent;
        border-right: 20px solid var(--primary-color);
      }
    }
  }
  :deep(.ant-upload.ant-upload-select) {
    width: 100%;
  }
  .primary-color {
    color: var(--primary-color);
  }
  .item-dialog {
    .upload-status {
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 25px;
    }
    .upload-data {
      font-size: 14px;
      color: rgba(133, 135, 138, 0.88);
      line-height: 18px;
    }
  }
  .fail-info {
    padding: 8px 13px;
  }
  .footer_button {
    margin-top: 16px;
    text-align: right;
  }
</style>
