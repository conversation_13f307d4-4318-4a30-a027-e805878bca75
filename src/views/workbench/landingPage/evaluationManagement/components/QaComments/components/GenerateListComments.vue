<template>
  <div class="qa_list">
    <div class="page_main_table">
      <a-table
        ref="tableref"
        bordered
        class="qa-table"
        :data-source="list"
        :columns="columns"
        :childrenColumnName="'_children'"
        :scroll="{ x: 730, y: 460 }"
        :row-key="(record:any) => record.key"
        :pagination="{
           pageSize: pageConfig.pageSize,
          showSizeChanger: true,
          showQuickJumper: true, // 是否可以快速跳转到指定页
          showTotal: (total:number) => `共 ${total} 条`, // 显示总条数和当前数据范围
          current: pageConfig.page, // 当前页数
          // total: 50, // 总条数
          onChange: handlePageChange // 页码改变时的回调函数
        }"
      >
        <template #headerCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'sort'">
            <div>
              <span>权重</span>
              <a-tooltip>
                <template #title>权重值越大，评论排序越靠前</template>
                <QuestionCircleFilled style="color: #939599" />
              </a-tooltip>
            </div>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'user_name'">
            <div class="flex">
              <div class="left">
                <Upload
                  class="generate-list-upload-avatar"
                  accept=".jpg,.png,.jpeg,.bmp,.gif"
                  :max="1"
                  use="landingPage"
                  v-model="record.avatar"
                >
                </Upload>
              </div>
              <div class="right ml-8px flex-col">
                <a-form-item
                  :name="['list', index, 'user_name']"
                  :rules="[
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      validator: () => {
                        return regular(record.user_name)
                      }
                    }
                  ]"
                >
                  <a-input
                    v-if="['add', 'edit'].includes(type)"
                    style="width: 188px"
                    v-model:value="record.user_name"
                    :maxlength="20"
                  ></a-input>
                  <div v-else class="text-left mt--10px">{{ record.user_name }}</div>
                </a-form-item>
                <a-date-picker
                  v-if="['add', 'edit'].includes(type)"
                  show-time
                  style="width: 188px; margin-top: 4px"
                  :allowClear="false"
                  v-model:value="record.created_at"
                  format="YYYY-MM-DD HH:mm:ss"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择时间"
                  :disabled-date="disabledDate(record)"
                  :disabled-time="disabledDateTime(record)"
                />
                <div v-else class="text-left mt--8px">{{ record.created_at }}</div>
                <div class="text-left mt-6px">
                  <span class="item-label mr-4px">
                    <LikeOutlined class="mr-2px" />
                    {{ record.like }}
                  </span>
                  <span class="item-label">{{ (sexOptions as any)[record.type] }}</span>
                </div>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'sort'">
            <span>{{ record.sort || 0 }}</span>
            <FormOutlined class="c-primary ml-4px" @click="handlerAction('setWeight', record)" />
          </template>
          <template v-if="column.dataIndex === 'content'">
            <div class="bg-#FFF4F6 p-8px border-rd-5px text-left mb-8px" v-if="record.check_status == 3">
              <span class="bg-#FDEAEE c-#A7213E font-size-12px pt-1px pb-1px pl-8px pr-8px">提示</span>
              <span class="c-#575354 ml-8px">{{ record.fail_msg }}</span>
            </div>
            <a-textarea
              v-if="record.check_status == 3 || type == 'add' || type == 'edit'"
              v-model:value="record.content"
              placeholder="请输入内容"
              resize="none"
              :maxlength="500"
              show-count
              :autoSize="{ minRows: 2, maxRows: 2 }"
              :style="{
                width: '100%'
              }"
            ></a-textarea>
            <div v-else class="text-left">{{ record.content }}</div>
            <div class="mt-8px mb-8px">
            </div>

            <div class="sub-content mt20px" v-for="(item, index) in record.children">
              <div class="flex justify-between" :class="{ 'mb-8px': record.children.length === 2 && index === 0 }">
                <div class="append-content" :class="['add', 'edit'].includes(type) ? 'w-335px' : 'w-435px'">
                  <div class="flex-y-center justify-between mb-8px">
                    <span class="c-#64636B font-size-14px"> {{ item.is_comment == 1 ? '追评内容' : '商家回复' }} </span>
                    <div>
                      <a-date-picker
                        v-if="type == 'add' || type == 'edit' || !!item.is_new"
                        show-time
                        size="small"
                        :allowClear="false"
                        v-model:value="item.created_at"
                        format="YYYY-MM-DD HH:mm:ss"
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledChildDate(record.created_at)"
                        :disabled-time="disabledChildDateTime(record.created_at)"
                        placeholder="请选择时间"
                      />
                      <div v-else>{{ item.created_at }}</div>
                    </div>
                  </div>
                  <div class="bg-#FFF4F6 p-8px border-rd-5px text-left mb-8px" v-if="item.check_status == 3">
                    <span class="bg-#FDEAEE c-#A7213E font-size-12px pt-1px pb-1px pl-8px pr-8px">提示</span>
                    <span class="c-#575354 ml-8px">{{ item.fail_msg }}</span>
                  </div>
                  <a-textarea
                    v-if="item.check_status == 3 || type == 'add' || type == 'edit' || !!item.is_new"
                    v-model:value="item.content"
                    class="item-textarea"
                    placeholder="请输入内容"
                    resize="none"
                    :maxlength="500"
                    show-count
                    :autoSize="{ minRows: 2, maxRows: 2 }"
                    :style="{
                      width: '100%'
                    }"
                  ></a-textarea>
                  <div v-else class="text-left">{{ item.content }}</div>
                  <div class="mt-8px" v-if="item.is_comment == 1">
                  </div>
                </div>
                <SvgIcon
                  icon="comment_del"
                  class="text-12px mt-5px ml8px cursor-pointer"
                  @click="item.is_comment == 0 ? delChild(record) : delChild(record, true)"
                ></SvgIcon>
              </div>
            </div>
          </template>

          <template v-else-if="column.dataIndex === 'action'">
            <a-space direction="vertical">
              <a-button
                class="p_0"
                v-if="record.children?.every((item) => item.is_comment == 0)"
                type="link"
                size="small"
                @click="addItem(true, record)"
                >追评</a-button
              >
              <a-popconfirm title="您确定要删除此数据吗？" @confirm="delItem(record)">
                <a-button class="p_0" type="link" v-if="['add', 'edit'].includes(type)" danger size="small">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <a-modal v-model:open="state.dialog.visible" :title="state.dialog.title" :width="400" :footer="null" destroyOnClose>
      <setWeightModal v-if="state.dialog.type === 'setWeight'" :data="state.dialog.data" @onEvent="onEvent" />
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  // import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import setWeightModal from '../../setWeightModal.vue'
  import { defineProps, onMounted, reactive, ref, watch } from 'vue'
  import { randomName, setRandomList, generateWithinTwoDays } from '../src/generation'
  import cloneImg from '../src/clone-img'
  import fileUpload from './upload.vue'
  import moment from 'moment'
  import { cloneDeep } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import { PlusOutlined, LikeOutlined, QuestionCircleFilled, FormOutlined } from '@ant-design/icons-vue'
  import datas from '../../../src/data'
  const { disabledChildDate, disabledChildDateTime } = datas()
  const antTableBody = ref<HTMLDivElement | null>(null)

  const { setClone, cloneList } = cloneImg()
  setClone()
  const sexOptions = {
    1: '男粉',
    2: '女粉',
    3: '老年粉'
  }
  // const sourceOptions = {
  //   1: '淘宝',
  //   2: '京东',
  //   3: '拼多多',
  //   4: '其他'
  // }

  const props = defineProps(['data', 'pageType', 'type', 'range', 'skuOption', 'templateName'])
  const emits = defineEmits(['setTableListWatch', 'getIds'])
  const list = ref([])
  const tableref = ref()
  const rowKeys = ref(null)
  const ids = ref([])
  const pageConfig = reactive({
    page: 1,
    pageSize: 10
  })

  const handlePageChange = (page: any, page_size: any) => {
    pageConfig.page = page
    pageConfig.pageSize = page_size
  }

  const regular = (value: string) => {
    let newName = value.replace(' ', '')
    if (!newName) return Promise.reject('昵称不能为空')
    if (newName.length < 2 || newName.length > 20) return Promise.reject('昵称限制2-20位')
    if (!/^[a-zA-Z0-9\u4e00-\u9fa5]{2,20}$/.test(newName)) {
      return Promise.reject('仅支持中英文、数字')
    }
    return Promise.resolve()
  }

  const columns = reactive([
    {
      title: '用户信息',
      key: 'user_name',
      dataIndex: 'user_name',
      width: 240,
      align: 'left',
      justify: 'center'
    },
    {
      title: '评论内容',
      key: 'content',
      dataIndex: 'content',
      align: 'left',
      width: 380
      // width: 370
    },
    {
      title: '权重',
      key: 'sort',
      dataIndex: 'sort',
      align: 'left',
      width: 90
    },
    {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      align: 'left'
    }
  ])
  let dialog = reactive({
    title: '上传用户头像',
    visible: false,
    width: 960,
    type: 'library'
  })
  // const currentIndex = ref(0)
  // const currentItemIndex = ref(0)
  // const isChild = ref(false)
  // const handleUploadAvatar = (row: { item_id: any; children: any }) => {
  //   if (row.children) {
  //     isChild.value = false
  //   } else {
  //     isChild.value = true
  //   }
  //   currentIndex.value = isChild.value
  //     ? list.value.findIndex((v) => v.item_id === row.parent_item_id)
  //     : list.value.findIndex((v) => v.item_id === row.item_id)
  //   currentItemIndex.value = list.value[currentIndex.value].children.findIndex(
  //     (v: { item_id: any }) => v.item_id === row.item_id
  //   )
  //   dialog.visible = true
  // }
  // const onEvent = (obj: { cmd: any; data: string | any[] }) => {
  //   switch (obj.cmd) {
  //     case 'close':
  //       dialog.visible = false
  //       break
  //     case 'material':
  //       dialog.visible = false
  //       if (obj.data.length) {
  //         if (isChild.value) {
  //           list.value[currentIndex.value].children[currentItemIndex.value].avatar = obj.data[0].file_url
  //         } else {
  //           list.value[currentIndex.value].avatar = obj.data[0].file_url
  //         }
  //       }
  //       break
  //   }
  // }
  // const getSkuList = (list) => {
  //   if (list?.length) {
  //     return list.map((item) => {
  //       return {
  //         ...item,
  //         title: item.title.replace(/[$&]/g, '')
  //       }
  //     })
  //   } else {
  //     return props.skuOption || []
  //   }
  // }
  onMounted(() => {
    list.value = props.data
    antTableBody.value = document.querySelector('.ant-table-body')
  })
  // const askItem = async (item: any) => {
  //   const index = list.value.findIndex((v) => v.item_id === item.item_id)
  //   await commentsAskItem(item, index)
  // }
  // 评论添加回复
  // const commentsAskItem = (
  //   row: { created_at: moment.Moment | undefined; item_id: any; key: any },
  //   index: string | number
  // ) => {
  //   // if (
  //   //   (list.value[index].is_comment && list.value[index].children.length === 1) ||
  //   //   (!list.value[index].is_comment && !list.value[index].children.length)
  //   // ) {

  //   const arr = setRandomList(1, null, cloneList, []).map((v) => {
  //     return {
  //       ...v,
  //       is_comment: 0,
  //       is_new: true,
  //       created_at: formatDate(generateWithinTwoDays(row.created_at, moment().unix())),
  //       parent_item_id: row.item_id,
  //       parentId: row.key
  //     }
  //   })
  //   list.value[index].children.push(...arr)
  // }
  // // }
  const formatDate = (stamp) => {
    return moment(stamp * 1000).format('YYYY-MM-DD HH:mm:ss')
  }
  const delItem = async (record: { key: any }) => {
    try {
      ids.value.push(record.id)
      const index = list.value.findIndex((v) => v.key === record.key)
      list.value.splice(index, 1)
      emits('getIds', [])
    } catch (error) {
      console.log(error)
    }
  }
  const delChild = async (item: { parent_item_id: any; key: any }, flag) => {
    try {
      console.log(item, 'itemitemitem')
      const type = flag ? 1 : 0
      const currentItem = item.children.filter((v) => v.is_comment == type)[0]
      const rowIdx = list.value.findIndex((v) => v.item_id === currentItem.parent_item_id)
      if (rowIdx !== -1) {
        const ind = list.value[rowIdx].children.findIndex((it: { key: any }) => it.key === currentItem.key)
        list.value[rowIdx].children.splice(ind, 1)
      }
    } catch (error) {
      console.log(error)
    }
  }
  // 评论添加追评
  const addItem = (
    v: any,
    item: {
      key: any
      is_comment: any
      user_name: any
      avatar: any
      type: any
      created_at: moment.Moment | undefined
      item_id: any
    }
  ) => {
    const index = list.value.findIndex((v) => v.key === item.key)
    item.is_comment = v
    if (v) {
      list.value[index].children.unshift({
        user_name: item.user_name,
        avatar: item.avatar,
        content: '',
        type: item.type,
        img: '',
        is_comment: 1,
        is_new: true,
        created_at: formatDate(generateWithinTwoDays(item.created_at, moment().unix())),
        key: randomName(1, 10),
        parentKey: item.key,
        parent_item_id: item.item_id,
        parentId: item.key
      })
    } else {
      list.value[index].children.splice(0, 1)
    }
  }

  const disabledDate = (row) => {
    if (row.children?.length) {
      return (currentDate) => {
        const earliest = row.children.reduce((prev, current) => {
          return moment(prev.created_at).isBefore(moment(current.created_at)) ? prev : current
        })
        const startDate = moment(earliest.created_at)
        return currentDate && currentDate > startDate
      }
    } else {
      return (currentDate) => {
        return currentDate && currentDate > moment()
      }
    }
  }
  const disabledDateTime = (row) => {
    if (row.children?.length) {
      return (date) => {
        const earliest = row.children.reduce((prev, current) => {
          return moment(prev.created_at).isBefore(moment(current.created_at)) ? prev : current
        })
        const startDate = moment(earliest.created_at)
        const endDate = moment()
        if (date && date.isAfter(startDate, 'day')) {
          return true
        }

        // 禁用 startDate 当天之后的时间
        if (date && date.isSame(startDate, 'day')) {
          const hours = startDate.hours()
          const minutes = startDate.minutes()
          const seconds = startDate.seconds()
          return {
            disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
            disabledMinutes: (selectedHour) =>
              selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
            disabledSeconds: (selectedHour, selectedMinute) =>
              selectedHour === hours && selectedMinute === minutes
                ? [...Array(60)].map((_, i) => i).slice(seconds + 1)
                : []
          }
        }
        return {}
      }
    } else {
      return (date) => {
        if (date && date.isSame(moment(), 'day')) {
          const hours = moment().hours()
          const minutes = moment().minutes()
          const seconds = moment().seconds()

          return {
            disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
            disabledMinutes: (selectedHour) =>
              selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
            disabledSeconds: (selectedHour, selectedMinute) =>
              selectedHour === hours && selectedMinute === minutes
                ? [...Array(60)].map((_, i) => i).slice(seconds + 1)
                : []
          }
        }
        return {}
      }
    }
  }
  // const disabledDateTime = (date) => {
  //   if (date && date.isSame(moment(), 'day')) {
  //     const hours = moment().hours()
  //     const minutes = moment().minutes()
  //     const seconds = moment().seconds()

  //     return {
  //       disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
  //       disabledMinutes: (selectedHour) =>
  //         selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
  //       disabledSeconds: (selectedHour, selectedMinute) =>
  //         selectedHour === hours && selectedMinute === minutes ? [...Array(60)].map((_, i) => i).slice(seconds + 1) : []
  //     }
  //   }
  //   return {}
  // }
  watch(
    () => props.data,
    (val, oldVal) => {
      if (JSON.stringify(val) != JSON.stringify(oldVal)) {
        list.value = cloneDeep(props.data)
        console.log('list.value', list.value)
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
  watch(
    () => list,
    () => {
      emits('setTableListWatch', list.value)
    },
    {
      deep: true
    }
  )

  const setCurrentPage = (page: number) => {
    pageConfig.page = page
  }
  const state = reactive({
    dialog: {
      visible: false,
      type: '',
      title: '',
      data: {}
    }
  })
  const handlerAction = (type: string, record: any) => {
    if (type === 'setWeight') {
      state.dialog.type = 'setWeight'
      state.dialog.data = record
      state.dialog.visible = true
      state.dialog.title = '设置权重'
    }
  }
  const onEvent = ({ cmd, data }: any) => {
    state.dialog.visible = false
    if (cmd === 'sort') {
      list.value.forEach((item) => {
        if (item.item_id === data.item_id) {
          item.sort = data.sort
        }
      })
    }
  }

  defineExpose({ tableref, rowKeys, setCurrentPage, antTableBody })
</script>

<style lang="scss" scoped>
  .page_main_table {
    width: 100%;
    padding: 0;
    margin: 0;
    :deep {
      .ant-table-cell {
        .ant-table-row-expand-icon {
          margin-top: 8px;
        }
      }
      .ant-form-item {
        margin-bottom: 0;
      }
      .left {
        .avatar_upload {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.02);
          border: 1px dashed #d9d9d9;
          border-radius: 4px;
          cursor: pointer;
        }
      }
      .item_upload {
        .item_box,
        .upload_content {
          width: 60px;
          height: 60px;
          .txt {
            display: none;
          }
        }
        .item_box {
          margin-bottom: 10px;
          margin-right: 10px;
        }
      }
      .avatar {
        width: 22px;
        height: 60px;
        position: relative;
        .avatar_ov {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          overflow: hidden;
        }
        .delete {
          width: 12px;
          height: 12px;
          position: absolute;
          top: -9px;
          right: -4px;
          cursor: pointer;
        }
      }
    }
  }
  .p_0 {
    padding: 0;
  }
  .item-label {
    padding: 4px;
    background: #f2f2f2;
    border-radius: 2px;
    border: 1px solid #e7e7e7;
  }
  .item-textarea {
    background: #f4f6f9;
    border-radius: 4px;
  }

  .append-content {
    background: #ffffff;
    border-radius: 4px;
    border: 1px dashed #d8d8d8;
    padding: 8px;
    :deep(.ant-input-textarea-show-count > .ant-input) {
      background: #f4f6f9;
      border: none;
      border-radius: 4px;
    }
  }
  .qa-table {
    :deep(.ant-table-cell) {
      vertical-align: top;
    }
  }
  .generate-list-upload-avatar {
    :deep(.upload_imgs) {
      .item_box {
        width: 20px;
        height: 20px;
        margin-right: 0;
        margin-bottom: 0;
      }
    }
    :deep(.upload_content) {
      width: 20px;
      height: 20px;
      .txt {
        display: none;
      }
      .icon {
        font-size: 12px;
      }
      .ant-progress-inner {
        display: none;
      }
    }
  }
  .generate-list-upload-img {
    :deep(.upload_imgs) {
      .item_box {
        width: 40px;
        height: 40px;
        margin-right: 8px;
        margin-bottom: 0px;
      }
    }
    :deep(.upload_content) {
      width: 40px;
      height: 40px;
      .txt {
        display: none;
      }
      .icon {
        font-size: 18px;
      }
      .ant-progress-inner {
        display: none;
      }
    }
  }
</style>
