<template>
  <div class="generate_qa">
    <div class="generate_qa_item flex">
      <span class="prepend">男粉</span>
      <a-input-number
        v-model:value="numData.man_num"
        :min="1"
        :max="10000"
        :controls="false"
        :precision="0"
        step-strictly
        placeholder="请输入"
      ></a-input-number>
      <span class="append">条</span>
    </div>
    <div class="generate_qa_item flex">
      <span class="prepend">女粉</span>
      <a-input-number
        v-model:value="numData.girl_num"
        :min="1"
        :max="10000"
        :controls="false"
        :precision="0"
        step-strictly
        placeholder="请输入"
      ></a-input-number>
      <span class="append">条</span>
    </div>
    <div class="generate_qa_item flex">
      <span class="prepend">老年粉</span>
      <a-input-number
        v-model:value="numData.old_num"
        :min="1"
        :max="10000"
        :controls="false"
        :precision="0"
        step-strictly
        placeholder="请输入"
      ></a-input-number>
      <span class="append">条</span>
    </div>

    <div class="wrapper_block generate mt-24px">
      <div class="wrapper_block_title">生成时间</div>
      <a-range-picker
        v-model:value="create_time"
        show-time
        :allowClear="false"
        format="YYYY-MM-DD HH:mm:ss"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :disabled-date="disabledDate"
        :disabled-time="disabledDateTime"
      />
    </div>
    <div class="wrapper_block generate mt-24px">
      <div class="wrapper_block_title">模板名称</div>
      <a-input
        v-model:value.trim="templateName"
        placeholder="请输入模板名称"
        :maxlength="20"
        show-count
        @keydown.space.prevent
        @blur="templateNameBlur"
      ></a-input>
    </div>
    <div class="generate_qa_item mt-16px">
      <!-- <a-button type="primary" @click="openAI"
        >生成AI智能评价
        <a-tooltip title="单次可生成评论数量有限，超出限制将无法全部生成">
          <QuestionCircleOutlined />
        </a-tooltip>
      </a-button> -->
      <a-button type="primary" class="ml-0px! mt-10px" @click="oneClickGeneration">一键生成</a-button>
    </div>
    <!-- <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      :maskClosable="false"
      centered
    >
      <AIForm v-if="state.dialog.type == 'ai'" :loading="state.loading" @event="onEvent" />
    </a-modal> -->
  </div>
</template>

<script setup lang="tsx">
  import { reactive, ref, computed, watch } from 'vue'
  import cloneImg from '../src/clone-img'
  import { message } from 'ant-design-vue'
  import moment from 'moment'
  // import AIForm from './AIForm.vue'
  // import { QuestionCircleOutlined } from '@ant-design/icons-vue'
  // import { gen_comment } from '../../../index.api.ts'
  import { randomName, setRandomList } from '../src/generation.ts'
  const { setClone, cloneList } = cloneImg()
  setClone()
  const props = defineProps({
    item: Object,
    list: Array,
    name: String
  })
  const emits = defineEmits(['setList', 'templateNameBlur'])
  const numData = ref({
    man_num: undefined,
    girl_num: undefined,
    old_num: undefined
  })
  const totalNum = computed(() => {
    const man = numData.value.man_num || 0
    const girl = numData.value.girl_num || 0
    const old = numData.value.old_num || 0
    return man + girl + old
  })
  // const state = reactive({
  //   dialog: {
  //     title: '',
  //     visible: false,
  //     width: 602,
  //     type: ''
  //   },
  //   loading: false
  // })
  const create_time = ref([
    moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
    moment().format('YYYY-MM-DD HH:mm:ss')
  ])
  const templateName = ref(props.name || '')
  // const openAI = () => {
  //   if (!isSuccess()) {
  //     return message.warning('请输入生成数量')
  //   }
  //   state.dialog = {
  //     visible: true,
  //     title: 'AI智能评价',
  //     width: 550,
  //     type: 'ai'
  //   }
  // }

  // const onEvent = async (data) => {
  //   if (data.cmd == 'submit') {
  //     const params = {
  //       ...data.data,
  //       num: 1000
  //     }
  //     state.loading = true
  //     try {
  //       const { data: comment_list } = await gen_comment(params)
  //       state.loading = false
  //       if (comment_list.length < totalNum.value) {
  //         // 去掉最后一条不完整
  //         comment_list.pop()
  //       }
  //       oneClickGeneration(comment_list)
  //     } catch (error) {
  //       state.loading = false
  //     }
  //   }

  //   state.dialog.visible = false
  // }
  watch(
    () => props.item,
    () => {
      numData.value = props.item
    },
    {
      immediate: true,
      deep: true
    }
  )
  const disabledDate = (current) => {
    // 禁用今天之后的日期
    return current && current > moment().endOf('day')
  }
  const disabledDateTime = (date) => {
    if (date && date.isSame(moment(), 'day')) {
      const hours = moment().hours()
      const minutes = moment().minutes()
      const seconds = moment().seconds()

      return {
        disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
        disabledMinutes: (selectedHour) =>
          selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
        disabledSeconds: (selectedHour, selectedMinute) =>
          selectedHour === hours && selectedMinute === minutes ? [...Array(60)].map((_, i) => i).slice(seconds + 1) : []
      }
    }
    return {}
  }
  const commentData = ref({
    man_num: 0,
    girl_num: 0,
    old_num: 0
  })
  // 随机分配函数
  const randomAllocate = (total: number, man_num: number, girl_num: number, old_num: number) => {
    const result = {
      man_num: 0,
      girl_num: 0,
      old_num: 0
    }

    // 如果某个数量为 0，则对应的随机数也为 0
    const availableTypes = []
    if (man_num > 0) availableTypes.push('man_num')
    if (girl_num > 0) availableTypes.push('girl_num')
    if (old_num > 0) availableTypes.push('old_num')

    // 随机分配
    for (let i = 0; i < total; i++) {
      const randomType = availableTypes[Math.floor(Math.random() * availableTypes.length)]
      result[randomType]++
    }

    return result
  }
  // 一键生成
  const oneClickGeneration = (comment_list: any) => {
    console.log('cloneList', cloneList)
    if (isSuccess()) {
      // 如果 comment_list 的长度小于 totalNum，则随机分配
      if (comment_list.length < totalNum.value) {
        const allocated = randomAllocate(
          comment_list.length,
          numData.value.man_num,
          numData.value.girl_num,
          numData.value.old_num
        )

        // 更新 commentData
        commentData.value = allocated
      } else {
        // 否则直接使用 numData
        commentData.value = { ...numData.value }
      }
      const list = setDataList(commentData)

      // 确定需要赋值的条数
      const assignCount = Math.min(list.length, comment_list.length)

      // 将 comment_list 赋值给 list 的前 assignCount 条的 content
      for (let i = 0; i < assignCount; i++) {
        list[i].content = comment_list[i]
      }
      const params = {
        ...numData.value,
        list: list
      }
      message.success(`智能生成${list.length}条评论`)
      console.log('params', params)
      emits('setList', params, create_time.value)
    } else {
      message.warning('请输入生成数量')
    }
  }
  // 生成数据的前置条件
  const isSuccess = () => {
    const total = Object.values(numData.value).reduce((prev, curr) => {
      return (prev || 0) + (curr || 0)
    })
    return total > 0
  }
  // 随机生成数据
  const setDataList = (numData) => {
    console.time('生成数据：', numData)
    const arr: any = []
    if (numData.value.man_num) {
      const item = setRandomList(numData.value.man_num, 1, cloneList, create_time.value)
      arr.push.apply(arr, item)
    }
    if (numData.value.girl_num) {
      const item = setRandomList(numData.value.girl_num, 2, cloneList, create_time.value)
      arr.push.apply(arr, item)
    }
    if (numData.value.old_num) {
      const item = setRandomList(numData.value.old_num, 3, cloneList, create_time.value)
      arr.push.apply(arr, item)
    }
    arr.sort(() => 0.5 - Math.random())
    arr.forEach((item) => {
      item.key = randomName(1, 10)
      ;(item.detail_resp || []).forEach((c) => {
        c.key = randomName(1, 10)
      })
    })
    // console.timeEnd('生成数据：', arr)
    console.log('生成数据：', arr)
    return arr
  }
  const templateNameBlur = () => {
    emits('templateNameBlur', templateName.value)
  }
  defineExpose({ setDataList, create_time })
</script>

<style lang="scss" scoped>
  .generate_qa {
    .generate_qa_item {
      align-items: center;
      .prepend,
      .append {
        display: inline-block;
        line-height: 32px;
        border-width: 1px;
        border-style: solid;
        border-color: #d9d9d9;
        padding: 0 8px;
        background-color: rgba(0, 0, 0, 0.02);
        text-align: center;
      }
      .prepend {
        border-radius: 6px 0 0 6px;
        border-right: none;
        width: 70px;
      }
      .append {
        border-radius: 0 6px 6px 0;
        border-left: none;
        width: 40px;
      }
      :deep {
        .ant-input-number {
          border-radius: 0;
          flex: 1;
        }
        .ant-btn {
          width: 100%;
        }
      }
    }
    .generate_qa_item + .generate_qa_item {
      margin-top: 20px;
    }
  }
  .wrapper_block {
    &_title {
      margin-bottom: 15px;
      font-size: 16px;
      padding-left: 12px;
      position: relative;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 4px;
        height: 16px;
        border-radius: 2px;
        display: inline-block;
        background-color: var(--primary-color);
        position: absolute;
        left: 0;
      }
    }
  }
</style>
