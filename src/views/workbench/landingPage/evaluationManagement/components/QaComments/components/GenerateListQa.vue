<template>
  <div class="qa_list">
    <div class="page_main_table">
      <a-table
        ref="tableref"
        bordered
        :data-source="list"
        class="qa-table"
        :columns="columns"
        :childrenColumnName="'_children'"
        :scroll="{ x: 760, y: 460 }"
        :row-key="(record:any) => record.key"
        :pagination="{
          pageSize: pageConfig.pageSize,
          showSizeChanger: true,
          showQuickJumper: true, // 是否可以快速跳转到指定页
          showTotal: (total:number) => `共 ${total} 条`, // 显示总条数和当前数据范围
          current: pageConfig.page, // 当前页数
          // total: 50, // 总条数
          onChange: handlePageChange // 页码改变时的回调函数
        }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'user_name'">
            <div class="flex">
              <div class="left">
                <div class="avatar" v-if="record.avatar">
                  <div class="avatar_ov">
                    <a-image :src="record.avatar"></a-image>
                  </div>

                  <div class="delete" v-if="['add'].includes(type)">
                    <SvgIcon
                      icon="avatar_del"
                      class="text-12px mt-5px cursor-pointer"
                      @click="record.avatar = ''"
                    ></SvgIcon>
                  </div>
                </div>
                <div v-else class="avatar_upload" @click="handleUploadAvatar(record)">
                  <PlusOutlined />
                </div>
              </div>
              <div class="right ml-8px flex-col">
                <a-form-item
                  :name="['list', index, 'user_name']"
                  :rules="[
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      validator: () => {
                        return regular(record.user_name)
                      }
                    }
                  ]"
                >
                  <a-input
                    v-if="['add'].includes(type)"
                    style="width: 188px"
                    v-model:value="record.user_name"
                    :maxlength="8"
                  ></a-input>
                  <div v-else class="text-left mt--10px">{{ record.user_name }}</div>
                </a-form-item>
                <a-date-picker
                  v-if="['add'].includes(type)"
                  show-time
                  style="width: 188px; margin-top: 8px"
                  :allowClear="false"
                  v-model:value="record.created_at"
                  format="YYYY-MM-DD HH:mm:ss"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择时间"
                  :disabled-date="disabledDate(record)"
                  :disabled-time="disabledDateTime(record)"
                />
                <div v-else class="text-left mt--8px">{{ record.created_at }}</div>
                <div class="text-left mt-8px">
                  <span class="item-label mr-4px">{{ (sourceOptions as any)[record.channel_type] }}</span>
                  <span class="item-label">{{ (sexOptions as any)[record.type] }}</span>
                </div>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'content'">
            <div class="bg-#FFF4F6 p-8px border-rd-5px text-left mb-8px" v-if="record.check_status == 3">
              <span class="bg-#FDEAEE c-#A7213E font-size-12px pt-1px pb-1px pl-8px pr-8px">提示</span>
              <span class="c-#575354 ml-8px">{{ record.fail_msg }}</span>
            </div>
            <a-textarea
              v-if="can_edit(record, 'check')"
              v-model:value="record.content"
              placeholder="请输入内容"
              resize="none"
              :maxlength="100"
              show-count
              :autoSize="{ minRows: 2, maxRows: 2 }"
              :style="{
                width: '100%'
              }"
            ></a-textarea>
            <div v-else class="text-left">{{ record.content }}</div>
            <div class="mt-8px mb-8px">
              <fileUpload
                :pageType="props.pageType"
                :isEdit="record.check_status == 3 || type == 'add'"
                :size="record.check_status == 3 || type == 'add' ? 6 : 0"
                v-model="record.img"
                class="main-img"
              ></fileUpload>
            </div>
            <div class="h-30px w-100%"></div>
            <div class="sub-content" v-for="(item, index) in record.children">
              <div class="flex justify-between mb-8px">
                <div class="append-content" :class="type == 'add' ? 'w-390px' : 'w-458px'">
                  <div class="flex justify-between mb-16px">
                    <a-select
                      v-if="can_edit(item, 'no_check')"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="item.user_type"
                      size="small"
                      style="width: 100px"
                    >
                      <a-select-option :value="1">商家回复</a-select-option>
                      <a-select-option :value="2">用户回复</a-select-option>
                    </a-select>
                    <div v-else>{{ item.user_type == 1 ? '商家回复' : '用户回复' }}</div>
                    <div>
                      <a-date-picker
                        v-if="can_edit(item, 'no_check')"
                        show-time
                        size="small"
                        :allowClear="false"
                        style="width: 176px"
                        v-model:value="item.created_at"
                        format="YYYY-MM-DD HH:mm:ss"
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledChildDate(record.created_at)"
                        :disabled-time="disabledChildDateTime(record.created_at)"
                        placeholder="请选择时间"
                      />
                      <div v-else>{{ item.created_at }}</div>
                    </div>
                  </div>
                  <div class="flex justify-between mb-8px" v-if="item.user_type == 2">
                    <div class="flex justify-between mb-8px w-100% flex-y-center">
                      <div class="flex flex-y-center">
                        <div class="item-avatr mr-6px">
                          <div class="avatar" v-if="item.avatar">
                            <div class="avatar_ov">
                              <a-image :src="item.avatar"></a-image>
                            </div>
                            <div class="delete" v-if="can_edit(item, 'no_check')">
                              <SvgIcon
                                icon="avatar_del"
                                class="text-12px mt-5px cursor-pointer"
                                @click="item.avatar = ''"
                              ></SvgIcon>
                            </div>
                          </div>
                          <div v-else class="avatar_upload" @click="handleUploadAvatar(item, index)">
                            <PlusOutlined v-if="!item.user_id" />
                          </div>
                        </div>
                        <a-form-item
                          v-if="can_edit(item, 'no_check')"
                          :name="['list', index, 'user_name']"
                          :rules="[
                            {
                              required: true,
                              trigger: ['blur', 'change'],
                              validator: () => {
                                return regular(item.user_name)
                              }
                            }
                          ]"
                        >
                          <a-input
                            style="width: 113px"
                            size="small"
                            v-model:value="item.user_name"
                            :maxlength="8"
                          ></a-input>
                        </a-form-item>
                        <div v-else>{{ item.user_name }}</div>
                      </div>
                      <div>
                        是否已购
                        <a-switch
                          size="small"
                          class="ml-13px"
                          v-model:checked="item.is_buy"
                          :checkedValue="1"
                          :unCheckedValue="0"
                        ></a-switch>
                      </div>
                    </div>
                  </div>
                  <div class="bg-#FFF4F6 p-8px border-rd-5px text-left mb-8px" v-if="item.check_status == 3">
                    <span class="bg-#FDEAEE c-#A7213E font-size-12px pt-1px pb-1px pl-8px pr-8px">提示</span>
                    <span class="c-#575354 ml-8px">{{ item.fail_msg }}</span>
                  </div>
                  <a-textarea
                    v-if="can_edit(item, 'check')"
                    v-model:value="item.content"
                    class="item-textarea"
                    placeholder="请输入内容"
                    resize="none"
                    :maxlength="100"
                    show-count
                    :autoSize="{ minRows: 2, maxRows: 2 }"
                    :style="{
                      width: '100%'
                    }"
                  ></a-textarea>
                  <div v-else class="text-left">{{ item.content }}</div>
                  <fileUpload
                    :pageType="props.pageType"
                    :isEdit="(item.check_status == 3 || type == 'add' || !!item.is_new) && !item.user_id"
                    :size="(item.check_status == 3 || type == 'add' || !!item.is_new) && !item.user_id ? 6 : 0"
                    v-model="item.img"
                    class="main-img mt-8px"
                  ></fileUpload>
                </div>
                <SvgIcon
                  v-if="!item.user_id"
                  icon="comment_del"
                  class="text-12px mt-5px cursor-pointer"
                  @click="delChild(item)"
                ></SvgIcon>
              </div>
            </div>
          </template>

          <template v-else-if="column.dataIndex === 'action'">
            <a-space direction="vertical">
              <a-button class="p_0" type="link" size="small" @click="askItem(record)">回复</a-button>

              <a-popconfirm title="您确定要删除此数据吗？" @confirm="delItem(record)">
                <a-button class="p_0" v-if="['add'].includes(type)" type="link" danger size="small">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary :size="1" v-if="dialog.visible" type="image" @event="onEvent" />
    </a-modal>
    <a-modal
      v-model:open="replayNumDialog.visible"
      :title="replayNumDialog.title"
      :width="replayNumDialog.width"
      :confirm-loading="replayNumDialog.loading"
      :footer="null"
      destroyOnClose
    >
      <a-form
        :model="replayform"
        :label-col="{ style: { width: '70px' } }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
        @finish="qaAskItem"
        v-if="replayNumDialog.visible"
      >
        <a-form-item label="回复类型" name="user_type">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="replayform.user_type">
            <a-select-option :value="1">商家回复</a-select-option>
            <a-select-option :value="2">用户回复</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="数量"
          name="num"
          :rules="[
            {
              required: true,
              trigger: ['blur', 'change'],
              validator: () => {
                return numValid(replayform.num)
              }
            }
          ]"
        >
          <a-input-number
            v-model:value="replayform.num"
            :min="1"
            :max="20"
            :controls="false"
            :stepStrictly="true"
            placeholder="请输入"
            style="width: 100%"
          />
        </a-form-item>
        <div class="text-right">
          <a-button @click="replayNumDialogCaccel">取消</a-button>
          <a-button type="primary" html-type="submit">确定</a-button>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import { defineProps, onMounted, reactive, ref, watch } from 'vue'
  import { randomName, setRandomList, generateWithinTwoDays, getArrayIndex } from '../src/generation'
  import cloneImg from '../src/clone-img'
  import moment from 'moment'
  import fileUpload from './upload.vue'
  import datas from '../../../src/data'
  const { disabledChildDate, disabledChildDateTime } = datas()
  import { message } from 'ant-design-vue'
  import { PlusOutlined } from '@ant-design/icons-vue'

  const antTableBody = ref<HTMLDivElement | null>(null)

  const { setClone, cloneList } = cloneImg()
  setClone()
  const sexOptions = {
    1: '男粉',
    2: '女粉',
    3: '老年粉'
  }
  const sourceOptions = {
    1: '淘宝',
    2: '京东',
    3: '拼多多',
    4: '其他'
  }

  const props = defineProps(['data', 'type', 'range', 'pageType'])
  const emits = defineEmits(['setTableListWatch', 'getIds'])
  const list = ref([])
  const tableref = ref()
  const ids = ref([])
  const rowKeys = ref(null)

  const pageConfig = reactive({
    page: 1,
    pageSize: 10
  })

  const handlePageChange = (page: any, page_size: any) => {
    pageConfig.page = page
    console.log(page_size, 'page_size')

    pageConfig.pageSize = page_size
  }

  const regular = (value: string) => {
    let newName = value.replace(' ', '')
    if (!newName) return Promise.reject('昵称不能为空')
    if (newName.length < 6 || newName.length > 8) return Promise.reject('昵称限制6-8位')
    if (!/^[a-zA-Z0-9\u4e00-\u9fa5]{6,8}$/.test(newName)) {
      return Promise.reject('仅支持中英文、数字')
    }
    return Promise.resolve()
  }
  const columns = reactive([
    {
      title: '用户信息',
      key: 'user_name',
      dataIndex: 'user_name',
      width: props.type == 'add' ? 240 : 200,
      align: 'left',
      justify: 'center'
    },

    {
      title: '问答内容',
      key: 'content',
      dataIndex: 'content',
      align: 'left',
      width: props.type == 'add' ? 420 : 470
    },

    {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      align: 'left'
    }
  ])

  const can_edit = (row, type) => {
    if (type == 'no_check') {
      // 回复用户的名称头像和日期
      if (props.type == 'add') {
        return true
      } else {
        return !row.id
      }
    } else {
      if (props.type == 'add') {
        return true
      } else {
        return (!row.id || row.check_status == 3) && !row.user_id
      }
    }
  }
  // 素材库文件上传
  const dialog = reactive({
    title: '上传用户头像',
    visible: false,
    width: 960
  })
  const currentIndex = ref(0)
  const currentItemIndex = ref(0)
  const isChild = ref(false)
  const handleUploadAvatar = (row: { item_id: any; children: any }) => {
    if (row.children) {
      isChild.value = false
    } else {
      isChild.value = true
    }
    currentIndex.value = isChild.value
      ? list.value.findIndex((v) => v.item_id === row.parent_item_id)
      : list.value.findIndex((v) => v.item_id === row.item_id)
    currentItemIndex.value = list.value[currentIndex.value].children.findIndex(
      (v: { item_id: any }) => v.item_id === row.item_id
    )
    dialog.visible = true
  }
  const onEvent = (obj: { cmd: any; data: string | any[] }) => {
    switch (obj.cmd) {
      case 'close':
        dialog.visible = false
        break
      case 'material':
        dialog.visible = false
        if (obj.data.length) {
          if (isChild.value) {
            list.value[currentIndex.value].children[currentItemIndex.value].avatar = obj.data[0].file_url
          } else {
            list.value[currentIndex.value].avatar = obj.data[0].file_url
          }
        }
        console.log(list)
        break
    }
  }
  const disabledDate = (row) => {
    // console.log(row, 'rowrowrowrow')
    if (row.children.length) {
      return (currentDate) => {
        const earliest = row.children.reduce((prev, current) => {
          return moment(prev.created_at).isBefore(moment(current.created_at)) ? prev : current
        })
        const startDate = moment(earliest.created_at)
        return currentDate && currentDate > startDate
      }
    } else {
      return (currentDate) => {
        return currentDate && currentDate > moment()
      }
    }
  }
  const disabledDateTime = (row) => {
    if (row.children.length) {
      return (date) => {
        const earliest = row.children.reduce((prev, current) => {
          return moment(prev.created_at).isBefore(moment(current.created_at)) ? prev : current
        })
        const startDate = moment(earliest.created_at)
        const endDate = moment()
        if (date && date.isAfter(startDate, 'day')) {
          return true
        }

        // 禁用 startDate 当天之后的时间
        if (date && date.isSame(startDate, 'day')) {
          const hours = startDate.hours()
          const minutes = startDate.minutes()
          const seconds = startDate.seconds()
          return {
            disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
            disabledMinutes: (selectedHour) =>
              selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
            disabledSeconds: (selectedHour, selectedMinute) =>
              selectedHour === hours && selectedMinute === minutes
                ? [...Array(60)].map((_, i) => i).slice(seconds + 1)
                : []
          }
        }
        return {}
      }
    } else {
      return (date) => {
        if (date && date.isSame(moment(), 'day')) {
          const hours = moment().hours()
          const minutes = moment().minutes()
          const seconds = moment().seconds()

          return {
            disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
            disabledMinutes: (selectedHour) =>
              selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
            disabledSeconds: (selectedHour, selectedMinute) =>
              selectedHour === hours && selectedMinute === minutes
                ? [...Array(60)].map((_, i) => i).slice(seconds + 1)
                : []
          }
        }
        return {}
      }
    }
  }

  const formatDate = (stamp) => {
    return moment(stamp * 1000).format('YYYY-MM-DD HH:mm:ss')
  }
  onMounted(() => {
    list.value = props.data
    antTableBody.value = document.querySelector('.ant-table-body')
  })

  // 回复条数
  const replayNumDialog = reactive({
    visible: false,
    title: '回复条数',
    width: 400,
    loading: false,
    item: null
  })
  // 确认回复条数
  const replayform = reactive({
    num: 1,
    user_type: 1
  })
  const askItem = async (item: any) => {
    replayNumDialog.item = item
    replayNumDialog.visible = true
    replayform.num = 1
  }
  // 问答添加回复
  const numValid = (value: any) => {
    if (!value) return Promise.reject('数量不能为空')
    return Promise.resolve()
  }
  // 取消输入回复条数
  const replayNumDialogCaccel = () => {
    replayNumDialog.visible = false
    replayform.num = 1
  }
  // 确定输入的回复条数
  const qaAskItem = async () => {
    try {
      const row = replayNumDialog.item
      const index = list.value.findIndex((v) => v.key === replayNumDialog.item.key)
      const arr = setRandomList(replayform.num, null, cloneList, []).map((v) => {
        return {
          ...v,
          created_at: formatDate(generateWithinTwoDays(row.created_at, moment().unix())),
          parent_item_id: row.item_id,
          user_type: replayform.user_type,
          is_new: true,
          // avatar: replayform.user_type == 1 ? '' : v.avatar,
          // user_name: replayform.user_type == 1 ? '' : v.user_name,
          avatar: v.avatar,
          user_name: v.user_name,
          parentId: row.key
        }
      })
      if (index !== -1) {
        list.value[index].children.push(...arr)
        replayNumDialog.visible = false
        replayform.num = undefined
      }
    } catch (error) {
      console.log(error)
    }
  }

  const delItem = async (record: { key: any }) => {
    try {
      ids.value.push(record.id)
      const index = list.value.findIndex((v) => v.key === record.key)
      list.value.splice(index, 1)
      emits('getIds', [])
    } catch (error) {
      console.log(error)
    }
  }
  const delChild = async (item: { parent_item_id: any; key: any }) => {
    try {
      const rowIdx = list.value.findIndex((v) => v.item_id === item.parent_item_id)
      if (rowIdx !== -1) {
        const ind = list.value[rowIdx].children.findIndex((it: { key: any }) => it.key === item.key)
        list.value[rowIdx].children.splice(ind, 1)
      }
    } catch (error) {
      console.log(error)
    }
  }

  watch(
    () => props.data,
    () => {
      list.value = props.data
    },
    {
      immediate: true,
      deep: true
    }
  )
  watch(
    () => list,
    () => {
      emits('setTableListWatch', list.value)
    },
    {
      deep: true
    }
  )

  const setCurrentPage = (page: number) => {
    pageConfig.page = page
  }

  defineExpose({ tableref, rowKeys, setCurrentPage, antTableBody })
</script>

<style lang="scss" scoped>
  .page_main_table {
    width: 100%;
    padding: 0;
    margin: 0;
    :deep {
      .ant-table-cell {
        .ant-table-row-expand-icon {
          margin-top: 8px;
        }
      }
      .ant-form-item {
        margin-bottom: 0;
      }
      .item_upload {
        .item_box,
        .upload_content {
          width: 60px;
          height: 60px;
          .txt {
            display: none;
          }
        }
        .item_box {
          margin-bottom: 10px;
          margin-right: 10px;
        }
      }
      .avatar {
        width: 22px;
        height: 22px;
        position: relative;
        .avatar_ov {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          overflow: hidden;
        }
        .delete {
          width: 12px;
          height: 12px;
          position: absolute;
          top: -9px;
          right: -4px;
          cursor: pointer;
        }

        .user_type {
          width: 15px;
          height: 15px;
          background: rgba(255, 255, 255, 0.9);
          position: absolute;
          bottom: 0;
          right: 0;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      // .avatar_upload {
      //   width: 20px;
      //   height: 20px;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      //   background-color: rgba(0, 0, 0, 0.02);
      //   border: 1px dashed #d9d9d9;
      //   border-radius: 4px;
      //   cursor: pointer;
      // }
    }
  }
  .item-label {
    padding: 4px;
    background: #f2f2f2;
    border-radius: 2px;
    border: 1px solid #e7e7e7;
  }
  .append-content {
    background: #ffffff;
    border-radius: 4px;
    border: 1px dashed #d8d8d8;
    padding: 8px;
    :deep(.ant-input-textarea-show-count > .ant-input) {
      background: #f4f6f9;
      border: none;
      border-radius: 4px;
    }
  }
  .p_0 {
    padding: 0;
  }
  .qa-table {
    :deep(.ant-table-cell) {
      vertical-align: top;
    }
  }
</style>
