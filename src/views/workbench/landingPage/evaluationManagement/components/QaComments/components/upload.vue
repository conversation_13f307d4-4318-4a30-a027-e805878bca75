<template>
  <div>
    <div class="flex">
      <div class="file_upload">
        <div class="img_list flex">
          <template v-if="imgList.length">
            <div class="img_list_item" :class="{ edit: isEdit }" v-for="(img, index) in imgList" :key="index">
              <a-image
                :style="imageStyle"
                :src="img.file_url"
                fit="fill"
                :preview-src-list="[img.file_url]"
                preview-teleported
              />
              <!-- <div class="file_review img">
                <SearchOutlined />
              </div> -->
              <div class="delete" @click="handleDelete(img)" v-if="isEdit">
                <CloseCircleFilled />
              </div>
            </div>
          </template>
          <template v-if="videoList.length">
            <div class="img_list_item" v-for="(vid, index) in videoList" :key="index">
              <video style="width: 40px; height: 40px; border-radius: 6px" :src="vid.file_url" />
              <div class="file_review" @click="handleReviewVideo(vid)">
                <SearchOutlined />
              </div>
              <div class="delete" @click="handleDelete(vid)">
                <CloseCircleFilled />
              </div>
            </div>
          </template>
        </div>
      </div>
      <div
        class="add-upload"
        :class="pageType == 'append' ? 'avatar_upload_big' : 'avatar_upload'"
        @click="handleUpload"
        v-if="props.size > fileCount"
      >
        <PlusOutlined class="c-#717171" />
        <div class="upload-txt c-#7B808D">上传</div>
      </div>
    </div>

    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        v-if="dialog.type == 'library'"
        :fileCheckedList="state.fileList"
        :isComments="props.pageType == 'comments'"
        type="image"
        :size="6"
        @event="onEvent"
      />
      <Video v-if="dialog.type == 'video'" :video="state.video" />
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import { computed, reactive, ref, watch, watchEffect } from 'vue'
  import { CloseCircleFilled, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
  import Video from './video.vue'

  const props = defineProps({
    modelValue: {
      type: [Array, String],
      deflaut: () => []
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    pageType: String,
    size: Number
  })

  const emit = defineEmits(['update:modelValue', 'change'])
  const state = reactive({
    value: [],
    imgList: [],
    videoList: [],
    fileList: [],
    video: {}
  })
  const fileCount = ref(0)
  watchEffect(() => {
    fileCount.value = state.fileList.length
  })

  const imgList = computed(() => {
    return state.fileList.filter((v) => v.file_type == 'img')
  })
  const videoList = computed(() => {
    return state.fileList.filter((v) => v.file_type == 'video')
  })

  // 素材库文件上传
  const dialog = reactive({
    title: '上传文件',
    visible: false,
    width: 960,
    type: ''
  })
  // 文件上传
  const handleUpload = () => {
    console.log('handleUpload')
    dialog.type = 'library'
    dialog.visible = true
    dialog.title = '上传文件'
  }

  // 查看视频
  const handleReviewVideo = (video) => {
    state.video = video
    dialog.type = 'video'
    dialog.visible = true
    dialog.title = ''
  }
  const imageStyle = computed(() => {
    return {
      width: props.pageType === 'append' ? '60px' : '40px',
      height: props.pageType === 'append' ? '60px' : '40px',
      borderRadius: '6px'
    }
  })
  const onEvent = (obj) => {
    switch (obj.cmd) {
      case 'close':
        dialog.visible = false
        break
      case 'material':
        dialog.visible = false
        if (fileCount.value + obj.data.length > props.size) {
          const difCount = props.size - fileCount.value
          state.fileList = [...state.fileList, ...obj.data.splice(0, difCount)]
        } else {
          state.fileList = [...state.fileList, ...obj.data]
        }
        emit('update:modelValue', (state.fileList || []).map((v) => v.file_url).join(','))
        break
    }
  }

  // 文件删除
  const handleDelete = (img) => {
    const idx = state.fileList.findIndex((v) => img.file_url == v.file_url)
    state.fileList.splice(idx, 1)
    emit('update:modelValue', (state.fileList || []).map((v) => v.file_url).join(','))
  }

  // watch(
  //   () => props.modelValue,
  //   () => {
  //     initData()
  //   },
  //   { immediate: true, deep: true }
  // )

  // function initData() {
  //   if (props.modelValue.length) {
  //     state.value = props.modelValue
  //   }
  // }

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        const res = props.modelValue.split(',')
        res.forEach((v) => {
          if (!state.fileList.find((f) => f.file_url === v)) {
            if (v.indexOf('.mp4') !== -1) {
              state.fileList.push({
                file_url: v,
                file_type: 'video'
              })
            } else {
              state.fileList.push({
                file_url: v,
                file_type: 'img'
              })
            }
          }
        })
        onEvent({ cmd: 'material', data: [] })
      }
    },
    {
      immediate: true,
      deep: true
    }
  )

  const onUploadChange = (value) => {
    emit('change', value)
  }
</script>

<style lang="scss" scoped>
  .file_upload {
    // height: 60px;
    .img_list_item {
      position: relative;
    }

    .delete {
      width: 12px;
      height: 12px;
      position: absolute;
      top: -9px;
      right: -6px;
      cursor: pointer;
      display: none;
    }
  }
  .avatar_upload {
    width: 40px;
    height: 40px;
    background: #f1f5fc;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid #d8dde8;
    .upload-txt {
      font-size: 10px;
    }
  }
  .avatar_upload_big {
    width: 60px;
    height: 60px;
    background: #f1f5fc;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid #d8dde8;
    .upload-txt {
      font-size: 14px;
    }
  }
  .img_list_item {
    margin-right: 10px;
  }
  .img_list_item.edit {
    margin-right: 10px;
    .file_review {
      display: none;
      &.img {
        pointer-events: none;
      }
    }
    &:hover {
      .file_review {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -12px;
        margin-top: -12px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .delete {
        display: block;
      }
    }
  }
  // .add-upload {
  //   width: 40px;
  //   height: 40px;
  //   background: #f1f5fc;
  //   border-radius: 4px;
  //   border: 1px solid #d8dde8;
  // }
</style>
