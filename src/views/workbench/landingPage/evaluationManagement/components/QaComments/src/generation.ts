import { ref } from 'vue'
import { IMGS_DATA } from './images'
import moment from 'moment'
import { getConfig, copyData } from '@/utils'

export function randomName(prefix, randomLength) {
  // 兼容更低版本的默认值写法
  prefix === undefined ? (prefix = '') : prefix
  randomLength === undefined ? (randomLength = 8) : randomLength
  // 设置随机用户名
  // 用户名随机词典数组
  const nameArr = [
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
    [
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'g',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z'
    ],
    [
      '怡',
      '彤',
      '欣',
      '志',
      '薇',
      '雅',
      '哲',
      '泽',
      '佳',
      '天',
      '晨',
      '云',
      '岚',
      '润',
      '金',
      '昊',
      '博',
      '杰',
      '馨',
      '萌',
      '卓',
      '江',
      '玥',
      '柏',
      '敏',
      '皓',
      '圣',
      '嫣',
      '逸',
      '丹',
      '辉',
      '力',
      '云',
      '涵',
      '玲',
      '雯',
      '逸',
      '冉',
      '弦',
      '铭',
      '华',
      '菡',
      '泽',
      '天',
      '婷',
      '金',
      '瑾',
      '永',
      '润',
      '冠',
      '一',
      '敬',
      '雅',
      '妍',
      '怡',
      '铭',
      '永',
      '敏',
      '彤',
      '珊',
      '建',
      '宏',
      '梓',
      '林',
      '学',
      '钰',
      '佳',
      '颜',
      '博',
      '健',
      '江',
      '欣',
      '澍',
      '倩',
      '婷',
      '志',
      '礼',
      '煊',
      '瑞',
      '昊',
      '博',
      '立',
      '柏',
      '铎',
      '哲',
      '卓',
      '萌',
      '雨',
      '思',
      '茹',
      '璐',
      '笑',
      '晚',
      '奶',
      '粉',
      '第',
      '不',
      '最',
      '你',
      '风',
      '神',
      '渴',
      '少',
      '爱',
      '感',
      '少',
      '五',
      '看',
      '月',
      '桜',
      '下',
      '倾',
      '微',
      '早',
      '海',
      '繁',
      '與',
      '初',
      '不',
      '奶',
      '时',
      '欢'
    ]
  ]
  // 随机名字字符串
  let name = prefix
  // 循环遍历从用户词典中随机抽出一个
  for (let i = 0; i < randomLength; i++) {
    // 随机生成index
    const index = Math.floor(Math.random() * 3)
    let zm = nameArr[index][Math.floor(Math.random() * nameArr[index].length)]
    // 如果随机出的是英文字母
    if (index === 1) {
      // 则百分之50的概率变为大写
      if (Math.floor(Math.random() * 2) === 1) {
        zm = zm.toUpperCase()
      }
    }
    // 拼接进名字变量中
    name += zm
  }
  // 将随机生成的名字返回
  return name
}
// export function randomText(num) {
//   let words = []

//   for (let i = 0; i < num; i++) {
//     eval('words.push(' + '"\\u' + (Math.round(Math.random() * 20901) + 19968).toString(16) + '")') //生成随机汉字
//   }
//   return words.join('')
// }

export function randomAvatar(arr) {
  let cloneArr = copyData(arr.value)
  const index = getRandomInteger(cloneArr.length - 1, 0)
  let url = cloneArr.splice(index, 1)

  if (url.length) {
    return [{ url: `${getConfig('NODE_OSS_URL')}/assets/accupload/${url}` }]
  } else {
    return [{ url: '' }]
  }
}

/**
 * 浏览器滚动到指定位置
 * @param {Element} element
 */
export function scrollToElement(element) {
  const dom = document.getElementById('__SCROLL_EL_ID__')
  dom.scrollTo({
    behavior: 'smooth',
    left: 0,
    top: element.offsetTop - 173
  })
}
// 动态引入图片
export const requireImg = (image) => {
  return new URL(`../../assets/images/${image}`, import.meta.url).href
}

/**
 * @param 金额元转千，保留2位小数
 */

// export const convertAmountToThousand = (amount) => {
//   var convertedAmountFloat = null
//   if (amount > 100000) {
//     var convertedAmount = (amount / 1000).toFixed(2) // 将原始金额除以1000并保留两位小数

//     // 转换后的金额可以以浮点型（float）或者字符串型（string）进行存储
//     convertedAmountFloat = parseFloat(convertedAmount) + 'K'
//   } else {
//     convertedAmountFloat = amount
//   }

//   return convertedAmountFloat // 返回转换后的金额（浮点型）
// }

/**
 * 判断是否手机登录
 */
// export function isMobileDevice() {
//   const userAgent = navigator.userAgent.toLowerCase()
//   const mobileKeywords = ['android', 'iphone', 'ipad', 'windows phone']

//   for (const keyword of mobileKeywords) {
//     if (userAgent.includes(keyword)) {
//       return true // 包含移动设备关键词，判断为手机登录
//     }
//   }

//   return false
//   // return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
// }

// 不能输入表情
export function noEmoji() {
  return /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/g
}

// 去除首位空格
export function noSideSpace(value) {
  return (value) => !value.startsWith(' ') && !value.endsWith(' ')
}

/**
 * 获取指定整数范围内的随机整数
 * @param start - 开始范围
 * @param end - 结束范围
 */
export function getRandomInteger(end, start = 0) {
  const range = end - start
  const random = Math.floor(Math.random() * range + start)
  return random
}

/**
 * 随机生成 2016-01-01 到 前天 的时间
 */

export function randomDate(startDate = moment().add(-9, 'days'), endDate = moment()) {
  const SDate = moment(startDate)
  const EDate = moment(endDate)

  // 确保开始时间和结束时间是有效的
  if (SDate.isAfter(EDate)) {
    throw new Error('开始时间必须早于结束时间')
  }

  // 计算时间差（毫秒）
  const diffMilliseconds = EDate.diff(SDate)

  // 随机生成一个时间戳
  const randomTimestamp = SDate.valueOf() + Math.floor(Math.random() * diffMilliseconds)

  // 返回随机时间的 Unix 时间戳
  return moment(randomTimestamp).unix()
}

/**
 * 随机生成当前到前天的
 */
export function generateWithinTwoDays(startDate = moment().add(-9, 'days'), endDate = moment()) {
  const startFlag = moment(startDate).valueOf()
  const endFlag = moment(endDate).valueOf() * 1000

  return moment(getRandomInteger(endFlag, startFlag)).unix()
}
/**
 * 生成一个随机的三位数（100 ~ 999）
 * @returns {number} 随机的三位整数
 */
function generateRandomThreeDigitNumber() {
  return Math.floor(Math.random() * 900) + 100
}
/**
 * 生成数据列表
 * @param total - 生成的条数
 * @param type - 生成数据的性别
 */
export function setRandomList(total, type, cloneList, range) {
  let [startDate, endDate] = range
  startDate = moment(startDate).valueOf()
  endDate = moment(endDate).valueOf()
  console.log(startDate, 'startDate')

  const arr = []
  for (let i = 0; i < total; i++) {
    const obj = type
      ? {
          user_name: randomName(),
          avatar: randomAvatar(cloneList),
          like: generateRandomThreeDigitNumber(),
          content: '',
          img: [],
          type: type,
          children: [],
          key: randomName(1, 10),
          item_id: randomString(),
          created_at: moment(randomDate(startDate, endDate) * 1000).format('YYYY-MM-DD HH:mm:ss')
        }
      : {
          user_name: randomName(),
          avatar: randomAvatar(cloneList),
          like: generateRandomThreeDigitNumber(),
          content: '',
          img: [],
          type: getRandomInteger(4, 1),
          key: randomName(1, 10),
          day: getRandomInteger(10, 1),
          item_id: randomString(),
          created_at: moment(randomDate(startDate, endDate) * 1000).format('YYYY-MM-DD HH:mm:ss')
        }
    arr.push(obj)
  }
  return arr
}

/**
 * 生成随机字符串
 */
export function randomString(e) {
  e = e || 32
  var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
    a = t.length,
    n = ''
  for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}

/**
 * 获取数组下标index
 */
export function getArrayIndex(array: any[], target: any, field: string) {
  if (array?.length || !target || !field) return -1
  return array.findIndex((v) => v[field] === target[field])
}
