<template>
  <div class="add_group">
    <!-- <a-alert class="mb-8px" show-icon type="warning">
      <template #message>
        <span>{{ `原有${pageTypeFilter()}组可通过”导入已有${pageTypeFilter()}组”功能按钮进行导入。` }}</span>
      </template>
    </a-alert>
    <div class="text-right" v-if="['add'].includes(type)">
      <a-button type="primary" @click="showUploadDialog('upload')">{{ `导入${pageTypeFilter()}` }}</a-button>
      <a-button type="primary" @click="showUploadDialog('list')" :disabled="state.import_ids.length">{{
        `导入已有${pageTypeFilter()}组`
      }}</a-button>
    </div> -->
    <div class="group_wrapper flex">
      <div class="group_wrapper_left pr-20px" v-if="['add', 'edit'].includes(type)">
        <!-- <div class="item-name mb-16px" v-if="pageType === 'comments'">
          <div class="wrapper_block name">
            <div class="wrapper_block_title">绑定商品</div>
            <a-radio-group v-model:value="state.form.is_bind_product" button-style="solid" @change="changeBind">
              <a-radio :value="2">不绑定</a-radio>
              <a-radio :value="1" class="ml-8px">绑定</a-radio>
            </a-radio-group>
            <div
              class="goods-info bg-#FAFAFA p-8px border-rd-4px mt-8px"
              v-if="state.form.product_id && state.form.is_bind_product == 1"
            >
              <div class="flex goods_info h-34px">
                <div class="img">
                  <a-image
                    style="width: 34px; height: 34px; border-radius: 3px"
                    :src="state.form.product_image"
                    fit="fill"
                  />
                </div>
                <div class="goods_info_data">
                  <div class="flex justify-between">
                    <a-tooltip
                      popper-class="toolt"
                      :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                      placement="topLeft"
                    >
                      <template #title>{{ state.form.product_name }}</template>
                      <div class="flex justify-between flex-items-center">
                        <div class="goods_info_data_name">
                          {{ state.form.product_name }}
                        </div>
                      </div>
                    </a-tooltip>
                    <div class="h-16px cursor-pointer flex-y-center">
                      <img :src="requireImg('qa/qa-change.png')" class="w-9px h-10px" @click="changeGoods" />
                    </div>
                  </div>

                  <span class="number-id">ID：{{ state.form.product_code }}</span>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <div class="item-name">
          <div class="wrapper_block generate">
            <div class="wrapper_block_title">生成列表</div>
            <!-- <a-radio-group v-model:value="state.form.channel_type" button-style="solid">
              <a-radio :value="1">淘宝</a-radio>
              <a-radio :value="2" class="ml-22px">京东</a-radio>
              <div class="mt-12px mb-12px">
                <a-radio :value="3">拼多多</a-radio>
                <a-radio :value="4" class="ml-8px">其他</a-radio>
              </div>
            </a-radio-group> -->
            <GenerateFans
              ref="fansRef"
              :pageType="props.pageType"
              :item="state.fansNum"
              :list="state.form.details_info"
              :name="state.templateName"
              @setList="setList"
              @templateNameBlur="templateNameBlur"
            />
          </div>
        </div>
      </div>
      <div class="group_wrapper_right">
        <div class="wrapper_block generate">
          <div class="flex_ju_sp">
            <div class="wrapper_block_title">{{ `${pageTypeFilter()}` }}列表</div>
            <div v-if="totalCount">
              <span>总数：{{ totalCount }}</span>
            </div>
          </div>
          <!-- <GenerateListQa
            :data="state.form.details_info"
            :skuOption="state.goodsInfo.sku_list"
            :range="state.dateRange"
            :pageType="props.pageType"
            :type="props.type"
            ref="listRef"
            @getIds="getIds"
            @setTableListWatch="setTableListWatch"
            v-if="props.pageType === 'QA'"
          /> -->
          <GenerateListComments
            :data="state.form.details_info"
            :skuOption="state.goodsInfo.sku_list"
            :range="state.dateRange"
            :templateName="state.templateName"
            :pageType="props.pageType"
            :type="props.type"
            ref="listRef"
            @getIds="getIds"
            @setTableListWatch="setTableListWatch"
            v-if="props.pageType === 'comments' && state.form.details_info.length"
          />
        </div>
      </div>
    </div>
    <div class="mt-10px text-right">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">保存</a-button>
    </div>
    <!-- <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      @cancel="cancelModal"
      destroyOnClose
      centered
    >
      <AddGoods v-if="state.dialog.type == 'goods'" :id="id" :item="state.goods_detail" @event="onEvent" />
      <BatchUpload v-if="state.dialog.type == 'upload'" :pageType="props.pageType" @event="onUpload" />

      <OldList v-if="state.dialog.type == 'list'" :pageType="props.pageType" @event="onEvent" />
      <UploadStatus
        v-if="state.dialog.type == 'status'"
        :pageType="props.pageType"
        :uploadStatus="state.uploadStatus"
        :item="state.uploadData"
        :currentIndex="state.currentIndex"
        @event="sureUpload"
      />
    </a-modal> -->
  </div>
</template>

<script setup name="QaComments" lang="tsx">
  import { message, Modal } from 'ant-design-vue'
  import { nextTick, onMounted, reactive, ref, watchEffect, createVNode } from 'vue'
  import GenerateFans from './components/GenerateFans.vue'
  // import GenerateListQa from './components/GenerateListQa.vue'
  // import AddGoods from './components/AddGoods.vue'
  // import OldList from './components/OldList.vue'
  // import BatchUpload from './components/BatchUpload.vue'
  // import UploadStatus from './components/UploadStatus.vue'
  import GenerateListComments from './components/GenerateListComments.vue'
  import {
    get_question_info,
    edit_question,
    import_question,
    importComment,
    fetchImportCommentCancel,
    add_comment,
    add_question,
    get_comment_info,
    edit_comment,
    getCommentTempDetail,
    getQuestionTempDetail,
    update_comment
  } from '../../index.api'

  import moment from 'moment'
  // import { getProductSku } from '@/views/shop/goods/goodList/index.api'
  import { cloneDeep } from 'lodash'
  import { requireImg } from '@/utils'
  import { randomString, randomName, generateWithinTwoDays, randomDate } from './src/generation'
  // props
  const props = defineProps({
    pageType: {
      type: String,
      default: 'comments' // 评论管理(comments) / 问答管理(QA)
    },
    id: {
      type: [Number, String],
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'add'
    },
    item: Object
  })
  // emit
  const emit = defineEmits(['event'])
  // 定义页面变量
  const state = reactive({
    form: {
      name: '', // 组名称
      channel_type: 1,
      // is_bind_product: 2,
      deleted_ids: '',
      details_info: [] // 生成的数据
    },
    oldData: [],
    import_ids: [],
    uploadStatus: 'uploading',
    currentIndex: 0,
    uploadData: {
      data: [],
      error_num: 0,
      success_num: 0,
      url: ''
    },
    dateRange: [],
    templateName: props.item?.title || '',
    goodsInfo: {
      sku_list: []
    },
    goods_detail: {},
    dialog: {
      title: '',
      visible: false,
      width: 602,
      type: ''
    },
    fansNum: {
      man_num: props.item?.man_num || undefined,
      girl_num: props.item?.woman_num || undefined,
      old_num: props.item?.mix_num || undefined
    },
    loading: false
  })
  console.log(props.item)
  if (props.item?.comment_content) {
    const arr = JSON.parse(props.item?.comment_content) || []
    arr.forEach((item) => {
      item.created_at = moment(item.created_at*1000).format('YYYY-MM-DD HH:mm:ss')
      item.children = item.detail_resp || []
      item.children.forEach((child) => {
        if (typeof child.created_at !== 'string') {
          child.created_at = moment((child.created_at || child.created_time)*1000).format('YYYY-MM-DD HH:mm:ss')
        } else {
          console.log(child.created_at)
        }
      })
    })
    state.form.details_info = arr
  }
  const totalCount = ref(0)
  watchEffect(() => {
    totalCount.value = state.form.details_info.length
  })

  // const platformList = [
  //   {
  //     key: 'tbao',
  //     name: '淘宝',
  //     icon: requireImg('qa/qa-tbao.png'),
  //     value: 1
  //   },
  //   {
  //     key: 'jd',
  //     name: '京东',
  //     icon: requireImg('qa/qa-jd.png'),
  //     value: 2
  //   },
  //   {
  //     key: 'pdd',
  //     name: '拼多多',
  //     icon: requireImg('qa/qa-pdd.png'),
  //     value: 3
  //   },
  //   {
  //     key: 'other',
  //     name: '其他',
  //     icon: requireImg('qa/qa-other.png'),
  //     value: 4
  //   }
  // ]

  // 一键生成数据
  const setList = (data: any, range: any) => {
    state.form = { ...state.form, ...data }
    data.list.forEach((item) => {
      item.channel_type = state.form.channel_type
    })
    state.form.details_info.unshift(...data.list)
    state.dateRange = range
    // if (state.goodsInfo.sku_list.length > 0) {
    //   addSku(state.goodsInfo.sku_list)
    // }
  }

  // 关联table修改后的数据
  const setTableListWatch = (data) => {
    state.form.details_info = data
  }
  // 页面filters
  const pageTypeFilter = () => {
    return props.pageType === 'comments' ? '评论' : '问答'
  }

  // 关闭页面
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  // const cancelModal = () => {
  //   if (state.dialog.type == 'goods' && !state.form.product_id) {
  //     state.form.is_bind_product = 2
  //   }
  //   if (state.dialog.type == 'status') {
  //     fetchImportCommentCancel()
  //   }
  // }
  const regular = (value) => {
    let newName = value
    if (!newName) return false

    if (/\s/.test(newName)) {
      return false
    }

    if (newName.length < 2 || newName.length > 20) return false
    if (!/^[a-zA-Z0-9\u4e00-\u9fa5]{2,20}$/.test(newName)) {
      return false
    }
    return true
  }

  // 数组切分
  const getPage = (index: number, pageSize: 10) => {
    return Math.ceil((index || 0) / pageSize)
  }

  const getIds = (data) => {
    console.log(data, '333333333')
    state.form.deleted_ids = data.join(',')
  }
  // 验证数据
  const scrollIndex = ref(null)
  const isPass = (treeData: any) => {
    for (let i = 0; i < treeData.length; i++) {
      if (!treeData[i].avatar || treeData[i].avatar.length == 0) {
        scrollIndex.value = { index: i, text: '头像', key: treeData[i].key, page: getPage(i + 1, 10) }
        return false
      }

      if (!regular(treeData[i].user_name)) {
        scrollIndex.value = { index: i, text: '用户名称', key: treeData[i].key, page: getPage(i + 1, 10) }
        //  message.warning('名称不能有空格')
        return false
      }
      if (treeData[i].content && treeData[i].children.length) {
        for (let j = 0; j < treeData[i].children.length; j++) {
          if (
            !regular(treeData[i].children[j].user_name) &&
            treeData[i].children[j].user_type != 1 &&
            !treeData[i].children[j].user_id
          ) {
            scrollIndex.value = {
              index: [i, j],
              text: '用户名称',
              key: treeData[i].key,
              page: getPage(i + 1, 10)
            }
            return false
          } else if (
            !treeData[i].children[j].avatar &&
            treeData[i].children[j].user_type != 1 &&
            !treeData[i].children[j].user_id
          ) {
            scrollIndex.value = {
              index: [i, j],
              text: '头像',
              key: treeData[i].key,
              page: getPage(i + 1, 10)
            }
            return false
          } else if (!treeData[i].children[j].content) {
            scrollIndex.value = {
              index: [i, j],
              text: '内容',
              key: treeData[i].key,
              page: getPage(i + 1, 10)
            }
            return false
          }
        }
      } else if (!treeData[i].content) {
        scrollIndex.value = { index: i, text: '内容', key: treeData[i].key, page: getPage(i + 1, 10) }
        return false
      }
    }
    return true
  }

  // 生成数据的前置条件
  const isSuccess = () => {
    const total = Object.values(state.fansNum).reduce((prev, curr) => {
      return (prev || 0) + (curr || 0)
    })
    return total > 0
  }

  const listRef = ref()
  const fansRef = ref()

  const filterPass = (code, arr) => {
    const queue = [...arr]
    while (queue.length) {
      const o = queue.shift()
      if (o['user_type'] == 1) return false
      if (!o[code]) return true
      queue.push(...(o.children || []))
    }
  }
  const templateNameBlur = (val: string) => {
    state.templateName = val
  }
  const submit = async () => {
    if (['add', 'edit'].includes(props.type)) {
      if (!isSuccess() && !state.form.details_info.length) {
        message.warning(`请输入生成数量`)
        return
      }
      if (!state.templateName) {
        message.warning('请输入模板名称')
        return
      }
      const isStatus = filterPass('user_name', state.form.details_info)
      if (isStatus) {
        message.warning(`请将${props.pageType === 'QA' ? '问答' : '评论'}列表填写完整`)
        return
      }
      if (state.form.details_info && state.form.details_info.length === 0) {
        message.warning(`${pageTypeFilter()}条数不能为空,请生成数据`)
        return
      }
    }

    if (!isPass(state.form.details_info)) {
      if (Array.isArray(scrollIndex.value['index'])) {
        message.warning(`请将${props.pageType === 'QA' ? '问答' : '评论'}列表${scrollIndex.value.text}填写完整`)
        listRef.value?.setCurrentPage(scrollIndex.value.page)
        // 滚动到指定位置
        if (listRef.value?.antTableBody) {
          nextTick(() => {
            listRef.value?.antTableBody?.scrollTo({
              behavior: 'smooth',
              left: 0,
              top: scrollIndex.value['index'][0] * 93 + scrollIndex.value['index'][1] * 93
            })
          })
        }
      } else {
        // await listRef.value?.expandedRow([scrollIndex.value.key])
        message.warning(`请将${props.pageType === 'QA' ? '问答' : '评论'}列表${scrollIndex.value.text}填写完整`)
        listRef.value?.setCurrentPage(scrollIndex.value.page)
        // 滚动到指定位置
        if (listRef.value?.antTableBody) {
          nextTick(() => {
            listRef.value?.antTableBody?.scrollTo({
              behavior: 'smooth',
              left: 0,
              top: scrollIndex.value['index'] * 93
            })
          })
        }
      }
      return
    }
    saveFormData()
  }
  // 递归函数将 create_at 转换为时间戳
  const convertCreateAtToTimestamp = (list, type) => {
    return list.map((item) => {
      const newItem = {
        ...item,
        created_at:
          type == 'stamp'
            ? moment(item.created_at).unix()
            : moment(item.created_at * 1000).format('YYYY-MM-DD HH:mm:ss') // 转换为 Unix 时间戳
      }

      // 如果有 children，递归调用
      if (newItem.detail_resp && newItem.detail_resp.length > 0) {
        newItem.detail_resp = convertCreateAtToTimestamp(newItem.detail_resp, type)
      }
      // 如果有 children，递归调用
      if (newItem.children && newItem.children.length > 0) {
        newItem.children = convertCreateAtToTimestamp(newItem.children, type)
      }

      return newItem
    })
  }

  // const updateIsUpdateFlag = (a, b) => {
  //   console.log(a, 'aaaaaaaaaa')
  //   console.log(b, 'bbbbb')

  //   b.forEach((bItem) => {
  //     const aItem = a.find((item) => item.id === bItem.id)

  //     if (aItem) {
  //       // 检查最外层的数据是否有变化
  //       if (aItem.content !== bItem.content || aItem.img !== bItem.img || aItem.sku_name !== bItem.sku_name) {
  //         bItem.is_update = 1
  //       }
  //       const aDetailResp = aItem.detail_resp || [] // 如果为 null，使用空数组
  //       const bDetailResp = bItem.detail_resp || [] // 如果为 null，使用空数组

  //       // 检查 detail_resp 中的数据是否有变化
  //       bDetailResp.forEach((bDetail, index) => {
  //         const aDetail = aDetailResp[index]
  //         if ((aDetail && (aDetail.content !== bDetail.content || aDetail.img !== bDetail.img)) || !aDetail) {
  //           bDetail.is_update = 1
  //         }
  //       })
  //     }
  //   })
  // }

  const saveFormData = () => {
    let copyFansUnm = cloneDeep(state.fansNum)
    copyFansUnm = {
      man_num: copyFansUnm.man_num || 0,
      girl_num: copyFansUnm.girl_num || 0,
      old_num: copyFansUnm.old_num || 0
    }
    const params = { ...state.form, ...copyFansUnm }
    console.log('submit', params)
    if (props.pageType === 'comments') {
      ;(params.details_info || []).forEach((v) => {
        v.is_comment = v.is_comment ? 1 : 0
        v.sort = v.sort || 0
      })
      params.details_info.sort((a, b) => {
        return b.sort - a.sort;
      });
      addComment({ ...params })
      // if (['batch', 'edit'].includes(props.type)) {
      //   editComment({ id: props.item?.id, ...params })
      // } else {
      //   addComment({ ...params })
      // }
    } else {
      // if (['batch', 'edit'].includes(props.type)) {
      //   editQa({ id: props.item?.id, ...params })
      // } else {
      //   addQa({ ...params })
      // }
    }
  }

  // 添加问答
  // const addQa = async (params) => {
  //   try {
  //     state.loading = true
  //     params = {
  //       ...params,
  //       question_temp_ids: state.import_ids.join(','),
  //       product_library_id: +props.id,
  //       details_info: resetChildrenField(params.details_info)
  //     }
  //     params.details_info = convertCreateAtToTimestamp(params.details_info, 'stamp')
  //     params.details_info.forEach((item) => {
  //       item.detail_resp.forEach((it) => {
  //         it.avatar = it.user_type == 2 ? it.avatar : ''
  //         it.user_name = it.user_type == 2 ? it.user_name : ''
  //       })
  //     })
  //     const res = await add_question(params)
  //     message.success(res.msg)
  //     emit('event', { cmd: 'edit' })
  //   } catch (error) {
  //     console.error(error)
  //   } finally {
  //     state.loading = false
  //   }
  // }
  // 编辑问答
  // const editQa = async (params) => {
  //   try {
  //     state.loading = true
  //     const newData = resetChildrenField(params.details_info)
  //     params = {
  //       ...params,
  //       product_library_id: +props.id,
  //       details_info: convertCreateAtToTimestamp(newData, 'stamp')
  //     }
  //     updateIsUpdateFlag(state.oldData, params.details_info)
  //     params.details_info.forEach((item) => {
  //       item.detail_resp.forEach((it) => {
  //         it.avatar = it.user_type == 2 ? it.avatar : ''
  //         it.user_name = it.user_type == 2 ? it.user_name : ''
  //       })
  //     })
  //     const res = await edit_question(params)
  //     message.success(res.msg)
  //     emit('event', { cmd: 'edit' })
  //   } catch (error) {
  //     console.error(error)
  //   } finally {
  //     state.loading = false
  //   }
  // }
  // 添加评论
  const addComment = async (params) => {
    try {
      state.loading = true
      params = {
        ...params,
        comment_temp_ids: state.import_ids.join(','),
        woman_num: params.girl_num,
        product_library_id: +props.id,
        details_info: resetChildrenField(params.details_info)
      }
      // if (params.is_bind_product == 2) {
      //   params.product_image = ''
      //   params.product_id = 0
      //   params.product_code = ''
      // }
      params.details_info = convertCreateAtToTimestamp(params.details_info, 'stamp')
      console.log('params', state.templateName, params)
      
      let res = null
      if (props.type === 'add') {
        res = await add_comment({
          title: state.templateName,
          num: params.details_info.length,
          man_num: params.man_num,
          woman_num: params.girl_num,
          mix_num: params.old_num,
          comment_content: JSON.stringify(params.details_info)
        })
      } else {
        res = await update_comment({
          id: props.item?.id,
          title: state.templateName,
          num: params.details_info.length,
          man_num: params.man_num,
          woman_num: params.girl_num,
          mix_num: params.old_num,
          comment_content: JSON.stringify(params.details_info)
        })
      }
      message.success(res.msg)
      emit('event', { cmd: 'edit' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  // 编辑评论
  // const editComment = async (params) => {
  //   try {
  //     state.loading = true
  //     const newData = resetChildrenField(params.details_info)
  //     params = {
  //       ...params,
  //       product_library_id: +props.id,
  //       details_info: convertCreateAtToTimestamp(newData, 'stamp')
  //     }
  //     updateIsUpdateFlag(state.oldData, params.details_info)
  //     const res = await edit_comment(params)
  //     message.success(res.msg)
  //     emit('event', { cmd: 'edit' })
  //   } catch (error) {
  //     console.error(error)
  //   } finally {
  //     state.loading = false
  //   }
  // }

  // 处理children字段
  const resetChildrenField = (data) => {
    const cloneData = cloneDeep(data)
    let arr = []
    cloneData.forEach((v) => {
      const obj = { ...v }
      if (v.children) {
        delete obj.children
        obj.detail_resp = resetChildrenField(v.children)
      }
      arr.push(obj)
    })
    return arr
  }
  // 设置 children 字段
  // const setChildrenField = (data) => {
  //   const cloneData = data ? cloneDeep(data) : []
  //   let arr = []
  //   cloneData.forEach((v) => {
  //     const obj = { ...v }
  //     if (v.detail_resp) {
  //       delete obj.detail_resp
  //       obj.children = resetChildrenField(v.detail_resp)
  //     } else {
  //       obj.children = []
  //     }
  //     arr.push(obj)
  //   })
  //   return arr
  // }

  // onMounted(() => {
  //   initDialig()
  // })
  // 初始化弹框
  // const initDialig = () => {
  //   if (props.type != 'add') {
  //     if (props.pageType === 'comments') {
  //       queryCommentInfo()
  //     } else {
  //       queryQAInfo()
  //     }
  //   }
  // }

  const setKeyId = (data, parent) => {
    const copyData = data ? cloneDeep(data) : []
    copyData.forEach((v) => {
      v.key = randomName(1, 10)
      v.item_id = randomString()
      v.is_comment = v.is_comment || 0
      v.channel_type = v.channel_type || 1
      if (parent) {
        v.parent_item_id = parent.item_id
        v.parentId = parent.key
      }
      if (v.children?.length) {
        v.children = setKeyId(v.children, v)
      }
    })
    return copyData
  }

  // 获取问答详情
  // const queryQAInfo = async () => {
  //   try {
  //     const ids = props.type == 'batch' ? props.list.map((item) => item.id).join(',') : props.item?.id
  //     const res = await get_question_info({ ids: ids })
  //     const data = res.data
  //     state.oldData = data.list
  //     const questionList = setKeyId(setChildrenField(data.list), null)
  //     state.form = {
  //       // channel_type: data.channel_type || 1,
  //       details_info: convertCreateAtToTimestamp(questionList, 'date')
  //     }
  //     state.form.details_info.map((item) => {})

  //     state.form.details_info.forEach((item) => {
  //       item.children.forEach((detail) => {
  //         detail.user_type = detail.user_name ? 2 : 1
  //       })
  //     })
  //     state.fansNum = {
  //       man_num: data.man_num,
  //       girl_num: data.girl_num,
  //       old_num: data.old_num
  //     }
  //   } catch (error) {
  //     console.error(error)
  //   }
  // }
  // 获取评论详情
  // const queryCommentInfo = async () => {
  //   try {
  //     const ids = props.type == 'batch' ? props.list.map((item) => item.id).join(',') : props.item?.id
  //     const res = await get_comment_info({ ids: ids })
  //     const data = res.data
  //     state.oldData = data.list
  //     data.list.forEach((item) => {
  //       item.sku_name = item.sku_name.replace(/[$&]/g, '')
  //     })
  //     const commentList = setKeyId(setChildrenField(data.list), null)
  //     state.form = {
  //       details_info: convertCreateAtToTimestamp(commentList, 'date')
  //     }

  //     if (state.form.product_id) {
  //       const { data } = await getProductSku(state.form.product_id)
  //       state.goodsInfo.sku_list = data
  //     }
  //   } catch (error) {
  //     console.error(error)
  //   }
  // }
  // const changeBind = (e) => {
  //   if (e.target.value == 1 && !state.form.product_id) {
  //     state.dialog = {
  //       visible: true,
  //       title: '选择落地页',
  //       width: 690,
  //       type: 'goods'
  //     }
  //   } else if (e.target.value == 1 && state.form.product_id) {
  //     addSku(state.goodsInfo.sku_list)
  //   } else {
  //     state.form.details_info.forEach((item) => {
  //       item.sku_name = ''
  //     })
  //   }
  // }
  // const resetGoodsInfo = () => {
  //   state.form.details_info.forEach((item) => {
  //     item.sku_name = ''
  //   })
  //   state.form.product_image = ''
  //   state.form.product_id = ''
  //   state.form.product_code = ''
  // }
  // const changeGoods = () => {
  //   state.dialog = {
  //     visible: true,
  //     title: '选择落地页',
  //     width: 602,
  //     type: 'goods'
  //   }
  // }
  // const onUpload = (data) => {
  //   if (data.cmd == 'import') {
  //     state.uploadStatus = 'uploading'
  //     state.dialog = {
  //       visible: true,
  //       title: '',
  //       width: 460,
  //       type: 'status'
  //     }
  //     console.log(state.uploadStatus, '1111111111')
  //     state.currentIndex = data.data.currentIndex
  //     uploadCb(data.data.file, 'add', data.data.resData)
  //   } else {
  //     state.dialog.visible = false
  //   }
  // }
  // const sureUpload = (data) => {
  //   if (data.cmd == 'sure') {
  //     state.dialog.visible = false

  //     const newData = setAvatar(data.data)
  //     const uploadData = setKeyId(setChildrenField(newData), null)
  //     state.form.details_info.push(...uploadData)
  //     if (props.pageType == 'comments' && state.goodsInfo.sku_list.length) {
  //       addSku(state.goodsInfo.sku_list)
  //     }
  //   } else {
  //     state.dialog.visible = false
  //     state.uploadStatus == 'uploading'
  //   }
  // }
  // const uploadCb = async (
  //   _file: any,
  //   type: any,
  //   res: { type?: 'success'; content: any; video?: { isVideo: boolean; src: string; cover: string } | undefined }
  // ) => {
  //   try {
  //     const result =
  //       props.pageType == 'comments'
  //         ? await importComment({
  //             file_url: res.content
  //           })
  //         : await import_question({
  //             file_url: res.content
  //           })
  //     if (result.code === 0) {
  //       state.uploadStatus = 'success'
  //       state.uploadData = result.data
  //       state.uploadData.total_num = state.uploadData.success_num + state.uploadData.error_num
  //       state.uploadData.data =
  //         state.uploadData.data &&
  //         state.uploadData.data.map((item) => {
  //           return {
  //             ...item,
  //             channel_type: platformList[state.currentIndex].value
  //           }
  //         })
  //     }
  //   } catch (error) {
  //     state.dialog.visible = false
  //     console.error(error)
  //   }
  // }

  // const generateRandomNumbers = (total) => {
  //   const manNum = Math.floor(Math.random() * total)
  //   const girlNum = Math.floor(Math.random() * (total - manNum))
  //   const oldNum = total - manNum - girlNum
  //   return ref({
  //     man_num: manNum,
  //     girl_num: girlNum,
  //     old_num: oldNum,
  //     woman_num: undefined
  //   })
  // }
  // const createDate = (startDate, type) => {
  //   let [sDate, eDate] = fansRef.value.create_time
  //   sDate = moment(sDate).valueOf()
  //   eDate = moment(eDate).valueOf()
  //   if (type == 'random') {
  //     const randomNewDate = moment(randomDate(sDate, eDate) * 1000).format('YYYY-MM-DD HH:mm:ss')
  //     console.log(randomNewDate, 'randomNewDate')

  //     const newDate = generateWithinTwoDays(randomNewDate, moment().unix())
  //     console.log(newDate, 'newDate')
  //     return moment(newDate * 1000).format('YYYY-MM-DD HH:mm:ss')
  //   } else {
  //     const newDate = generateWithinTwoDays(startDate, moment().unix())
  //     return moment(newDate * 1000).format('YYYY-MM-DD HH:mm:ss')
  //   }
  // }
  // const setAvatar = (data) => {
  //   const total = data.length
  //   // 评论
  //   const list = fansRef.value.setDataList(generateRandomNumbers(total))
  //   // 回复
  //   const list2 = fansRef.value.setDataList(generateRandomNumbers(total))

  //   list.forEach((item, index) => {
  //     data[index].avatar = item.avatar
  //     data[index].user_name = data[index].user_name || item.user_name
  //     data[index].created_at = createDate(undefined, 'random')
  //     if (Array.isArray(data[index].detail_resp) && data[index].detail_resp.length > 0) {
  //       data[index].detail_resp.forEach((child) => {
  //         // 追评
  //         if (child.is_comment == 1) {
  //           child.avatar = item.avatar
  //           child.user_name = child.user_name || item.user_name
  //           child.created_at = createDate(data[index].created_at, 'child')
  //         }
  //       })
  //     }
  //   })

  //   list2.forEach((item, index) => {
  //     if (Array.isArray(data[index].detail_resp) && data[index].detail_resp.length > 0) {
  //       data[index].detail_resp.forEach((child) => {
  //         // 回复
  //         if (child.is_comment == 0 && props.pageType === 'comments') {
  //           child.avatar = item.avatar
  //           child.user_name = child.user_name || item.user_name
  //           child.created_at = createDate(data[index].created_at, 'child')
  //         }
  //       })
  //     }
  //   })
  //   if (props.pageType === 'QA') {
  //     const child_total = data.reduce((acc, item) => acc + (item.detail_resp ? item.detail_resp.length : 0), 0)
  //     const child_list = fansRef.value.setDataList(generateRandomNumbers(child_total))
  //     console.log(child_list, 'child_list')
  //     // const getRandomAvatar = () => child_list[Math.floor(Math.random() * child_list.length)].avatar
  //     // const getRandomName = () => child_list[Math.floor(Math.random() * child_list.length)].user_name
  //     // 问答添加回复
  //     data.forEach((item, index) => {
  //       item.detail_resp.forEach((child, childIndex) => {
  //         child.user_type = child.user_name ? 2 : 1
  //         child.user_name = child.user_name || child_list[Math.floor(Math.random() * child_list.length)].user_name
  //         child.avatar = child_list[Math.floor(Math.random() * child_list.length)].avatar
  //         child.created_at = createDate(data[index].created_at, 'child')
  //       })
  //     })
  //   }
  //   return data
  // }
  // const onEvent = (data) => {
  //   if (data.cmd == 'addGoods') {
  //     const info = data.data.info
  //     state.goods_detail = data.data.info
  //     state.form.product_image = info.image
  //     state.form.product_name = info.title
  //     state.form.product_code = info.code
  //     state.form.product_id = info.id
  //     state.goodsInfo.sku_list = data.data.sku_list
  //     // if (data.data?.sku_list.length) {
  //     addSku(data.data.sku_list)
  //     // }
  //   } else if (data.cmd == 'importData') {
  //     console.log(data, 'datadatadatadatadata')
  //     state.import_ids.push(data.data.id)
  //     importOldData(data.data.id)
  //   } else {
  //     console.log(data.data, 'data.data')
  //     if (!data.data?.info && !data.data?.length) {
  //       state.form.is_bind_product = 2
  //     }
  //   }
  //   state.dialog.visible = false
  // }

  // 获取评论详情
  // const importOldData = async (id) => {
  //   try {
  //     const res =
  //       props.pageType == 'comments' ? await getCommentTempDetail({ id: id }) : await getQuestionTempDetail({ id: id })
  //     const data = res.data

  //     const importList = setKeyId(setChildrenField(data.details), null)
  //     reCreateDate(importList)
  //     if (props.pageType == 'QA') {
  //       importList.forEach((item) => {
  //         item.children.forEach((it) => {
  //           it.user_type = it.user_name ? 2 : 1
  //         })
  //       })
  //     }

  //     state.form = {
  //       is_bind_product: data.is_bind_product,
  //       product_id: data.product_id,
  //       product_code: data.product_code,
  //       product_image: data.product_image,
  //       product_name: data.product_name,
  //       channel_type: data.channel_type || 1,
  //       details_info: [...state.form.details_info, ...importList] // 合并数组
  //     }
  //     if (state.form.product_id) {
  //       const { data } = await getProductSku(state.form.product_id)
  //       state.goodsInfo.sku_list = data
  //     }
  //   } catch (error) {
  //     console.error(error)
  //   }
  // }
  // const reCreateDate = (list) => {
  //   list.forEach((item, index) => {
  //     item.created_at = createDate(undefined, 'random')
  //     console.log(item.created_at, 'item.created_atitem.created_at')

  //     if (Array.isArray(item.children) && item.children.length > 0) {
  //       item.children.forEach((child) => {
  //         // 追评
  //         child.created_at = createDate(item.created_at, 'child')
  //         console.log(child.created_at, 'child.created_at')
  //       })
  //     }
  //   })
  // }
  // const addSku = (sku) => {
  //   state.form.details_info = state.form.details_info.map((item) => {
  //     return {
  //       ...item,
  //       sku_name: sku && sku.length ? sku[Math.floor(Math.random() * sku.length)].title : ''
  //     }
  //   })
  // }
  // const showUploadDialog = (type) => {
  //   if (type == 'upload') {
  //     state.dialog = {
  //       visible: true,
  //       title: `导入${pageTypeFilter()}`,
  //       width: 576,
  //       type: 'upload'
  //     }
  //   } else {
  //     state.dialog = {
  //       visible: true,
  //       title: `${pageTypeFilter()}`,
  //       width: 800,
  //       type: 'list'
  //     }
  //   }
  // }
</script>

<style lang="scss" scoped>
  // @import './src/assets/css/mixin_scss_fn';
  .add_group {
    .group_wrapper {
      &_left {
        width: 300px;
      }
      &_right {
        flex: 1;
        // overflow-x: auto;
        width: 380px;
      }
      .wrapper_block {
        &_title {
          margin-bottom: 15px;
          font-size: 16px;
          padding-left: 12px;
          position: relative;
          display: flex;
          align-items: center;
          &::before {
            content: '';
            width: 4px;
            height: 16px;
            border-radius: 2px;
            display: inline-block;
            background-color: var(--primary-color);
            position: absolute;
            left: 0;
          }
        }
      }
    }
  }
  .item-name {
    border-radius: 8px;
    border: 1px solid #eaebed;
    padding: 12px 16px 16px;
    min-height: 470px;
    height: calc(100% - 66px);
  }
  // .goods_info {
  //   .img_item {
  //     width: 60px;
  //     height: 60px;
  //     background: #bec6d6;
  //     border-radius: 6px;
  //   }

  //   // .goods_info_data {
  //   //   flex: 1;
  //   // }

  //   &_data {
  //     margin-left: 8px;
  //     font-family: PingFang SC;
  //     font-weight: 400;

  //     &_name {
  //       overflow: hidden; //多出的隐藏
  //       text-overflow: ellipsis; //多出部分用...代替
  //       display: -webkit-box; //定义为盒子模型显示
  //       -webkit-line-clamp: 1; //用来限制在一个块元素显示的文本的行数
  //       -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
  //       font-size: 12px;
  //       color: #313233;
  //       width: 138px;
  //     }

  //     &_number {
  //       font-size: 12px;
  //       color: #7a869f;
  //     }
  //   }
  // }
</style>
