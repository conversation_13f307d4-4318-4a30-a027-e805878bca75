<template>
  <div class="qd_Edit">
    <a-form
      :model="data.addForm"
      :rules="rulesEdit"
      :labelCol="type == 'reply' ? { style: 'width: 107px' } : { style: 'width: 79px' }"
      ref="ruleForm"
    >
      <div class="form_div">
        <a-form-item label="评价内容:" name="">
          <div class="bg-#F4F6F9 p-8px border-ra-6px">
            <div>
              {{ item.content }}
            </div>
            <!-- <fileUpload
              v-if="data.images"
              :size="0"
              :isEdit="false"
              v-model="data.images"
              class="main-img mt-8px"
            ></fileUpload> -->
          </div>
        </a-form-item>
        <a-form-item label="追评时间" name="created_time">
          <a-date-picker
            v-model:value="data.addForm.created_time"
            show-time
            :allowClear="false"
            :disabled-date="disabledChildDate(item.created_at)"
            :disabled-time="disabledChildDateTime(item.created_at)"
            format="YYYY-MM-DD HH:mm:ss"
            valueFormat="YYYY-MM-DD HH:mm:ss"
          />
        </a-form-item>
        <a-form-item label="追评内容" required>
          <a-form-item name="content" class="mb-0">
            <a-textarea
              class="form-width"
              v-model:value.trim="data.addForm.content"
              :rows="4"
              resize="none"
              maxlength="100"
              showCount
              :placeholder="请输入追评的内容"
              show-word-limit
              type="textarea"
            />
          </a-form-item>
          <!-- <a-form-item name="images">
            <Upload v-model="data.addForm.images" :multiple="true" accept=".jpg,.png,.jpeg" :size="10" :max="6">
            </Upload>
            <template #extra>最多可以上传6张图片</template>
          </a-form-item> -->
        </a-form-item>
      </div>
    </a-form>
    <div class="footer_button">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" :loading="data.loading" @click="submit">确认</a-button>
    </div>
  </div>
</template>

<script setup name="QaEdit">
  import { reactive, ref } from 'vue'
  import { append_resp, update_append_comment } from '../index.api.ts'
  import { message, Modal } from 'ant-design-vue'
  import { generateWithinTwoDays, randomName } from './QaComments/src/generation'
  import { cloneDeep } from 'lodash-es'
  // import fileUpload from './QaComments/components/upload.vue'
  import moment from 'moment'
  import datas from '../src/data'
  const { disabledChildDate, disabledChildDateTime } = datas()
  const emit = defineEmits(['onEvent'])
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'append'
    }
  })
  const ruleForm = ref(null)
  const rulesEdit = {
    content: [{ required: true, message: '请输入追评的内容', trigger: ['change'] }]
  }
  const data = reactive({
    addForm: {
      created_time: undefined,
      content: ''
      // images: []
    },
    // images: Array.isArray(props.item?.images) && props.item?.images?.join(','),
    loading: false,
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    },
    product: {} // 商品信息
  })

  const formatDate = (stamp) => {
    return moment(stamp * 1000).format('YYYY-MM-DD HH:mm:ss')
  }
  const initData = () => {
    // console.log('moment(startDate).valueOf()', moment(props.item.created_at * 1000).format('YYYY-MM-DD HH:mm:ss'))
    // if (props.item?.id) {
    data.addForm.created_time = props.item.detail_resp[0]?.created_at
      ? formatDate(generateWithinTwoDays(props.item.detail_resp[0].created_at, moment().unix()))
      : formatDate(generateWithinTwoDays(props.item.created_at * 1000, moment().unix()))
    data.addForm.content = props.item.detail_resp[0]?.content || ''
    // }
  }
  initData()
  // 提交数据
  async function submit() {
    try {
      data.loading = true
      await ruleForm.value.validate()
      let p = {
        user_name: props.item.user_name,
        avatar: props.item.avatar,
        content: data.addForm.content,
        type: props.item.type,
        img: '',
        is_comment: 1,
        is_new: true,
        created_at: formatDate(generateWithinTwoDays(props.item.created_at, moment().unix())),
        created_time: moment(data.addForm.created_time, 'YYYY-MM-DD HH:mm:ss').unix(),
        key: randomName(1, 10),
        parentKey: props.item.key,
        parent_item_id: props.item.item_id,
        parentId: props.item.key
      }
      message.success('追评添加成功')
      emit('onEvent', { cmd: 'submit', data: [p] })
    } catch (error) {
      console.error(error)
    } finally {
      data.loading = false
    }
  }
  const close = () => {
    emit('onEvent', { cmd: 'close' })
  }
</script>

<style lang="scss" scoped>
  .fooler_btn {
    text-align: end;
    margin-right: 20px;
  }

  .footer_button {
    margin-top: 14px;
    text-align: right;
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #85878a;
    margin-top: 10px;
    line-height: 1;
  }
</style>
