<template>
  <div class="qd_Edit">
    <a-form :model="data.addForm" :rules="rulesEdit" ref="ruleForm">
      <div class="form_div">
        <a-form-item label="评价内容:" name="">
          <div class="bg-#F4F6F9 p-8px">
            <div>
              {{ item.content }}
            </div>
            <!-- <fileUpload :size="0" :isEdit="false" v-model="data.images" class="main-img mt-8px"></fileUpload> -->
          </div>
        </a-form-item>
        <TableZebraCrossing :data="data.tableConfig" @change="pageChange">
          <template #bodyCell="{ scope, data }">
            <template v-if="scope.column.key === 'appraise'">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.content }}</template>
                  <div class="text_overflow_row1">
                    {{ scope.record.content }}
                  </div>
                </a-tooltip>
                <!-- <a-image-preview-group>
                  <a-space v-if="scope.record.images.length" :size="[16, 0]">
                    <div class="table_appraise_box" v-for="(item, index) in scope.record.images" :key="index">
                      <a-image width="50px" height="50px" :preview-src-list="scope.record.images" :src="item" />

                      <div v-if="index == 3 && scope.record.images.length > 4" class="table_appraise_imgMark">
                        +{{ scope.record.images.length - 4 }}
                      </div>
                    </div>
                  </a-space>
                </a-image-preview-group> -->
              </div>
            </template>
            <template v-if="scope.column.key === 'date'">
              <span> {{ dayjs(scope.record.created_time * 1000).format('YYYY-MM-DD hh:mm:ss') }}</span>
            </template>
          </template>
        </TableZebraCrossing>
      </div>
    </a-form>
  </div>
</template>

<script setup name="QaEdit">
  import { reactive, ref } from 'vue'
  import { append_resp, get_comment_info } from '../index.api.ts'
  import { message, Modal } from 'ant-design-vue'
  import { formatDate } from '@/utils'
  import { QuestionCircleOutlined } from '@ant-design/icons-vue'
  import fileUpload from './QaComments/components/upload.vue'
  import dayjs from 'dayjs'
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'appendlist'
    }
  })
  const data = reactive({
    images: Array.isArray(props.item?.images) && props.item?.images?.join(','),
    tableConfig: {
      loading: false,
      size: 'small',
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 760
      },
      columns: [
        {
          title: '评价内容',
          key: 'appraise',
          dataIndex: 'appraise',
          width: 260
        },
        {
          title: '追评时间',
          key: 'date',
          dataIndex: 'date',
          width: 180
        }
      ],
      dataSource: [],
      pagination: false
    },
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    },
    product: {} // 商品信息
  })
  const initData = async () => {
    // if (props.item?.id) {
    // const res = await get_comment_info({ ids: props.item.id })

    const type = 1
    // data.mainData = res.data.list
    // if (res.data.list[0].detail_resp) {
    const newData = props.item.detail_resp.filter((item) => item.is_comment == type)
    data.tableConfig.dataSource = (newData || []).map((item) => {
      return {
        ...item,
        create_at: formatDate(item.created_at * 1000),
        images: item.img && item.img.length != 0 ? item.img.split(',') : []
      }
    })
    // }
    // }
  }
  initData()
  const close = () => {
    emit('event', { cmd: 'close' })
  }
</script>

<style lang="scss" scoped>
  .table_appraise_box {
    width: 50px;
    height: 50px;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    .table_appraise_imgMark {
      position: absolute;
      width: 50px;
      height: 50px;
      top: 0px;
      left: 0px;
      text-align: center;
      line-height: 50px;
      font-size: 14px;
      color: #ffffff;
      pointer-events: none;
    }
  }

  .footer_button {
    margin-top: 10px;
    text-align: right;
  }
  :deep(.ant-btn-link) {
    padding: 0 !important;
  }
</style>
