<template>
  <div>
    <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
      <template #headerCell="{ scope: { column } }">
        <template v-if="column.key === 'sort'">
          <div>
            <span>置顶权重</span>
            <a-tooltip>
              <template #title>权重值越大，评论排序越靠前</template>
              <QuestionCircleFilled style="color: #939599" class="m-l-8px" />
            </a-tooltip>
          </div>
        </template>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'created_at'">
          {{ dayjs(scope.record.created_at * 1000).format('YYYY-MM-DD hh:mm:ss') }}
        </template>
        <template v-if="scope.column.key === 'content'">
          <a-tooltip>
            <template #title>{{ scope.record.content }}</template>
            <div class="text_overflow_row1 max-w-140px">{{ scope.record.content }}</div>
          </a-tooltip>
          <a-image v-if="scope.record.img" style="width: 40px" :src="scope.record.images" />
        </template>
        <template v-if="scope.column.key === 'append'">
          <a-button
            :disabled="!scope.record.detail_resp?.length"
            type="link"
            class="pa-0! h-auto!"
            size="small"
            @click="handlerAction('lookDetail', scope.record)"
            >{{ scope.record.detail_resp.length ? '1' : '-' }}</a-button
          >
        </template>
        <template v-if="scope.column.key === 'sort'">
          <span>{{ scope.record.sort }}</span>
          <FormOutlined class="c-primary ml-4px" @click="handlerAction('setWeight', scope.record)" />
        </template>
        <template v-if="scope.column.key === 'action'">
          <a-button
            type="link"
            size="small"
            class="pa-0! h-auto!"
            v-if="!scope.record.detail_resp?.length"
            @click="handlerAction('edit', scope.record)"
            >追评</a-button
          >
          <a-popconfirm
            title="是否确认删除当前数据？"
            placement="topRight"
            @confirm="handlerActionDelete(scope.record)"
          >
            <a-button type="link" size="small" class="pa-0! h-auto!">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </TableZebraCrossing>
    <div class="text-right mt-8px">
      <a-button @click="emits('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">保存</a-button>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      :centered="true"
    >
      <setWeightModal v-if="state.dialog.type === 'setWeight'" :data="state.dialog.data" @onEvent="onEvent" />
      <Reply v-if="state.dialog.type === 'edit'" type="append" :item="state.dialog.data" @onEvent="onEvent" />
      <ReplyDetail v-if="state.dialog.type === 'lookDetail'" :item="state.dialog.data" @onEvent="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import setWeightModal from './setWeightModal.vue'
  import Reply from './replyModal.vue'
  import ReplyDetail from './replyDetailModal.vue'
  import dayjs from 'dayjs'
  import { reactive, onMounted } from 'vue'
  import { FormOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
  import { delete_comment, update_comment } from '../index.api'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  const props = defineProps(['item'])
  const emits = defineEmits(['onEvent'])
  const state = reactive({
    loading: false,
    params: {
      page: 1,
      page_size: 20
    },
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      // size: 'small',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns: [
        {
          title: '评价内容',
          dataIndex: 'content',
          key: 'content',
          minWidth: 200
        },
        {
          title: '追评',
          dataIndex: 'append',
          key: 'append',
          width: 80
        },
        {
          title: '评价时间',
          dataIndex: 'created_at',
          key: 'created_at',
          width: 180
        },
        {
          title: '置顶权重',
          dataIndex: 'sort',
          key: 'sort',
          width: 120
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          width: 120,
          fixed: 'right'
        }
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        current: 1,
        total: 0,
        pageSize: 20,
        size: 'small',
        showTotal: (total: String | Number) => `共${total}条数据`
      }
    },
    // 存储原始数据用于前端分页
    rawDataSource: [],
    dialog: {
      visible: false,
      title: '',
      width: null,
      type: '',
      data: null
    } as any
  })
  const handlerActionDelete = async (item) => {
    try {
      // if (state.rawDataSource.length <= 1) {
      //   message.error('至少保留一条评价')
      //   return
      // }
      const contentList = state.rawDataSource.filter((i) => i.item_id !== item.item_id)
      // await update_comment({
      //   title: props.item.title,
      //   num: contentList.length,
      //   id: props.item.id,
      //   comment_content: JSON.stringify(contentList)
      // })
      state.rawDataSource = contentList
      // 4. 重置分页到第一页
      state.tableConfigOptions.pagination.current = 1

      updateTableData()
    } catch (error) {
      console.error(error)
    }
  }
  const handlerAction = async (type: string, item?: any) => {
    try {
      state.dialog.visible = true
      state.dialog.type = type
      state.dialog.data = item
      if (['lookDetail', 'edit'].includes(type)) {
        state.dialog.width = 860
        state.dialog.title = type === 'edit' ? '追评' : '追评详情'
      } else {
        state.dialog.width = 400
        state.dialog.title = '设置权重'
      }
    } catch (err) {
      console.log(err)
    }
  }
  const handler = (data: any, type: string) => {
    state.rawDataSource.forEach((it: any) => {
      if (type === 'sort' && it.item_id === data?.item_id) {
        it.sort = data.sort
      }
      if (type === 'evaluate' && it.item_id === data?.[0]?.parent_item_id) {
        it.detail_resp = []
        it.detail_resp = data
      }
    })
    console.log('state.rawDataSource---handler', state.rawDataSource)
    state.tableConfigOptions.pagination.current = 1
    updateTableData()
  }
  const submit = async () => {
    try {
      state.rawDataSource = state.rawDataSource.sort((a: any, b: any) => b.sort - a.sort)
      let params = !state.rawDataSource.length
        ? { id: props.item.id, is_deleted: 1 }
        : {
            title: props.item.title,
            num: state.rawDataSource.length,
            id: props.item.id,
            comment_content: state.rawDataSource.length ? JSON.stringify(state.rawDataSource) : undefined
          }
      await update_comment(params)
      message.success('操作成功')
      emits('onEvent', { cmd: 'submit' })
    } catch (err) {
      console.log(err)
    }
  }
  const onEvent = ({ cmd, data }: any) => {
    state.dialog.visible = false
    if (cmd === 'submit') {
      handler(data, 'evaluate')
    } else if (cmd === 'sort') {
      handler(data, 'sort')
    }
  }
  onMounted(() => {
    // getList()
    init()
  })
  // 获取当前页数据
  const getCurrentPageData = () => {
    const { current, pageSize } = state.tableConfigOptions.pagination
    const start = (current - 1) * pageSize
    const end = start + pageSize

    return state.rawDataSource.slice(start, end)
  }
  // 更新表格显示数据
  const updateTableData = () => {
    state.tableConfigOptions.dataSource = getCurrentPageData()
    state.tableConfigOptions.pagination.total = state.rawDataSource.length
    if (state.tableConfigOptions.dataSource.length > 4) {
      state.tableConfigOptions.scroll.y = 350
    } else {
      delete state.tableConfigOptions.scroll.y
    }
  }
  //分页
  const pageChange = (pagination) => {
    state.tableConfigOptions.pagination.current = pagination.current
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    updateTableData()
  }
  const init = () => {
    const { comment_content } = props.item
    state.rawDataSource = JSON.parse(comment_content)
    console.log('state.rawDataSource', state.rawDataSource)

    // 4. 重置分页到第一页
    state.tableConfigOptions.pagination.current = 1
    updateTableData()
  }
</script>
