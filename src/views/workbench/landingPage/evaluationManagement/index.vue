<template>
  <div>
    <DesTablePage>
      <template #title>
        <div>评价管理</div>
      </template>
      <template #extra>
        <a-button v-auth="['templateAdd']" type="primary" @click="handlerAction('add')">新增模板</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'title'">
              <a-tooltip placement="topLeft">
                <template #title v-if="scope.record.title.length > 15">{{ scope.record.title }}</template>
                <div class="text_overflow_row1">{{ scope.record.title }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="handle_btns">
                <div class="flex_align_center">
                  <a-button v-auth="['templateEdit']" type="link" size="small" class="pa-0!" @click="handlerAction('edit', scope.record)"
                    >编辑</a-button
                  >
                  <a-popconfirm
                    title="是否确认删除当前数据？"
                    placement="topRight"
                    @confirm="handlerAction('delete', scope.record)"
                  >
                    <a-button v-auth="['templateDel']" type="link" size="small" class="pa-0! ml-10px">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      :centered="true"
    >
      <template #title>
        <div class="flex" style="align-items: end">
          <div>{{ state.dialog.title }}</div>
          <div class="c-#656D7D font-size-12px ml-8px" v-if="state.dialog.type === 'look' && state.dialog.data?.name">
            {{ state.dialog.data?.name }}
          </div>
        </div>
      </template>
      <QaComments
        v-if="['add', 'edit'].includes(state.dialog.type)"
        :item="state.dialog.data"
        :type="state.dialog.type"
        pageType="comments"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import datas from './src/data'
  import lookEvaluationModal from './components/lookEvaluationModal.vue'
  import QaComments from './components/QaComments/index.vue'
  const { pageChange, searchForm, searchConfig, state, getList, handlerAction, onEvent } = datas()
  getList()
</script>

<style lang="scss" scoped></style>
