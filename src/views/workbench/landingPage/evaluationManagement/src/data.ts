import { onMounted, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { getCommentList, delete_comment, update_comment } from '../index.api'
import moment from 'moment'
export default function datas() {
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'title',
        value: undefined,
        props: {
          placeholder: '请输入评价模板'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '评价模板',
        dataIndex: 'title',
        key: 'title',
        minWidth: 200
      },
      {
        title: '评价数量',
        dataIndex: 'num',
        key: 'num',
        minWidth: 120
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 220,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      current: 1,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const state = reactive({
    tableData: [],
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      title: '',
      width: null,
      type: '',
      data: null
    } as any
  })
  // 获取列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let res = await getCommentList(state.params)
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    state.params = {
      ...state.params,
      ...v.formData
    }
    state.params.page = 1
    getList()
  }
  const handlerAction = async (type: string, item?: any) => {
    try {
      if (type === 'delete') {
        await update_comment({ id: item.id, is_deleted: 1 })
        message.success('删除成功')
        getList()
      } else {
        state.dialog.visible = true
        state.dialog.width = 1200
        state.dialog.type = type
        state.dialog.data = type === 'add' ? undefined : item
        state.dialog.title = type === 'add' ? '添加评论' : '编辑评论'
      }
    } catch (err) {
      console.log(err)
    }
  }
  const disabledChildDate = (created_at) => {
    const startDate = moment(created_at) // 将 startDate 设置为当天的开始时间
    return (currentDate) => {
      return currentDate && (currentDate > moment() || currentDate < startDate)
    }
  }
  const disabledChildDateTime = (created_at) => {
    return (date) => {
      const startDate = moment(created_at).add(1, 'second') // 将开始时间推迟一秒
      const endDate = moment()
      if (date && date.isSame(moment(), 'day')) {
        if (startDate.isSame(endDate, 'day')) {
          console.log('startDate 和 endDate 是同一天')

          const startHours = startDate.hours()
          const startMinutes = startDate.minutes()
          const startSeconds = startDate.seconds()
          const endHours = endDate.hours()
          const endMinutes = endDate.minutes()
          const endSeconds = endDate.seconds()

          return {
            // 禁用开始时间之前的小时和结束时间之后的小时
            disabledHours: () => {
              const disabledHours = [...Array(24)].map((_, i) => i)
              // 禁用开始时间之前的小时
              const disabledBeforeStart = disabledHours.slice(0, startHours)
              // 禁用结束时间之后的小时
              const disabledAfterEnd = disabledHours.slice(endHours + 1)
              return [...disabledBeforeStart, ...disabledAfterEnd] // 合并禁用的小时
            },
            disabledMinutes: (selectedHour: any) => {
              if (selectedHour < startHours || selectedHour > endHours) {
                return [...Array(60)].map((_, i) => i) // 禁用所有分钟
              }
              if (selectedHour === startHours) {
                // 禁用开始时间之前的分钟
                return [...Array(60)].map((_, i) => i).slice(0, startMinutes) // 允许选择当前分钟及其之后的分钟
              }
              if (selectedHour === endHours) {
                // 禁用结束时间之后的分钟
                return [...Array(60)].map((_, i) => i).slice(endMinutes + 1) // 允许选择当前分钟及其之前的分钟
              }
              return [] // 允许选择所有分钟
            },
            disabledSeconds: (selectedHour: any, selectedMinute: any) => {
              if (
                selectedHour < startHours ||
                (selectedHour === startHours && selectedMinute < startMinutes) ||
                selectedHour > endHours ||
                (selectedHour === endHours && selectedMinute > endMinutes)
              ) {
                return [...Array(60)].map((_, i) => i) // 禁用所有秒
              }
              if (selectedHour === startHours && selectedMinute === startMinutes) {
                // 禁用开始时间之前的秒
                return [...Array(60)].map((_, i) => i).slice(0, startSeconds) // 允许选择当前秒及其之后的秒
              }
              if (selectedHour === endHours && selectedMinute === endMinutes) {
                // 禁用结束时间之后的秒
                return [...Array(60)].map((_, i) => i).slice(endSeconds + 1) // 允许选择当前秒及其之前的秒
              }
              return [] // 允许选择所有秒
            }
          }
        } else {
          console.log('startDate 和 endDate 不是同一天')
          const hours = moment().hours()
          const minutes = moment().minutes()
          const seconds = moment().seconds()
          return {
            // 禁用当前小时之前的小时
            disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
            // 允许选择当前小时及其之后的小时
            disabledMinutes: (selectedHour: any) =>
              selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [], // 允许选择当前分钟及其之后的分钟
            disabledSeconds: (selectedHour: any, selectedMinute: any) =>
              selectedHour === hours && selectedMinute === minutes
                ? [...Array(60)].map((_, i) => i).slice(seconds + 1) // 允许选择当前秒及其之后的秒
                : []
          }
        }
      }
      // 限制时间在 startDate 和 endDate 之间
      if (date && date.isSame(startDate, 'day')) {
        console.log('限制时间在 startDate 和 endDate 之间限制时间在 startDate 和 endDate 之间')
        return {
          disabledHours: () => [...Array(24)].map((_, i) => i).slice(0, startDate.hours()),
          disabledMinutes: (selectedHour: any) =>
            selectedHour === startDate.hours() ? [...Array(60)].map((_, i) => i).slice(0, startDate.minutes()) : [],
          disabledSeconds: (selectedHour: any, selectedMinute: any) =>
            selectedHour === startDate.hours() && selectedMinute === startDate.minutes()
              ? [...Array(60)].map((_, i) => i).slice(0, startDate.seconds())
              : []
        }
      }
      if (date && date.isSame(endDate, 'day')) {
        return {
          disabledHours: () => [...Array(24)].map((_, i) => i).slice(endDate.hours() + 1),
          disabledMinutes: (selectedHour: any) =>
            selectedHour === endDate.hours() ? [...Array(60)].map((_, i) => i).slice(endDate.minutes() + 1) : [],
          disabledSeconds: (selectedHour: any, selectedMinute: any) =>
            selectedHour === endDate.hours() && selectedMinute === endDate.minutes()
              ? [...Array(60)].map((_, i) => i).slice(endDate.seconds() + 1)
              : []
        }
      } // 如果选择的日期在范围内，禁用时间
      if ((date && date.isBefore(startDate)) || (date && date.isAfter(endDate))) {
        return {
          disabledHours: () => [...Array(24)].map((_, i) => i),
          disabledMinutes: () => [...Array(60)].map((_, i) => i),
          disabledSeconds: () => [...Array(60)].map((_, i) => i)
        }
      }

      return {}
    }
  }
  const onEvent = ({ cmd }: any) => {
    state.dialog.visible = false
    if (cmd === 'edit' || cmd === 'submit') {
      getList()
    }
  }
  return {
    pageChange,
    searchForm,
    searchConfig,
    state,
    handlerAction,
    getList,
    disabledChildDate,
    disabledChildDateTime,
    onEvent
  }
}
