import http from '@/utils/request'

/**
 * 表单列表
 */
export const getFormList = (data) => {
  return http('get', `/common/ad_form/list`, data)
}

/**
 * 表单详情
 */
export const getFormInfo = (data) => {
  return http('get', `/admin/ad_form/info`, data)
}

// 删除评论
export const delete_comment = (data: any) => {
  return http('post', `/admin/comment/delete_comment`, data)
}
// 增加追评
export const append_resp = (data: any) => {
  return http('post', `/admin/comment/append_resp`, data)
}
// 获取批量编辑详情
export const get_comment_info = (data: any) => {
  return http('get', `/admin/productLibrary/get_comment_info`, data)
}

// 设置权重
export const set_sort = (data: any) => {
  return http('post', `/admin/comment/update_comment_sort`, data)
}
export const post_comment_resp = (data: any) => {
  return http('post', `/admin/comment/resp`, data)
}
// 修改追评内容
export const update_append_comment = (data: any) => {
  return http('post', `/admin/productLibrary/update_append_comment`, data)
}

/**
 * 导入问答组
 */
export const import_question = (data: any) => {
  return http('post', `/shop-admin/question_temp/import_question`, data)
}

import axios, { Canceler } from 'axios'
import { isFunction } from 'lodash-es'
/**
 * 获取评论组列表
 */
export const getCommentTempList = (data: any) => {
  return http('get', `/shop-admin/comment_temp/list`, data)
}

/**
 * 获取评论组详情
 */
export const getCommentTempDetail = (data: any) => {
  return http('get', `/shop-admin/comment_temp/detail`, data)
}

/**
 * 创建评论组
 */
export const createCommentTemp = (data: any) => {
  return http('post', `/shop-admin/comment_temp/create`, data)
}

/**
 * 修改评论组
 */
export const updateCommentTemp = (data: any) => {
  return http('post', `/shop-admin/comment_temp/update`, data)
}

/**
 * 复制评论组
 */
export const copyCommentTemp = (data: any) => {
  return http('post', `/shop-admin/comment_temp/copy`, data)
}

/**
 * 删除评论组
 */
export const deleteCommentTemp = (data: any) => {
  return http('post', `/shop-admin/comment_temp/delete`, data)
}

const CancelToken = axios.CancelToken
let cancel: Canceler
/**
 * 导入评论组
 */
export const importComment = (data: any) => {
  return http('post', `/shop-admin/comment_temp/importComment`, data, {
    cancelToken: new CancelToken((c) => {
      cancel = c
    })
  })
}
/**
 * 取消评论组导入
 */

export const fetchImportCommentCancel = () => {
  if (isFunction(cancel)) {
    console.log('用户手动取消上传评论')
    cancel('用户手动取消上传')
  }
}

// 创建商品库评价
export const add_comment = (data) => {
  return http('post', `/admin/comment/add`, data)
}
// 编辑商品库评价
export const update_comment = (data) => {
  return http('post', `/admin/comment/update`, data)
}
// 保存商品库问答
export const add_question = (data) => {
  return http('post', `/shop-admin/productLibrary/question/save`, data)
}
// 保存编辑评论数据
export const edit_comment = (data) => {
  return http('post', `/shop-admin/productLibrary/edit_comment`, data)
}

/**
 * 获取问答组详情
 */
export const getQuestionTempDetail = (data: any) => {
  return http('get', `/shop-admin/question_temp/detail`, data)
}

// 获取问答列表
export const get_question_info = (data) => {
  return http('get', `/shop-admin/productLibrary/get_question_info`, data)
}

// 编辑商品库问答
export const edit_question = (data) => {
  return http('post', `/shop-admin/productLibrary/edit_question`, data)
}

// 智能评价
export const gen_comment = (data) => {
  return http('post', `/shop-admin/productLibrary/gen_comment`, data)
}

// 获取评论列表
export const getCommentList = (data?: any) => {
  return http('get', `/admin/comment/list`, data)
}
