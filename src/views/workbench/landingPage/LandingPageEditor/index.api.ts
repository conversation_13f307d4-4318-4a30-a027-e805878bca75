// 导出请求封装方法
import http from '@/utils/request'

// h5详情
export const setAdH5Info = (data: any) => {
  return http('get', `/admin/ad_h5/info`, data)
}

// h5添加修改
export const setAddAdH5 = (data: any) => {
  return http('post', `/admin/ad_h5/add`, data)
}

// 获取用户记录
export const setAdUserInfo = (data: any) => {
  return http('get', `/admin/ad/user`, data)
}

// 评价详情
export const setCommentInfo = (data: any) => {
  return http('get', `/admin/comment/info`, data)
}
