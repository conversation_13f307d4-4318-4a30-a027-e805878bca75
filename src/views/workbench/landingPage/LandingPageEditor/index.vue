<template>
  <a-spin class="landing-spin" size="large" :spinning="false">
    <a-layout v-if="!state.fullscreenLoading" class="app_landing">
      <a-layout-header>
        <Header
          :bodyloading="state.bodyloading"
          :activity-data="store.getActivityData"
          @saveActivity="saveActivity"
          @toPreview="toPreview"
        />
      </a-layout-header>
      <a-layout>
        <a-layout-sider class="left-panel" width="400px">
          <LeftAside ref="leftAside" />
        </a-layout-sider>
        <a-layout-content class="main-area scroll-edit-area">
          <MainEdit ref="mainEdit" />
        </a-layout-content>
        <a-layout-sider
          class="right-panel"
          :width="['NgQaChat'].includes(store.getActiveItems.name) ? '801px' : '361px'"
        >
          <RightAside ref="rightAside" />
        </a-layout-sider>
      </a-layout>
    </a-layout>
    <a-layout class="read-argee" v-if="!state.fullscreenLoading">
      <div :class="['read-content', { 'spin-blink': state.spinblink }]">
        <a-checkbox v-model:checked="store.getActivityData.checkoutProtocol"></a-checkbox>
        我已阅读并同意以下免责声明;广告落地页展示的所有信息由用户自行提供，其真实性、合法性、有效性由用户负责，斗量智投仅作为技术服务方授权用户使用域名，不对广告发布的后果提供任何保证，并不承担任何法律责任。请阅读并同意<span
          class="ant-btn-link"
          @click="state.dialogProtocolVisible = true"
          >《用户服务协议》</span
        >
      </div>
    </a-layout>

    <a-modal
      v-model:open="state.dialogVisible"
      width="620px"
      :footer="null"
      :closable="false"
      :maskClosable="false"
      centered
      class="landing-preview-wrapper"
    >
      <Preview
        :info="state.info"
        :activity-id="state.activityId"
        :page_name="store.getActivityData.title"
        v-if="state.dialogVisible"
        @close="state.dialogVisible = false"
      />
    </a-modal>

    <a-modal v-model:open="state.dialogProtocolVisible" title="用户协议" width="820px" :footer="null" centered>
      <div class="dialog-agree h-80vh overflow-y-auto">
        <div v-html="protocolText"></div>
      </div>
    </a-modal>
  </a-spin>
</template>

<script setup>
  import { onBeforeMount, reactive } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { cloneDeep } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import html2canvas from 'html2canvas'
  import Header from './wrapper/Header/Index.vue'
  import LeftAside from './wrapper/LeftAside/Index.vue'
  import MainEdit from './wrapper/Edit/Index.vue'
  import RightAside from './wrapper/RightAside/Index.vue'
  import Preview from './preview.vue'
  import { verificationFunc } from './verify.js'
  import { setAdH5Info, setAddAdH5, setCommentInfo } from './index.api.ts'
  import { randomWord, getPageNameFromUrl, sleep } from '@/utils'
  import Cos from '@/utils/cos'
  import { setFormInfo } from '@/views/workbench/landingPage/formEdit/index.api.ts'
  import { protocolText } from '@/views/workbench/landingPage/landingPageList/src/protocol.ts'
  import useGenerateH5 from '@/views/workbench/landingPage/landingPageList/src/useGenerateH5.ts'
  import { activity as activityConfig } from '@ng_visualization/config/json_scheme.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()
  const { generateTempH5 } = useGenerateH5()
  const router = useRouter()
  const route = useRoute()

  const state = reactive({
    bodyloading: false,
    fullscreenLoading: false, // 数据加载icon
    dialogProtocolVisible: false, // 协议弹窗
    spinblink: false,
    timer: null,
    activityId: null, // 从列表跳转过来的ID
    asign: null,
    adtype: null,
    dInnerHTML: '',
    cover_url: null,
    info: {},
    dialogVisible: false,
    storage_name: null,
    isCopy: false
  })

  onBeforeMount(() => {
    store.storage_name = 'pageEleH5Data'
    store.modifyQaData('reset')
    store.setLandingPageType()
    store.modifyUpdatetime()
    store.modifyActiveItems({})
    initData()
  })

  const initData = async () => {
    try {
      state.fullscreenLoading = true
      state.activityId = route.query.id ? Number(route.query.id) : null
      state.asign = route.query.sign
      state.isCopy = route.query.type == 'copy'

      let actData = {}
      let jsonData = null
      let fetch_resp = {}

      const storageItem = localStorage.getItem(store.storage_name)

      if (state.activityId) {
        // 存在ID 编辑
        const res = await setAdH5Info({ id: state.activityId })
        fetch_resp = (res && res.data) || {}
        jsonData = res && res.data.content
        state.info = res.data || {}
      } else {
        if (storageItem) {
          const json = JSON.parse(storageItem)
          if (json.activityId > 0) localStorage.removeItem(store.storage_name)
        }
      }
      // console.log(jsonData, 'jsonData')
      if (jsonData && jsonData != 'null') {
        // actData 为false 自身赋值
        actData = JSON.parse(jsonData)
        const curr = (actData.pages[0]?.elements && actData.pages[0]?.elements[0]) || {}
        if (curr?.name) store.modifyActiveItems(curr)
      } else {
        if (storageItem) {
          const json = JSON.parse(storageItem)
          if (json && Array.isArray(json.pages)) {
            if (json.pages[0].elements.length) {
              if (json.activityId) delete json.activityId
              actData = json
            } else {
              actData = cloneDeep(activityConfig)
              actData.author = 'qd'
            }
          }
        } else {
          actData = cloneDeep(activityConfig)
          actData.author = 'qd'
        }
      }
      if (actData && Array.isArray(actData.pages)) {
        if (actData.pages.length) {
          for (const v of actData.pages[0].elements) {
            if (v.name === 'NgButton' && v.qa_fields?.id) {
              try {
                const qa = await getQaInfo(v.qa_fields.id)
                if (qa) v.qa_fields = qa
              } catch (error) {
                console.error('获取QA信息失败:', error)
              }
            }
            if (v.name === 'NgForm' && v.form_fields?.id) {
              try {
                const resp = await setFormInfo({ id: v.form_fields?.id })
                if (resp.data.content) {
                  const form = JSON.parse(resp.data.content)
                  v.form_fields.btnText = form.btnText || '立即提交'
                  form.list.forEach((v) => {
                    if (v.key === 'checkout') v.form_value = Array.isArray(v.form_value) ? v.form_value : []
                  })
                  v.form_fields.elelist = form.list
                }
              } catch (error) {
                console.error('获取表单信息失败:', error)
              }
            }
            if (v.name === 'NgComment' && v.comment_code) {
              try {
                const resp = await setCommentInfo({ code: v.comment_code })
                if (resp.data.comment) {
                  const comment = JSON.parse(resp.data.comment)
                  v.commentList = comment?.slice(0, 4)
                }
              } catch (error) {
                console.log(error, '获取评论信息失败')
              }
            }
            if (v.name == 'NgQaChat') {
              v.isRepetAnswer = v.isRepetAnswer || null
              if (v.qaDataList?.length) {
                v.qaDataList?.forEach((item) => {
                  item.rowBtnStyle = item?.rowBtnStyle || 4
                  item.btnRadius = item?.btnRadius || 6
                })
                const item = v.qaDataList[0] || {}
                if (!item.id || !item.replyList?.length) return

                store.qaCurrInfo = {
                  ...item
                  // contxt: (item.contextList && item.contextList[0]) || ''
                }
                store.qaLists = []
                store.qaLists.push(store.qaCurrInfo)
              }
            }
          }
        }
      }
      if (fetch_resp.name !== actData.title) actData.title = fetch_resp.name
      // store.timer = setInterval(
      //   () => {
      //     store.getActivityData.activityId = state.activityId
      //     localStorage.setItem(store.storage_name, JSON.stringify(store.getActivityData))
      //   },
      //   1 * 60 * 1000
      // )
      store.modifyActivityData(actData)
      store.modifyIsAllowedDrag()

      setTimeout(() => {
        state.fullscreenLoading = false
      }, 100)
    } catch (error) {
      console.log(error, 'sss')
    }
  }

  // 预览
  const toPreview = (data) => {
    if (data && data.cmd == 1) {
      saveActivity(data.cmd)
    } else {
      state.dialogVisible = true
    }
  }

  // 保存
  const saveActivity = async (desc) => {
    try {
      let timerErr = null
      const saveActivityData = cloneDeep(store.getActivityData)
      const { title, name_alias, description } = saveActivityData
      console.log(desc, saveActivityData, 'saveActivityData')
      if (!name_alias) {
        return message.error('请在落地页配置中设置页面名称')
      }
      const elementsData = saveActivityData?.pages[0]?.elements || []
      if (!elementsData.length) {
        return message.error('当前落地页为空，请选择组件内容～')
      }
      // 组件验证
      const verif = verificationFunc(elementsData) || {}
      store.modifyErrTip(verif)
      if (verif.message) {
        clearTimeout(timerErr)
        // 清空错误提示
        timerErr = setTimeout(() => store.modifyErrTip({}), 3500)
        return message.error(verif.message)
      }

      if (!store.getActivityData.checkoutProtocol) {
        state.spinblink = true
        const timer = setTimeout(() => {
          state.spinblink = false
          clearTimeout(timer)
        }, 1000)
        message.warning('请先阅读并勾选免责声明')
        return
      }
      if (state.isCopy) state.activityId = null
      const elements = saveActivityData.pages && saveActivityData.pages[0].elements
      const fids = [],
        cids = []
      let isExistQa = false
      elements.forEach((v) => {
        if (v.name == 'NgForm' && v.form_fields?.id) fids.push(v.form_fields?.id)
        if (v.name == 'NgComment' && v.comment_id) cids.push(v.comment_id)
        // if (v.name == 'NgButton' && !isExistQa) {
        //   isExistQa = !!v.qa_fields?.id
        // }
      })
      const params = {
        name: title,
        content: JSON.stringify(store.getActivityData),
        fid: fids.join() || '',
        comment_ids: cids.join() || '',
        name_alias,
        remarks: description,
        type: 1,
        h5_url: state.activityId && state.info.h5_url ? state.info.h5_url : ''
      }
      if (state.activityId) params.id = state.activityId
      state.bodyloading = true
      try {
        await dataURItoBlod()
        params.cover_url = state.cover_url
      } catch (error) {
        console.log(error)
      }

      const temp = await generateTempH5({
        isUpload: true,
        oldTemplatePath: state.activityId && state.info.h5_url ? getPageNameFromUrl(state.info.h5_url) : null,
        dataJson: params,
        pt: 'l'
      })
      if (temp.url) params.h5_url = temp.url
      const resp = await setAddAdH5(params)
      state.info = resp.data || {}
      state.activityId = resp.data.id
      state.bodyloading = false
      message.success(resp.msg || '保存成功')
      if (desc == 1) {
        state.dialogVisible = true
      } else {
        if (desc && desc.data != null) router.push({ name: 'LandingPageList' })
      }
    } catch (error) {
      console.log(error, 'error')
      state.bodyloading = false
    }
  }

  async function getQaInfo(id) {
    try {
      const resp = await setAdH5Info({ id })
      const { data } = resp
      let content = {}
      if (data.content) {
        content = JSON.parse(data.content).pages[0].elements[0]
      }
      return {
        id: data.id,
        name: data.name || '',
        name_alias: data.name_alias || '',
        h5_url: data.h5_url || '',
        content
      }
    } catch (error) {
      return ''
    }
  }

  async function dataURItoBlod() {
    try {
      await sleep(200)
      let dataURI = await convertToImage(document.querySelector('.rendering-layer'))
      const fileNames = `${+new Date()}_${randomWord(12)}`
      let file = base64toFile(dataURI, fileNames)
      await Cos.upload(file, 'landingPage', false, (res) => {
        if (res.type == 'success') state.cover_url = res.content
      })
    } catch (error) {
      console.log(error)
    }
  }

  async function convertToImage(container, options = {}) {
    // 设置放大倍数
    const scale = window.devicePixelRatio

    // 传入节点原始宽高
    const width = 375 || container.offsetWidth
    const height = 585 || container.offsetHeight

    // html2canvas配置项
    const ops = {
      scale,
      width,
      height,
      backgroundColor: 'white',
      allowTaint: false,
      useCORS: true,
      logging: true,
      proxy: '',
      ...options
    }
    const canvas = await html2canvas(container, ops)
    return canvas.toDataURL('image/png')
  }

  function base64toFile(dataurl, filename = 'file') {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const suffix = mime.split('/')[1]
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], `${filename}.${suffix}`, {
      type: mime
    })
  }
</script>

<style lang="scss" scoped></style>
