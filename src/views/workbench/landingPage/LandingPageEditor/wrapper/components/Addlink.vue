<template>
  <div class="edit-container">
    <a-form
      ref="ruleForm"
      :model="state.addForm"
      :rules="rules"
      class="demo-addForm"
      label-width="140px"
      label-position="right"
    >
      <a-form-item label="跳转方式" name="type">
        <a-radio-group v-model:value="state.addForm.type">
          <a-radio-button value="applet" v-if="isLink">跳转客服</a-radio-button>
          <a-radio-button value="page">跳转网页</a-radio-button>
          <a-radio-button value="qa" v-if="isLink">跳转问答页</a-radio-button>
          <!-- <a-radio-button v-if="discern == 1" label="discern">跳转微信</a-radio-button> -->
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="state.addForm.type == 'page'" label="网页链接" name="url">
        <a-input
          v-model:value.trim="state.addForm.url"
          placeholder="请输入URL"
          :maxlength="100"
          class="input-theme"
          @keydown.space.prevent
        />
      </a-form-item>
      <a-form-item v-if="state.addForm.type == 'qa'" label="问答页" name="qa_id">
        <a-select
          v-model:value="state.addForm.qa_id"
          show-search
          :fieldNames="{ label: 'name_alias', value: 'id' }"
          :options="state.list"
          placeholder="请选择问答页"
          class="flex-1 select-theme"
          style="width: 100%"
          :filterOption="onFiterOption"
        >
        </a-select>
        <div class="c-#FF4D4F mt-2px text-14px">若您更改问答页，须再点击落地页保存并发布</div>
      </a-form-item>
      <div class="footer flex-align">
        <a-button class="btn cancel upf" @click="onCancel">取消</a-button>
        <a-button type="primary" :loading="state.loading" class="btn comfrim upf" @click="submitForm(ruleForm)"
          >确定</a-button
        >
      </div>
    </a-form>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { getH5List } from '@/views/workbench/landingPage/landingPageList/index.api'

  const props = defineProps({
    type: {
      type: String,
      default: null
    },
    item: {
      type: Object,
      default: () => ({})
    },
    activeItems: {
      type: Object,
      default: () => ({})
    },
    discern: {
      type: Number,
      default: null
    },
    isLink: {
      type: Boolean,
      default: false
    }
  })

  const emits = defineEmits(['event'])

  const ruleForm = ref(null)
  const state = reactive({
    loading: false,
    addForm: {
      type: null,
      url: null,
      qa_id: null
    },
    list: []
  })

  const rules = ref({
    type: [{ required: true, message: '请选择跳转方式', trigger: 'change' }],
    url: [
      { required: true, message: '请输入URL', trigger: 'blur' },
      {
        pattern: /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,
        message: '格式错误，请重新输入',
        trigger: 'blur'
      }
    ],
    qa_id: [{ required: true, message: '请选择问答页', trigger: 'change' }]
  })

  onMounted(() => {
    if (props.activeItems.name == 'NgCarousel') {
      state.addForm = {
        type: props.item.linksType ? props.item.linksType : props.isLink ? 'applet' : 'page',
        url: props.item.linksUrl || null
      }
    } else {
      state.addForm = {
        type: props.item.type ? props.item.type : props.isLink ? 'applet' : 'page',
        url: props.item.url || null,
        qa_id: props.item.qa_id || null
      }
    }
    getList()
  })

  const getList = async () => {
    try {
      let resp = await getH5List({ page: 1, page_size: 500, type: 2 })
      const { data } = resp
      state.list = data.list || []
    } catch (error) {
      console.log(error)
    }
  }

  // select搜索
  const onFiterOption = (value, option) => {
    if (option.name_alias.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  // 取消
  const onCancel = () => {
    emits('event', { cmd: 'close' })
  }
  // 确定
  const submitForm = (formName) => {
    formName.validate().then((valid) => {
      if (!valid) return false
      state.loading = true
      save()
    })
  }
  const save = () => {
    const obj = state.list.find((item) => item.id == state.addForm.qa_id) || {}
    emits('event', { cmd: 'addlink', data: state.addForm, qa_fileds: obj })
    state.loading = false
  }
</script>

<style lang="scss" scoped>
  .demo-addForm {
    .footer {
      justify-content: flex-end;
      padding: 20px 0 0 0;
      .comfrim {
        background: #4e90ff;
        border-color: #4e90ff;
        margin-left: 25px;
      }
      .btn {
        padding: 7px 32px;
      }
      .cancel {
        border-color: #4e90ff;
        color: #4e90ff;
      }
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
    .ant-radio-group .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child,
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
    .ant-radio-button-wrapper:hover {
      border-color: #409eff;
      color: #409eff;
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
      background-color: #409eff;
    }
  }
</style>
