<template>
  <div>
    <div v-if="activeItems.link.type == 'page'" class="link">
      <div class="link-item">
        <p class="label">跳转网页：</p>
        <div class="content">{{ activeItems.link.url }}</div>
      </div>
    </div>
    <div v-if="activeItems.link.type == 'applet'" class="labels">已选择获客链接</div>
    <div v-if="activeItems.link.type == 'discern'" class="labels">已选择跳转微信</div>
    <div v-if="activeItems.link.type == 'qa'" class="link">
      <div class="link-item">
        <p class="label">跳转问答页：</p>
        <div class="content">{{ props.activeItems.qa_fields?.name_alias }}</div>
      </div>
    </div>
    <a-button
      v-if="!activeItems.link.type"
      :icon="h(PlusOutlined)"
      type="link"
      :underline="false"
      @click="handleLink('add')"
      >添加链接</a-button
    >
    <div v-else class="operation flex-align">
      <a-button
        :icon="h(EditOutlined)"
        type="link"
        :underline="false"
        style="margin-right: 10px"
        @click="handleLink('edit')"
        >编辑</a-button
      >
      <a-button :icon="h(DeleteOutlined)" type="link" :underline="false" @click="handleLink('delete')">删除</a-button>
    </div>

    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.size"
      :footer="null"
      destroyOnClose
    >
      <AddLink :type="state.dialog.mode" :item="activeItems.link" :isLink="isLink" @event="onEvent" />
    </a-modal>
  </div>
</template>

<script setup>
  import { h, reactive } from 'vue'
  import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
  import AddLink from './Addlink.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const props = defineProps({
    activeItems: {
      type: Object,
      default: () => ({})
    },
    isLink: {
      type: Boolean,
      default: false
    }
  })

  const state = reactive({
    dialog: {
      id: null,
      visible: false,
      title: null,
      show: null,
      mode: null,
      size: null
    }
  })

  function formatDialog(params = {}) {
    state.dialog.id = params.id || null
    state.dialog.visible = params.visible || false
    state.dialog.title = params.title || null
    state.dialog.show = params.show || null
    state.dialog.mode = params.mode || null
    state.dialog.size = params.size || null
  }

  function closeDialog() {
    state.dialog.visible = false
    state.dialog.show = null
  }

  function handleLink(type) {
    if (type == 'add' || type == 'edit') {
      formatDialog({ id: null, visible: true, title: '添加链接', show: 'addlink', mode: type, size: '560px' })
    } else if (type == 'delete') {
      props.activeItems.link = { type: '', url: '' }
    }
  }
  function onEvent(data) {
    if (data.cmd == 'close') {
      closeDialog()
    } else if (data.cmd == 'addlink') {
      closeDialog()
      props.activeItems.link.type = data.data.type
      if (data.data.type == 'page') props.activeItems.link.url = data.data.url
      if (data.data.type == 'qa') {
        props.activeItems.link.qa_id = data.data.qa_id
        const fields = data.qa_fileds || {}
        let content = {}
        if (fields.content) {
          const item = JSON.parse(fields.content || '{}')
          content = (item.pages && item.pages[0] && item.pages[0].elements[0]) || {}
          if (item.qaModule) content.qaModule = item.qaModule
        }
        props.activeItems.qa_fields = {
          id: data.data.qa_id,
          name: fields.name || '',
          name_alias: fields.name_alias || '',
          h5_url: fields.h5_url || '',
          content
        }
      }
      store.modifyUpdatetime()
    }
  }
</script>

<style lang="scss" scoped>
  .link {
    .link-item {
      font-size: 12px;
      line-height: 20px;
    }
    .label {
      margin: 0;
      color: #333;
    }
    .content {
      color: #999;
      font-size: 14px;
      // max-width: 245px;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .operation {
    margin-top: 10px;
  }
  .labels {
    color: #999;
    font-size: 14px;
    max-width: 245px;
    margin: 4px 0 6px 0;
  }
</style>
