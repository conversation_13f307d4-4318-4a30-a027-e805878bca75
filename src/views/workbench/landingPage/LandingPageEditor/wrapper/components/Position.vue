<template>
  <div class="control-panel__section-body">
    <div
      v-if="xl.includes(store.getActiveItems.name) || yl.includes(store.getActiveItems.name) || isPosition"
      class="control-panel-group"
    >
      <span class="control-panel-group__label flex-align">位置</span>
      <div class="control-panel-group__body">
        <div class="multiline">
          <div class="flex-1 f-flex">
            <div v-if="xl.includes(store.getActiveItems.name)" class="input-inline flex-align">
              <span class="left-l">横轴</span>
              <a-input-number
                v-model:value="store.getActiveItems.eboxValue.x"
                controls-position="right"
                size="medium"
                class="fix-input__number"
                @change="(val) => handleChange(val, 'x')"
              />
            </div>
            <div v-if="yl.includes(store.getActiveItems.name) || isPosition" class="input-inline flex-align">
              <span class="left-l">纵轴</span>
              <a-input-number
                v-model:value="store.getActiveItems.eboxValue.y"
                controls-position="right"
                size="medium"
                class="fix-input__number"
                @change="(val) => handleChange(val, 'y')"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="wl.includes(store.getActiveItems.name) || hl.includes(store.getActiveItems.name)"
      class="control-panel-group"
    >
      <span class="control-panel-group__label flex-align">尺寸</span>
      <div class="control-panel-group__body">
        <div class="multiline">
          <div class="flex-1 f-flex">
            <div v-if="wl.includes(store.getActiveItems.name)" class="input-inline flex-align">
              <span class="left-l">宽度</span>
              <a-input-number
                v-model:value="store.getActiveItems.eboxValue.width"
                controls-position="right"
                :disabled="disabled"
                size="medium"
                class="fix-input__number"
                @change="(val) => handleChange(val, 'width')"
              />
            </div>
            <div v-if="hl.includes(store.getActiveItems.name)" class="input-inline flex-align">
              <span class="left-l">高度</span>
              <a-input-number
                v-model:value="store.getActiveItems.eboxValue.height"
                controls-position="right"
                :disabled="disabled"
                size="medium"
                class="fix-input__number"
                @change="(val) => handleChange(val, 'height')"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="rl.includes(store.getActiveItems.name) || sl.includes(store.getActiveItems.name)"
      class="control-panel-group"
    >
      <span class="control-panel-group__label flex-align">旋转</span>
      <div class="control-panel-group__body">
        <div class="multiline">
          <div class="flex-1 f-flex">
            <div v-if="rl.includes(store.getActiveItems.name)" class="input-inline flex-align">
              <span class="left-l">旋转</span>
              <a-input-number
                v-model:value="store.getActiveItems.rotation"
                controls-position="right"
                size="medium"
                class="fix-input__number"
                @change="(val) => handleChange(val, 'rotation')"
              />
            </div>
            <div v-if="sl.includes(store.getActiveItems.name)" class="flex-align">
              <a-checkbox-group
                v-model:value="state.checkboxGroup"
                class="scale-group"
                size="medium"
                @change="(val) => handleChange(val, 'scale')"
              >
                <a-checkbox value="x">
                  <div class="reverse"><i class="iconfont icon-chuizhifanzhuan1" /></div>
                </a-checkbox>
                <a-checkbox value="y"><i class="iconfont icon-chuizhifanzhuan1" /></a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onBeforeMount, reactive, ref } from 'vue'
  import { hasUseToolbar } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const xl = [
    'NgHeadline',
    'NgRichText',
    'NgForm',
    'NgVideo',
    'NgVideoGroup',
    'NgRolling',
    'NgWxfans',
    'NgWeChatTop',
    'NgWeChatBottom',
    'NgBackTop'
  ]
  const yl = [
    'NgHeadline',
    'NgRichText',
    'NgBtnTop',
    'NgBtnBottom',
    'NgBuyNow',
    'NgForm',
    'NgVideo',
    'NgVideoGroup',
    'NgRolling',
    'NgWxfans',
    'NgWeChatTop',
    'NgWeChatBottom',
    'NgBackTop'
  ]
  const wl = [
    'NgHeadline',
    'NgRichText',
    'NgButton',
    'NgBtnTop',
    'NgBtnBottom',
    'NgBuyNow',
    'NgForm',
    'NgVideo',
    'NgVideoGroup',
    'NgWxfans',
    'NgWeChatTop',
    'NgWeChatBottom',
    'NgTelephone'
  ]
  const hl = ['NgButton', 'NgBtnTop', 'NgBtnBottom', 'NgBuyNow', 'NgForm', 'NgVideo', 'NgVideoGroup', 'NgTelephone']
  const rl = ['NgImage', 'NgText']
  const sl = ['NgImage', 'NgText']

  const props = defineProps({
    isPosition: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  })

  const emits = defineEmits(['position'])

  const state = reactive({
    checkboxGroup: [],
    xl, // 横轴
    yl, // 纵轴
    wl, // 宽度
    hl, // 高度
    rl, // 旋转
    sl // 翻转
  })

  onBeforeMount(() => {
    if (store.getActiveItems.scaleX == -1 && store.getActiveItems.scaleY == -1) {
      state.checkboxGroup = ['x', 'y']
    } else {
      if (store.getActiveItems.scaleX == -1) state.checkboxGroup = ['x']
      if (store.getActiveItems.scaleY == -1) state.checkboxGroup = ['y']
    }
  })

  function handleChange(val, type) {
    if (type == 'width') store.getActiveItems.styles.width = val / 100 + 'rem'
    if (type == 'height') store.getActiveItems.styles.height = val / 100 + 'rem'
    if (['NgButton'].includes(store.getActiveItems.name) && ['top', 'bottom'].includes(store.getActiveItems.position)) {
      const left = (store.getCanvasSize.width - val) / 2
      if (type == 'width') {
        store.getActiveItems.styles.left = left / 100 + 'rem'
        store.getActiveItems.eboxValue.left = left
      }
      if (type == 'y' && ['top'].includes(store.getActiveItems.position)) {
        store.getActiveItems.eboxValue.top = val
        store.getActiveItems.styles.top = val / 100 + 'rem'
      }
      if (type == 'y' && ['bottom'].includes(store.getActiveItems.position)) {
        store.getActiveItems.eboxValue.bottom = val
        store.getActiveItems.styles.bottom = val / 100 + 'rem'
      }
      return
    }
    if (
      ['NgBtnTop', 'NgBtnBottom', 'NgWeChatTop', 'NgWeChatBottom', 'NgBackTop', 'NgBuyNow'].includes(
        store.getActiveItems.name
      )
    ) {
      const left = (store.getCanvasSize.width - val) / 2
      if (type == 'width') {
        store.getActiveItems.styles.left = left / 100 + 'rem'
        store.getActiveItems.eboxValue.left = left
      }
      if (type == 'y' && ['NgBtnTop', 'NgWeChatTop'].includes(store.getActiveItems.name)) {
        store.getActiveItems.eboxValue.top = val
        store.getActiveItems.styles.top = val / 100 + 'rem'
      }
      if (
        type == 'y' &&
        ['NgBtnBottom', 'NgWeChatBottom', 'NgBackTop', 'NgBuyNow'].includes(store.getActiveItems.name)
      ) {
        store.getActiveItems.eboxValue.bottom = val
        store.getActiveItems.styles.bottom = val / 100 + 'rem'
      }
      if (type == 'x' && ['NgWeChatTop', 'NgWeChatBottom'].includes(store.getActiveItems.name)) {
        store.getActiveItems.eboxValue.left = val
        store.getActiveItems.styles.left = val / 100 + 'rem'
      }
      if (type == 'x' && ['NgBackTop'].includes(store.getActiveItems.name)) {
        store.getActiveItems.styles.x = val / 100 + 'rem'
      }
    } else if (['NgRolling'].includes(store.getActiveItems.name)) {
      if (type == 'x') {
        store.getActiveItems.eboxValue.left = val
        store.getActiveItems.styles.left = val / 100 + 'rem'
      }
      if (type == 'y') {
        store.getActiveItems.eboxValue.top = val
        store.getActiveItems.styles.top = val / 100 + 'rem'
      }
    } else {
      if (type == 'x') store.getActiveItems.styles.x = val / 100 + 'rem'
      if (type == 'y') store.getActiveItems.styles.y = val / 100 + 'rem'
    }
    if (type == 'scale') {
      store.getActiveItems.scaleX = val.includes('x') ? -1 : 1
      store.getActiveItems.scaleY = val.includes('y') ? -1 : 1
    }
    if (['NgTelephone'].includes(store.getActiveItems.name)) emits('position', val + 18)
    store.modifyUpdatetime()
  }
</script>

<style lang="scss" scoped>
  .mb {
    margin-bottom: 12px;
  }
  .iconfont.icon-chuizhifanzhuan1 {
    font-size: 20px;
  }
  .reverse {
    transform: rotate(-90deg);
  }
</style>
