<template>
  <div class="center-panel-wrap">
    <div class="center-panel">
      <!-- 占位 -->
      <div class="top-blank" />
      <div class="content-block">
        <!-- 区域背景 -->
        <div class="background" style="padding-top: 0">
          <template v-for="v in store.getCurPageData.elements">
            <div
              v-if="!hasUseToolbar(v)"
              :key="v.uuid"
              :class="['bg-block', { 'bg-block--selected': v.uuid === store.getActiveItems.uuid }]"
              :style="{ height: heightFunc(v) }"
            >
              <div v-if="v.uuid === store.getErrTip.uuid" class="h5_popup" />
            </div>
          </template>
        </div>
        <div class="edit-area">
          <!-- 数据渲染区 实际数据显示 -->
          <Rendering />

          <!-- 数据互动区 -->
          <Interactive />

          <!-- 侧边操作区 如上移 下移 -->
          <Operator />

          <!-- v-if="store.allNoToolbar.includes(store.getActiveItems.name)" -->
          <div class="screen-verrify-ruler" @click="onSelect" v-if="isShowScreenVerify">
            {{ store.allNoToolbar.includes(store.getActiveItems.name) ? '隐藏' : '展示' }}首屏验证
            <VerticalLeftOutlined />
          </div>

          <!-- 首屏提示 -->
          <div class="screen-ruler first-screen">
            <span>首屏</span>
            <a-tooltip placement="top">
              <template #title><span>以上内容首屏可见</span></template>
              <QuestionCircleOutlined />
            </a-tooltip>
          </div>
        </div>
      </div>
      <!-- 占位 -->
      <div class="bottom-blank" />
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { QuestionCircleOutlined, VerticalLeftOutlined } from '@ant-design/icons-vue'
  // 数据渲染区 实际数据显示
  import Rendering from './components/Rendering.vue'
  // 数据互动区
  import Interactive from './components/Interactive.vue'
  // 侧边操作区
  import Operator from './components/Operator.vue'
  import { calcModuleValue, hasUseToolbar } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const isShowScreenVerify = computed(() => {
    const newArr = store.getCurPageData?.elements || []
    return newArr.some((v) => store.allNoToolbar.includes(v.name))
  })

  const onSelect = () => {
    let index = store.getCurPageData.elements.findIndex((v) => store.allNoToolbar.includes(v.name))
    if (store.allNoToolbar.includes(store.getActiveItems.name)) {
      store.modifyActiveItems(store.getCurPageData.elements[index == 0 ? index + 1 : 0] || {})
      return
    }
    if (index >= 0) {
      store.modifyActiveItems(store.getCurPageData.elements[index])
    } else {
      store.modifyActiveItems({})
    }
  }

  const heightFunc = (v) => {
    return calcModuleValue(v) / 100 + 'rem'
  }

  // 选择区域背景 activeItems重新赋值 计算工具栏 top 定位
  const handleSelectBg = (item) => {
    if (store.getCurPageData.elements) {
      let maps = []
      store.getCurPageData.elements.forEach((v, i, arr) => {
        if (v.uuid == item.uuid) maps = arr.slice(0, i)
      })
      const nums = maps.reduce((total, v) => total + v.eboxValue.bgHeight, 0)
      item.toolTop = nums + 'px'
    }
    item.isBg = true
    item.isWire = false
    store.modifyActiveItems(item)
  }
</script>

<style lang="scss" scoped>
  .center-panel {
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .content-block {
    position: relative;
  }
  .top-blank,
  .bottom-blank {
    height: 85px;
  }
  .background,
  .bg-block {
    display: block;
  }
  .bg-block--hovered,
  .bg-block--selected,
  .bg-block:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  .edit-area {
    width: 375px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    user-select: none;
  }

  .screen-verrify-ruler {
    position: absolute;
    top: 400px;
    left: -30px;
    width: 30px;
    padding: 10px 8px;
    border-radius: 8px 0px 0px 8px;
    box-shadow: -3px 0px 4px 0px rgba(181, 172, 163, 0.46);
    border: 1px solid #ffac54;
    border-right-width: none;
    background: #ffffff;
    color: #fe9d35;
    line-height: 17px;
    display: flex;
    align-content: center;
    justify-content: space-around;
    flex-direction: column;
    cursor: pointer;
  }

  .screen-ruler {
    position: absolute;
    padding-top: 4px;
    border-top: 1px dashed #505f79;
    color: #505f79;
    opacity: 0.25;
    font-size: 12px;
    display: flex;
    align-content: center;
    justify-content: space-around;
    &.first-screen {
      top: 585px;
      left: -48px;
      width: 40px;
    }
  }
</style>
