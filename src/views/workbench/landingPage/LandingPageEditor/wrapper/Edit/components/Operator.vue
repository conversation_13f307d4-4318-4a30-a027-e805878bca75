<template>
  <div v-if="!store.getActiveItems?.allForbidToolbar">
    <div
      v-if="store.getActiveItems && store.getActiveItems.enabled"
      class="operator-bar on-single-edge"
      :style="`
    height: ${store.getActiveItems.boxBg.height};
    ${state.toolTop};
    right: -56px;
    margin-top: 0px;
  `"
    >
      <div v-if="state.isShow" class="operator-side-bar">
        <template v-for="v in state.operatorList">
          <div
            v-if="!hasUseToolbar(store.getActiveItems)"
            :key="v.key"
            class="operator-item"
            :class="[
              {
                disabled:
                  ['NgQaChat'].includes(store.getActiveItems.name) &&
                  ['CopyOutlined', 'SettingOutlined'].includes(v.icon)
              }
            ]"
            @click.stop="handleOperator(v)"
          >
            <a-tooltip placement="right" class="operator-tooltip__edit single-operator upf">
              <template #title>
                <span>{{ v.label }}</span>
              </template>
              <!-- <i :class="v.icon" /> -->
              <!-- <component :is="v.icon" /> -->
              <ArrowUpOutlined v-if="v.icon == 'ArrowUpOutlined'" />
              <ArrowDownOutlined v-if="v.icon == 'ArrowDownOutlined'" />
              <CopyOutlined v-if="v.icon == 'CopyOutlined'" />
              <SettingOutlined v-if="v.icon == 'SettingOutlined'" />
              <DeleteOutlined v-if="v.icon == 'DeleteOutlined'" />
            </a-tooltip>
          </div>
        </template>
        <div
          v-if="hasUseToolbar(store.getActiveItems)"
          class="operator-item"
          @click.stop="handleOperator({ key: 'delete' })"
        >
          <a-tooltip placement="right" class="operator-tooltip__edit single-operator upf">
            <template #title><span>删除</span></template>
            <DeleteOutlined />
          </a-tooltip>
        </div>
      </div>
      <div
        v-if="state.isShow && !hasUseToolbar(store.getActiveItems)"
        class="height-operators"
        :style="{ height: store.getActiveItems.boxBg.height }"
      >
        <a-tooltip ref="tooltipTc" placement="top" class="operators-drag__h top upf">
          <template #title>
            <span>当前高度：{{ store.getActiveItems.boxBg.height }}</span>
          </template>
          <!-- <div @mousedown.stop.prevent="(e) => { handlerMousedown(e, 'tc')}" /> 禁止拖动 -->
        </a-tooltip>
        <a-tooltip ref="tooltipBc" placement="bottom" class="operators-drag__h bottom upf">
          <template #title>
            <span>当前高度：{{ store.getActiveItems.boxBg.height }}</span>
          </template>
          <!-- <div @mousedown.stop.prevent="(e) => { handlerMousedown(e, 'bc')}" /> 禁止拖动 -->
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { watch, reactive, ref } from 'vue'
  import { v4 as uuidv4 } from 'uuid'
  import { message } from 'ant-design-vue'

  import {
    DeleteOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    CopyOutlined,
    SettingOutlined
  } from '@ant-design/icons-vue'
  import { throttle, deepClone, calcToolTop, hasUseToolbar } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const refreshRate = 60 // 刷新率为 60Hz 比较流畅
  const _throttleHandler = throttle(1000 / refreshRate)

  const tooltipBc = ref(null)
  const state = reactive({
    isShow: true,
    nglist: ['NgBtnTop', 'NgRolling', 'NgWeChatTop', 'NgWxfans'],
    operatorList: [
      // { key: 'upEmpty', label: '上插空白模块', icon: 'el-icon-plus' },
      { key: 'up', label: '上移', icon: 'ArrowUpOutlined' },
      { key: 'bottom', label: '下移', icon: 'ArrowDownOutlined' },
      { key: 'copy', label: '复制', icon: 'CopyOutlined' },
      { key: 'set', label: '模块设置', icon: 'SettingOutlined' },
      { key: 'delete', label: '删除', icon: 'DeleteOutlined' }
      // { key: 'bottomEmpty', label: '下插空白模块', icon: 'el-icon-plus' },
    ],
    handled: null,
    lastMouseX: 0,
    lastMouseY: 0,
    tcDrag: false,
    bcDrag: false,
    tooltipLeftX: null,
    toolTop: ''
  })

  watch(
    () => store.getActiveItems?.uuid,
    (newVal) => {
      state.toolTop = toolTopFunc()
    },
    {
      immediate: true
    }
  )

  const handleOperator = (v) => {
    if (['NgQaChat'].includes(store.getActiveItems.name) && ['CopyOutlined', 'SettingOutlined'].includes(v.icon)) return
    const index = store.getCurPageData.elements.findIndex((v) => v.uuid === store.getActiveItems.uuid)
    // 上移
    if (v.key == 'up') up(index)
    // 下移
    if (v.key == 'bottom') down(index)
    // 复制
    if (v.key == 'copy') copy(index)
    // 模块设置
    if (v.key == 'set') set(index)
    // 删除
    if (v.key == 'delete') del(index)
    store.modifyUpdatetime()
  }
  const up = (index) => {
    const newArr = store.getCurPageData.elements.slice(0, 1).filter((v) => {
      return !v.useToolbar || (v.name == 'NgButton' && !['top', 'bottom'].includes(v.position))
    })
    if (index === 0 || newArr.length <= 0) {
      message.warning('已经是第一个了')
      return
    }
    store.isShow = false
    const list = store.getCurPageData.elements
    list[index] = list.splice(index - 1, 1, list[index])[0]
    list[index - 1].toolTop = calcToolTop(store.getCurPageData.elements, list[index - 1])
    selData(list[index - 1])
  }
  const down = (index) => {
    if (index === store.getCurPageData.elements.length - 1) {
      message.warning('已经是最后一个了')
      return
    }
    store.isShow = false
    const list = store.getCurPageData.elements
    list[index] = list.splice(index + 1, 1, list[index])[0]
    list[index + 1].toolTop = calcToolTop(store.getCurPageData.elements, list[index + 1])
    selData(list[index + 1])
  }
  const set = (index) => {
    if (store.getActiveItems.backBtn) store.getActiveItems.backBtn = false
    store.getActiveItems.isBg = true
    store.getActiveItems.isWire = false
  }
  const del = (index) => {
    console.log(index)
    store.isShow = false
    store.getCurPageData.elements.splice(index, 1)
    selData({})
    store.modifyQaData('reset')
    store.modifyIsAllowedDrag()
  }
  const copy = (index) => {
    const item = deepClone(store.getCurPageData.elements[index])
    if (item && validateCom(item)) return
    item.uuid = uuidv4()
    store.getCurPageData.elements.splice(index + 1, 0, item)
  }
  const validateCom = (data) => {
    try {
      if (!data.limitSize) return false
      const elements = store.getCurPageData.elements
      const newArr = elements?.filter((item) => {
        return item.name == data.name
      })
      if (newArr.length >= data.limitSize) {
        message.warning('组件已上限，请删除后再添加')
        return true
      }
      return false
    } catch (error) {
      console.log('error', error)
      return false
    }
  }
  const selData = (data) => {
    store.modifyActiveItems(data)
    state.toolTop = toolTopFunc()
    // eslint-disable-next-line no-return-assign
    setTimeout(() => (store.isShow = true), 0)
  }
  // 拖拽手柄
  const handlerMousedown = (ev, type) => {
    console.log(ev, type)
    // const startY = ev.y;
    const { y: startY } = this.getMouseCoordinate(ev)
    const targetEl = document.getElementById(store.getActiveItems.uuid)
    const originH = targetEl.offsetHeight
    state.lastMouseX = store.getActiveItems.eboxValue.x
    state.lastMouseY = store.getActiveItems.eboxValue.y
    console.log('lastMouse', state.lastMouseX, state.lastMouseY)
    if (type == 'tc') state.tcDrag = true
    if (type == 'bc') state.tcDrag = true
    document.onmousemove = (e) => {
      e.preventDefault()
      _throttleHandler(() => {
        this.resizeFun({ e, type, startY, originH })
      })
    }

    document.onmouseup = () => {
      state.lastMouseX = store.getActiveItems.eboxValue.x
      state.lastMouseY = store.getActiveItems.eboxValue.y
      if (type == 'tc') state.tcDrag = false
      if (type == 'bc') state.tcDrag = false
      state.tooltipLeftX = null
      store.modifyUpdatetime()
      document.onmousemove = null
      document.onmouseup = null
    }
  }
  const resizeFun = ({ e, type, startY, originH }) => {
    let targetH
    const moveY = e.y - startY
    switch (type) {
      case 'tc':
        // eslint-disable-next-line no-case-declarations
        const top = moveY - state.lastMouseY
        targetH = originH - moveY
        store.getActiveItems.eboxValue.y = -top
        store.getActiveItems.styles.y = -(top / 100) + 'rem'
        break
      case 'bc':
        targetH = originH + moveY
        break
    }
    if (targetH < 0) return
    store.getActiveItems.eboxValue.bgHeight = targetH || 0
    store.getActiveItems.boxBg.height = (targetH || 0) + 'px'
    if (type == 'bc') modifyToolTipTop(tooltipBc.value, targetH)
  }
  const getMouseCoordinate = (e) => {
    return {
      x: e.pageX || e.clientX + document.documentElement.scrollLeft,
      y: e.pageY || e.clientY + document.documentElement.scrollTop
    }
  }
  const modifyToolTipTop = (target = {}, targetH) => {
    if (target.tooltipId) {
      const scrollTop = document.querySelector('.main-area').scrollTop
      const calH = parseInt(calcToolTop(store.getCurPageData.elements, store.getActiveItems)) || 0
      const top = targetH + calH + 160 - scrollTop + 'px'
      const ele = document.querySelector(`#${target.tooltipId}`)
      if (!state.tooltipLeftX) state.tooltipLeftX = ele.style.left
      ele.style.top = top
      if (ele.style.display == 'none') {
        ele.setAttribute('x-placement', 'bottom')
        ele.style.display = 'block'
        ele.style.left = state.tooltipLeftX
      }
    }
  }

  function toolTopFunc() {
    const l = store.getActiveItems
    if (hasUseToolbar(l, 'button')) {
      return l.position == 'top' || l.name == 'NgRolling'
        ? `top: ${l.styles.top};min-height:auto;`
        : `bottom: ${l.styles.bottom};min-height:auto;`
    }
    if (l.useToolbar) {
      return state.nglist.includes(l.name)
        ? `top: ${l.currStyleClass == 'style-five' ? l.styles.y : l.styles.top};min-height:auto;`
        : `bottom: ${l.styles.bottom};min-height:auto;`
    } else {
      return `top: ${l.toolTop || '0px'}`
    }
  }
</script>

<style lang="scss" scoped>
  .operator-bar {
    min-height: 344px;
    position: absolute;
    pointer-events: none;
    &.on-single-edge {
      min-height: 272px;
    }
    .operator-side-bar {
      height: 100%;
      min-width: 40px;
    }
    .operator-item {
      background: #fff;
      border-radius: 20px;
      width: 40px;
      margin-bottom: 15px;
      pointer-events: auto;
      box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
      &:first-of-type {
        margin-top: -20px;
      }
      &.disabled {
        pointer-events: none;
        // background: rgba(255, 255, 255, 0.25);
        .operator-tooltip__edit {
          color: #ccc;
        }
      }
      // &:first-of-type, &:nth-last-of-type(2) { margin-bottom: 30px; }
    }
    .height-operators {
      position: absolute;
      top: 0;
      left: -234px;
    }
    .operators-drag__h {
      width: 64px;
      height: 16px;
      position: absolute;
      left: 0;
      background: #fff;
      pointer-events: all;
      outline: 0;
      &.top {
        box-shadow: 0 -2px 10px 0 rgb(0 0 0 / 10%);
        border-radius: 64px 64px 0 0;
        top: -16px;
      }
      &.bottom {
        box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
        border-radius: 0 0 64px 64px;
        bottom: -16px;
      }
      &.top,
      &.bottom {
        cursor: ns-resize;
        &::after {
          content: '';
          display: block;
          width: 26px;
          height: 3px;
          border-top: 1px solid rgba(0, 0, 0, 0.25);
          border-bottom: 1px solid rgba(0, 0, 0, 0.25);
        }
      }
    }
  }
</style>
