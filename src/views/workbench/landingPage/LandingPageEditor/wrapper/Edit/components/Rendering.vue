<template>
  <div class="rendering-layer" style="padding-top: 0px; padding-bottom: 0px; background-color: white">
    <template v-if="store.getCurPageData.elements && store.getCurPageData.elements.length">
      <div
        v-for="v in store.getCurPageData.elements"
        :id="v.name + '_' + v.uuid"
        :key="v.uuid"
        class="xr-brick-wrapper"
        :style="store.allNoToolbar.includes(v.name) && store.getActiveItems.name != v.name ? 'display: none' : ''"
      >
        <div v-show="!v.dragStatus" class="stream-frame" :style="!hasUseToolbar(v) ? { height: heightFunc(v) } : ''">
          <div
            class="brick-mdl"
            :style="
              v.boxBg.bgMode == 2
                ? `${
                    v.boxBg.backgroundImage
                      ? `background:url('${v.boxBg.backgroundImage}') no-repeat;background-size:100% 100%;`
                      : ''
                  }`
                : `background-color:${v.boxBg.backgroundColor}`
            "
          >
            <div class="mdl-content">
              <div class="absolute-frame" :style="absoluteFrameStyl(v)">
                <component :is="v.name" :item="v" :isShow="store.getActiveItems.name == v.name" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="empty-page">
      <img :src="getConfig('NODE_OSS_URL') + '/assets/h5/h5-editor_empty.png'" alt="empty_page" width="289" />
      <p class="title">添加需要的模块或组件</p>
      <p class="subtitle">可从左侧点击或拖拽</p>
    </div>

    <div class="float-section top-float-section">
      <div class="float-items top-float-items" />
    </div>
  </div>
</template>

<script setup>
  import { reactive } from 'vue'
  import { getConfig } from '@/utils'
  import { calcModuleValue, hasUseToolbar } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const state = reactive({
    nglist: ['NgBtnTop', 'NgRolling', 'NgWeChatTop', 'NgWxfans']
  })

  const heightFunc = (v) => {
    return calcModuleValue(v) / 100 + 'rem'
  }
  const absoluteFrameStyl = (v) => {
    const uHeight = calcModuleValue(v) / 100 + 'rem'
    // console.log(JSON.parse(JSON.stringify(v)), 'absoluteFrameStyl')
    if (v.name == 'NgButton' && ['top', 'bottom'].includes(v.position)) {
      return `width: ${v.styles.width};height: ${uHeight};left: 50%;${
        ['top'].includes(v.position) ? `top: ${v.styles.top};` : `bottom: ${v.styles.bottom};`
      }z-index: ${999 + (v.eboxValue.zIndex || 0)};position: fixed;transform: translateX(-50%);`
    }
    return !v.useToolbar
      ? `
              top: 0;
              left: 0;
              width: 100%;
              height: ${store.allNoToolbar.includes(v.name) ? 'auto' : uHeight};
              transform: rotate(${v.rotation}deg) scale(${v.scaleX}, ${v.scaleY});
            `
      : `
                width: ${v.styles.width};
                height: ${store.allNoToolbar.includes(v.name) ? 'auto' : uHeight};
                ${
                  (v.name == 'NgWxfans' && v.currStyleClass == 'style-five') || v.name == 'NgBackTop'
                    ? `right: ${v.styles.x};`
                    : `left: ${v.styles.left};`
                }
                ${
                  state.nglist.includes(v.name)
                    ? `top: ${v.currStyleClass == 'style-five' ? v.styles.y : v.styles.top};`
                    : `bottom: ${v.styles.bottom};`
                }
                z-index: ${store.allNoToolbar.includes(v.name) ? 999999 : 999 + (v.eboxValue.zIndex || 0)};
                position: ${v.styles.position};
              `
  }
</script>

<style lang="scss" scoped>
  .rendering-layer {
    min-height: 585px;
  }
  .xr-brick-wrapper {
    .stream-frame {
      position: relative;
    }
    div.brick-mdl {
      height: 100%;
      background-size: cover;
      background-position: 50%;
      background-repeat: no-repeat;
      position: relative;
      overflow: hidden;
    }
    .mdl-content {
      height: 100%;
    }
    .absolute-frame {
      position: absolute;
      overflow: hidden;
    }
    .brick-mdl div,
    div.brick-mdl {
      box-sizing: border-box;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      -webkit-text-size-adjust: none;
      font-size: 0.14rem;
      line-height: 1.5;
      outline: none;
    }
  }

  .empty-page {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -32px;
    text-align: center;
    .title {
      font-size: 16px;
      color: #ccc;
      margin-top: 15px;
    }
    .subtitle {
      font-size: 12px;
      color: #d9d9d9;
      margin-top: 12px;
    }
  }

  .center-panel--slide .xr-brick-wrapper,
  .center-panel:not(.center-panel--slide) .rendering-layer {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
  }
</style>
