<template>
  <div
    class="interactive-layer"
    style="padding-top: 0px"
    @drop="handleDrog"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
  >
    <div class="float-section top-float-section" />
    <div
      v-for="(v, index) in store.getCurPageData.elements"
      :id="v.uuid"
      :key="v.uuid"
      :class="[
        'interactive-layer__section',
        { active: v.uuid === store.getActiveItems.uuid },
        { 'nofloat-layer__section': !hasUseToolbar(v) }
      ]"
      :style="{
        width: store.getCanvasSize.width + 'px',
        height: heightFunc(v, 'v1')
      }"
      @contextmenu.stop.prevent
    >
      <template v-if="!v.allForbidToolbar">
        <div
          class="interactive-section"
          :style="{
            width: store.getCanvasSize.width + 'px',
            height: heightFunc(v, 'v2')
          }"
          @click.stop="handleSelectBg(v)"
        >
          <!-- @mousedown.stop="elDownHandler" 禁止拖动元素 -->
          <div class="wireframe-layer" :style="{ width: store.getCanvasSize.width + 'px' }" :data-index="index">
            <div
              :ref="v.name + v.uuid"
              class="absolute-frame"
              :style="absoluteFrameStyl(v, 'v1')"
              @click.stop="handleSelectWire(v)"
            >
              <div :class="['wireframe', { selected: v.uuid === store.getActiveItems.uuid }]" :data-index="index">
                <div />
              </div>
            </div>
          </div>
          <div class="co-update-layer" />
          <div class="guides-layer" />
          <div v-if="v.dragStatus == 2" class="wait">{{ v.desc }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { watch, reactive } from 'vue'
  import { v4 as uuidv4 } from 'uuid'
  import { useDraggableResizable, defaultProps } from '../src/draggable-resizable.js'
  import {
    throttle,
    deepClone,
    formatData,
    calcModuleValue,
    calcToolTop,
    hasUseToolbar
  } from '@ng_visualization/utils/index.js'
  import configList from '@ng_visualization/config/config.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()
  const refreshRate = 60 // 刷新率为 60Hz 比较流畅
  const _throttleHandler = throttle(1000 / refreshRate)

  const state = reactive({
    nglist: ['NgBtnTop', 'NgRolling', 'NgWeChatTop', 'NgWxfans'],
    nglist2: ['NgBtnTop', 'NgRolling', 'NgWeChatTop', 'NgWeChatBottom', 'NgBackTop'],
    configList,
    curData: {},
    handles: [
      { key: 'tl', value: 'nw-resize' },
      { key: 'tc', value: 'n-resize' },
      { key: 'tr', value: 'ne-resize' },
      { key: 'mr', value: 'e-resize' },
      { key: 'br', value: 'se-resize' },
      { key: 'bc', value: 's-resize' },
      { key: 'bl', value: 'sw-resize' },
      { key: 'ml', value: 'w-resize' }
    ]
  })

  watch(
    () => store.getErrTip.uuid,
    (newVal) => {
      if (newVal) {
        const obj = store.getCurPageData.elements.find((v) => v.uuid === newVal) || {}
        if (obj.uuid) store.modifyActiveItems(obj)
      }
    }
  )

  function heightFunc(v, type) {
    const uHeight = calcModuleValue(v) / 100 + 'rem'
    if ((v.name == 'NgButton' && ['top', 'bottom'].includes(v.position)) || v.useToolbar) {
      return 'auto'
    }
    if (type === 'v1') {
      return state.nglist2.includes(v.name) ||
        (v.name == 'NgWxfans' && v.currStyleClass == 'style-five') ||
        v.dragStatus
        ? 'auto'
        : uHeight
    } else {
      return state.nglist2.includes(v.name) || (v.name == 'NgWxfans' && v.currStyleClass == 'style-five')
        ? 'auto'
        : uHeight
    }
  }
  function absoluteFrameStyl(v, type) {
    const uHeight = calcModuleValue(v) / 100 + 'rem'
    // console.log(JSON.parse(JSON.stringify(v)), 'absoluteFrameStyl', type)
    if (v.name == 'NgButton' && ['top', 'bottom'].includes(v.position)) {
      return `width: ${v.styles.width};height: ${uHeight};left: 50%;${
        ['top'].includes(v.position) ? `top: ${v.styles.top};` : `bottom: ${v.styles.bottom};`
      }z-index: ${999 + (v.eboxValue.zIndex || 0)};position: fixed;transform: translateX(-50%);`
    }
    return !v.useToolbar
      ? `top: 0;left: 0;width: 100%;height: ${uHeight}; transform: rotate(${v.rotation}deg);pointer-events: none;`
      : `width: ${v.styles.width};height: ${uHeight};
                ${
                  (v.name == 'NgWxfans' && v.currStyleClass == 'style-five') || v.name == 'NgBackTop'
                    ? `right: ${v.styles.x};`
                    : `left: ${v.styles.left};`
                }
                ${
                  state.nglist.includes(v.name)
                    ? `top: ${v.currStyleClass == 'style-five' ? v.styles.y : v.styles.top};`
                    : `bottom: ${v.styles.bottom};`
                }
                position: ${v.styles.position};
                pointer-events: none;
                z-index: ${999 + (v.eboxValue.zIndex || 0)};
                transform: rotate(${v.rotation}deg);`
  }
  function handleDragOver(e) {
    if (!store.getDragType) return

    e.preventDefault()
    e.stopPropagation()

    _throttleHandler(() => {
      dragAddDataFun(e)
    })
  }
  function dragAddDataFun(e) {
    const target = e.target || e.srcElement
    const className = target.className
    // interactive-layer__section
    const name = className !== 'interactive-layer' ? 'wireframe' : 'interactive-layer'
    const data = store.getBrickTempl.find((v) => v.name === store.getDragType) || {}
    const item = deepClone(data)
    item.uuid = uuidv4()
    item.currStyleClass = store.getCurrStyleClass
    item.dragStatus = 2 // 为2 表示正在拖拽中 显示
    const newArr = [
      'NgButton',
      'NgBtnTop',
      'NgBtnBottom',
      'NgBuyNow',
      'NgForm',
      'NgWxfans',
      'NgWeChatTop',
      'NgWeChatBottom',
      'NgTelephone'
    ]
    if (newArr.includes(item.name)) formatData(item)
    if (item.name == 'NgRichText') item.styles.fontFamily = item.currStyleClass
    if (item.name == 'NgWxfans' && store.getLandingPageType == 'applet') item.currWxStyleClass = item.currStyleClass
    if (['NgQaChat'].includes(item.name)) item.qaModule = item.currStyleClass
    delete item.styleTypeMap
    const curData = store.getCurPageData
    // item.configCode = store.getDragType;
    if (name === 'interactive-layer') {
      if (!store.getDragIsPush) {
        store.modifyDragIndex(curData.elements.length)
        store.modifyDragIsPush(true)
        curData.elements.push(item)
      }
    } else if (className.indexOf(name) >= 0) {
      let [y, h, curIndex] = [e.offsetY, target.offsetHeight, target.dataset.index]
      const direction = y < h / 2
      if (!store.getDragIsPush) {
        if (direction) {
          if (curIndex == 0) {
            curData.elements.unshift(item)
          } else {
            curData.elements.splice(curIndex, 0, item)
          }
        } else {
          curIndex = +curIndex + 1
          curData.elements.splice(curIndex, 0, item)
        }
      } else {
        if (direction) {
          var i = curIndex == 0 ? 0 : curIndex - 1
          var result = curData.elements[i]['dragStatus'] == 2
        } else {
          var i = +curIndex + 1
          var result = curData.elements.length > i && curData.elements[i]['dragStatus'] == 2
        }

        if (result) return

        const temp = curData.elements.splice(store.getDragIndex, 1)
        curData.elements.splice(curIndex, 0, temp[0])
      }
      store.modifyDragIndex(curIndex)
      store.modifyDragIsPush(true)
    }
    // console.log(e, item, curData)
  }
  function handleDragLeave(e) {
    // console.log('leave', e)
  }
  function handleDrog(e) {
    if (!store.getDragType) return
    e.preventDefault()
    e.stopPropagation()
    store.modifyIsDragLeave(true)
    if (store.getCurPageData.elements.length) {
      const data = store.getCurPageData.elements[store.getDragIndex]
      // this.$delete(data, 'dragStatus')
      delete data.dragStatus
      store.dragEnd({ currData: store.getCurPageData, isPush: false, type: null })

      data.toolTop = calcToolTop(store.getCurPageData.elements, data)
      data.eboxValue.zIndex = store.getCurPageData.elements.length + 1
      store.modifyActiveItems(data)
    }
  }
  function handleSelectBg(item) {
    if (hasUseToolbar(item)) return
    item.toolTop = calcToolTop(store.getCurPageData.elements, item)
    if (item.backBtn) item.backBtn = false
    item.isBg = true
    item.isWire = false

    store.modifyActiveItems(item)
  }
  function handleSelectWire(item) {
    item.toolTop = calcToolTop(store.getCurPageData.elements, item)
    if (item.backBtn) item.backBtn = false
    item.isBg = false
    item.isWire = true
    store.modifyActiveItems(item)
  }
</script>

<style lang="scss" scoped>
  .interactive-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    bottom: 0;
    .interactive-layer__section {
      position: relative;
      &.nofloat-layer__section.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #81b0ff;
      }
    }
    .interactive-section {
      position: relative;
    }
    .wireframe {
      height: 100%;
      pointer-events: auto;
      &.selected:after,
      &.selected:before,
      &:hover:after,
      &:hover:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: -2px;
      }
      &.selected:before,
      &:hover:before {
        border: 1px solid rgba(0, 0, 0, 0.15);
      }
      &.selected:after,
      &:hover:after {
        border: 1px dashed #fff;
      }
    }

    .knob-item-layer .knob {
      pointer-events: auto;
      position: absolute;
      width: 7px;
      height: 7px;
      box-sizing: border-box;
      border: 1px solid #fff;
      background: #666;
      box-shadow: 0 0 2px 0 rgb(0 0 0 / 25%);
      &.tl {
        left: -3.5px;
        top: -3.5px;
      }
      &.tc {
        left: calc(50% - 3.5px);
        top: -3.5px;
      }
      &.tr {
        right: -3.5px;
        top: -3.5px;
      }
      &.mr {
        right: -3.5px;
        top: calc(50% - 3.5px);
      }
      &.br {
        right: -3.5px;
        bottom: -3.5px;
      }
      &.bc {
        left: calc(50% - 3.5px);
        bottom: -3.5px;
      }
      &.bl {
        left: -3.5px;
        bottom: -3.5px;
      }
      &.ml {
        left: -3.5px;
        top: calc(50% - 3.5px);
      }
    }
    .rotation-item {
      pointer-events: none;
      overflow: visible;
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: flex-end;
    }
    .rotation-item__button {
      pointer-events: auto;
      cursor: pointer;
      position: relative;
      margin-bottom: -35px;
      width: 19px;
      height: 19px;
      border-radius: 19px;
      box-shadow: 0 0 2px 0 rgb(0 0 0 / 25%);
      background-color: #666;
      i {
        color: #fff;
        font-size: 12px;
        font-weight: 700;
      }
      &::before {
        content: '';
        position: absolute;
        left: 9px;
        bottom: 20px;
        width: 0;
        height: 10px;
        border-left: 1px dashed rgba(0, 0, 0, 0.15);
      }
      &:hover {
        background-color: #4e90ff;
      }
    }

    .wireframe-layer,
    .absolute-frame,
    .knob-item-layer,
    .guides-layer,
    .guides-layer .guide {
      position: absolute;
    }
    .wireframe-layer,
    .knob-item-layer,
    .guides-layer {
      top: 0;
      left: 0;
    }
    .wait {
      background: #deedff;
      height: 35px;
      text-align: center;
      line-height: 35px;
      font-size: 12px;
      color: #666;
    }
  }
</style>
