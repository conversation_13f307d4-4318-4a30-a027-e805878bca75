<template>
  <div class="app-left-aside f-flex">
    <div class="tab-nav f-flex">
      <div
        v-for="(v, i) in state.tabNavList"
        :key="i"
        :class="['tab-nav__bar', { selected: state.tabNavCurrIndex == i }]"
        @click="handleClickCurr(i)"
      >
        <i :class="['iconfont', v.icon]" />{{ v.name }}
      </div>
    </div>
    <div class="tab-panel">
      <PageTemplate v-if="state.tabNavCurrIndex == 0" />
      <Modular v-if="state.tabNavCurrIndex == 1" />
      <Brick v-if="state.tabNavCurrIndex == 2" />
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { page as pageConfig } from '@ng_visualization/config/json_scheme.js'
  import { v4 as uuidv4 } from 'uuid'
  import { deepClone } from '@ng_visualization/utils/index.js'
  import PageTemplate from './PageTemplate.vue'
  import Modular from './Modular.vue'
  import Brick from './Brick.vue'

  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const state = reactive({
    tabNavCurrIndex: 2,
    tabNavList: [
      { icon: 'icon-yemianmoban_page-template', name: '页面模版' },
      { icon: 'icon-fuzhi_copy', name: '模块' },
      { icon: 'icon-zujian1', name: '组件' }
    ]
  })

  onMounted(() => {
    initData()
  })

  const handleClickCurr = (index) => {
    state.tabNavCurrIndex = index
  }
  const initData = () => {
    const actData = store.getActivityData
    if (actData.pages.length == 0) {
      const item = deepClone(pageConfig)
      item.uuid = uuidv4()
      actData.pages.push(item)
      setCurPageData(item, 0)
    } else {
      setCurPageData(actData.pages[0], 0)
    }
    store.modifyActivityData(actData)
  }
  const setCurPageData = (item, index) => {
    item.index = index
    store.modifyCurPageData(item)
  }
</script>

<style lang="scss" scoped>
  .app-left-aside {
    position: relative;
    box-sizing: border-box;
    padding-top: 56px;
    width: 100%;
    height: 100%;
    flex: 1;
    flex-direction: column;
    .tab-nav {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
    }
    .tab-nav__bar {
      flex: 1;
      height: 56px;
      display: flex;
      color: #666;
      font-size: 16px;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      border-bottom: 1px solid #e8e8e8;
      background-color: #fff;
      &.selected {
        color: #fff;
        box-sizing: border-box;
        border-bottom: none;
        border-left: none;
        background-color: #4e90ff;
        background:
          linear-gradient(-45deg, transparent 5px, #4e90ff 0) 0,
          linear-gradient(45deg, transparent 5px, #4e90ff 0) 100%;
        background-size: 51% 100%;
        background-repeat: no-repeat;
      }
      .iconfont {
        margin-right: 10px;
      }
    }
    .tab-nav__bar + .tab-nav__bar {
      border-left: 1px solid #e8e8e8;
    }
    .selected + .tab-nav__bar {
      border-left: none;
    }
    .clickable,
    .tab-nav__bar {
      cursor: pointer;
      user-select: none;
    }
    .tab-panel {
      display: flex;
      flex: 1;
      max-height: 100%;
      height: 100%;
    }
  }
</style>
