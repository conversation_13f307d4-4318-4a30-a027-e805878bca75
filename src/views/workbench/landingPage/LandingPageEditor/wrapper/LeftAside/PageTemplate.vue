<template>
  <div class="template-list">
    <div class="list-content flex-align">
      <Empty />
    </div>
  </div>
</template>

<script setup>
  import Empty from '../components/Empty.vue'
</script>

<style lang="scss" scoped>
  .template-list {
    position: relative;
    width: 100%;
    height: 100%;
    .list-content {
      height: 100%;
      flex-direction: column;
      // overflow-y: scroll;
    }
  }
</style>
