<template>
  <div class="brick-list">
    <div class="type-list">
      <a-tree
        :tree-data="state.menulist"
        :props="{ children: 'children', label: 'label' }"
        default-expand-all
        class="type-list__tree"
      >
        <template #title="{ dataRef }">
          <span class="custom-tree-node">
            <span
              :class="[
                dataRef?.children?.length ? 'type-list__collection' : 'type-list__item',
                { selected: store.getCurrMenusIndex == dataRef.key }
              ]"
              @click="handleNodeClick(dataRef)"
            >
              {{ dataRef.label }}
            </span>
          </span>
        </template>
      </a-tree>
    </div>
    <div class="content-list flex-align w-full">
      <template v-if="store.getBrickTempl && store.getBrickTempl.length">
        <div v-for="v in store.getBrickTempl" :key="v.name" class="content-list__section">
          <template v-if="store.allNoToolbar.includes(v.name)">
            <FirstScreenVerify :item="v" />
          </template>
          <template v-else>
            <div class="section__label flex-align" v-if="v.menuName">
              <div class="section__name">{{ v.menuName }}</div>
              <div class="section__description">{{ v.menuDesc }}</div>
            </div>
            <!-- :class="[{ 'allowed-gray': isAllowedDrag || disabledFunc(v) }]" -->
            <div @dragstart.stop="handleDragStart" @dragend="handleDragEnd">
              <div class="section__item" v-for="(t, key) in v.styleTypeMap" :key="key">
                <div class="section__label flex-align" :class="[key == 0 ? '' : 'mt-20px']" v-if="t.menuName">
                  <div class="section__name">{{ t.menuName }}</div>
                  <div class="section__description">{{ t.menuDesc }}</div>
                </div>
                <div
                  class="drag-item content-list__item clickable"
                  :data-type="v.name"
                  :data-currl="t.key"
                  :draggable="disabledFunc(v)"
                  @dblclick.stop="handleDblclick(v, t)"
                >
                  <img
                    :src="t.value"
                    class="content-list__thumbnail"
                    draggable="false"
                    :style="['NgHeadline', 'NgReading'].includes(v.name) ? 'padding:10px 0;' : ''"
                  />
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
      <Empty v-else />
    </div>
  </div>
</template>

<script setup>
  import { onBeforeMount, watch, reactive } from 'vue'
  import { calcToolTop, deepClone, formatData } from '@ng_visualization/utils/index.js'
  import { v4 as uuidv4 } from 'uuid'
  import brickType from '@ng_visualization/config/brick_type.json'
  import configList from '@ng_visualization/config/config.js'
  import Empty from '../components/Empty.vue'
  import FirstScreenVerify from './brickComp/FirstScreenVerify.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import { message } from 'ant-design-vue'
  const store = useNgVisualizationStore()

  const state = reactive({
    loading: false,
    adtype: null,
    disabledTooltipDesc: '页面上已添加互动组件，<br>不能使用该组件',
    menulist: [], // 菜单列表
    configList // 组件模版配置信息
  })

  watch(
    () => store.getCurrMenusIndex,
    (newVal) => {
      const configList = JSON.parse(JSON.stringify(state.configList))
      let newArr = configList[newVal]
      store.modifyBrickTempl(newArr)
    }
  )

  onBeforeMount(() => {
    state.menulist = brickType
    store.modifyIsAllowedDrag()
    store.modifyCurrMenusIndex('Image')
    if (store.getCurrMenusIndex) store.modifyBrickTempl(state.configList[store.getCurrMenusIndex])
  })

  const disabledFunc = (data) => {
    if (!data.limitSize) return false
    const elements = store.getActivityData.pages[0]?.elements
    const newArr =
      elements?.filter((item) => {
        return item.name == data.name
      }) || []
    return newArr?.length + 1 < data.limitSize
  }
  const handleNodeClick = (data, node) => {
    const cl = data.children
    const currVal = cl && cl.length ? cl[0].key : data.key
    store.modifyCurrMenusIndex(currVal)
    console.log(currVal)
    if (currVal == 'FirstScreenVerify') {
      const obj = store.getCurPageData.elements.find((v) => v.name == 'Ng' + currVal)
      if (obj) store.modifyActiveItems(obj)
    } else {
      if (store.allNoToolbar.includes(store.getActiveItems.name)) {
        const index = store.getCurPageData.elements.findIndex((v) => v.name == store.getActiveItems.name)
        if (index >= 0 && store.getCurPageData.elements.length > 1) {
          store.modifyActiveItems(store.getCurPageData.elements[index == 0 ? 1 : 0])
        } else {
          store.modifyActiveItems({})
        }
      }
    }
  }
  const handleDragStart = (e) => {
    const type = e.target.dataset.type
    const item = store.getBrickTempl.find((item) => item.name === type)
    if (item && validateCom(item, 2, true)) return

    store.modifyDragIndex('x')
    store.modifyDragType(type)
    store.modifyCurrStyleClass(e.target.dataset.currl)
  }
  const handleDragEnd = (e) => {
    const type = e.target.dataset.type
    const item = store.getBrickTempl.find((item) => item.name === type)
    if (item && validateCom(item, 2)) return
    if (store.getCurPageData.elements.length) {
      if (!store.getIsDragLeave && store.getDragIndex >= 0) {
        store.getCurPageData.elements.splice(store.getDragIndex, 1)
        store.modifyActiveItems({})
        store.dragEnd({ currData: store.getCurPageData, isPush: false, type: null })
      }
      store.modifyIsDragLeave(false)
      if (store.getCurPageData.elements.length == store.getDragIndex - 1) {
        // this.$delete(store.getCurPageData.elements[store.getDragIndex], 'dragStatus')
        store.dragEnd({ currData: store.getCurPageData, isPush: false, type: null })
      }
    }
    store.modifyUpdatetime()
  }
  const validateCom = (data, type, isTip) => {
    try {
      if (!data.limitSize) return false
      const elements = store.getActivityData.pages[0]?.elements
      const newArr = elements?.filter((item) => {
        return item.name == data.name
      })

      if (
        (type == 1 && newArr.length >= data.limitSize) ||
        (type == 2 && newArr.length > data.limitSize) ||
        (type == 1 && data.name == 'NgQaChat' && newArr.length + 1 >= data.limitSize)
      ) {
        if (!isTip) message.warning('组件已上限，请删除后再添加')
        return true
      }
      return false
    } catch (error) {
      return false
    }
  }
  const handleDblclick = (data, subItem) => {
    // if (this.isAllowedDrag) return
    if (validateCom(data, 1)) return
    const item = deepClone(data)
    store.modifyCurrStyleClass(subItem.key)
    item.uuid = uuidv4()
    item.currStyleClass = store.getCurrStyleClass
    item.isBg = false
    item.isWire = true
    const newArr = ['NgButton', 'NgBtnTop', 'NgBtnBottom', 'NgForm', 'NgBuyNow']
    if (newArr.includes(item.name)) formatData(item)
    if (item.name == 'NgRichText') item.styles.fontFamily = subItem.key
    if (['NgQaChat'].includes(item.name)) item.qaModule = item.currStyleClass
    item.eboxValue.zIndex = store.getCurPageData.elements.length + 1
    delete item.styleTypeMap
    store.getCurPageData.elements.push(item)
    item.toolTop = calcToolTop(store.getCurPageData.elements, item)
    store.modifyActiveItems(item)
    store.modifyUpdatetime()
    store.modifyIsAllowedDrag()
  }
</script>

<style lang="scss" scoped>
  .brick-list {
    display: flex;
    flex: 1;
    height: 100%;
    .type-list {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      overflow-y: auto;
      min-width: 118px;
      box-sizing: border-box;
      padding: 16px 8px 0;
      background-color: #fff;
      box-shadow: 0 2px 6px 0 rgb(0 0 0 / 8%);
    }
    .content-list {
      flex: 1;
      height: 100%;
      overflow-y: scroll;
      flex-direction: column;
    }
    .content-list__section {
      padding: 24px 24px 32px;
      width: 100%;
    }
    .section__label {
      height: 24px;
    }
    .section__name {
      color: #333;
      font-size: 16px;
      white-space: nowrap;
    }
    .section__description {
      padding-left: 8px;
      color: #999;
      font-size: 12px;
    }
    .content-list__item {
      display: inline-block;
      font-size: 0;
      margin-top: 16px;
      border-radius: 4px;
      box-shadow: 0 2px 6px 0 rgb(0 0 0 / 10%);
      background-color: #fff;
      text-align: center;
      transition: all 0.15s;
      &:hover {
        transform: scale(1.06);
      }
    }
    .content-list__thumbnail {
      max-width: 100%;
      max-height: 100%;
      border-radius: 4px;
    }
  }
  .allowed-gray {
    pointer-events: none;
    // cursor: not-allowed;
    // opacity: 0.6;
    // filter: grayscale(1);
  }
</style>
