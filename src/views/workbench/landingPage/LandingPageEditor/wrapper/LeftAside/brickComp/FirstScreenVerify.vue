<template>
  <div class="wrapper">
    <div class="switch flex-y-center">
      <span class="desc">开启验证码</span>
      <a-switch v-model:checked="state.verCodeFlag" @change="onChnage" />
    </div>
    <div class="tip">注：开启后，用户将先进行验证码验证，成功后才可以浏览页面</div>
    <div class="switch flex-col" v-if="state.verCodeFlag">
      <span class="desc">温馨提示</span>
      <div class="textarea">
        <a-textarea
          v-model:value.trim="state.verTips"
          :rows="7"
          show-word-limit
          :maxlength="200"
          show-count
          class="txt"
          @keydown.space.prevent
          @change="onChangeTextarea"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { v4 as uuidv4 } from 'uuid'
  import { cloneDeep } from 'lodash-es'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    }
  })

  const state = reactive({
    verCodeFlag: false,
    verTips:
      '亲，如果您是为了做任务，到这一步说明您已经完成任务，请不要再点了，把机会留给真正需要的人，谢谢！！！如果您真的需要，请输入验证码，联系客服，添加客服注册免费领取奖品，名额有限，仅限前200！'
  })

  onMounted(() => {
    let data = store.getCurPageData.elements.find((v) => v.name == props.item.name)
    if (data && data.verCodeFlag) {
      state.verCodeFlag = data.verCodeFlag
      state.verTips = data.verTips
    }
  })

  const onChangeTextarea = () => {
    store.getActiveItems.verTips = state.verTips.slice(0, 200)
  }

  const onChnage = () => {
    console.log(state.verCodeFlag)
    if (state.verCodeFlag) {
      if (!state.verTips) state.verTips = store.defaultVerTips
      const item = cloneDeep(props.item)
      item.uuid = uuidv4()
      item.currStyleClass = store.getCurrStyleClass
      item.isBg = false
      item.isWire = true
      item.eboxValue.zIndex = store.getCurPageData.elements.length + 1
      item.verCodeFlag = state.verCodeFlag
      item.verTips = state.verTips
      delete item.styleTypeMap
      store.getCurPageData.elements.push(item)
      store.modifyActiveItems(item)
      store.modifyUpdatetime()
      store.modifyIsAllowedDrag()
    } else {
      const index = store.getCurPageData.elements.findIndex((v) => v.uuid === store.getActiveItems.uuid)
      if (index >= 0) {
        store.getCurPageData.elements.splice(index, 1)
        store.modifyActiveItems({})
      }
    }
    console.log(props.item, store.getActiveItems)
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    .switch {
      .desc {
        margin-right: 8px;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
    }
    .tip {
      margin: 8px 0 24px;
      font-size: 14px;
      color: #fe4d4f;
      line-height: 17px;
    }
    .textarea {
      margin-top: 18px;
      .txt {
      }
    }
  }
  :deep(.ant-switch.ant-switch-checked) {
    &:hover {
      background: #2f88ff;
    }
    background: #2f88ff;
  }
  :deep(.ant-switch:hover) {
    background: #2f88ff;
  }
  :deep(.textarea) {
    position: relative;
    background: #ffffff;
    border-radius: 6px;
    padding-bottom: 2px;
    border: 1px dashed #dcdcdc;
    .txt {
      border: none;
      resize: none;
      border-radius: 6px;
    }
    .ant-input:focus,
    .ant-input:hover,
    .ant-input-focused {
      border-color: transparent;
    }
    .num {
      position: absolute;
      right: 10px;
      bottom: 10px;
      font-weight: 400;
      font-size: 12px;
      color: #ababab;
      line-height: 17px;
      span {
        color: #313233;
      }
    }
  }
</style>
