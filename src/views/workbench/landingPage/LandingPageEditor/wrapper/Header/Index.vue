<template>
  <div class="top-bar">
    <div class="top-bar__section flex-align">
      <a-tooltip placement="right">
        <template #title><span>返回列表</span></template>
        <a-button type="text" :icon="h(LeftOutlined)" class="back" @click="handleClosePage">列表</a-button>
      </a-tooltip>
      <div class="landing-info">
        <div class="landing-name flex-align">
          <span class="text ellipsis"> {{ activityData.name_alias || '新增落地页' }}</span>
          <EditOutlined class="edit" @click="handleSet" />
        </div>
      </div>
      <div class="landing-modify-time">上次修改：{{ store.getUpdateTime }}</div>
    </div>
    <div class="top-bar__section flex-align">
      <a-tooltip placement="bottom">
        <template #title><span>保存</span></template>
        <a-button type="text" class="back1" :loading="bodyloading" @click="saveActivity(null)">
          <i v-show="!bodyloading" class="iconfont icon-baocun" />
        </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title><span>预览效果</span></template>
        <a-button
          type="text"
          :icon="h(EyeOutlined)"
          :disabled="bodyloading"
          class="back1"
          @click="emits('toPreview', { cmd: 1 })"
        />
      </a-tooltip>
      <a-button class="publish flex-align" type="primary" :loading="bodyloading" round @click="saveActivity('title')">
        保存并发布
      </a-button>
    </div>

    <a-modal
      title="落地页设置"
      v-model:open="state.dialogVisible"
      :append-to-body="true"
      destroyOnClose
      width="430px"
      top="0"
      :footer="null"
      wrapClassName="jsonpretty-dialog"
      @cancel="handleClose"
    >
      <a-form
        v-if="state.dialogStatus == 'siteset'"
        ref="ruleForm"
        :model="activityData"
        :rules="rules"
        class="demos-addForm"
        layout="vertical"
      >
        <a-form-item label="页面名称" name="name_alias">
          <a-input
            v-model:value.trim="activityData.name_alias"
            placeholder="请输入页面名称(1~64个字符)"
            size="middle"
            class="input-theme"
            :maxlength="64"
            show-count
            @keydown.space.prevent
          />
        </a-form-item>
        <a-form-item label="落地页标题" name="title">
          <a-tooltip placement="right">
            <template #title><span>您的落地页发布之后，此标题将会显示在浏览器的标题栏</span></template>
            <QuestionCircleOutlined
              class="t-tips el-icon-question"
              style="position: absolute; top: -26px; left: 86px; font-size: 14px; cursor: pointer"
            />
          </a-tooltip>
          <a-input
            v-model:value.trim="activityData.title"
            placeholder="请输入落地页标题(1~64个字符)"
            size="middle"
            class="input-theme"
            :maxlength="64"
            show-count
            @keydown.space.prevent
          />
        </a-form-item>
        <a-form-item label="页面备注" name="description">
          <a-textarea
            v-model:value.trim="activityData.description"
            :rows="5"
            :maxlength="255"
            placeholder="请输入页面描述(255字以内)"
            class="input-theme"
            size="middle"
          />
        </a-form-item>
        <div class="footer flex-align">
          <a-button
            type="primary"
            class="btn comfrim"
            :loading="state.activityDataLoading"
            @click="submitForm(ruleForm)"
            >确定</a-button
          >
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { h, createVNode, onBeforeMount, reactive, ref } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import {
    EditOutlined,
    LeftOutlined,
    EyeOutlined,
    ExclamationCircleOutlined,
    QuestionCircleOutlined
  } from '@ant-design/icons-vue'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const props = defineProps({
    activityData: {
      type: Object,
      default: () => ({})
    },
    bodyloading: {
      type: Boolean,
      default: false
    }
  })

  const emits = defineEmits(['update:activityData', 'update:bodyloading', 'saveActivity', 'toPreview'])

  const ruleForm = ref(null)
  const state = reactive({
    siteStatus: 1 as any,
    dialogVisible: false,
    activityDataLoading: false,
    dialogStatus: 'siteset',
    stitle: null
  })

  const rules = ref({
    name_alias: [{ required: true, message: '请输入页面名称', trigger: 'blur' }],
    title: [{ required: true, message: '请输入落地页标题', trigger: 'blur' }]
  })

  onBeforeMount(() => {})

  // 站点设置
  const handleSet = () => {
    state.dialogStatus = 'siteset'
    state.dialogVisible = true
    state.siteStatus = true
  }
  const saveActivity = (data: any) => {
    state.stitle = data
    state.siteStatus = false
    if (!props.activityData.name_alias || !props.activityData.title) {
      state.dialogStatus = 'siteset'
      state.dialogVisible = true
      return
    }
    emits('saveActivity', { data })
  }
  const handleClosePage = () => {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '离开此页面，系统可能不会保存您所做的更改',
      okText: '确定',
      cancelText: '取消',
      wrapClassName: 'landing-page-confirm',
      onOk() {
        router.go(-1)
      }
    })
  }
  // 确定
  const submitForm = (formName: any) => {
    formName.validate().then((valid: any) => {
      if (!valid) return false
      handlerSure()
    })
  }
  const handlerSure = () => {
    if (state.dialogStatus == 'siteset') {
      state.dialogVisible = false
      if (state.siteStatus) {
        message.success('落地页设置成功')
      } else {
        emits('saveActivity', { data: state.stitle })
      }
    }
  }
  const handleClose = () => {
    state.dialogVisible = false
  }
</script>

<style lang="scss" scoped>
  .top-bar {
    display: flex;
    position: relative;
    height: 60px;
    justify-content: space-between;
    .back,
    .back1 {
      color: #444;
      &:hover {
        color: #409eff;
      }
    }
    .back {
      font-size: 16px;
    }
    .back1 {
      font-size: 22px;
      padding: 0 5px;
    }
    .landing-info {
      font-size: 15px;
      color: #444;
      margin: 0 25px 0 32px;
      .landing-name {
        max-width: 160px;
      }
      .text {
        display: block;
        max-width: 140px;
      }
      .edit {
        margin-left: 5px;
        cursor: pointer;
      }
    }
    .landing-modify-time {
      font-size: 12px;
      font-weight: 400;
      color: #999;
      line-height: 25px;
    }
    .icon-baocun {
      font-size: 22px;
    }
    .publish {
      padding: 10px 27px;
      border-color: #2f88ff;
      background-color: #2f88ff;
      margin-left: 20px;
      color: #fff;
      &:hover {
        color: #fff;
      }
    }
  }

  .footer {
    justify-content: flex-end;
    .btn {
      border-color: #2f88ff;
      background-color: #2f88ff;
    }
  }

  ::v-deep {
    .jsonpretty-dialog {
      .el-dialog__body {
        max-height: calc(100vh - 150px);
        overflow: auto;
      }
    }
    .preview .el-icon-view,
    .preview .el-icon-folder-opened {
      font-size: 22px;
      font-weight: bolder;
    }
    .demos-addForm {
      padding: 0 10px;
      .a-form-item__label {
        line-height: 24px;
      }
      .t-tips {
        position: absolute;
        top: -30px;
        left: 80px;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
</style>
