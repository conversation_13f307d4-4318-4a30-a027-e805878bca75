<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">图片内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">选择</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <div class="uimgg-box upf flex-column" @click="uploadsImg('library')">
                  <img v-if="store.getActiveItems.srcs.imgurl" :src="store.getActiveItems.srcs.imgurl" class="avatar" />
                  <template v-else><PlusOutlined /><span>添加图片</span></template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">图片链接</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" style="padding-top: 10px">
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1" style="padding-left: 12px">
                <LinkBlock :active-items="store.getActiveItems" :discern="1" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">图片布局</div>
      </div>
      <Position />
    </div>
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        :isH5Upload="dialog.isH5Upload"
        :size="1"
        :fileSize="5 * 1024 * 1024"
        :extension="'.jpg,jpeg,.png,.gif'"
        type="image"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { nextTick, reactive } from 'vue'
  import { debounce, isObject, isArray, cloneDeep } from 'lodash-es'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import LinkBlock from '../../components/LinkBlock.vue'
  import Position from '../../components/Position.vue'
  import { getContainerHeight } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  const store = useNgVisualizationStore()

  const dialog = reactive({
    visible: false,
    title: '',
    width: '',
    type: '',
    isH5Upload: true
  })
  const uploadsImg = (type) => {
    dialog.type = type
    dialog.title = '图片素材'
    dialog.width = '80%'
    dialog.visible = true
  }
  const onEvent = (v) => {
    dialog.visible = false
    if (v?.data?.length) {
      const img = v.data[0]
      if (!img) return
      let pic = img?.file_url
      let calcH = 0
      if (isArray(img)) {
        store.getActiveItems.sensitiveImgs = img
        pic = img[0]?.original_image || ''
        calcH = (store.getCanvasSize.width / img?.[0]?.fileInfo?.width) * img?.[0]?.fileInfo?.height
      } else {
        calcH = (store.getCanvasSize.width / img?.width) * img.height
      }
      store.getActiveItems.srcs = {
        height: img?.[0]?.fileInfo?.height,
        icId: '',
        size: img?.[0]?.fileInfo?.file_size,
        imgurl: pic,
        width: img?.[0]?.fileInfo?.width
      }
      store.getActiveItems.eboxValue.bgHeight = calcH
      store.getActiveItems.boxBg.height = calcH + 'px'
      store.getActiveItems.eboxValue.height = calcH
      store.getActiveItems.styles.height = calcH / 100 + 'rem'
      store.modifyUpdatetime()
    }
  }

  function handleInputText(val) {
    nextTick(() => {
      getContainerHeight(`#image-${store.getActiveItems.uuid}`, '.odd-picture')
        .then((height) => {
          console.log(height, '图片高度')
          store.getActiveItems.eboxValue.height = height
          store.getActiveItems.styles.height = height / 100 + 'rem'
          const bgH = store.getActiveItems.eboxValue.y + height
          store.getActiveItems.eboxValue.bgHeight = bgH
          store.getActiveItems.boxBg.height = bgH + 'px'
        })
        .catch((error) => {
          console.error('获取图片高度失败:', error)
        })
    })
  }
</script>

<style lang="scss" scoped></style>
