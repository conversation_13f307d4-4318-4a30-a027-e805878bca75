<template>
  <div class="control-panel__wrapper">
    <div v-if="store.getActiveItems.currStyleClass != 'style-three'" class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">按钮</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">文案</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 flex-align">
                <a-input
                  v-model:value.trim="store.getActiveItems.textContent"
                  :maxlength="20"
                  class="elselect_box input-theme"
                  @keydown.space.prevent
                />
                <ColorPicker
                  :value="store.getActiveItems.styles.btnColor"
                  class="ml12 btn-color-picker"
                  @change="(val) => (store.getActiveItems.styles.btnColor = val)"
                  @input="(val) => (store.getActiveItems.styles.btnColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">链接</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" style="padding-top: 10px">
          <!-- <span class="control-panel-group__label flex-align"></span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <LinkBlock :active-items="store.getActiveItems" :isLink="true" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="store.getActiveItems.currStyleClass != 'style-three'" class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">样式</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">文字</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.btnColor"
                  show-alpha
                  class="bgPicker"
                  @change="(val) => (store.getActiveItems.styles.btnColor = val)"
                  @input="(val) => (store.getActiveItems.styles.btnColor = val)"
                />
                <FonstSize :size="store.getActiveItems.eboxValue.fontSize" type="text" @inputText="handleChageText" />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align" style="width: 52px">填充</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.btnBackgroundColor"
                  show-alpha
                  class="bgPicker"
                  @change="(val) => (store.getActiveItems.styles.btnBackgroundColor = val)"
                  @input="(val) => (store.getActiveItems.styles.btnBackgroundColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align" style="width: 52px">边框</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.borderColor"
                  show-alpha
                  class="bgPicker"
                  @change="(val) => (store.getActiveItems.styles.borderColor = val)"
                  @input="(val) => (store.getActiveItems.styles.borderColor = val)"
                />
                <div class="input-inline flex-align ml15">
                  <span class="left-l">粗细</span>
                  <a-input-number
                    v-model:value="store.getActiveItems.eboxValue.borderWidth"
                    controls-position="right"
                    size="medium"
                    :min="1"
                    :max="100"
                    class="fix-input__number"
                    @change="(val) => handleChange(val, 'borderWidth')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">圆角</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <div class="flex-1">
                  <a-slider
                    v-model:value="store.getActiveItems.eboxValue.borderRadius"
                    class="fslider"
                    :min="0"
                    :max="100"
                    @change="(val) => handleChange(val, 'borderRadius')"
                  />
                </div>
                <a-input-number
                  v-model:value="store.getActiveItems.eboxValue.borderRadius"
                  class="finputval"
                  size="mini"
                  :min="0"
                  :max="100"
                  :controls="false"
                  @change="(val) => handleChange(val, 'borderRadius')"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="store.getActiveItems.currStyleClass == 'style-three'" class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">图片内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">选择</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <div class="uimgg-box upf flex-column" @click="uploadsImg('library')">
                  <img v-if="store.getActiveItems.textContent" :src="store.getActiveItems.textContent" class="avatar" />
                  <template v-else><i class="el-icon-plus" /><span>添加图片</span></template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">按钮位置</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" style="padding-top: 10px">
          <!-- <span class="control-panel-group__label flex-align"></span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <a-radio-group v-model:value="store.getActiveItems.position" @change="onChangePosition">
                  <a-radio-button value="none" v-if="store.getActiveItems.name == 'NgButton'">正常</a-radio-button>
                  <a-radio-button value="top" v-if="['NgButton', 'NgBtnTop'].includes(store.getActiveItems.name)">
                    顶部
                  </a-radio-button>
                  <a-radio-button value="bottom" v-if="['NgButton', 'NgBtnBottom'].includes(store.getActiveItems.name)">
                    底部
                  </a-radio-button>
                </a-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">按钮布局</div>
      </div>
      <Position
        :disabled="
          store.getActiveItems.currStyleClass == 'style-three' &&
          !['top', 'bottom'].includes(store.getActiveItems.position)
        "
        :isPosition="['top', 'bottom'].includes(store.getActiveItems.position)"
      />
    </div>
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        :isH5Upload="dialog.isH5Upload"
        :fileSize="5 * 1024 * 1024"
        :extension="'.jpg,jpeg,.png,.gif'"
        :size="1"
        type="image"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { debounce, isArray } from 'lodash-es'
  import { nextTick, computed, reactive } from 'vue'
  import ColorPicker from '@ng_visualization/components/ColorPicker.vue'
  import LinkBlock from '../../components/LinkBlock.vue'
  import Position from '../../components/Position.vue'
  import FonstSize from '../../components/FontSize.vue'
  import { getContainerHeight } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  const store = useNgVisualizationStore()

  const state = reactive({
    dialogRadio: 1,
    payflag: true,
    selectPicType: null
  })
  const dialog = reactive({
    visible: false,
    title: '',
    width: '',
    type: '',
    isH5Upload: true
  })
  const uploadsImg = (type) => {
    dialog.type = type
    dialog.title = '图片素材'
    dialog.width = '80%'
    dialog.visible = true
  }
  const onEvent = (v) => {
    dialog.visible = false
    if (v?.data?.length) {
      const img = v.data[0]
      let pic = img?.file_url
      let calcH = 0
      if (isArray(img)) {
        store.getActiveItems.sensitiveImgs = img
        pic = img[0]?.original_image || ''
        calcH = (store.getCanvasSize.width / img?.[0]?.fileInfo?.width) * img?.[0]?.fileInfo?.height
      } else {
        calcH = (store.getCanvasSize.width / img?.width) * img.height
        delete store.getActiveItems.sensitiveImgs
      }
      store.getActiveItems.textContent = pic
      console.log(calcH)
      store.getActiveItems.eboxValue.bgHeight = calcH
      store.getActiveItems.boxBg.height = calcH + 'px'
      store.getActiveItems.eboxValue.height = calcH
      store.getActiveItems.styles.height = calcH / 100 + 'rem'
      store.modifyUpdatetime()
    }
  }

  function handleInputText(val) {
    nextTick(() => {
      const type =
        {
          NgButton: 'button',
          NgBtnTop: 'btnTop',
          NgBtnBottom: 'btnBottom'
        }[store.getActiveItems.name] || 'button'
      getContainerHeight(`#${type}-${store.getActiveItems.uuid}`, '.sens-picture')
        .then((calcH) => {
          console.log(calcH, '图片高度')
          store.getActiveItems.eboxValue.bgHeight = calcH
          store.getActiveItems.boxBg.height = calcH + 'px'
          store.getActiveItems.eboxValue.height = calcH
          store.getActiveItems.styles.height = calcH / 100 + 'rem'
        })
        .catch((error) => {
          console.error('获取图片高度失败:', error)
        })
    })
  }

  function onChangePosition() {
    let num = 0
    store.getActiveItems.eboxValue.y = num
    store.getActiveItems.eboxValue.top = num
    store.getActiveItems.styles.top = '0rem'
    store.getActiveItems.styles.bottom = '0rem'
  }

  function handleChageText(data) {
    if (data.type === 'text') {
      store.getActiveItems.eboxValue.fontSize = data.size
      store.getActiveItems.styles.fontSize = data.size / 100 + 'rem'
      store.modifyUpdatetime()
    }
  }

  function handleChange(val, type) {
    console.log(val, type)
    if (type === 'borderWidth') store.getActiveItems.styles.borderWidth = val / 100 + 'rem'
    if (type === 'borderRadius') store.getActiveItems.styles.borderRadius = val / 100 + 'rem'
    store.modifyUpdatetime()
  }
</script>

<style lang="scss" scoped>
  .link {
    .link-item {
      font-size: 12px;
      line-height: 20px;
    }
    .label {
      margin: 0;
      color: #333;
    }
    .content {
      color: #999;
      font-size: 14px;
      max-width: 142px;
    }
  }
  .operation {
    margin-top: 10px;
  }
  .labels {
    color: #999;
    font-size: 14px;
    max-width: 245px;
    margin: 4px 0 6px 0;
  }
  .mt16 {
    margin-top: 16px;
  }

  .ml12 {
    margin-left: 12px;
  }

  .ml15 {
    margin-left: 15px;
  }

  .mt10 {
    margin-top: 10px;
  }

  .btnCnt {
    position: absolute;
    color: #333;
    font-size: 12px;
    left: -67px;
    display: inline-block;
    width: 55px;
    top: 4px;
  }

  ::v-deep {
    .btn-color-picker {
      .c__panel {
        left: -236px;
      }
    }
    .scale-group.font .el-checkbox-button--medium .el-checkbox-button__inner {
      padding: 7px 10px;
    }
    .radio-button-group-control {
      margin-bottom: 10px;
      .el-radio {
        margin-right: 24px;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #333;
        font-weight: 400;
      }
      .el-radio__input.is-checked .el-radio__inner {
        background-color: #338aff;
        border-color: #338aff;
      }
    }
    .tagPosition-group.t {
      .el-radio-button--medium .el-radio-button__inner {
        padding: 8px;
      }
    }
    .input-inline.pay {
      width: 220px;
      .left-l.y {
        border-left: 1px solid #d9d9d9;
      }
      .red {
        color: #f56c6c;
        font-size: 13px;
      }
    }
  }
</style>
