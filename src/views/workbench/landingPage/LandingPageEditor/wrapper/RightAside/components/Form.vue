<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">表单内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">文案</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <a-select
                  v-model:value="store.getActiveItems.form_fields.id"
                  show-search
                  placeholder="请选择当前组件中的默认表单"
                  class="flex-1 select-theme"
                  style="width: 100%"
                  @change="onsync"
                >
                  <a-select-option v-for="v in state.formlist" :key="v.id" :value="v.id">{{ v.name }}</a-select-option>
                </a-select>
                <div class="c-#ff4d4f">若您更改表单内容，须再点击落地页保存并发布</div>
                <div class="operate">
                  <a-button type="link" @click="handleCreateForm"> <PlusOutlined /> 创建表单 </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align" style="line-height: 1.2">提交成功提示</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <a-textarea
                  v-model:value="store.getActiveItems.form_fields.tipValue"
                  :rows="3"
                  :maxlength="20"
                  class="input-theme"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">表单样式</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">字段颜色</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <ColorPicker
                  :value="store.getActiveItems.txtColors.title"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.txtColors.title = val)"
                  @input="(val) => (store.getActiveItems.txtColors.title = val)"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-if="store.getActiveItems.currStyleClass != 'style-three'" class="control-panel-group">
          <span class="control-panel-group__label flex-align">文本框</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 inline-textarea">
                <div
                  v-for="(v, index) in store.getActiveItems.eboxValue.inputBorderList"
                  :key="index"
                  class="inline-item upf"
                  @click="handleClickTextarea(v, index)"
                >
                  <div class="item_image" :class="{ selected: v.check }" :style="`background-image:url(${v.value})`" />
                  <div class="item_text">{{ v.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">背景</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 inline-textarea" style="flex-direction: column">
                <a-radio-group
                  v-model:value="store.getActiveItems.boxBg.bgMode"
                  size="medium"
                  class="tagPosition-group"
                >
                  <a-radio-button :value="1">使用颜色</a-radio-button>
                  <a-radio-button :value="2">使用图片</a-radio-button>
                </a-radio-group>
                <div class="bgs">
                  <ColorPicker
                    v-if="store.getActiveItems.boxBg.bgMode == 1"
                    :value="store.getActiveItems.boxBg.backgroundColor"
                    class="bgPicker btn-color-picker"
                    @change="(val) => (store.getActiveItems.boxBg.backgroundColor = val)"
                    @input="(val) => (store.getActiveItems.boxBg.backgroundColor = val)"
                  />
                  <div
                    class="uimgg-box upf"
                    style="width: 230px"
                    v-if="store.getActiveItems.boxBg.bgMode == 2"
                    @click="uploadsImg('library')"
                  >
                    <img
                      v-if="store.getActiveItems.boxBg.backgroundImage"
                      :src="store.getActiveItems.boxBg.backgroundImage"
                      class="avatar"
                    />
                    <template v-else><PlusOutlined /><span style="font-size: 16px">添加图片</span></template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">按钮样式</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">文字</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.btnStyle.color"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.btnStyle.color = val)"
                  @input="(val) => (store.getActiveItems.btnStyle.color = val)"
                />
                <FonstSize :size="store.getActiveItems.eboxValue.fontSize" type="text" @inputText="handleChageText" />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align" style="width: 52px">填充</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <ColorPicker
                  :value="store.getActiveItems.btnStyle.backgroundColor"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.btnStyle.backgroundColor = val)"
                  @input="(val) => (store.getActiveItems.btnStyle.backgroundColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">边框</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.btnStyle.borderColor"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.btnStyle.borderColor = val)"
                  @input="(val) => (store.getActiveItems.btnStyle.borderColor = val)"
                />
                <div class="input-inline flex-align ml15">
                  <span class="left-l">粗细</span>
                  <a-input-number
                    v-model:value="store.getActiveItems.eboxValue.btnWidth"
                    controls-position="right"
                    size="medium"
                    :min="0"
                    :max="100"
                    class="fix-input__number"
                    @change="(val) => handleChange(val, 'btnWidth')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">圆角</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <div class="flex-1">
                  <a-slider
                    v-model:value="store.getActiveItems.eboxValue.btnRadius"
                    class="fslider"
                    :min="0"
                    :max="100"
                    @change="(val) => handleChange(val, 'btnRadius')"
                  />
                </div>
                <a-input-number
                  v-model:value="store.getActiveItems.eboxValue.btnRadius"
                  class="finputval"
                  size="mini"
                  :min="0"
                  :max="100"
                  :controls="false"
                  @change="(val) => handleChange(val, 'btnRadius')"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        :isH5Upload="dialog.isH5Upload"
        :size="1"
        :fileSize="5 * 1024 * 1024"
        :extension="'.jpg,jpeg,.png,.gif'"
        type="image"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { onBeforeMount, nextTick, reactive } from 'vue'
  import { debounce, isArray } from 'lodash-es'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import ColorPicker from '@ng_visualization/components/ColorPicker.vue'
  import FonstSize from '../../components/FontSize.vue'
  import { getFormList, getFormInfo } from '@/views/workbench/landingPage/formList/index.api.ts'
  import { getContainerHeight } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import { useRouter } from '@/hooks/use-router'
  const { routerResolve } = useRouter()
  const store = useNgVisualizationStore()

  const state = reactive({
    formlist: []
  })

  onBeforeMount(() => {
    fetchDataHtmlDropDownList()
  })

  function handleClickTextarea(v, index) {
    // 选择文本框
    store.getActiveItems.eboxValue.inputBorderList.forEach((v, lindex) => {
      v.check = false
      if (lindex === index) {
        v.check = true
        store.getActiveItems.currInputStyleClass = v.key
        handleInputText()
      }
    })
  }
  function handleCreateForm() {
    routerResolve({ name: 'FormList', query: {} })
  }
  function handleChange(val, type) {
    console.log(val, type)
    if (type === 'btnWidth') store.getActiveItems.btnStyle.btnWidth = val / 100 + 'rem'
    if (type === 'btnRadius') store.getActiveItems.btnStyle.radius = val / 100 + 'rem'
    store.modifyUpdatetime()
  }
  function handleChageText(data) {
    if (data.type === 'text') {
      store.getActiveItems.eboxValue.fontSize = data.size
      store.getActiveItems.styles.fontSize = data.size / 100 + 'rem'
      store.modifyUpdatetime()
    }
  }
  function onsync(val) {
    try {
      const item = state.formlist.find((item) => item.id == val) || {}
      if (item.content) {
        const json = JSON.parse(item.content)
        store.getActiveItems.form_fields.btnText = json?.btnText || '立即提交'
        const res_data = json?.list || []
        res_data.forEach((v) => {
          if (v.key === 'checkout') v.form_value = Array.isArray(v.form_value) ? v.form_value : []
        })
        const obj = res_data.find((v) => v.key === 'phone') || {}
        if (!obj.key) {
          store.getActiveItems.sms_fields.isOpen = false
        }
        store.getActiveItems.form_fields.elelist = res_data
      }
      handleInputText()
    } catch (error) {
      console.log(val)
    }
  }
  function handleInputText(val) {
    nextTick(() => {
      const ele = document.querySelector('#form-' + store.getActiveItems.uuid)
      const height = ele.offsetHeight
      store.getActiveItems.eboxValue.height = height
      store.getActiveItems.styles.height = height / 100 + 'rem'
      const bgH = store.getActiveItems.eboxValue.y + height
      store.getActiveItems.eboxValue.bgHeight = bgH
      store.getActiveItems.boxBg.height = bgH + 'px'
    })
  }
  async function fetchDataHtmlDropDownList() {
    try {
      const resp = await getFormList({ page: 1, page_size: 1000, type: 1 })
      const { data } = resp
      state.formlist = data.list || []
    } catch (error) {
      console.log(error)
    }
  }
  const dialog = reactive({
    visible: false,
    title: '',
    width: '',
    type: '',
    isH5Upload: true
  })
  const uploadsImg = (type) => {
    dialog.type = type
    dialog.title = '图片素材'
    dialog.width = '80%'
    dialog.visible = true
  }
  const onEvent = (v) => {
    dialog.visible = false
    if (v?.data?.length) {
      const img = v.data[0]
      let pic = img?.file_url
      if (isArray(img)) {
        store.getActiveItems.sensBackgroundImg = img
        pic = img[0]?.original_image || ''
      } else {
        delete store.getActiveItems.sensBackgroundImg
      }
      store.getActiveItems.boxBg.backgroundImage = pic
    }
  }
  const onUploads = debounce((v) => {
    if (v.content) {
      let pic = v.content
      if (isArray(v.content)) {
        store.getActiveItems.sensBackgroundImg = v.content
        pic = v.content[0].original_image || ''
      } else {
        delete store.getActiveItems.sensBackgroundImg
      }
      store.getActiveItems.boxBg.backgroundImage = pic
    }
  }, 500)
</script>

<style lang="scss" scoped>
  .mt16 {
    margin-top: 16px;
  }

  .ml12 {
    margin-left: 12px;
  }

  .ml15 {
    margin-left: 15px;
  }

  .operate {
    margin: 5px 0 5px 0px;
  }

  .desc2 {
    margin: 0 0 10px 0;
  }

  .labels {
    color: #999;
    font-size: 14px;
    max-width: 245px;
    margin: 4px 0 6px 0;
  }

  // .uimgg-box { width: 195px; height: 90px; }

  .inline-textarea {
    display: flex;
    box-sizing: border-box;
    flex-direction: row;
    flex-wrap: wrap;
    .inline-item {
      cursor: pointer;
      flex-direction: column;
      &:first-child {
        margin-right: 10px;
      }
    }
    .item_image {
      position: relative;
      box-sizing: border-box;
      border: 1px solid #d9d9d9;
      background-color: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50%;
      width: 111px;
      height: 74px;
      border-radius: 4px;
      transition: all 0.2s;
      &.selected {
        border-color: #409eff;
      }
    }
    .item_text {
      margin-top: 8px;
      font-size: 14px;
      color: #333;
      line-height: 22px;
    }
    .bgs {
      font-size: 0;
      margin-top: 10px;
    }
  }

  ::v-deep {
    .tagPosition-group.t {
      .el-radio-button--medium .el-radio-button__inner {
        padding: 8px;
      }
    }
  }
</style>
