<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">悬浮跑马灯规则</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">文案</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <div class="clx">跑马灯文案：</div>
                <div class="list">
                  <div
                    class="item flex-y-center"
                    v-for="(v, index) in store.getActiveItems.rollingRuleList"
                    :key="index"
                  >
                    <span>{{ v.content }}</span>
                    <a-input
                      v-model:value.trim="v.tag"
                      :maxlength="10"
                      placeholder="领取成功"
                      class="elselect_box input-theme"
                      @keydown.space.prevent
                      @focus="(val) => onFocusTag(val, v)"
                      @blur="(val) => onBlurTag(val, v)"
                    />
                    <a-button
                      type="link"
                      class="p-0 ml-8px"
                      v-if="store.getActiveItems.rollingRuleList.length == index + 1"
                      @click="onAddRule"
                    >
                      +新增
                    </a-button>
                    <a-button type="link" class="p-0 ml-8px c-#ff4d4f!" @click="onDelRule(index)" v-else danger
                      >删除</a-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">按钮位置</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" style="padding-top: 10px">
          <!-- <span class="control-panel-group__label flex-align"></span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <a-radio-group v-model:value="store.getActiveItems.position">
                  <a-radio-button value="absolute">跟随页面滑动</a-radio-button>
                  <a-radio-button value="fixed">固定顶部</a-radio-button>
                </a-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onBeforeMount, reactive } from 'vue'
  import { message } from 'ant-design-vue'
  import { randomAvatar, useRandomUser } from '@/views/workbench/landingPage/LandingPageEditor/src/randomUser.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()
  const { getRandomUser } = useRandomUser()

  const ruleTotalNum = 5 // 最大规则数量
  const state = reactive({
    nameList: [],
    recordVal: null
  })

  onBeforeMount(async () => {
    const resp = await getRandomUser()
    state.nameList = resp.names || []
    initRolling()
  })

  const onFocusTag = (e, val) => {
    state.recordVal = val?.tag
  }

  const onBlurTag = (e, val) => {
    if (val?.tag == state.recordVal) return
    state.recordVal = val?.tag
    initRolling()
  }

  const onAddRule = () => {
    if (store.getActiveItems.rollingRuleList.length >= ruleTotalNum) {
      return message.warning(`最多只能添加${ruleTotalNum}条规则`)
    }
    store.getActiveItems.rollingRuleList.push({
      id: store.getActiveItems.rollingRuleList.length + 1,
      tag: '',
      content: '用户{客户名称}刚刚/1分钟前'
    })
    // initRolling()
  }

  const onDelRule = (index) => {
    if (store.getActiveItems.rollingRuleList.length <= 1) {
      return message.warning('至少保留一条规则')
    }
    store.getActiveItems.rollingRuleList.splice(index, 1)
  }

  const initRolling = () => {
    let totalNum = 50
    let names = state.nameList
    let suffix = ['刚刚', '1分钟前']
    let tags = store.getActiveItems.rollingRuleList.map((v) => v.tag).filter((tag) => tag)

    let dataList = []
    for (let i = 0; i < totalNum; i++) {
      const randomName = formatChineseName(names[randomNum(names.length)])
      const rAvatar = randomAvatar(i)
      const randomSuffix = suffix[randomNum(suffix.length)]
      const randomTag = tags[randomNum(tags.length)]
      dataList.push({
        id: i + 1,
        key: i,
        translateY: i * 38 + 38 * 2,
        name: randomName,
        avatar: rAvatar,
        suffix: randomSuffix,
        tag: randomTag || '',
        content: `用户${randomName}${randomSuffix}${randomTag}`
      })
    }
    console.log(dataList, 'dataList')
    store.getActiveItems.rollingList = dataList
  }

  function formatChineseName(name) {
    try {
      if (typeof name !== 'string' || name.trim() === '') {
        return ''
      }
      const trimmedName = name.trim()
      const length = trimmedName.length
      if (length === 1) return trimmedName
      if (length === 2) return `${trimmedName.slice(0, 1)}***`
      return `${trimmedName.slice(0, 1)}***${trimmedName.slice(-1)}`
    } catch (error) {
      return name
    }
  }

  function randomNum(num) {
    return Math.floor(Math.random() * num)
  }
</script>

<style lang="scss" scoped>
  .clx {
    position: relative;
    font-size: 14px;
    color: #313233;
    line-height: 20px;
    margin-bottom: 8px;
    &::before {
      content: '*';
      color: #fe4d4f;
    }
  }
  .list {
    width: 100%;
    padding: 8px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #f6f6f6;
    .item {
      font-weight: 400;
      font-size: 13px;
      color: #313233;
      line-height: 18px;
      margin-bottom: 18px;
      &:last-child {
        margin-bottom: 0;
      }
      .elselect_box {
        width: 76px;
        margin-left: 8px;
        padding: 5px;
      }
    }
  }
</style>
