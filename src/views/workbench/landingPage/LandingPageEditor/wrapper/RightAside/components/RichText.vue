<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">文字内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">文案</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div style="width: 330px">
                <WangeditorV5 :text-content="store.getActiveItems.textContent" @wang="handleChangeWang" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">链接</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" style="padding-top: 10px">
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1" style="padding-left: 12px">
                <LinkBlock :active-items="store.getActiveItems" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { nextTick } from 'vue'
  import LinkBlock from '../../components/LinkBlock.vue'
  import WangeditorV5 from '@ng_visualization/components/WangeditorV5.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  function handleInputText(val) {
    nextTick(() => {
      const ele = document.querySelector('#richtext-' + store.getActiveItems.uuid)
      const height = ele.offsetHeight > 35 ? ele.offsetHeight : 35
      store.getActiveItems.eboxValue.height = height
      store.getActiveItems.styles.height = height / 100 + 'rem'
      const bgH = store.getActiveItems.eboxValue.y + height
      if (bgH > store.getActiveItems.eboxValue.bgHeight) {
        store.getActiveItems.eboxValue.bgHeight = bgH
        store.getActiveItems.boxBg.height = bgH + 'px'
      }
    })
  }
  function handleChangeWang(val) {
    store.getActiveItems.textContent = val
    handleInputText()
    store.modifyUpdatetime()
  }
</script>

<style lang="scss" scoped>
  .mt16 {
    margin-top: 16px;
  }
</style>
