<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">{{ renderTitle }}</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group" v-if="hasleftArr.includes(store.getActiveItems.currStyleClass)">
          <span class="control-panel-group__label flex-align">加购按钮文案:</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 flex-align">
                <a-input
                  v-model:value.trim="store.getActiveItems.leftText"
                  :maxlength="store.getActiveItems.currStyleClass == 'style-one' ? 8 : 9"
                  class="elselect_box input-theme"
                  @keydown.space.prevent
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">立即购买按钮文案:</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 flex-align">
                <a-input
                  v-model:value.trim="store.getActiveItems.rightText"
                  :maxlength="store.getActiveItems.currStyleClass == 'style-one' ? 8 : 9"
                  class="elselect_box input-theme"
                  @keydown.space.prevent
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">点击交互:</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 flex-align">
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  v-model:value="store.getActiveItems.actionType"
                  placeholder="请选择"
                  class="w-100%"
                >
                  <a-select-option v-for="item in state.actionArr" :key="item.id" :value="item.id">{{
                    item.name
                  }}</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group" v-if="store.getActiveItems.actionType == 3">
          <span class="control-panel-group__label flex-align">url:</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 flex-align">
                <a-input
                  v-model:value.trim="store.getActiveItems.url"
                  class="elselect_box input-theme"
                  @keydown.space.prevent
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { debounce } from 'lodash-es'
  import { computed, reactive } from 'vue'
  import { message } from 'ant-design-vue'
  import { urlRegex } from '@/utils/method.ts'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const state = reactive({
    dialogRadio: 1,
    payflag: true,
    selectPicType: null,
    actionArr: [
      {
        id: 1,
        name: '无交互'
      },
      {
        id: 2,
        name: '跳转客服'
      },
      {
        id: 3,
        name: '跳转外部链接'
      }
    ]
  })
  const validateUrl = () => {
    // console.log('store.getActiveItems.url', store.getActiveItems.url)
    if (!urlRegex.test(store.getActiveItems.url)) {
      message.error('请输入有效的URL地址')
    }
  }
  const hasleftArr = ['style-one', 'style-four']

  const renderTitle = computed(() => {
    const type = store.getActiveItems.currStyleClass
    let str = ''
    if (type === 'style-one') {
      str = '经典'
    } else if (type === 'style-two') {
      str = '参团'
    } else if (type === 'style-three') {
      str = '种草1'
    } else if (type === 'style-four') {
      str = '种草2'
    }
    return str + '样式'
  })
</script>

<style lang="scss" scoped>
  .control-panel__wrapper {
    .control-panel-group__label {
      text-align: right;
      margin-right: 10px;
      width: 125px !important;
      &::before {
        content: '*';
        color: #ff4d4f;
      }
    }
  }
</style>
