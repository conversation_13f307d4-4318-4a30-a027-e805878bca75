<template>
  <div class="control-panel__wrapper">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">评价内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">评价模板</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1">
                <a-select
                  v-model:value="store.getActiveItems.comment_id"
                  show-search
                  placeholder="请选择评价模板"
                  class="flex-1 select-theme"
                  style="width: 100%"
                  @change="onsync"
                >
                  <a-select-option v-for="v in state.commentList" :key="v.id" :value="v.id">{{
                    v.title
                  }}</a-select-option>
                </a-select>
                <div class="c-#ff4d4f">若您更改评价内容，须再点击落地页保存并发布</div>
                <!-- <div class="operate">
                  <a-button type="link" @click="handleCreateForm"> <PlusOutlined /> 创建评价 </a-button>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">内容</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">文案</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 f-flex">
                <div class="input-inline c flex-align w-140px!">
                  <span class="left-l">阅读量</span>
                  <a-input-number
                    v-model:value="store.getActiveItems.readNum"
                    :controls="false"
                    :min="0"
                    :max="99999999"
                    :precision="0"
                    controls-position="right"
                    size="medium"
                    class="fix-input__number w-82px!"
                  />
                </div>
                <div class="input-inline c flex-align w-140px!">
                  <span class="left-l">在看数</span>

                  <a-input-number
                    v-model:value="store.getActiveItems.seeNum"
                    :controls="false"
                    :min="0"
                    :max="99999999"
                    :precision="0"
                    controls-position="right"
                    size="medium"
                    class="fix-input__number w-82px!"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">样式</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align w-80px!">用户名颜色</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.userNameColor"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.styles.userNameColor = val)"
                  @input="(val) => (store.getActiveItems.styles.userNameColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align w-80px!">评论颜色</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.contentColor"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.styles.contentColor = val)"
                  @input="(val) => (store.getActiveItems.styles.contentColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align w-80px!">背景颜色</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-align">
                <ColorPicker
                  :value="store.getActiveItems.styles.backgroundColor"
                  class="bgPicker btn-color-picker"
                  @change="(val) => (store.getActiveItems.styles.backgroundColor = val)"
                  @input="(val) => (store.getActiveItems.styles.backgroundColor = val)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">边距</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <!-- <span class="control-panel-group__label flex-align">文案</span> -->
          <div class="control-panel-group__body">
            <div class="multiline">
              <div class="flex-1 f-flex">
                <div class="input-inline c flex-align">
                  <span class="left-l">上边距</span>
                  <a-input-number
                    v-model:value="store.getActiveItems.eboxValue.paddingTop"
                    :controls="false"
                    controls-position="right"
                    size="medium"
                    class="fix-input__number"
                    @change="(val) => handleInputStyleSlider(val, 'paddingTop')"
                  />
                </div>
                <div class="input-inline c flex-align w-140px!">
                  <span class="left-l">下边距</span>

                  <a-input-number
                    v-model:value="store.getActiveItems.eboxValue.paddingBottom"
                    :controls="false"
                    controls-position="right"
                    size="medium"
                    class="fix-input__number"
                    @change="(val) => handleInputStyleSlider(val, 'paddingBottom')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onBeforeMount, nextTick, reactive } from 'vue'
  import { debounce } from 'lodash-es'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import ColorPicker from '@ng_visualization/components/ColorPicker.vue'
  import FonstSize from '../../components/FontSize.vue'
  import { getCommentList } from '@/views/workbench/landingPage/evaluationManagement/index.api.ts'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import { useRouter } from '@/hooks/use-router'
  const { routerResolve } = useRouter()
  const store = useNgVisualizationStore()

  const state = reactive({
    commentList: []
  })

  onBeforeMount(() => {
    fetchDataHtmlDropDownList()
  })

  function handleCreateForm() {
    routerResolve({ name: 'CommentList', query: {} })
  }

  function handleInputStyleSlider(val, type) {
    if (type == 'paddingTop') store.getActiveItems.styles.paddingTop = val / 100 + 'rem'
    if (type == 'paddingBottom') store.getActiveItems.styles.paddingBottom = val / 100 + 'rem'
    handleInputText()
    store.modifyUpdatetime()
  }

  function onsync(val) {
    try {
      const item = state.commentList.find((item) => item.id == val) || {}
      if (item.comment_content) {
        const json = JSON.parse(item.comment_content)
        store.getActiveItems.commentList = json.slice(0, 4)
        store.getActiveItems.comment_code = item.code
      }
      handleInputText()
    } catch (error) {
      console.log(val)
    }
  }
  function handleInputText(val) {
    nextTick(() => {
      const ele = document.querySelector('#comment-' + store.getActiveItems.uuid)
      const height = ele.offsetHeight
      store.getActiveItems.eboxValue.height = height
      store.getActiveItems.styles.height = height / 100 + 'rem'
      const bgH = store.getActiveItems.eboxValue.y + height
      store.getActiveItems.eboxValue.bgHeight = bgH
      store.getActiveItems.boxBg.height = bgH + 'px'
    })
  }
  async function fetchDataHtmlDropDownList() {
    try {
      const resp = await getCommentList({ page: 1, page_size: 1000 })
      const { data } = resp
      state.commentList = data.list || []
    } catch (error) {
      console.log(error)
    }
  }

  const onUploads = debounce((v) => {
    if (v.content) {
      store.getActiveItems.boxBg.backgroundImage = v.content
    }
  }, 500)
</script>

<style lang="scss" scoped>
  .mt16 {
    margin-top: 16px;
  }

  .ml12 {
    margin-left: 12px;
  }

  .ml15 {
    margin-left: 15px;
  }

  .operate {
    margin: 5px 0 5px 0px;
  }

  .desc2 {
    margin: 0 0 10px 0;
  }

  .labels {
    color: #999;
    font-size: 14px;
    max-width: 245px;
    margin: 4px 0 6px 0;
  }

  // .uimgg-box { width: 195px; height: 90px; }

  .inline-textarea {
    display: flex;
    box-sizing: border-box;
    flex-direction: row;
    flex-wrap: wrap;
    .inline-item {
      cursor: pointer;
      flex-direction: column;
      &:first-child {
        margin-right: 10px;
      }
    }
    .item_image {
      position: relative;
      box-sizing: border-box;
      border: 1px solid #d9d9d9;
      background-color: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50%;
      width: 111px;
      height: 74px;
      border-radius: 4px;
      transition: all 0.2s;
      &.selected {
        border-color: #409eff;
      }
    }
    .item_text {
      margin-top: 8px;
      font-size: 14px;
      color: #333;
      line-height: 22px;
    }
    .bgs {
      font-size: 0;
      margin-top: 10px;
    }
  }

  ::v-deep {
    .tagPosition-group.t {
      .el-radio-button--medium .el-radio-button__inner {
        padding: 8px;
      }
    }
  }
</style>
