<template>
  <div class="app-right-aside" v-if="!store.allNoToolbar.includes(store.getActiveItems.name)">
    <div v-if="store.getActiveItems.backBtn" class="back-btn clickable" @click.stop="handleBack">
      <span class="text">返回</span>
    </div>
    <div :class="['panel-wrapper', { 'blank-panel': !store.getActiveItems.name }]">
      <div class="panel-body">
        <div v-if="store.getActiveItems.name" class="control-panel__inner">
          <template v-if="store.getActiveItems.backBtn">
            <component :is="'Back' + store.getActiveItems.name" />
          </template>
          <template v-else>
            <component :is="NCurrComponent" v-if="store.getActiveItems.isWire" />
            <Background v-if="store.getActiveItems.isBg" />
          </template>
          <div class="delete-button">
            <a-button :icon="h(DeleteOutlined)" @click.stop="handleDelete">删除当前组件</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { h, computed } from 'vue'
  import { DeleteOutlined } from '@ant-design/icons-vue'
  import Background from './background.vue'
  import NNgImage from './components/Image.vue'
  import NNgRichText from './components/RichText.vue'
  import NNgButton from './components/Button.vue'
  import NNgBtnTop from './components/Button.vue'
  import NNgBtnBottom from './components/Button.vue'
  import NNgForm from './components/Form.vue'
  import NNgBuyNow from './components/BuyNow.vue'
  import NNgComment from './components/Comment.vue'
  import NNgRolling from './components/Rolling.vue'
  import NNgQaChat from './components/QaChat.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const componentMap = {
    NgImage: NNgImage,
    NgRichText: NNgRichText,
    NgButton: NNgButton,
    NgForm: NNgForm,
    NgBtnTop: NNgBtnTop,
    NgBtnBottom: NNgBtnBottom,
    NgComment: NNgComment,
    NgBuyNow: NNgBuyNow,
    NgRolling: NNgRolling,
    NgQaChat: NNgQaChat
  }
  const NCurrComponent = computed(() => {
    const componentName = store.getActiveItems.name
    console.log('当前组件名称:', componentName)
    return componentMap[componentName] || null // 添加默认值处理
  })

  // 删除当前模块
  function handleDelete() {
    const index = store.getCurPageData.elements.findIndex((v) => v.uuid === store.getActiveItems.uuid)
    store.getCurPageData.elements.splice(index, 1)
    if (store.getActiveItems.name == 'NgQaChat') store.modifyQaData('reset')
    store.modifyActiveItems({})
    store.modifyIsAllowedDrag()
  }
  function handleBack() {
    // 返回
    store.getActiveItems.backBtn = false
  }
</script>

<style lang="scss" scoped>
  .app-right-aside {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fff;
    .panel-wrapper {
      // overflow-x: hidden;
    }
    .blank-panel {
      background-color: #f4f7f9;
    }
    .panel-body,
    .panel-wrapper {
      width: 100%;
      height: 100%;
    }
    .back-btn {
      position: absolute;
      left: 0;
      top: 8px;
      transform: translateX(-100%);
      .text {
        position: relative;
        display: inline-block;
        padding: 6px 10px 4px;
        box-sizing: border-box;
        color: #666;
        font-size: 12px;
        line-height: 20px;
        &::after {
          content: '';
          z-index: -1;
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          background: #fff;
          border-bottom: none;
          box-shadow:
            -2px 0 12px 0 rgba(0, 0, 0, 0.1),
            0 2px 12px 0 rgba(0, 0, 0, 0.1),
            none,
            0 2px 12px 0 rgba(0, 0, 0, 0.1);
          border-radius: 4px 4px 0 0;
          transform: rotate(-90deg) scale(0.8, 1.3) perspective(0.8em) rotateX(5deg);
          transform-origin: center;
        }
      }
    }
  }
</style>
