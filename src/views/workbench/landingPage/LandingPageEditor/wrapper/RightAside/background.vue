<template>
  <div class="control-panel__bg">
    <div class="panel-section__block">
      <div class="header flex-align">
        <div class="icon" />
        <div class="title">模块背景</div>
      </div>
      <div class="control-panel__section-body">
        <div class="control-panel-group">
          <span class="control-panel-group__label flex-align">选择</span>
          <div class="control-panel-group__body">
            <div class="multiline">
              <a-radio-group v-model:value="store.getActiveItems.boxBg.bgMode" size="medium" class="flex-1 fbgradio">
                <a-radio-button :value="1">使用颜色</a-radio-button>
                <a-radio-button :value="2">使用图片</a-radio-button>
              </a-radio-group>
              <div class="flex-1" style="margin-top: 16px">
                <ColorPicker
                  v-if="store.getActiveItems.boxBg.bgMode == 1"
                  :value="store.getActiveItems.boxBg.backgroundColor"
                  show-alpha
                  class="bgPicker"
                  @change="(val) => (store.getActiveItems.boxBg.backgroundColor = val)"
                  @input="(val) => (store.getActiveItems.boxBg.backgroundColor = val)"
                />
                <div
                  class="uimgg-box upf"
                  style="width: 230px"
                  @click="uploadsImg('library')"
                  v-if="store.getActiveItems.boxBg.bgMode == 2"
                >
                  <img
                    v-if="store.getActiveItems.boxBg.backgroundImage"
                    :src="store.getActiveItems.boxBg.backgroundImage"
                    class="avatar"
                  />
                  <template v-else><PlusOutlined /><span>添加图片</span></template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="panel-section__block">
    <div class="header flex-align">
      <div class="icon"></div><div class="title">模块高度</div>
    </div>
    <div class="control-panel__section-body">
      <div class="control-panel-group">
        <span class="control-panel-group__label flex-align">高度</span>
        <div class="control-panel-group__body">
          <div class="multiline">
            <el-input-number
              v-model="store.getActiveItems.eboxValue.bgHeight"
              controls-position="right"
              size="medium"
              :min="0"
              class="height-input__number"
              @change="handleChangeBgHeight">
            </el-input-number>
            <div class="width-text">组件宽度：{{canvasSize.width}}px</div>
          </div>
        </div>
      </div>
    </div>
  </div> -->
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        :fileSize="5 * 1024 * 1024"
        :extension="'.jpg,jpeg,.png,.gif'"
        :size="1"
        type="image"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { reactive } from 'vue'
  import { debounce, isArray } from 'lodash-es'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import ColorPicker from '@ng_visualization/components/ColorPicker.vue'
  import { getContainerHeight } from '@ng_visualization/utils/index.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  const store = useNgVisualizationStore()

  const dialog = reactive({
    visible: false,
    title: '',
    width: '',
    type: ''
  })
  const uploadsImg = (type) => {
    dialog.type = type
    dialog.title = '图片素材'
    dialog.width = '80%'
    dialog.visible = true
  }
  const onEvent = (v) => {
    dialog.visible = false
    if (v?.data?.length) {
      const img = v.data
      let pic = img?.file_url
      if (isArray(img)) {
        store.getActiveItems.sensBackgroundImg = img
        pic = img[0]?.file_url || ''
      }
      store.getActiveItems.boxBg.backgroundImage = pic
    }
  }
</script>
