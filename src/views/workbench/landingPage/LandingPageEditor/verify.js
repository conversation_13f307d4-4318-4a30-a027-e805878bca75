const regPhone = /^1[3456789]\d{9}$/
import { urlRegex } from '@/utils/method.ts'

function validateComponent(component, obj, params) {
  const validations = {
    NgImage: () => {
      if (obj.srcs?.imgurl?.includes('2ed9ede7ab0bb')) {
        return { ...params, message: `【${obj.desc}组件】请选择展示的图片内容` }
      }
      return null
    },
    NgCarousel: () => {
      if (!obj.carouseList?.length) {
        return { ...params, message: `【${obj.desc}组件】请选择展示的图片内容` }
      }
      return null
    },
    NgText: () => validateTextOrHeadline(obj, params),
    NgHeadline: () => validateTextOrHeadline(obj, params),
    NgRichText: () => {
      if (obj.textContent === '<p><br></p>' || !obj.textContent) {
        return { ...params, message: `【${obj.desc}组件】请填写文案内容` }
      }
      return null
    },
    NgButton: () => validateButton(obj, params),
    NgBtnBottom: () => validateButton(obj, params),
    NgBtnTop: () => validateButton(obj, params),
    NgForm: () => {
      if (!obj.form_fields.id) return { ...params, message: `【${obj.desc}组件】请选择表单` }
      if (!obj.form_fields.tipValue) return { ...params, message: `【${obj.desc}组件】请填写提交成功提示文案内容` }
      if (obj.autoplay && obj.clickType === 1 && !obj.link?.type) {
        return { ...params, message: `【${obj.desc}组件】请设置相应的跳转内容` }
      }
      if (obj.sms_fields?.isOpen && !obj.sms_fields?.id) {
        return { ...params, message: `【${obj.desc}组件】请选择短信模版` }
      }
      return null
    },
    NgTelephone: () => {
      if (obj.currStyleClass === 'style-three' && obj.textContent.includes('/77aa9jv9_q76.png')) {
        return { ...params, message: `【${obj.desc}】请选择展示的图片内容` }
      } else {
        if (!obj.textContent) return { ...params, message: `【${obj.desc}】请填写文案内容` }
      }

      if (!regPhone.test(obj.phoneContent)) return { ...params, message: `【${obj.desc}】请填写正确格式电话号码` }
      return null
    },
    NgWxfans: () => validateWxfans(obj, params),
    NgWeChatTop: () => validateWxfans(obj, params),
    NgWeChatBottom: () => validateWxfans(obj, params),
    NgGoods: () => {
      const goods_fields = obj.good_fields || {}
      if (!goods_fields.id) return { ...params, message: `【${obj.desc}组件】请选择商品内容` }
      if (goods_fields.isContactFixed) {
        if (!goods_fields.fixedBtnText) return { ...params, message: `【${obj.desc}组件】请设置底部悬浮按钮文案` }
        if (goods_fields.isShowPhone && !regPhone.test(goods_fields.fixedPhone)) {
          return { ...params, message: `【${obj.desc}组件】请设置正确格式咨询电话号码` }
        }
      }
      return null
    },
    NgReading: () => {
      if (!obj.readingList?.read) return { ...params, message: `【${obj.desc}组件】请填写阅读量` }
      if (!obj.readingList?.see) return { ...params, message: `【${obj.desc}组件】请填写在看数` }
      return null
    },
    NgComment: () => {
      if (!obj.comment_id) return { ...params, message: `【${obj.desc}组件】请选择评论组` }
      return null
    },
    NgRolling: () => {
      if (!obj.rollingList?.length) return { ...params, message: `【${obj.desc}组件】请添加消息内容` }
      return null
    },
    NgVideo: () => {
      if (!obj.srcs?.videourl) return { ...params, message: `【${obj.desc}组件】请选择视频内容` }
      return null
    },
    NgVideoGroup: () => {
      if (!obj.videoList?.length) return { ...params, message: `【${obj.desc}组件】请选择视频内容` }
      return null
    },
    NgQaChat: () => validateQaChat(obj, params),
    NgBuyNow:() => {
      if (!obj.rightText) return { ...params, message: `【${obj.desc}】请输入立即购买文案` }
      if (!obj.actionType) return { ...params, message: `【${obj.desc}】请选择立即交互类型` }
      const hasleftArr = ['style-one', 'style-four']
      if(hasleftArr.includes(obj.currStyleClass) && !obj.leftText) {
        return { ...params, message: `【${obj.desc}】请输入加入购物车文案` }
      }
      if(obj.actionType === 3 && !urlRegex.test(obj.url)) {
        return { ...params, message: `【${obj.desc}】请输入有效的URL地址` }
      }
      
      
    }
  }
  // console.log(component, obj, params)
  if (validations[component]) {
    return validations[component]()
  }

  return null
}

/**
 * 函数验证
 * @param {*} eleData
 * @returns
 */
export const verificationFunc = (eleData) => {
  try {
    for (const i in eleData) {
      const obj = eleData[i] || {}
      const params = { index: i, name: obj.name, uuid: obj.uuid }

      const result = validateComponent(obj.name, obj, params)
      if (result) {
        // 跳转报错组件位置
        if (['NgBtnTop', 'NgRolling', 'NgWeChatTop'].includes(result.name)) {
          document.querySelector('.center-panel-wrap').scrollIntoView()
        } else {
          document.getElementById(`${result.name}_${result.uuid}`).scrollIntoView()
        }
        return result
      }
    }
    return null
  } catch (error) {
    console.log(error)
    return null
  }
}

/**
 * 文本、标题组件验证
 * @param {*} obj
 * @param {*} params
 * @returns
 */

function validateTextOrHeadline(obj, params) {
  if (!obj.textContent) {
    return { ...params, message: `【${obj.desc}组件】请填写文案内容` }
  }
  return null
}

/**
 * 按钮组件验证
 * @param {*} obj
 * @param {*} params
 * @returns
 */
function validateButton(obj, params) {
  if (obj.currStyleClass === 'style-three') {
    if (obj.textContent.includes('77aa9jv9_q76.png')) {
      return { ...params, message: `【${obj.desc}组件】请选择展示的图片内容` }
    }
  } else {
    if (!obj.textContent) return { ...params, message: `【${obj.desc}组件】请填写文案内容` }
  }
  return null
}

/**
 * 微信组件验证
 * @param {*} obj
 * @param {*} params
 * @returns
 */
function validateWxfans(obj, params) {
  const name = obj.name === 'NgWxfans'
  if (name && obj.currStyleClass === 'style-three' && obj.wxImgUrl.includes('wxthreeb.png')) {
    return { ...params, message: `【${obj.desc}组件】请选择展示的图片内容` }
  } else {
    const flag = (obj.currStyleClass === 'style-one' && !obj.text) ||
                 (obj.currStyleClass === 'style-two' && !obj.btnText) ||
                 (obj.currStyleClass === 'style-four' && !obj.tipText)
    if (flag) return { ...params, message: `【${(name ? '' : '微信加粉') + obj.desc}组件】请填写文案内容` }
  }
  return null
}

function validateQaChat(item, params) {
    if (!item.isRepetAnswer) return { ...params, message: `【${item.desc}组件】请选择是否重复回答` }
    if (!item.qaDataList || !item.qaDataList.length) {
      return { ...params, message: `【${item.desc}组件】请至少添加一个对话组` }
    }
    let index = 0
    const flag = item.qaDataList.every((x, i) => {
      const context = (x.contextList || []).some((v) => {
        return !v.content || v.content === '<p><br></p>'
      })
      const reply = (x.replyList || []).some((v) => {
        return !v.content || (v.clickStatus == 3 && !v.link) || (v.clickStatus == 4 && !v.otherReply)
      })
      if (context || reply) index = i + 1
      return context || reply ? false : true
    })
    if (!flag) {
      return { ...params, message: `【${item.desc}组件-对话组${index}】请补充完整` }
    }
    return null
}
