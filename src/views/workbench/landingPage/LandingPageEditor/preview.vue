<template>
  <div class="pop-wrapper">
    <div class="left flex-column">
      <div class="preview">
        <div ref="iframeWrapper" class="iframe-wrapper relative">
          <div v-if="state.tipsVisible" class="tips">
            <span>此页面为预览链接，不可进行广告投放</span>
            <span class="close" @click="hideTips"><CloseOutlined class="c-#2b2b2b" /></span>
          </div>
          <a-spin wrapperClassName="my-spin" :spinning="state.spinning" tip="拼命加载中...">
            <iframe :src="state.src" width="375" height="675" class="iframe" @load="loadingIframe" />
          </a-spin>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="section-2">
        <p class="title">{{ info?.name }}</p>
        <p class="description" v-show="info?.remarks">{{ info?.remarks }}</p>
        <div class="line" />
        <div class="qr-wrapper">
          <a-qrcode :value="state.src ? state.middleUrl : ''" :bordered="false" />
        </div>
        <div class="button button-use" @click="copy(state.src ? state.middleUrl : '')">复制链接</div>
        <div class="button button-close" @click="emits('close')">关闭</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, computed } from 'vue'
  import { copy } from '@/utils'
  import { CloseOutlined } from '@ant-design/icons-vue'
  const props = defineProps(['activityId', 'dInnerHTML', 'page_name', 'info'])

  const emits = defineEmits(['close'])

  const state = reactive({
    imgurl: null,
    src: props.info?.h5_url ? props.info.h5_url + '?landMode=preview&time=' + new Date().getTime() : '',
    curPageData: {},
    jsonData: {},
    tipsVisible: true,
    middleUrl: '',
    spinning: true
  })

  const pageStyle = computed(() => {
    const style = state.curPageData.commonStyle
    for (const k in state.curPageData.commonStyle) {
      if (
        k === 'background-image' &&
        state.curPageData.commonStyle[k] &&
        !state.curPageData.commonStyle[k].includes('url(')
      ) {
        style[k] = `url(${state.curPageData.commonStyle[k]})`
      }
    }
    return style
  })
  const loadingIframe = () => {
    state.spinning = false
  }
  const dealUrlData = () => {
    const url = props.info.h5_url ? new URL(props.info.h5_url) : {}
    const name = url.pathname?.substring(1).replace(/\.\w+$/, '')
    return {
      url: url.origin,
      name
    }
  }
  onMounted(() => {
    const originObj = dealUrlData()
    state.middleUrl = originObj.url + '/previewMiddle.html?name=' + originObj.name
    // state.middleUrl = 'http://*************:4099' + '/previewMiddle.html?name=' + originObj.name
  })
  const hideTips = () => {
    state.tipsVisible = false
  }

  async function getPageData() {
    console.log('1111')
  }
</script>

<style lang="scss" scoped>
  .h5 {
    height: 100%;
  }
  .pop-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;

    .left {
      width: 404px;
      margin-right: 24px;
      .preview {
        width: 100%;
        height: 756px;
        background: transparent url($imgSrc + '/assets/h5_bg.svg') no-repeat;
        background-size: 100% 100%;
        padding: 42px 12px 40px 12px;
      }
      .iframe-wrapper {
        width: 375px;
        // height: 569px;
        // overflow-x: hidden;
        // overflow-y: auto;
        &::-webkit-scrollbar {
          width: 0px; /*对垂直流动条有效*/
          height: 0px; /*对水平流动条有效*/
        }
        .iframe {
          transform-origin: 0 0;
          // transform: scale3d(0.8666, 0.8666, 1);
          border: none;
        }
      }
      .tips {
        position: absolute;
        width: 100%;
        z-index: 9999;
        font-size: 13px;
        color: #2b2b2b;
        display: flex;
        background: #ffe1c1;
        justify-content: flex-end;
        padding: 10px;
        .close {
          margin-left: 38px;
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: left;
      width: 200px;
      .section-2 {
        background: #fff;
        border-radius: 12px;
        width: 100%;
        padding: 16px;
        p.title {
          position: relative;
          font-size: 14px;
          font-weight: 700;
          color: #595959;
          line-height: 18px;
          margin: 0 0 15px;
          width: 100%;
          max-height: 36px;
          word-wrap: break-word;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        p.description {
          position: relative;
          width: 100%;
          max-height: 45px;
          font-size: 12px;
          line-height: 15px;
          color: #a4a4a4;
          margin: 0 0 10px;
          word-wrap: break-word;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .line {
          width: 100%;
          height: 1px;
          margin: 10px 0;
        }
        .qr-wrapper {
          width: 168px;
          height: 168px;
          padding: 4px;
          background: #f3f3f3;
          border-radius: 2px;
          margin-bottom: 13px;
          z-index: 10;
          overflow: hidden;
          transition: all 0.3s linear;
          transform-origin: 50% 100%;
          &:hover {
            transform: scale(1.35);
          }
          :deep(.ant-qrcode) {
            padding: 0;
            canvas {
              width: 100% !important;
              height: 100% !important;
            }
          }
        }
        .qr-code {
          background-color: inherit;
          width: 100%;
          height: 100%;
        }
        .button {
          display: inline-block;
          text-align: center;
          width: 100%;
          height: 32px;
          line-height: 30px;
          background: transparent;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          color: #595959;
          font-size: 14px;
          cursor: pointer;
          &.button-use {
            margin-bottom: 7px;
            border-color: #1095fe;
            background: #1095fe;
            color: #fff;
          }
          &.button-close {
          }
        }
      }
    }
  }
  .flex-column,
  .flex-column-warp {
    display: flex;
    flex-direction: column;
  }
</style>
