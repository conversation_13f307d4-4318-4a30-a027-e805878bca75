import { getConfig } from '@/utils';
import { setAdUserInfo } from '../index.api';
import { IMGS_DATA } from '@/views/workbench/landingPage/evaluationManagement/components/QaComments/src/images.ts'
import { isNumber } from 'lodash-es'

export const IMGS_GROUP_DATA = IMGS_DATA

export const randomAvatar = (index) => {
  if (isNumber(index) && IMGS_DATA[index]) {
    return `${getConfig('NODE_OSS_URL')}/assets/accupload/${IMGS_DATA[index]}`
  }
  const cloneArr = JSON.parse(JSON.stringify(IMGS_DATA))
  let url = cloneArr[getRandomInteger(cloneArr.length - 1, 0)]
  if (url) {
    return `${getConfig('NODE_OSS_URL')}/assets/accupload/${url}`
  } else {
    return ``
  }
}

function getRandomInteger(end, start = 0) {
  const range = end - start
  const random = Math.floor(Math.random() * range + start)
  return random
}
export function useRandomUser() {
  const getRandomUser = async () => {
    try {
      const res = await setAdUserInfo({});
      return res.data || '';
    } catch (error) {
      console.error(error);
      return ''
    }
  };

  

  return { getRandomUser, randomAvatar };
}