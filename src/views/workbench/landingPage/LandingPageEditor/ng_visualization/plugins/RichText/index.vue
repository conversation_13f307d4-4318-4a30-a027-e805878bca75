<template>
  <div :id="'richtext-' + item.uuid" class="brick-rich-text" :style="textStyl">
    <p :class="item.styles.fontFamily" v-html="item.textContent" />
  </div>
</template>

<script>
  export default {
    name: 'NgRichText',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    computed: {
      textStyl() {
        return `padding: 0.02rem .05rem;`
      }
    }
  }
</script>

<style lang="scss" scoped>
  .brick-rich-text {
    display: flex;
    flex-direction: column;
    word-break: break-all;
    outline: none;
    font-size: 0.16rem;
    line-height: 1.5;
    padding: 0.05rem;
    p,
    div {
      font-size: 0.16rem;
      line-height: 1.5;
    }
  }

  .brick-rich-text div,
  .brick-rich-text p,
  div.brick-rich-text,
  p.brick-rich-text {
    margin: 0;
    padding: 0;
    border: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-text-size-adjust: none;
  }
</style>
