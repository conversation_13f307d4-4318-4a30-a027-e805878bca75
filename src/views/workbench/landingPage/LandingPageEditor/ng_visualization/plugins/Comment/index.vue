<template>
  <div :id="'comment-' + item.uuid" class="comment-box" :style="bgColor">
    <div class="num ups">
      <div class="read flex-y-center">
        阅读量：<span>{{ readNum }}</span>
      </div>
      <div class="see flex-y-center">
        <img src="@/assets/images/h5/see.png" alt="" class="img" />
        在看 <span>{{ seeNum }}</span>
      </div>
    </div>
    <div class="comment-list">
      <div class="item flex" v-for="(v, index) in commentList" :key="index">
        <img :src="v.avatar[0]?.url" class="img" v-if="v.avatar[0]?.url" />
        <div class="content flex-1">
          <div class="flex-y-center justify-between">
            <div class="name" :style="`color:${item.styles.userNameColor};`">{{ v.user_name }}</div>
            <div class="zan flex-y-center">
              <img src="@/assets/images/h5/zan.png" alt="" class="icon" />
              <span>{{ v.like }}</span>
            </div>
          </div>
          <div class="text" :style="`color:${item.styles.contentColor};`">{{ v.content }}</div>
          <div class="reply-info" v-if="v.detail_resp && v.detail_resp.length">
            <div v-for="(t, i) in v.detail_resp" :key="i" class="replay-item flex">
              <img :src="t.avatar[0]?.url" class="img" v-if="t.avatar[0]?.url" />
              <div class="content flex-1">
                <div class="flex-y-center justify-between">
                  <div class="name" :style="`color:${item.styles.userNameColor};`">{{ t.user_name }}</div>
                  <!-- <div class="zan flex-y-center">
                    <img src="@/assets/images/h5/zan.png" alt="" class="icon" />
                    <span>{{ t.like }}</span>
                  </div> -->
                </div>
                <div class="text" :style="`color:${item.styles.contentColor};`">{{ t.content }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { randomAvatar } from '@/views/workbench/landingPage/LandingPageEditor/src/randomUser.js'
  export default {
    name: 'NgComment',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {}
    },
    computed: {
      bgColor() {
        return `
                    padding-top: ${this.item.styles.paddingTop};
                    padding-bottom: ${this.item.styles.paddingBottom};
                    background-color:${this.item.styles.backgroundColor || 'transparent'};
                   `
      },
      readNum() {
        return this.item.readNum > 100000 ? '10w+' : this.item.readNum
      },
      seeNum() {
        return this.item.seeNum > 100000 ? '10w+' : this.item.seeNum
      },
      commentList() {
        if (this.item.comment_id) {
          return this.item.commentList || []
        }
        return [
          {
            id: 1,
            avatar: [{ url: randomAvatar(0) }],
            user_name: '张三',
            like: 452,
            content: '这个是评论的内容；这展示具体的内容；这展示具体',
            detail_resp: []
          },
          {
            id: 2,
            avatar: [{ url: randomAvatar(1) }],
            user_name: '张三',
            like: 452,
            content: '这个是评论的内容；这展示具体的内容；这展示具体',
            detail_resp: []
          },
          {
            id: 3,
            avatar: [{ url: randomAvatar(2) }],
            user_name: '张三',
            like: 452,
            content: '这个是评论的内容；这展示具体的内容；这展示具体',
            detail_resp: []
          }
        ]
      }
    }
  }
</script>

<style lang="scss" scoped>
  .comment-box {
    .num {
      padding: 14px 15px;
      font-size: 14px;
      color: #778195;
      line-height: 20px;

      .see {
        .img {
          width: 13px;
          height: 14px;
          margin-right: 3px;
        }
        span {
          margin-left: 6px;
        }
      }
    }
    .comment-list {
      padding: 14px 0;
      .item {
        padding: 0 13px 30px;
        &:last-child {
          padding-bottom: 0;
        }
      }
      .img {
        width: 42px;
        height: 42px;
        border-radius: 4px;
        margin-right: 10px;
      }
      .name {
        font-size: 16px;
        color: #595959;
        line-height: 22px;
      }
      .zan {
        font-size: 14px;
        color: #778195;
        line-height: 20px;
        .icon {
          width: 14px;
          height: 14px;
          margin-right: 2px;
        }
      }
      .text {
        margin-top: 5px;
        font-size: 14px;
        color: #595959;
        line-height: 20px;
        word-wrap: break-word;
        word-break: break-all;
        line-break: anywhere;
      }
      .reply-info {
        .replay-item {
          margin-top: 10px;
        }
      }
    }
  }
</style>
