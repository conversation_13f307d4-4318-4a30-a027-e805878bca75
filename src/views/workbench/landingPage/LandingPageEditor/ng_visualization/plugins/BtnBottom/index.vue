<template>
  <div
    :id="'btnBottom-' + item.uuid"
    class="brick-button upf"
    :style="item.currStyleClass == 'style-three' ? imgStyl : btnBgStyle"
  >
    <div class="button-content upf">
      <span class="ellipsis" :style="textStyl">
        <template v-if="item.currStyleClass != 'style-three'">{{ item.textContent }}</template>
      </span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'NgBtnBottom',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        btnFlag: false
      }
    },
    computed: {
      btnBgStyle() {
        return `
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-color: ${this.item.styles.btnBackgroundColor};
                   `
      },
      textStyl() {
        return `
                    font-size: ${this.item.styles.fontSize};
                   `
      },
      imgStyl() {
        return `
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-size: cover;
                    background-repeat: no-repeat;
                    background-image: url(${this.item.textContent});
                   `
      }
    }
  }
</script>

<style lang="scss" scoped>
  .brick-button {
    height: 100%;
    border-style: solid;
    cursor: pointer;
    .button-content {
      max-width: 100%;
      line-height: 1.5;
      font-size: 0.14rem;
    }
  }
</style>
