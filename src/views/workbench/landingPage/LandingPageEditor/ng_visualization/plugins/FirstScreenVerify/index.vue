<template>
  <div class="first-screen-verify" v-show="isShow">
    <div class="mask"></div>
    <div class="mask-bg"></div>
    <div class="first-screen-verify-content flex-center flex-col">
      <div class="title">温馨提示</div>
      <div class="desc flex-center">
        {{ item.verTips }}
      </div>
      <div class="verifynum flex-center">{{ code }}</div>
      <div class="input">
        <a-input v-model:value="verifyCode" placeholder="请输入验证码" class="inp" />
      </div>
      <div class="btn-group">
        <a-button type="primary" class="btn">确认</a-button>
      </div>
    </div>
  </div>
</template>

<script>
  import { generateVerificationCode } from '@/utils'
  export default {
    name: 'NgFirstScreenVerify',
    props: {
      item: {
        type: Object,
        default: () => ({})
      },
      isShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        code: generateVerificationCode(),
        verifyCode: null
      }
    }
  }
</script>

<style lang="scss" scoped>
  .first-screen-verify {
    .mask,
    .mask-bg {
      height: 100%;
      left: 0;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 9999999;
      background-color: rgba(0, 0, 0, 0.7);
      box-sizing: border-box;
    }
    .mask-bg {
      background-color: transparent;
      z-index: 99999999999;
    }
    .first-screen-verify-content {
      position: fixed;
      margin: 0 auto;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      width: 320px;
      border-radius: 16px;
      padding: 20px 22px;
      background: #ffffff;
      max-height: 100%;
      transition: transform 0.3s;
      -webkit-overflow-scrolling: touch;
      box-sizing: border-box;
      overflow-y: auto;
      z-index: 99999999;
      .title {
        margin-bottom: 13px;
        font-weight: 500;
        font-size: 17px;
        color: #020202;
        line-height: 24px;
      }
      .desc {
        font-size: 15px;
        color: #de0000;
        line-height: 24px;
        word-wrap: break-word;
        word-break: break-all;
      }
      .verifynum {
        width: 234px;
        height: 40px;
        margin-top: 14px;
        background: #fcf6e6;
        border-radius: 6px;
        font-weight: 600;
        font-size: 25px;
        color: #020202;
        line-height: 36px;
        text-align: center;
      }
      .input {
        margin: 10px 0 40px;
        width: 234px;
      }
      :deep(.inp) {
        text-align: center;
        &:focus,
        &:hover {
          border-color: #d9d9d9;
        }
      }
      .btn-group {
        .btn {
          width: 234px;
          height: 38px;
          background: #fe9d35;
          border-radius: 6px;
          font-size: 15px;
          color: #ffffff;
          line-height: 21px;
        }
      }
    }
  }
</style>
