/**
 * @desc 组件库入口
 */

import Image from './Image/index.vue'
import RichText from './RichText/index.vue'
import Button from './Button/index.vue'
import Form from './Form/index.vue'
import BuyNow from './BuyNow/index.vue'
import BtnTop from './BtnTop/index.vue'
import BtnBottom from './BtnBottom/index.vue'
import QaChat from './QaChat/index.vue'
import NgFirstScreenVerify from './FirstScreenVerify/index.vue'
import Rolling from './Rolling/index.vue'
import Comment from './Comment/index.vue'

const components = [
  Image,
  RichText,
  Button,
  Form,
  BuyNow,
  BtnTop,
  BtnBottom,
  NgFirstScreenVerify,
  Comment,
  Rolling,
  QaChat
]

const install = function(Vue) {
  if (install.installed) return
  install.installed = true
  components.map(component => Vue.component(component.name, component))
}

if (window && window.Vue) {
  // CDN 方式引入
  install(window.Vue)
}

export default {
  install,
  Image,
  RichText,
  Button,
  BuyNow,
  BtnTop,
  BtnBottom,
  NgFirstScreenVerify,
  Comment,
  Rolling,
  QaChat
}
