<template>
  <div :id="'rolling-' + item.uuid" class="brick-rolling">
    <div class="list">
      <div :id="'item' + item.uuid + index" class="item flex-y-center" v-for="(v, index) in renderList" :key="index">
        <img :src="v.avatar" class="img" />
        <div class="text">{{ v.content }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getConfig } from '@/utils'
  import { h, renderList } from 'vue'
  var timer = null,
    animating = true,
    itemHeight = 38,
    domItemStyl = {}, // dom样式
    currIndex = 0, // 当前索引
    maxDomCount = 5, // 最大展示dom数量
    speed = 1200, // 更新速度
    lastTime = 0, // 上次更新时间
    replaceIndex = -1, // 替换索引
    currentReplaceIndex = 0, // 当前替换索引
    currentReplaceIndex = maxDomCount

  export default {
    name: 'NgRolling',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        top: 100,
        translateY: 0,
        rawList: [],
        renderList: []
      }
    },
    created() {},
    unmounted() {
      domItemStyl = {}
      this.stopAnimation()
    },
    watch: {
      'item.rollingList': {
        handler(val) {
          this.rawList = JSON.parse(JSON.stringify(val))
          this.initRolling(val)
        },
        deep: true
      }
    },
    methods: {
      initRolling(data) {
        data = data || []
        this.renderList = data.slice(0, 5)
        this.$nextTick(() => {
          this.renderList.forEach((item, index) => {
            let dom = document.getElementById(`item${this.item.uuid}${index}`)
            if (dom) domItemStyl[index] = dom
          })
          this.updateBarrageList(1)
          this.startAnimation()
        })
      },
      startAnimation() {
        let _this = this
        function animate(currentTime) {
          if (!lastTime) lastTime = currentTime
          var elapsed = currentTime - lastTime

          if (elapsed >= speed) {
            console.log(elapsed)
            _this.updateBarrageList(currentTime)
          }

          if (animating) {
            timer = requestAnimationFrame(function () {
              animate(_this.getTimestamp())
            })
          }
        }

        timer = requestAnimationFrame(function () {
          animate(_this.getTimestamp())
        })
      },
      stopAnimation() {
        timer = null
        animating = false
        cancelAnimationFrame(timer)
      },
      updateBarrageList(currentTime) {
        const _this = this
        let renderList = JSON.parse(JSON.stringify(_this.renderList))
        let rawList = _this.rawList
        currIndex++
        if (currentTime > 1) {
          for (var i = 0; i < renderList.length; i++) {
            var item = renderList[i]
            item.translateY -= itemHeight
          }
        }
        // 检查是否需要替换元素
        var needsReplace = []
        for (var j = 0; j < renderList.length; j++) {
          var item = renderList[j]
          if (item.translateY < 0) {
            needsReplace.push(item)
          }
        }
        var newItem = {}
        if (needsReplace.length > 0) {
          var maxTranslateY = 0
          for (var m = 0; m < renderList.length; m++) {
            var item = renderList[m]
            if (item.translateY > maxTranslateY) {
              maxTranslateY = item.translateY
            }
          }
          for (var k = 0; k < needsReplace.length; k++) {
            var item = needsReplace[k]
            // 找到需要替换的元素的索引
            for (var n = 0; n < renderList.length; n++) {
              if (renderList[n].translateY < 0) {
                replaceIndex = n
                break
              }
            }
            if (replaceIndex !== -1) {
              // 按顺序获取替换项
              var nextIndex = currentReplaceIndex % rawList.length
              var curr = rawList[nextIndex]
              newItem = {
                ...curr,
                id: replaceIndex + 1,
                translateY: maxTranslateY + itemHeight
              }
              currentReplaceIndex++
              // 替换元素
              renderList.splice(replaceIndex, 1, newItem)
            }
          }
        }
        lastTime = currentTime
        this.renderList = renderList
        _this.setItemStyl()
      },
      setItemStyl() {
        this.$nextTick(() => {
          this.renderList.forEach((item, index) => {
            const dom = domItemStyl[index]
            if (!dom) return
            if (
              item.translateY == 0 * itemHeight ||
              item.translateY == 1 * itemHeight ||
              item.translateY == 2 * itemHeight
            ) {
              dom.classList.add('transition')
            } else {
              if (dom.classList.contains('transition')) {
                dom.classList.remove('transition')
              }
            }
            const styls = this.computeStyle(item.translateY)
            dom.style.transform = styls.transform
            dom.style.opacity = styls.opacity
          })
        })
      },
      computeStyle(top) {
        let opacity = 0
        if (top == 2 * itemHeight) {
          opacity = 1
        } else if (top == 1 * itemHeight) {
          opacity = 0.5
        }
        return {
          transform: 'translateY(' + top + 'px)',
          opacity: opacity
        }
      },
      getTimestamp() {
        return new Date().getTime()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .brick-rolling {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    .list {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      will-change: transform;
      .transition {
        transition: all 1.2s linear;
      }

      .item {
        position: absolute;
        left: 0;
        padding: 6px 5px;
        height: 28px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 6px;
        margin-bottom: 10px;
        pointer-events: none;
        box-sizing: border-box;
        opacity: 0;
        transform: translateY(100px);
        .img {
          width: 19px;
          height: 19px;
          border-radius: 19px;
          margin-right: 6px;
        }
        .text {
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
        }
      }
    }
  }
</style>
