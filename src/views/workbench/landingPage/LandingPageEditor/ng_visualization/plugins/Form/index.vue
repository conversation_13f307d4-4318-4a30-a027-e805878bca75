<template>
  <div
    :id="'form-' + item.uuid"
    :class="['NgForm', 'ngform-' + item.currStyleClass]"
    :style="{ color: item.txtColors.title }"
  >
    <div :class="['vanform', item.currStyleClass === 'style-three' ? 'left' : 'top']">
      <template v-if="item.form_fields.elelist.length">
        <template v-for="(v, index) in item.form_fields.elelist">
          <div
            :key="index"
            v-if="['username', 'phone', 'email', 'text'].includes(v.key)"
            :class="['vanfield', item.currInputStyleClass, v.key + '-' + index, v.must ? 'required' : '']"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput">
                <input
                  type="text"
                  :placeholder="`请输入${v.title}`"
                  class="vaninput-inner"
                  :name="v.key"
                  autocomplete="off"
                  maxlength="50"
                />
              </div>
              <div class="vanerror">请输入{{ ['phone', 'email'].includes(v.key) ? '正确的' : '' }}{{ v.title }}</div>
            </div>
          </div>
          <div
            :key="index"
            v-if="['cityOne', 'cityTwo', 'date', 'select'].includes(v.key)"
            :class="['vanfield', item.currInputStyleClass, v.key + '-' + index, v.must ? 'required' : '']"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput">
                <input
                  type="text"
                  :placeholder="`请选择${v.title}`"
                  class="vaninput-inner"
                  :name="v.key"
                  autocomplete="off"
                  maxlength="50"
                />
                <div class="vanarrow"></div>
              </div>
              <div class="vanerror">请选择{{ v.title }}</div>
            </div>
          </div>
          <div
            :key="index"
            v-if="['gender'].includes(v.key)"
            :class="['vanfield b-btm-none', item.currInputStyleClass, v.key + '-' + index, v.must ? 'required' : '']"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput">
                <div class="vanselect" data-value="男">
                  <div class="vancheckbox"></div>
                  男
                </div>
                <div class="vanselect" data-value="女">
                  <div class="vancheckbox"></div>
                  女
                </div>
              </div>
              <div class="vanerror">请选择{{ v.title }}</div>
            </div>
          </div>
          <div
            :key="index"
            v-if="['textarea'].includes(v.key)"
            :class="['vanfield', item.currInputStyleClass, v.key + '-' + index, v.must ? 'required' : '']"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput">
                <textarea
                  :placeholder="`请输入${v.title}`"
                  class="vaninput-inner"
                  :name="v.key"
                  autocomplete="off"
                  maxlength="300"
                ></textarea>
              </div>
              <div class="vanerror">请输入{{ v.title }}</div>
            </div>
          </div>
          <div
            :key="index"
            v-if="['inputNumber'].includes(v.key)"
            :class="['vanfield', item.currInputStyleClass, v.key + '-' + index, v.must ? 'required' : '']"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput">
                <input
                  type="number"
                  :placeholder="`请输入${v.title}`"
                  class="vaninput-inner"
                  :name="v.key"
                  autocomplete="off"
                />
              </div>
              <div class="vanerror">请输入{{ ['phone', 'email'].includes(v.key) ? '正确的' : '' }}{{ v.title }}</div>
            </div>
          </div>
          <div
            :key="index"
            v-if="['radio', 'checkout'].includes(v.key)"
            :class="[
              'vanfield b-btm-none',
              v.key + 's',
              item.currInputStyleClass,
              v.key + '-' + index,
              v.must ? 'required' : ''
            ]"
            :style="{ color: item.txtColors.title }"
          >
            <div class="vantitle">
              <span>{{ v.title }}</span>
            </div>
            <div class="vanvalue">
              <div class="vaninput flex-wrap">
                <div class="vanselect" v-for="(n, nIndex) in v.subs" :key="nIndex">
                  <div :class="['vancheckbox', n.checked ? 'active' : '']"></div>
                  {{ n.title }}
                </div>
              </div>
              <div class="vanerror">请选择{{ v.title }}</div>
            </div>
          </div>
        </template>
        <div class="NgForm-agreement-warp">
          <div class="vancheckbox"></div>
          <p>阅读并同意<span class="agree-tag">《个人信息保护声明》</span></p>
        </div>
        <div class="NgForm-btn-wap">
          <div class="vanbutton vansubmit" :style="btnBg">
            <span class="ellipsis-1">{{ item.form_fields.btnText }}</span>
          </div>
        </div>
      </template>
    </div>
    <!--  协议  -->
    <div v-if="closeModal" class="privacymodal"><PrivacyModal @close="closeModal = false" /></div>
    <div v-if="closeAlter"><AlertModal v-if="closeAlter" :item="item" :subm="subm" @close="closeAlter = false" /></div>
  </div>
</template>

<script>
  import { parseTime } from '@/utils/index'
  import PrivacyModal from '@ng_visualization/components/PrivacyModal.vue'
  import AlertModal from '@ng_visualization/components/AlertModal.vue'
  import AreaCom from '@ng_visualization/components/AreaCom.vue'
  import SelectDate from '@ng_visualization/components/SelectDate.vue'
  import SelectOptions from '@ng_visualization/components/SelectOptions.vue'

  export default {
    name: 'NgForm',
    components: {
      PrivacyModal,
      AlertModal,
      AreaCom,
      SelectDate,
      SelectOptions
    },
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        selctedList: [],
        currentDate: null,
        isHide: false,
        isSelectHide: false,
        isAreaCom: false,
        isAgreement: false,
        indexItem: null,
        cityType: null,
        subm: {
          success: false,
          error: false
        },
        closeAlter: false,
        closeModal: false,
        addForm: {
          username: null,
          phone: null
        }
      }
    },
    computed: {
      btnBg() {
        return `
                    font-size: ${this.item.styles.fontSize};
                    color: ${this.item.btnStyle.color};
                    border-radius: ${this.item.btnStyle.radius};
                    background-color: ${this.item.btnStyle.backgroundColor};
                    border-color: ${this.item.btnStyle.borderColor};
                    border-width: ${this.item.btnStyle.btnWidth};
                   `
      },
      flag() {
        if (this.item.form_fields.elelist.length) {
          return this.item.form_fields.elelist.some((v) => v.key === 'phone')
        }
        return false
      }
    },
    watch: {
      'item.uuid': {
        handler: function () {
          if (!this.item.form_fields.elelist.length) {
            this.item.form_fields.elelist = [
              {
                key: 'username',
                types: ['1', '1-1'],
                title: '您的姓名',
                must: true,
                isErr: false,
                isOpen: false,
                form_value: null,
                subs: []
              },
              {
                key: 'phone',
                types: ['1', '1-2'],
                title: '您的电话',
                must: true,
                isErr: false,
                isOpen: false,
                form_value: null,
                subs: []
              }
            ]
          }
        },
        immediate: true
      }
    },
    created() {},
    methods: {
      // 关闭地区弹窗
      onAreaCancel() {
        this.isAreaCom = false
      },
      // 地址
      onAreaConfirm(val) {
        const arr = []
        val && val.forEach((item) => arr.push(item.name))
        this.item.form_fields.elelist[this.indexItem].form_value = arr.join('/')
        this.isAreaCom = false
      },
      // 选择地址
      selectAreas(index) {
        this.indexItem = index
        this.isAreaCom = true
        this.cityType = this.item.form_fields.elelist[index].key || null
      },
      // 点击选择日期
      selectDate(index) {
        this.indexItem = index
        this.isHide = true
      },
      // 选择日期
      dateConfirm(data) {
        const date = parseTime(data, '{y}-{m}-{d}')
        this.item.form_fields.elelist[this.indexItem].form_value = date
        this.currentDate = date
        this.bindCancel()
      },
      bindCancel() {
        this.isHide = false
      },
      // 多选
      selectOptions(index) {
        this.indexItem = index
        this.isSelectHide = true
        const arr = this.item.form_fields.elelist[this.indexItem] || {}
        this.selctedList = (arr.subs && arr.subs.map((v) => v.title)) || []
      },
      selectOptionsConfirm(data) {
        this.item.form_fields.elelist[this.indexItem].form_value = data
        this.selectOptionsCancel()
      },
      selectOptionsCancel() {
        this.isSelectHide = false
      },

      onSubmit(values) {
        // console.log('submit', values)
        if (!this.isAgreement) {
          alert('请阅读并同意《个人信息保护声明》')
          return
        }
        if (this.subm.success) this.subm.success = false
        if (this.subm.error) this.subm.error = false
        this.closeAlter = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  .NgForm .vanfield {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 0.1rem 0.16rem;
    font-size: 0.14rem;
    line-height: 0.24rem;
    overflow: hidden;
    color: #323233;
  }
  .vanfield.required {
    overflow: visible;
  }
  .vanfield.required::before {
    position: absolute;
    left: 0.08rem;
    color: #ee0a24;
    font-size: 0.14rem;
    content: '*';
  }
  .vanfield::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0.16rem;
    bottom: 0;
    left: 0.16rem;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
  .b-btm-none.vanfield::after {
    border-bottom: none;
  }
  .vantitle {
    flex: none;
    width: 100%;
    line-height: 0.2rem;
    color: inherit;
    box-sizing: border-box;
    text-align: left;
    word-wrap: break-word;
  }
  .vantitle span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    word-break: break-all;
  }
  .vanvalue {
    position: relative;
    width: 100%;
    margin-top: 0.04rem;
    color: #969799;
    text-align: right;
    vertical-align: middle;
    word-wrap: break-word;
  }
  .vaninput {
    display: flex;
    align-items: center;
    width: 100%;
  }
  .vaninput-inner {
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    font-size: 14px;
    color: #323233;
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    border: 0;
    resize: none;
    outline: none;
  }
  .vaninput-inner::placeholder {
    color: #c8c9cc;
  }
  .vanselect {
    display: flex;
    align-items: center;
    margin-right: 0.12rem;
    color: #323232;
  }
  .vanselect .vancheckbox {
    margin-right: 0.08rem;
    border-radius: 0.18rem;
    pointer-events: none;
  }
  .checkouts .vanselect .vancheckbox {
    border-radius: 0;
  }
  .vanerror {
    display: none;
    color: #ee0a24;
    font-size: 0.12rem;
    text-align: left;
  }
  .vanarrow {
    display: inline-block;
    width: 0.2rem;
    height: 0.2rem;
    position: relative;
  }
  .vanarrow::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 0.08rem;
    height: 0.08rem;
    border: 0.5rem solid #969799; /* 箭头颜色 */
    border-width: 0 1px 1px 0;
    transform: translate(-30%, -50%) rotate(-45deg);
  }
  .NgForm-agreement-warp {
    display: flex;
    align-items: center;
    padding: 0.15rem;
    font-size: 0.14rem;
  }
  .NgForm-agreement-warp p {
    padding-left: 0.1rem;
    color: #999999;
  }
  .vancheckbox {
    position: relative;
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid #dcdfe6;
    transition:
      color,
      border-color,
      background-color 0.2s;
  }
  .vancheckbox.active {
    border-color: #0188fb;
    background-color: #0188fb;
  }

  /* 对号实现（优化版本） */
  .vancheckbox.active::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 0.09rem;
    height: 0.04rem;
    border: 0.03rem solid #fff;
    border-width: 0 0 0.02rem 0.02rem;
    transform: translate(-50%, -60%) rotate(-45deg);
  }
  .NgForm-agreement-warp .agree-tag {
    color: #208bf1;
  }
  .NgForm-btn-wap {
    padding: 0.1rem 0.15rem 0.15rem;
    overflow: hidden;
  }
  .vanbutton {
    width: 100%;
    position: relative;
    height: 0.44rem;
    margin: 0;
    padding: 0 15px;
    text-align: center;
    transition: opacity 0.3s;
    -webkit-appearance: none;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .vanbutton::before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    transition: all 0.2s;
    opacity: 0;
    content: ' ';
  }
  .vanbutton:active::before {
    opacity: 0.1;
  }

  /* 四周框线 */
  .NgForm .vanfield.range::after {
    border-bottom: none;
  }
  .range .vanvalue {
    width: 100%;
    border: 1px solid #dcdcdc;
    padding: 0.06rem;
  }
  .checkouts,
  .radios {
    &.range .vanvalue {
      border: none;
    }
  }

  /* 表单样式二 */
  .ngform-style-two .vantitle,
  .ngform-style-two .vanfield.required::before {
    display: none;
  }
  /* 表单样式三 */
  .ngform-style-three .vanfield {
    flex-wrap: nowrap;
    margin-bottom: 0.095rem;
    padding-bottom: 0;
  }
  .ngform-style-three .vanfield.required::before {
    top: 0.17rem;
  }
  .ngform-style-three .vanfield .vantitle {
    width: 0.9rem;
    margin-top: 0.09rem;
    padding-right: 0.05rem;
    box-sizing: border-box;
  }
  .ngform-style-three .vanfield .vanvalue {
    margin-top: 0;
    padding: 0.07rem 0;
    border-bottom: 1px solid #ebedf0;
  }
  .ngform-style-three .vanfield::after {
    border-bottom: none;
  }

  .ellipsis-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    word-break: break-all;
  }

  .t-overlay.agreeForm .overlay-wrap .contents .contents-title {
    font-size: 0.2rem;
    text-align: center;
    font-weight: 600;
    padding: 0.1rem 0;
  }

  .t-overlay.agreeForm .overlay-wrap .contents .guarantee-text {
    padding: 0.15rem 0.15rem 0.05rem;
    font-size: 0.14rem;
    color: #7b7b7b;
    span {
      color: rgb(64, 136, 255);
    }
  }

  .t-overlay.agreeForm .overlay-wrap .contents .submit {
    position: relative;
    display: inline-block;
    color: white;
    background: #4088ff;
    border: 0px;
    width: 100%;
    height: 44px;
    font-size: 14px;
    padding: 0 15px;
    border-radius: 4px;
    outline: none;
    border: none;
  }
</style>
