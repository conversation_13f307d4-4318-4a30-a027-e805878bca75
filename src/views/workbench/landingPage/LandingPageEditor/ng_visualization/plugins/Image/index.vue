<template>
  <div :id="'image-' + item.uuid" class="image-holder">
    <img
      v-if="item.srcs"
      :src="item.srcs.imgurl"
      class="brick-picture"
      style="border-radius: 0rem"
      @click.stop="handleClickImage"
    />
  </div>
</template>

<script>
  export default {
    name: 'NgImage',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {}
    },
    computed: {
      imageStyl() {
        return `transform: rotate(${this.item.rotation}deg) scale(${this.item.scaleX}, ${this.item.scaleY});`
      }
    },
    methods: {
      handleClickImage() {
        if (this.item.link.type === 'page' && this.item.link.url) window.location.href = this.item.link.url
        if (this.item.link.type === 'applet') {
          this.$message.warning('未绑定项目，暂不能跳转小程序')
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .image-holder,
  .brick-picture {
    display: block;
    width: 100%;
    height: 100%;
  }
  .brick-picture img,
  img.brick-picture,
  .odd-picture {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-text-size-adjust: none;
    vertical-align: middle;
    outline: none;
  }
  .odd-picture {
    font-size: 0;
    img {
      width: 100%;
      height: auto;
    }
  }
</style>
