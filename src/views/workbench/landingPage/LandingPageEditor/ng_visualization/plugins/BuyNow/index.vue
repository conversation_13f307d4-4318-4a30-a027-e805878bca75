<template>
  <div>
    <div v-if="item.currStyleClass === 'style-one'" class="wrap pr-10px! pl-5px!">
      <div class="img-box flex-center flex-col">
        <img :src="imgObj.shop" class="brick-image" />
        <span> 店铺</span>
      </div>
      <div class="img-box flex-center flex-col">
        <img :src="imgObj.kefu" class="brick-image" />
        <span>客服</span>
      </div>
      <div class="img-box flex-center flex-col">
        <img :src="imgObj.collect" class="brick-image" />
        <span>收藏</span>
      </div>
      <div class="flex">
        <div class="one-left text">{{ item.leftText }}</div>
        <div class="one-right text">{{ item.rightText }}</div>
      </div>
    </div>
    <div v-if="item.currStyleClass === 'style-two'" class="wrap two-wrap pr-10px! pl-10px!">
      <div class="two-img-box">
        <div class="img-box flex-center flex-col">
          <img :src="imgObj.kefu" class="brick-image" />
          <span>客服</span>
        </div>
      </div>

      <div class="two-right text">{{ item.rightText }}</div>
    </div>
    <div v-if="item.currStyleClass === 'style-three'" class="wrap two-wrap pr-10px! pl-10px!">
      <div class="three-img-box">
        <div class="img-box flex-center flex-col">
          <img :src="imgObj.buy" class="brick-image" />
          <span>加入购物车</span>
        </div>
      </div>

      <div class="three-right text">{{ item.rightText }}</div>
    </div>
    <div v-if="item.currStyleClass === 'style-four'" class="wrap two-wrap pr-10px! pl-10px!">
      <div class="three-img-box">
        <div class="img-box flex-center flex-col">
          <img :src="imgObj.buy" class="brick-image" />
          <span>加入购物车</span>
        </div>
      </div>

      <div class="flex">
        <div class="four-left text">{{ item.leftText }}</div>
        <div class="four-right text">{{ item.rightText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getConfig } from '@/utils'

  export default {
    name: 'NgBuyNow',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        btnFlag: false,
        imgObj: {
          shop: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-home.png',
          kefu: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-kf.png',
          collect: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-collect.png',
          buy: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-buy.png'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrap {
    display: flex;
    padding: 0.13rem 0rem;
    text-align: center;
    background: #ffffff;
    .img-box {
      text-align: center;
      font-size: 13px;
      flex: 1;
      height: 100%;
    }
    .text {
      line-height: 44px;
      font-size: 13px;
      color: #ffffff;
    }
    .one-left {
      width: 108px;
      height: 44px;

      background: linear-gradient(90deg, #fecc1f 0%, #fe8b17 100%);
      border-radius: 27px 0px 0px 27px;
    }
    .one-right {
      width: 114px;
      height: 44px;
      background: linear-gradient(270deg, #ee0e24 0%, #fd5c32 100%);
      border-radius: 0px 27px 27px 0px;
    }
    .brick-image {
      width: 0.14rem;
      height: 0.14rem;
      margin: 0 auto;
      // margin-left: 0.07rem;
      margin-bottom: 0.02rem;
      margin-bottom: 3px;
    }
    .two-right {
      width: 272px;
      height: 44px;
      background: linear-gradient(270deg, #ff724e 0%, #ff621a 100%);
      border-radius: 22px;
    }
    .four-left {
      width: 136px;
      height: 44px;
      background: linear-gradient(90deg, #fecc1f 0%, #fe8b17 100%);
      border-radius: 22px 0px 0px 22px;
    }
    .four-right {
      width: 136px;
      height: 44px;
      background: linear-gradient(270deg, #ee0e24 0%, #fd5c32 100%);
      border-radius: 0px 27px 27px 0px;
    }
    // .three-img-box {
    //   .img-box {
    //     margin-left: 0.06rem;
    //     margin-right: 0;
    //   }
    // }
  }
  .two-wrap {
    justify-content: space-between;
    .two-img-box {
      padding-left: 0.25rem;
    }
    .three-right {
      width: 272px;
      height: 44px;
      background: linear-gradient(270deg, #ec3015 0%, #fd5c32 100%);
      border-radius: 22px;
    }
  }
</style>
