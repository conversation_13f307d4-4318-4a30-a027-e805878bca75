<template>
  <div :id="'qachats-' + item.uuid" class="brick-qaH5">
    <div class="wrapper relative" :class="['wrapper' + qaModule]">
      <div v-if="item.isRule" class="rule" :style="topStyl">
        <div class="rule_text" @click.stop="state.isShowRuleContent = !state.isShowRuleContent">
          {{ item.ruleTitle || '活动规则' }}
        </div>
      </div>
      <div class="content" :style="fontSize" ref="contentRef">
        <template v-if="state.list.length || store.qaLists.length || item.carouselList.length">
          <template v-if="item.carousel_type == 1">
            <a-carousel autoplay arrows style="height: auto">
              <template #prevArrow>
                <div class="custom-slick-arrow flex-center!" style="left: 10px; z-index: 1">&lt;</div>
              </template>
              <template #nextArrow>
                <div class="custom-slick-arrow flex-center!" style="right: 10px">&gt;</div>
              </template>
              <div class="swiper-item" v-for="(v, index) in item.carouselList" :key="index">
                <img :src="v.pic + '?time=' + Date.now()" crossOrigin="anonymous" class="img w-100% h-100%" />
              </div>
            </a-carousel>
          </template>
          <template v-if="item.carousel_type == 2">
            <div class="swiper-item text-0!" v-for="(v, index) in item.carouselList" :key="index">
              <img :src="v.pic + '?time=' + Date.now()" crossOrigin="anonymous" class="img w-100% h-100%" />
            </div>
          </template>

          <div class="dialog-box" v-if="qaModule == 1">
            <div class="pl-10px pr-10px" v-for="(v, idx) in store.qaLists" :key="idx + 'x'">
              <div class="a f-flex" v-if="v.replyTxt">
                <div v-if="v.replyTxt" class="a_content" v-html="v.replyTxt" />
                <img class="a_avatar" :src="item.user_avatar + '?time=' + Date.now()" crossOrigin="anonymous" />
              </div>
              <template v-else>
                <div class="q f-flex" v-for="(t, index) in v.contextList" :key="index + 'x'">
                  <img class="q_avatar" :src="item.custome_avatar + '?time=' + Date.now()" crossOrigin="anonymous" />
                  <div class="q_content q_content2">
                    <div class="q_content_title">
                      <div v-html="t.content" />
                      <img
                        :src="t.pic + '?time=' + Date.now()"
                        crossOrigin="anonymous"
                        class="imgg mt-4px"
                        v-if="t.pic"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div class="replay_bottom flex-center" v-if="store.qaCurrInfo.replyList?.length">
              <div class="item-box flex-wrap w-100% gap-8px">
                <template v-for="(v, i) in store.qaCurrInfo.replyList">
                  <div
                    class="item"
                    :class="'btn-style-' + store.qaCurrInfo.rowBtnStyle"
                    :style="{
                      borderRadius: store.qaCurrInfo.btnRadius + 'px',
                      width: `calc(${100 / calcl}% - 8px)`
                    }"
                    :key="i + 'x'"
                    @click="onSelectReply(v, i)"
                    v-if="v.content"
                  >
                    {{ v.content }}
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="dialog-box" v-else>
            <div class="pl-10px pr-10px" v-for="(it, idx) in state.list" :key="idx + 'x'">
              <div
                :class="['q f-flex', { end: it.contextList.length == index + 1 }]"
                v-for="(v, index) in it.contextList"
                :key="index"
              >
                <img class="q_avatar" :src="item.custome_avatar + '?time=' + Date.now()" crossOrigin="anonymous" />
                <div class="q_content q_content2">
                  <div class="q_content_title">
                    <div v-html="v.content" />
                    <img
                      :src="v.pic + '?time=' + Date.now()"
                      crossOrigin="anonymous"
                      class="imgg mt-4px"
                      v-if="v.pic"
                    />
                  </div>
                  <template v-if="it.contextList.length == index + 1">
                    <div v-if="it.replyList && it.replyList.length" class="q_content_list">
                      <div
                        v-for="(it2, idx2) in it.replyList || []"
                        :key="idx2"
                        class="q_content_item flex-align"
                        @click="sendUserMsg(it2, idx, idx2)"
                      >
                        <div
                          class="q_content_item_title"
                          :class="{ n_color: it2.isNot == 1 }"
                          :style="item.fontColor ? `color:${item.fontColor};` : ''"
                          v-html="it2.content"
                        />
                        <img class="q_content_item_img" src="@/assets/images/h5/kf_icon1.png" />
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <div v-for="(v, index) in it.dialogue" :key="index">
                <div class="a f-flex">
                  <div v-if="v.content" class="a_content" v-html="v.content" />
                  <img class="a_avatar" :src="item.user_avatar + '?time=' + Date.now()" crossOrigin="anonymous" />
                </div>
                <div v-if="v.replyText && v.replyText != '<p><br></p>'" class="q f-flex">
                  <img class="q_avatar" :src="item.custome_avatar + '?time=' + Date.now()" crossOrigin="anonymous" />
                  <div class="q_content">
                    <div class="q_content_title" v-html="v.replyText" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <div v-else class="empty-page-box">
          <div class="empty-page">
            <img :src="getConfig('NODE_OSS_URL') + '/assets/h5/h5-editor_empty.png'" alt="empty_page" width="289" />
            <p class="title">请配置对话组</p>
          </div>
        </div>
      </div>
      <div v-show="store.getQaChatType == 3" class="qa-customer">
        <div class="btn-customer upf">
          <button v-if="item.customer_btn.style == 1" class="btn" :style="btnStyl">
            {{ item.customer_btn.text }}
          </button>
          <div v-if="item.customer_btn.style == 2" class="btn2 flex-align">
            <img :src="accountAvatar + '?time=' + Date.now()" crossOrigin="anonymous" class="avatar" />
            <div class="fixedBtn-text">
              <div class="text-top">
                <h5 class="text-title">{{ item.customer_btn.title || '标题' }}</h5>
              </div>
              <span class="text-desc">{{ item.customer_btn.desc || '描述' }}</span>
            </div>
            <div class="transform-btn">
              <button class="t upf" :style="btnStyl">{{ item.customer_btn.text }}</button>
            </div>
          </div>
        </div>
      </div>
      <div v-show="state.isShowRuleContent" class="popup-rule-qa">
        <div class="rule-mask"></div>
        <div class="rule-modal upf">
          <div class="rule-content relative">
            <div class="rule-head">{{ item.ruleTitle || '活动规则' }}</div>
            <img src="@/assets/images/h5/close.png" class="rule-close" @click.stop="state.isShowRuleContent = false" />
            <div class="rule-body">
              <div class="rule-body-content common_scroll_bar_width" v-html="item.ruleContent" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'NgQaChat'
  }
</script>

<script setup>
  import { nextTick, onBeforeMount, watch, computed, reactive, ref } from 'vue'
  import { cloneDeep, throttle } from 'lodash-es'
  import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue'
  import { getConfig } from '@/utils'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import { message } from 'ant-design-vue'
  const store = useNgVisualizationStore()

  const props = defineProps({
    item: {
      type: Object,
      default: () => ({})
    }
  })

  const contentRef = ref(null)
  const state = reactive({
    isShowRuleContent: false,
    isEnd: false,
    list: [],
    replayIndex: -1,
    qIdx: 0
  })

  const qaModule = computed(() => {
    return props.item.currStyleClass
  })

  const fontSize = computed(() => {
    return (props.item.fontSize || 14) + 'px'
  })

  const topStyl = computed(() => {
    return (props.item.top || 10) + '%'
  })

  const btnStyl = computed(() => {
    return {
      color: props.item.customer_btn.textColor,
      backgroundColor: props.item.customer_btn.bgColor,
      fontSize: props.item.customer_btn.fontSize + 'px',
      borderRadius: props.item.customer_btn.radius + 'px'
    }
  })

  const accountAvatar = computed(() => {
    return props.item.customer_btn.avatar || getConfig('NODE_OSS_URL') + '/assets/h5/wechat_logo_Wecom.png'
  })

  const calcl = computed(() => {
    const item = store.qaCurrInfo || {}
    return item.replyList?.length < item.rowBtnStyle ? item.replyList?.length : item.rowBtnStyle
  })

  watch(
    () => props.item.isRule,
    (newVal) => {
      if (!newVal) state.isShowRuleContent = false
    }
  )
  watch(
    () => store.getQaActive,
    (newVal) => {
      if (qaModule.value == 1) {
        if (!state.isEnd) setCurrInfo(store.qaCurrIndex)
      } else {
        state.list = []
        pushWelcomeList()
      }
    },
    { deep: true }
  )

  onBeforeMount(() => {
    if (props.item?.welcomeList?.length) {
      if (qaModule.value == 1) {
        setCurrInfo(store.qaCurrIndex)
      } else {
        pushWelcomeList()
      }
    }
  })

  let timer1 = null
  function onSelectReply(item, index) {
    if (item.sessionStatus == 2) {
      store.qaLists.push({
        replyTxt: item.content
      })
      timer1 = setTimeout(() => {
        store.qaLists.push({
          contxt: {
            content: item.endValue
          }
        })
        scrollToBottom()
        clearTimeout(timer1)
        store.qaCurrInfo = {}
        state.isEnd = true
      }, 200)
      return
    }
    console.log(item, state.replayIndex, store.qaCurrIndex, 'item')
    if (item.clickStatus == 2 || item.clickStatus == 3) {
      return message.warning('当前为预览模式，不可操作！')
    }
    if (item.clickStatus == 4 && !item.otherReply) {
      return message.warning('请选择跳转的回答')
    }
    store.qaCurrIndex = store.qaCurrIndex + 1
    store.qaLists.push({
      replyTxt: item.content
    })
    if (item.clickStatus == 4) {
      const curr = props.item.qaDataList[item.otherReply - 1] || {}
      store.qaCurrInfo = {
        ...curr,
        contxt: (curr.contextList && curr.contextList[0]) || ''
      }
      const t = store.qaLists.findIndex((v) => v.id == store.qaCurrInfo.id)
      if (t <= 0) {
        store.qaLists.push(store.qaCurrInfo)
      }
      scrollToBottom()
      return
    }
    setCurrInfo(store.qaCurrIndex)
    scrollToBottom()
    if (state.replayIndex >= 0) return
    const timer = setTimeout(() => {
      if (store.qaCurrIndex >= props.item.qaDataList.length) {
        store.qaCurrInfo = {}
        state.isEnd = true
      }
      clearTimeout(timer)
    }, 100)
  }

  function scrollToBottom() {
    nextTick(() => {
      contentRef.value.scrollTo({
        top: contentRef.value.scrollHeight,
        behavior: 'smooth'
      })
    })
  }

  function setCurrInfo(index) {
    if (props.item?.qaDataList?.length) {
      const item = props.item.qaDataList[store.qaCurrIndex] || {}
      if (!item.id || !item.replyList?.length) return
      store.qaCurrInfo = {
        ...item
        // contxt: (item.contextList && item.contextList[0]) || ''
      }
      const t = store.qaLists.findIndex((v) => v.id == store.qaCurrInfo.id)
      if (t < 0) {
        store.qaLists = []
        store.qaLists.push(store.qaCurrInfo)
      }
    }
  }

  async function pushWelcomeList() {
    if (props.item?.qaDataList?.length) {
      for (const val of props.item.qaDataList) {
        const status = store.getQaItemInfo.id === val.id
        pushQaList(val, status)
      }
    }
  }
  function pushQaList(it, status) {
    const index = 0
    it = cloneDeep(it)
    it.replyList[index].isNot = 1
    state.list.push({
      ...it,
      // contxt: (it.contextList && it.contextList[0]) || '',
      dialogue: [{ ...it.replyList[index] }]
    })
  }
  function sendUserMsg(it, idx1, idx2) {
    console.log(it, idx1, idx2)
    state.list[idx1].replyList[idx2].isNot = 1
    state.list[idx1].dialogue.push(it)
  }
</script>

<style lang="scss" scoped>
  .wrapper1 {
    .q_content2 {
      min-width: auto;
    }
    .replay_bottom {
      // position: fixed;
      // bottom: 84px;
      width: 100%;
      padding: 0 16px;
      margin-top: 20px;
      animation: fadeInToRightAnswerOptions 0.4s ease forwards;
      .item-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
      }
      .item {
        max-width: 350px;
        // min-height: 40px;
        padding: 10px 8px;
        // margin: 0 8px 10px 0;
        background: #fe9d35;
        text-align: center;
        border-radius: 6px;
        font-size: 15px;
        color: #ffffff;
        line-height: 20px;
        word-wrap: break-word;
        word-break: break-all;
        cursor: pointer;
        overflow: hidden;
        line-break: anywhere;
      }
      .btn-style-1 {
        width: 98%;
      }
      .btn-style-2 {
        width: calc(50% - 8px);
      }
      .btn-style-3 {
        width: calc(33.33% - 8px);
      }
      .btn-style-4 {
        width: calc(25% - 8px);
      }
    }
  }
  @keyframes fadeInToRightAnswerOptions {
    0% {
      opacity: 0;
      -webkit-transform: translateX(-20px);
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      -webkit-transform: translateX(0);
      transform: translateX(0);
    }
  }
  .brick-qaH5 {
    color: #333;
    font-family: PingFang SC;
  }
  .content {
    background: #f4f4f4;
    padding: 1px 0 110px;
    width: 100%;
    min-height: 585px !important;
    overflow: scroll;
    position: relative;
  }
  .rule {
    position: absolute;
    z-index: 1000;
    right: 0;
    width: 60px;
    line-height: 20px;
    color: #000;
    top: 10%;
    background: hsla(0, 0%, 100%, 0.85);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-right: 0;
    border-radius: 10px 0 0 10px;
    text-align: center;
    text-indent: 2px;
    .rule_text {
      font-size: 12px;
      transform: scale(0.8333);
    }
  }
  .qa-customer {
    position: absolute;
    left: 0;
    bottom: 20px;
    width: 100%;
    z-index: 3;
    .btn-customer {
      width: 100%;
      text-align: center;
      padding: 0;
      button {
        outline: none;
        text-decoration: none;
        border: none;
      }
      .btn {
        width: 90%;
        height: 40px;
        line-height: 40px;
      }
      .btn2 {
        pointer-events: all;
        width: 90%;
        box-sizing: border-box;
        padding: 7px 82px 7px 12px;
        border-radius: 8px;
        background-color: #fff;
        position: relative;
        .avatar {
          width: 56px;
          height: 56px;
          border-radius: 4px;
        }
        .fixedBtn-text {
          padding: 0 4px 0 12px;
          flex: 1;
          width: 75%;
        }
        h5 {
          padding: 0;
          margin: 0;
        }
        .text-top {
          text-align: left;
        }
        .text-title {
          line-height: 25px;
          font-size: 16px;
          font-weight: 500;
          color: #030303;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left;
        }
        .text-desc {
          font-size: 12px;
          color: #868e9f;
          white-space: nowrap;
          text-align: left;
          overflow: hidden;
          text-indent: 1px;
          text-overflow: ellipsis;
          display: block;
        }
        .transform-btn {
          position: absolute;
          right: 10px;
          .t {
            min-width: 70px;
            border-radius: 4px !important;
            line-height: 31px;
            height: 31px;
            padding: 0px 4px;
          }
        }
      }
    }
  }
  .content::-webkit-scrollbar {
    /* 对于 Chrome, Safari 和 Opera */
    display: none;
  }
  .q {
    margin-top: 20px;
    &.end {
      .q_content2 {
        min-width: 236px;
      }
    }
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
  .q_content,
  .a_content {
    display: flex;
    flex-direction: column;
    // animation: fadeInToRightAnswerOptions 0.4s ease forwards;
    background: #ffffff;
    min-width: 18px;
    margin: 0 15px;
    line-height: 1.4;
    padding: 12px;
    border-radius: 8px;
    max-width: 264px;
    position: relative;
    word-break: break-all;
    word-wrap: break-word;
    font-size: inherit;
    font-weight: 400;
    color: #030303;
  }
  .q_content2 {
    min-width: auto;
    max-width: 260px;
    font-size: 15px;
    .q_content_title {
      font-size: inherit;
      color: #030303;
      font-weight: 500;
      .imgg {
        width: 100%;
        height: auto;
        border-radius: 0;
      }
    }

    .q_content_list {
      padding-top: 12px;
      margin-bottom: -12px;
      .q_content_item {
        &:active {
          background-color: #adddf2;
        }
        .n_color {
          color: rgb(203, 205, 212) !important;
        }
        padding: 12px 0 12px 0;
        border-top: 0.5px solid rgba(0, 0, 0, 0.1);
        cursor: pointer;
        .q_content_item_title {
          color: #296aef;
          font-size: inherit;
          line-height: 1.4;
          flex: 1;
          font-weight: 500;
          word-break: break-all;
          word-wrap: break-word;
          max-width: 214px;
          font-size: 15px;
        }
        .q_content_item_img {
          width: 10px;
          height: 10px;
          margin-left: 12px;
        }
      }
    }
  }
  .a {
    margin-top: 20px;
    justify-content: flex-end;
    .a_avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .a_content {
      background: #cce4fc;
    }
  }
  @keyframes fadeInToRightAnswerOptions {
    0% {
      opacity: 0;
      -webkit-transform: translateX(-20px);
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      -webkit-transform: translateX(0);
      transform: translateX(0);
    }
  }
  .popup-rule-qa {
    .rule-mask,
    .rule-modal {
      height: 100%;
      left: 0;
      position: absolute;
      top: 0;
      width: 100%;
      z-index: 2000;
    }
    .rule-modal {
      padding: 30px;
    }
    .rule-content {
      background-color: #fff;
      border-radius: 8px 8px 8px 8px;
      box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.2);
      height: 390px;
      width: 100%;
      z-index: 2000;
    }
    .rule-head {
      color: #000;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      padding: 20px 0 15px;
      text-align: center;
    }
    .rule-close {
      cursor: pointer;
      height: 20px;
      position: absolute;
      right: 20px;
      top: 20px;
      width: 20px;
    }
    .rule-body {
      color: #4d5869;
      font-size: 14px;
      height: 100%;
      line-height: 24px;
      margin-top: -57px;
      padding: 57px 20px 20px;
      word-break: break-all;
    }
    .rule-body-content {
      height: 100%;
    }
  }
  .empty-page-box {
    height: 585px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .empty-page {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -62px;
    text-align: center;
    .title {
      font-size: 16px;
      color: #ccc;
      margin-top: 15px;
    }
    .subtitle {
      font-size: 12px;
      color: #d9d9d9;
      margin-top: 12px;
    }
  }

  :deep(.slick-arrow.custom-slick-arrow) {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 8px 10px;
    cursor: pointer;
    font-size: 14px;
    inset-inline-end: 0;
    width: 27px;
    height: 31px;
    &::before {
      content: '';
      display: none;
    }
  }
</style>
