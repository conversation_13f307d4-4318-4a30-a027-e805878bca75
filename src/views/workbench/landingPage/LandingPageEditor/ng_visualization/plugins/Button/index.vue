<template>
  <div :id="'button-' + item.uuid" class="brick-button upf" :style="bgHeight" @click="handleClickLink">
    <div class="button-content upf" :style="item.currStyleClass == 'style-three' ? imgStyl : btnBgStyle">
      <span class="ellipsis" :style="textStyl">
        <template v-if="item.currStyleClass != 'style-three'">{{ item.textContent }}</template>
      </span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'NgButton',
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        btnFlag: false
      }
    },
    computed: {
      bgHeight() {
        if (['top', 'bottom'].includes(this.item.position)) {
          return `height: 100%;`
        }
        return `height: ${this.item.eboxValue.bgHeight / 100}rem;`
      },
      btnBgStyle() {
        if (['top', 'bottom'].includes(this.item.position)) {
          return `
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-color: ${this.item.styles.btnBackgroundColor};
                   `
        }
        return `
                    width: ${this.item.styles.width};
                    height: ${this.item.styles.height};
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-color: ${this.item.styles.btnBackgroundColor};
                   `
      },
      textStyl() {
        return `
                    font-size: ${this.item.styles.fontSize};
                   `
      },
      imgStyl() {
        if (['top', 'bottom'].includes(this.item.position)) {
          return `
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-size: cover;
                    background-repeat: no-repeat;
                    background-image: url(${this.item.textContent});
                   `
        }
        return `
                    height: ${this.item.styles.height};
                    color: ${this.item.styles.btnColor};
                    border-color: ${this.item.styles.borderColor};
                    border-width: ${this.item.styles.borderWidth};
                    border-radius: ${this.item.styles.borderRadius};
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-image: url(${this.item.textContent});
                   `
      }
    },
    methods: {
      handleClickLink() {
        if (this.item.clickType === 1) {
          if (this.item.link.type === 'page' && this.item.link.url) window.location.href = this.item.link.url
          if (this.item.link.type === 'applet') {
            this.$message.warning('未绑定项目，暂不能跳转小程序')
          }
        }
        if (this.item.clickType === 2 && this.item.popup.srcs.imgurl) this.btnFlag = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  .brick-button {
    height: 100%;
    .button-content {
      height: 100%;
      width: 100%;
      line-height: 1.5;
      font-size: 0.14rem;
      border-style: solid;
      cursor: pointer;
      overflow: hidden;
    }
  }
</style>
