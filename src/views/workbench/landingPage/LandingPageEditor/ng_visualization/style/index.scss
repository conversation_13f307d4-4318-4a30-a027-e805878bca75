$theme-color: #4e90ff;
$bottom-height: 54px;

img {
  display: inline-block;
}
div,
span {
  font-variant: normal;
}

.landing-spin {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
}
.app_landing {
  height: 100%;
  min-width: 1680px;
  // height: 100vh;
  height: calc(100vh - $bottom-height);
  p {
    margin: 0;
    padding: 0;
    word-break: break-all;
    word-wrap: break-word;
  }
  .ant-layout-header,
  .ant-layout-sider,
  .ant-layout-content {
    position: relative;
    background-color: #fff;
  }
  .ant-layout-sider,
  .ant-layout-header {
    box-sizing: border-box;
    flex-shrink: 1;
  }
  .ant-layout-header {
    height: 60px;
    line-height: 60px;
    padding: 0 32px;
    z-index: 20;
    box-shadow: 0 2px 6px 0 rgb(0 0 0 / 8%);
  }
  .ant-layout-sider-children {
    width: 100%;
    overflow-y: auto;
  }
  .main-wrap {
    overflow-y: hidden;
  }
  .ant-layout-sider.left-panel {
    overflow: visible;
    background-color: #fafafa;
  }
  .ant-layout-sider {
    display: flex;
    box-shadow: 0 12px 12px 0 rgb(0 0 0 / 10%);
    &:last-child {
      overflow: visible;
    }
  }
  .ant-layout-content.main-area {
    background-color: #f4f7f9;
    padding: 0;
    overflow-y: auto;
  }

  .type-list__tree {
    min-width: 103px;
    .ant-tree-node-content-wrapper {
      display: flex;
      align-items: center;
      height: 38px;
      background-color: transparent;
      &:hover {
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper .type-list__item {
      margin-left: 15px;
    }
    .ant-tree-switcher {
      width: 16px;
      margin-top: 6px;
    }
    .ant-tree-node-content-wrapper:hover {
      background-color: transparent;
    }
    .ant-tree-switcher.ant-tree-switcher-noop,
    .ant-tree-indent {
      display: none;
    }
    .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected,
    .ant-tree .ant-tree-checkbox + span.ant-tree-node-selected {
      background-color: transparent;
    }
    .type-list__item {
      color: #666;
      font-size: 14px;
      line-height: 22px;
      border-radius: 16px;
      padding: 6px 8px;
      margin-left: 8px;
      transition: all 0.15s;
      font-weight: 500;
      &.selected {
        background-color: #4e90ff;
        color: #fff;
        &:hover {
          background-color: #66b1ff;
        }
      }
      &:hover {
        background-color: #f4f7f9;
      }
    }
    .type-list__collection {
      font-size: 14px;
      &.active {
        color: #4e90ff;
      }
    }
    .type-list__collection,
    .clickable,
    .type-list__item {
      user-select: none;
    }
  }

  .operator-tooltip__edit {
    flex-direction: column;
    position: relative;
    color: #333;
    cursor: pointer;
    outline: 0;
    font-size: 16px;
    font-weight: 600;
    &:hover {
      color: #4e90ff;
    }
    &.single-operator {
      height: 40px;
    }
    &.multiple-operator {
      height: 56px;
      &:first-of-type::after {
        content: '';
        width: 20px;
        position: absolute;
        bottom: 0;
        border-bottom: 1px solid #e8e8e8;
      }
    }
  }
  .float-section {
    position: absolute;
    height: 100%;
    width: 375px;
    pointer-events: none;
  }
  .float-items {
    position: sticky !important;
    pointer-events: auto;
    transform: translate(0);
  }
  .top-float-items,
  .top-float-section {
    top: 0;
    z-index: 1;
  }
  .control-panel__inner {
    padding: 0 16px;
    box-sizing: border-box;
  }
  .delete-button {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-top: 24px;
    padding-bottom: 24px;
    .ant-btn,
    .ant-btn:focus {
      font-weight: 400;
      width: 100%;
      margin: 0 8px;
      color: #4e90ff;
      border-color: #4e90ff;
      background-color: #fff;
      font-weight: 500;
      border-radius: 40px;
    }
    .ant-btn:hover {
      color: #649bf6;
    }
  }
  .clickable,
  .delete-button {
    cursor: pointer;
    user-select: none;
  }

  .panel-section__block {
    border-bottom: 1px solid #e8e8e8;
    padding: 24px 8px;
    .header {
      margin-bottom: 24px;
      user-select: none;
    }
    .icon {
      width: 4px;
      height: 16px;
      border-radius: 2px;
      display: inline-block;
      background-color: #4e90ff;
    }
    .title {
      color: #333;
      font-size: 20px;
      padding-left: 8px;
    }
    .control-panel__section-body {
      margin-top: -16px;
    }
    .control-panel-group {
      display: flex;
      margin-left: -12px;
      align-items: flex-start;
      padding-top: 14px;
    }
    .control-panel-group__label,
    .control-panel-group__label--large {
      display: inline-block;
      line-height: 24px;
      padding: 4px 0;
      color: #333;
      font-size: 14px;
      user-select: none;
    }
    .control-panel-group__label {
      width: 68px;
      margin-left: 10px;
    }
    // .control-l {margin-left: 12px; margin-top: 16px;}
    .control-panel-group__body {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: space-between;
      flex-wrap: wrap;
    }
    .control-panel-group__label + .control-panel-group__body {
      margin-left: -12px;
    }
    .multiline {
      display: flex;
      box-sizing: border-box;
      flex: auto;
      flex-direction: column;
      width: 100%;
      padding-left: 10px;
    }
    .height-input__number {
      line-height: 34px;
      height: 34px;
      width: 100%;
      &.el-input-number .el-input__inner {
        text-align: left;
      }
    }
    .width-text {
      text-align: left;
      margin-top: 8px;
      font-size: 12px;
      line-height: 20px;
      color: rgb(153, 153, 153);
      font-weight: 300;
    }
  }

  .fbgradio {
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      color: #409eff;
      background-color: transparent;
      border-color: #409eff;
    }
  }
  .uimgg-box {
    width: 100%;
    height: 137px;
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    text-align: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    color: #333;
    flex-direction: column;
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
    .el-icon-plus {
      font-size: 23px;
    }
    .avatar {
      width: 100%;
    }
    span {
      margin-top: 8px;
      line-height: 22px;
    }
    .operation-area {
      display: flex;
      width: 100%;
      justify-content: space-around;
      background-color: rgba(0, 0, 0, 0.35);
      color: #fff;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      position: absolute;
      bottom: 0;
      .operation-area__item {
        line-height: 32px;
        flex: auto;
        text-align: center;
        font-size: 14px;
      }
    }
  }
  .bgPicker {
    height: 34px;
    width: 110px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 4px;
    cursor: pointer;
    .c__block {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 0;
    }
    .c__panel {
      left: -80px;
    }
  }
  .input-inline {
    width: 110px;
    height: 32px;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #333;
    font-size: 12px;
    position: relative;
    &:first-child {
      margin-right: 15px;
    }
    .left-l {
      // width: 52px;
      box-sizing: border-box;
      line-height: 32px;
      vertical-align: middle;
      background-color: hsla(0, 0%, 84.7%, 0.12);
      display: inline-block;
      // padding-left: 8px;
      padding: 0 10px;
      box-shadow: 1px 0 0 0 #d9d9d9;
      white-space: pre;
    }
    .fix-input__number {
      flex: 1;
      line-height: 32px;
      &.ant-input-number {
        width: 100%;
        border-right-width: 0;
        &:hover {
          border-color: transparent;
        }
        &.ant-input-number-focused {
          border-color: transparent;
        }
      }
      &.el-input-number.is-controls-right[class*='medium'] [class*='decrease'],
      &.el-input-number.is-controls-right[class*='medium'] [class*='increase'] {
        line-height: 16px;
      }
      &.ant-input-number .el-input-number__decrease,
      &.ant-input-number .el-input-number__increase {
        width: 18px;
      }
      &.el-input-number.is-controls-right .el-input__inner {
        padding-left: 4px;
        padding-right: 19px;
      }
      .el-input--medium .el-input__inner {
        height: 34px;
        line-height: 34px;
      }
      .el-input__inner {
        border-right-width: 0;
      }
      .el-input-number__increase,
      &.el-input-number.is-controls-right .el-input-number__decrease {
        right: 0;
      }
      .el-input__inner:focus {
        border-color: #d9d9d9;
      }
    }
    .fix-pay__input {
      .el-input__inner {
        border-right-width: 0;
      }
      .el-input--medium .el-input__inner {
        height: 34px;
        line-height: 34px;
      }
      .el-input__inner:focus {
        border-color: #d9d9d9;
      }
    }
  }
  .scale-group {
    .ant-checkbox {
      display: none;
    }
    &.ant-checkbox-group {
      column-gap: 0;
    }
    .ant-checkbox-wrapper {
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-radius: 0;
      height: 32px;
      line-height: normal;
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
        border-left-width: 0;
      }
      &.ant-checkbox-wrapper-checked {
        border-color: $theme-color;
        &:last-child {
          border-left-width: 0;
        }
      }
      // .ant-checkbox-wrapper {
      //   line-height: 1.5;
      //   &:first-child {
      //     border-right: 1px solid $theme-color;
      //   }
      // }
    }
    .el-checkbox-button.is-checked .el-checkbox-button__inner {
      background-color: transparent;
      color: #409eff;
    }
    .el-checkbox-button.is-checked.is-focus .el-checkbox-button__inner {
      border-color: #409eff;
    }
    .el-checkbox-button.is-focus .el-checkbox-button__inner {
      border-color: #dcdfe6;
    }
  }
  .elselect_box {
    width: 100%;
    .el-input__inner {
      line-height: 32px;
      height: 32px;
    }
  }

  .tagPosition-group {
    .el-radio-button--medium .el-radio-button__inner {
      padding: 8px 17px;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: transparent;
      color: #409eff;
    }
  }

  .finputval {
    width: 55px;
    margin: 0 2px 0 15px;

    &.ant-input-number:focus,
    &.ant-input-number-focused,
    &.ant-input-number:hover {
      border-color: $theme-color;
    }
    .el-input__inner {
      padding: 0 5px;
      text-align: center;
    }
  }
  .fslider {
    display: flex;
    align-items: center;
    .ant-slider-track {
      background-color: $theme-color;
    }
    .ant-slider-handle::after {
      box-shadow: 0 0 0 2px $theme-color;
    }
  }
  .operation .el-link {
    font-size: 12px;
  }

  .switch-content {
    margin-left: 10px;
    color: #333;
    font-size: 14px;
    &.disabled {
      color: #999;
    }
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
  .ant-radio-group .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child,
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    border-color: #409eff;
    color: #409eff;
  }

  // .ant-radio-button-wrapper
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
    background-color: #409eff;
  }
  .ant-radio-button:hover,
  .ant-radio-button-wrapper:hover,
  .ant-btn,
  .ant-btn:hover {
    color: #409eff;
  }
}

.input-theme.ant-input:focus,
.input-theme.ant-input-focused,
.input-theme.ant-input:hover,
.select-theme.ant-select:focus,
.select-theme.ant-select-focused,
.select-theme.ant-select:hover,
.select-theme .ant-select-selector {
  border-color: $theme-color !important;
}

.landing-preview-wrapper {
  .ant-modal-content {
    background-color: transparent;
    box-shadow: none;
    padding: 0;
    border-radius: 0;
  }
}

.landing-page-confirm {
  .ant-btn-default {
    &:hover {
      background-color: #fff;
      border-color: #d9d9d9;
      color: #333;
    }
  }
  .ant-btn-primary {
    background-color: $theme-color;
    border-color: $theme-color;
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

.read-argee {
  position: relative;
  height: $bottom-height;
  background: #fff1e3;
  box-shadow: 0px -3px 4px 0px rgba(201, 170, 137, 0.19);
  // border-radius: 8px 8px 0px 0px;
  display: flex;
  flex-wrap: wrap;
  min-width: 1680px;
  .read-content {
    height: 100%;
    font-size: 13px;
    color: #d46f00;
    line-height: 18px;
    padding: 16px 24px 0 24px;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;
    .ant-checkbox-wrapper {
      margin-right: 4px;
    }
    .ant-btn-link {
      padding: 0;
      font-size: 13px;
      color: #1677ff;
      line-height: 18px;
      height: 18px;
    }
  }
  .spin-blink {
    animation: shakeX 1.2s linear infinite;

    @keyframes shakeX {
      0%,
      to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
      }
      10%,
      30%,
      50%,
      70%,
      90% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
      }
      20%,
      40%,
      60%,
      80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
      }
    }
  }
}

@media (max-width: 1800px) {
  .app_landing {
    height: calc(100vh - 70px);
  }
  .read-argee {
    height: 70px;
  }
}

.h5_popup {
  // position: relative;
  background-color: #feea3d;
  // z-index: 10;
  border-radius: 5px;
  opacity: 0;
  width: 100%;
  height: 100%;
  animation-name: pop;
  animation-duration: 3.5s;
}

@keyframes pop {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.4;
  }
  80% {
    opacity: 0.4;
  }
  99% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
