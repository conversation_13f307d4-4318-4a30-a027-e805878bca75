<template>
  <van-popup
    v-model:show="isShow"
    position="bottom"
    :style="{ borderRadius: '.2rem .2rem 0 0' }"
    @click-overlay="() => $emit('cancel')"
  >
    <van-date-picker
      v-model="currentDate"
      title="选择年月日"
      type="date"
      @confirm="(data) => $emit('confirm', data)"
      @cancel="() => $emit('cancel')"
    />
  </van-popup>
</template>

<script>
  export default {
    props: {
      show: {
        type: Boolean,
        default: false
      },
      currData: {
        type: String,
        default: null
      }
    },
    data() {
      return {
        isShow: this.show,
        currentDate: this.currData ? new Date(this.currData) : null
      }
    },
    watch: {
      show(val) {
        this.isShow = val
      }
    }
  }
</script>
