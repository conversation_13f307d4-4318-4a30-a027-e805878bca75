<template>
  <div class="alert-wrap">
    <div class="alert-mask upf">
      <div class="alert-box upf">
        <div class="alert-icon error alert-icon-error" />
        <div v-if="subm.error" class="alert-icon error alert-icon-error" />
        <div v-if="subm.success" class="alert-icon success alert-icon-success" />
        <div class="alert-text-wrap">
          <p class="alert-text">当前为预览状态，"页面绑定项目"后才能进行提交。</p>
          <p v-if="subm.success" class="alert-text">{{ item.form_fields.tipValue }}</p>
          <p v-if="subm.error" class="alert-text">数据提交失败，请重试！</p>
        </div>
        <div class="alert-btn alert-btn-success" @click="$emit('close')">确定</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: () => ({})
      },
      subm: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {}
    }
  }
</script>

<style lang="scss" scoped>
  .alert-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    .alert-mask {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
    }
    .alert-box {
      flex-direction: column;
      width: 3rem;
      padding: 0.3rem 0.2rem;
      background: #fff;
    }
    .alert-text-wrap {
      margin: 0.19875rem 0;
    }
    .alert-text {
      color: #595959;
      font-size: 0.14rem;
      line-height: 1.5;
    }
    .alert-icon {
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      &.alert-icon-error {
        &::before {
          content: '';
          display: block;
          width: 0.35rem;
          height: 0.05rem;
          background: #fff;
          border-radius: 0.05rem;
          transform: translate(0.2rem, 0.37rem) rotate(45deg);
        }
        &::after {
          content: '';
          display: block;
          width: 0.35rem;
          height: 0.05rem;
          background: #fff;
          border-radius: 0.05rem;
          transform: translate(0.2rem, 0.32rem) rotate(135deg);
        }
      }
      &.alert-icon-success {
        &::before {
          content: '';
          display: block;
          width: 0.35rem;
          height: 0.05rem;
          background: #fff;
          border-radius: 0.05rem;
          transform: translate(0.06rem, 0.42rem) rotate(45deg);
        }
        &::after {
          content: '';
          display: block;
          width: 0.46rem;
          height: 0.05rem;
          background: #fff;
          border-radius: 0.05rem;
          transform: translate(0.28rem, 0.35rem) rotate(135deg);
        }
      }
      &.error {
        background: #fe4a70;
      }
      &.success {
        background-color: #67c23a;
      }
    }
    .alert-btn {
      width: 1.2rem;
      height: 0.42rem;
      line-height: 0.42rem;
      background: #0188fb;
      border-radius: 0.04rem;
      color: #fff;
      font-size: 0.16rem;
      text-align: center;
      cursor: pointer;
    }
  }
</style>
