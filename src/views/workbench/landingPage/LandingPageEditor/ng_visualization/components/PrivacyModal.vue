<template>
  <div class="privacy_box">
    <div class="default-modal" style="display: block">
      <div class="dialog-wrapper" />
      <!-- <transition name="drop"> -->
      <div class="dialog-container">
        <i class="el-icon-close default-close-btn" @click="$emit('close')" />
        <div class="head-title">个人信息授权与保护声明</div>
        <div class="scroll_rule">
          <p class="article">
            我们非常重视用户信息的保护。您在使用我们的服务时，我们可能会收集和使用您的相关信息。我们希望通过本声明向您说明在适用于具体的留资场景时我们如何收集、使用、共享、保护您的信息，以进一步保障您的信息安全和为您提供更具个性化的优质服务。在使用我们的留资服务（以下简称“服务”），并在相应页面填写相关信息前，请您务必仔细阅读本声明。您一旦主动在相应页面填写信息并进行提交操作，即意味着您同意我们按照本声明收集、使用、共享您的相关信息。
          </p>
          <p class="article font_bold">一、个人信息的收集</p>
          <p class="article">
            1、我们提供服务时，可能会收集您的姓名、手机号。请您注意，具体需要填写的信息可能会根据我们提供的产品/服务的不同而有所差异，请以届时向您展示的服务以及所对应的要求填写相关个人信息，但我们会在下文向您说明我们可能收集的全部个人信息的收集和使用规则。
          </p>
          <p class="article">
            2、您需要注意的是，以上个人信息均是您自愿提供。您有权拒绝提供，但可能无法正常使用相应的服务、功能或者无法达到服务拟达到的效果。
          </p>
          <p class="article">
            3、对于不满 18
            岁的用户，须在其监护人陪同未成年人用户已经阅读和理解本声明并且许可的情况下向我们提交相关个人信息。
          </p>

          <p class="article font_bold">二、个人信息的使用和共享</p>
          <p class="article">1、您同意，我们可以通过以下方式对个人信息进行使用和共享：</p>
          <p class="article">
            ①为了保障你能正常使用由我们的关联方或合作方提供的相关功能或服务，或为保障使用安全或进行必要的产品使用分析，我们可能会与上述各方共享所必需的信息；
          </p>
          <p class="article">
            ②我们接受您留资所对应的相关业务广告主的委托，向其共享您在表单中主动提供的信息，并由其在合法正当以及您已经向其授权的范围内使用（例如与您及时进行联络）。
          </p>
          <p class="article">
            此外，为保证您获得更好的服务体验，我们可能会与广告主共享难以识别您个人身份的必要的用户画像标签信息并由其在合法正当的范围内使用（例如促使相关联络内容、联络时间更符合您的要求）；
          </p>
          <p class="article">③我们和关联公司为满足您的需求，可能通过您提供的信息与您联系；</p>
          <p class="article">④我们和关联公司可能定期或不定期向您发送有关产品、服务或相关活动的信息；</p>
          <p class="article">
            ⑤经您授权同意后，您已知晓并同意在您下次浏览广告页面时，我们会帮您预先填写您上次输入的历史信息以提升您的使用体验，且只有您点击提交按钮之后才会传输给关联公司、广告主。若您拒绝使用预填充功能，或者拟撤回使用预填充功能的授权，您可以根据产品所提供的功能进行。但也请您注意，您一旦拒绝或撤回授权，您将无法使用预填充功能，但您仍然可以主动输入相关信息并继续使用我们的产品/服务。
          </p>
          <p class="article">
            ⑥基于我们与通信运营商的合作，当您点击“使用本机号码”时，经过您的明示同意，运营商会将您的手机号码发送给我们，便于我们为您提供快捷提交服务。您也可以自主选择手动填写手机号码的方式提交。
          </p>
          <p class="article">
            ⑦如您拟撤回授权/删除您提交的个人信息的，您可以通过客服与我们联系，我们将尽快配合处理，如相关广告主在您撤回授权或申请删除前已经获得了您的相关信息，我们会敦促其响应您的诉求并要求其删除您的相关信息。
          </p>
          <p class="article">
            2、我们将严格保护您的个人信息安全。我们将采用适当制度、安全技术和程序等措施来保护您的个人信息不被未经授权的访问、使用或泄漏。我们亦将促使本声明所述各接收和使用方尽力保护和不予泄露您的个人信息。
          </p>
          <p class="article">
            3、我们将要求并敦促个人信息接收方遵循本声明和法律法规的规定，谨慎使用其接收到的个人信息，如其发生超越本声明范围使用您的个人信息，进而侵害您合法权益的情形，我们将协助您追究该接收方的法律责任。
          </p>

          <p class="article font_bold">三、适用法律</p>
          <p class="article">1、本声明适用中华人民共和国法律。</p>
          <p class="article">2、如果您对信息保护问题有任何疑问或选择取消授权的，您可以通过客服联系我们。</p>

          <p class="article font_bold">四、变更</p>
          <p class="article">
            我们会适时对本声明进行修订，该等修订构成本声明的一部分。公司（即【河南起点智库科技发展有限公司】）会将修改后的声明予以发布。在该种情况下，若您继续使用我们的服务，即表示同意接受经修订的声明的约束。
          </p>
        </div>
      </div>
      <!-- </transition> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .privacy_box {
    .default-modal {
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 9999;
      display: none;
    }
    .dialog-wrapper {
      background-color: rgba(0, 0, 0, 0.7);
      opacity: 0.9;
    }

    .dialog-wrapper {
      position: fixed;
      height: 100%;
      width: 100%;
      z-index: 5;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      transition: 0.5s;
    }
    .dialog-container {
      position: fixed;
      z-index: 80;
      top: 17%;
      left: 7%;
      width: 86%;
      background-color: #ffffff;
      border-radius: 0.142rem;
      overflow: hidden;
      box-shadow: 0 0.142rem 0.142rem rgb(0 0 0 / 30%);
    }
    .default-close-btn {
      position: absolute;
      top: 0.114rem;
      right: 0.114rem;
      width: 0.224rem;
      height: 0.224rem;
      cursor: pointer;
      font-size: 0.251rem;
      font-weight: bold;
    }
    .head-title {
      margin: 0 auto;
      margin-top: 0.25rem;
      margin-bottom: 0.16rem;
      font-size: 0.21rem;
      text-align: center;
      font-weight: bold;
    }
    .scroll_rule {
      padding: 0 0.25rem;
      margin-bottom: 0.25rem;
      box-sizing: border-box;
      font-size: 0.16rem;
      height: 3.5rem;
      overflow-y: scroll;
    }
    .article {
      font-size: 0.16rem;
      text-align: justify;
      text-align-last: left;
    }
  }
</style>
