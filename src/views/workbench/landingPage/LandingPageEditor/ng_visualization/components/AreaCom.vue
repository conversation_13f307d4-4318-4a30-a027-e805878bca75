<template>
  <van-popup
    v-model:show="isShow"
    position="bottom"
    :style="{ borderRadius: '.2rem .2rem 0 0' }"
    @click-overlay="() => $emit('cancel')"
  >
    <van-area
      :area-list="areaList"
      :columns-num="cityType == 'cityOne' ? 2 : 3"
      @confirm="(val) => $emit('confirm', val)"
      @cancel="() => $emit('cancel')"
    />
  </van-popup>
</template>

<script>
  import { areaList } from '@vant/area-data'

  export default {
    props: {
      show: {
        type: Boolean,
        default: false
      },
      cityType: {
        type: String,
        default: null
      }
    },
    data() {
      return {
        isShow: this.show,
        areaList
      }
    },
    watch: {
      show(val) {
        this.isShow = val
      }
    }
  }
</script>
