<template>
  <div class="wangeditor-v5__content" :style="minHeight ? `min-height:${minHeight}` : ''">
    <div v-if="state.isEditorShow">
      <Toolbar
        style="border-bottom: 1px solid #ccc; padding: 2px"
        :editor="state.editor"
        :defaultConfig="state.toolbarConfig"
        :mode="state.mode"
      />
      <Editor
        v-model="state.html"
        style="overflow-y: hidden; padding: 5px 0; background-color: #f7f7f7"
        :style="{ height: props.contentHeight, backgroundColor, borderRadius }"
        :class="styles.fontFamily"
        :defaultConfig="state.editorConfig"
        :mode="state.mode"
        @onChange="onChange"
        @onCreated="onCreated"
      />
    </div>
  </div>
</template>

<script setup>
  import '@wangeditor/editor/dist/css/style.css' // 引入 css

  import { onBeforeUnmount, ref, shallowRef, onMounted, watch, computed, reactive } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const props = defineProps({
    textContent: {
      type: String,
      default: ''
    },
    minHeight: {
      type: [String, Number],
      default: ''
    },
    contentHeight: {
      type: [String, Number],
      default: '230px'
    },
    backgroundColor: {
      type: String,
      default: '#f7f7f7'
    },
    borderRadius: {
      type: [String, Number],
      default: '0'
    }
  })

  const emits = defineEmits(['wang'])

  const state = reactive({
    isEditorShow: false,
    editor: null,
    html: props.textContent,
    toolbarConfig: {},
    editorConfig: { placeholder: '请输入内容...' },
    mode: 'simple' // default or 'simple'
  })

  const styles = computed(() => {
    return store.getActiveItems.styles || {}
  })

  watch(
    () => props.textContent,
    (newVal) => {
      state.html = newVal
    }
  )

  onMounted(() => {
    seteditor()
    // 模拟 ajax 请求，异步渲染编辑器
    setTimeout(() => (state.isEditorShow = true), 0)
  })

  onBeforeUnmount(() => {
    const editor = state.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  })

  function seteditor() {
    state.toolbarConfig = {
      toolbarKeys: [
        'bold', // 粗体
        'italic', // 斜体
        'underline', // 下划线
        // 'fontFamily', // 字体类型
        'fontSize', // 字号
        'lineHeight', // 行高
        'color', // 文字颜色
        'bgColor', // 背景颜色
        {
          key: 'group-justify-style', // 必填，要以 group 开头
          title: '对齐', // 必填
          iconSvg:
            '<svg viewBox="0 0 1024 1024"><path d="M870.4 793.6v102.4H153.6v-102.4h716.8z m102.4-230.4v102.4H51.2v-102.4h921.6z m-102.4-230.4v102.4H153.6v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
          menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter'] // 下级菜单 key ，必填
        }
      ]
    }
  }
  function onChange(ev) {
    // let html = ev.getHtml()
    // let newArr = html.split("font-size: ")
    // if (newArr.length) {
    //   for (let i = 0; i < newArr.length; i++) {
    //     if (i != 0) {
    //       let data = newArr[i].substring(0, newArr[i].indexOf("px"))
    //       html = html.replaceAll("font-size: " + data + "px;", "font-size: "+(data / 100)+"rem;")
    //     }
    //   }
    // }
    state.info_ = ev.getHtml()
    emits('wang', state.info_)
  }
  function onCreated(editor) {
    state.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
  }
</script>

<style lang="scss">
  .wangeditor-v5__content {
    border: 1px solid #ccc;
    border-radius: 4px;
    min-height: 300px;
    .w-e-text-container {
      background-color: #f7f7f7;
    }
    .w-e-bar-item {
      height: 32px;
    }
    .w-e-bar-item-group {
      .w-e-bar-item .title {
        font-size: 14px;
      }
      .w-e-bar-item-menus-container {
        left: 50%;
        transform: translateX(-50%);
        margin-top: 32px;
      }
    }
    .w-e-bar-item button {
      padding: 0 8px;
    }
    .w-e-text-container p {
      margin: 0;
      padding: 0;
    }
    .w-e-text-placeholder {
      top: 0;
    }
  }
</style>
