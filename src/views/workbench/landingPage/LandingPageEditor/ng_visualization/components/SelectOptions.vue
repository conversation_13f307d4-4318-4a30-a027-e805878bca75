<template>
  <van-popup
    v-model:show="isShow"
    position="bottom"
    :style="{ borderRadius: '.2rem .2rem 0 0' }"
    @click-overlay="() => $emit('cancel')"
  >
    <van-picker
      title="标题"
      :columns="columns"
      :show-toolbar="true"
      @cancel="() => $emit('cancel')"
      @confirm="(data) => $emit('confirm', data)"
    />
  </van-popup>
</template>

<script>
  export default {
    props: {
      show: {
        type: Boolean,
        default: false
      },
      columns: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        isShow: this.show
      }
    },
    watch: {
      show(val) {
        this.isShow = val
      }
    }
  }
</script>
