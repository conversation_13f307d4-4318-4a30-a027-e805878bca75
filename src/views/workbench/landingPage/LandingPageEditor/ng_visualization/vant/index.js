import { Button } from 'vant'
import { Loading } from 'vant'
import { Swipe } from 'vant'
import { SwipeItem } from 'vant'
import { Lazyload } from 'vant'
import { Popup } from 'vant'
import { Dialog } from 'vant'
import { Toast } from 'vant'
import { Image } from 'vant'
import { Divider } from 'vant'
import { Overlay } from 'vant'
import { Form } from 'vant'
import { Field } from 'vant'
import { CellGroup } from 'vant'
import { Area } from 'vant'
import { Radio } from 'vant'
import { Icon } from 'vant'
// import { BackTop } from 'vant'
import { DatePicker } from 'vant'
import { Picker } from 'vant'
import { RadioGroup } from 'vant'
import { Checkbox } from 'vant'
import { CheckboxGroup } from 'vant'
import { Notify } from 'vant'
import { Popover } from 'vant'
import { Cell } from 'vant'

const components = {
  Button,
  Loading,
  Swipe,
  SwipeItem,
  Lazyload,
  Popup,
  Dialog,
  Toast,
  Image,
  Divider,
  Overlay,
  Form,
  Field,
  CellGroup,
  Area,
  Radio,
  Icon,
  // BackTop,
  DatePicker,
  Picker,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  Notify,
  Popover,
  Cell
}

export function setupVant(app) {
  for (const i in components) {
    app.use(components[i])
  }
}

// 导出所有组件，方便按需引入
export {
  Button,
  Loading,
  Swipe,
  SwipeItem,
  Lazyload,
  Popup,
  Dialog,
  Toast,
  Image,
  Divider,
  Overlay,
  Form,
  Field,
  CellGroup,
  Area,
  Radio,
  Icon,
  DatePicker,
  Picker,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  Notify,
  Popover,
  Cell
}
