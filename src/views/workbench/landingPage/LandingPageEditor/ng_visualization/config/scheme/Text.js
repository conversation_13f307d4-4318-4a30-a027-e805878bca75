import { getConfig } from '@/utils'

export default [
  // {
  //   name: 'NgText', // 组件名
  //   desc: '文本',
  //   icon: '',
  //   currStyleClass: '', // 当前选择的组件风格
  //   animate: '', // 图层的动画,可以支持多个动画
  //   events: [], // 事件配置数据，每个图层可以添加多个事件
  //   useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
  //   menuName: '简单文本',
  //   menuDesc: '',
  //   backBtn: false, // 返回按钮
  //   isBg: false, // 是否是选中背景
  //   isWire: true, // 是否是选中wire
  //   enabled: true, // 禁用拖拽 true 不禁用 false 禁用
  //   rotatable: true, // 可旋转
  //   rotation: 0, // 旋转角度
  //   scaleX: 1, // 缩放转换X
  //   scaleY: 1, // 缩放转换Y
  //   textContent: '请添加文本内容', // 文本内容
  //   link: { // 图片链接
  //     type: '', // page 网页 applet 小程序
  //     url: ''
  //   },
  //   styles: { // 组件样式
  //     top: '0',
  //     bottom: '0',
  //     left: '0',
  //     color: '#333333',
  //     fontFamily: 'font-0',
  //     fontSize: '.22rem',
  //     textAlign: 'left',
  //     fontStyle: '',
  //     fontWeight: '',
  //     textDecoration: '',
  //     letterSpacing: '0',
  //     lineHeight: 1.5,
  //     height: '.35rem',
  //     width: '3.65rem',
  //     x: '.05rem', // 位置 横轴
  //     y: '.05rem' // 位置 纵轴
  //   },
  //   boxBg: { // 背景颜色
  //     backgroundImage: '',
  //     backgroundColor: '',
  //     backgroundRepeat: 'repeat',
  //     backgroundSize: '100%',
  //     bgMode: 1, // 1 背景颜色   2 背景图
  //     width: '375px',
  //     height: '90px'
  //   },
  //   eboxValue: {
  //     top: 0,
  //     bottom: 0,
  //     left: 0,
  //     fontSize: 22,
  //     letterSpacing: 0,
  //     bgWidth: 375,
  //     bgHeight: 90,
  //     height: 35,
  //     width: 365,
  //     x: 5, // 位置 横轴
  //     y: 5 // 位置 纵轴
  //   },
  //   styleTypeMap: [ // 组件风格配置
  //     {
  //       key: 'style-one',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/textSingleOne.jpeg'
  //     }
  //   ]
  // },
  {
    name: 'NgRichText', // 组件名
    desc: '富文本',
    icon: '',
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '富文本',
    menuDesc: '双击添加模块和图片',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    textContent: '<p>请添加文本内容</p>', // 文本内容
    link: { // 图片链接
      type: '', // page 网页 applet 小程序
      url: ''
    },
    styles: { // 组件样式
      top: '0',
      bottom: '0',
      left: '0',
      fontFamily: 'font-0',
      color: '#333333',
      fontSize: '.2rem',
      height: '.35rem',
      width: '3.65rem',
      x: '.05rem', // 位置 横轴
      y: '.05rem' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      width: '375px',
      height: '60px'
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      fontSize: 20,
      bgWidth: 375,
      bgHeight: 60,
      height: 35,
      width: 365,
      x: 5, // 位置 横轴
      y: 5 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'font-0',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richTextOne.png'
      },
      {
        key: 'font-4',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText02.png'
      },
      {
        key: 'font-7',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText03.png'
      },
      {
        key: 'font-10',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText04.png'
      },
      {
        key: 'font-13',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText05.png'
      },
      {
        key: 'font-14',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText06.png'
      },
      {
        key: 'font-15',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText07.png'
      },
      {
        key: 'font-16',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText08.png'
      },
      {
        key: 'font-17',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText09.png'
      },
      {
        key: 'font-23',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText10.png'
      },
      {
        key: 'font-24',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText11.png'
      },
      {
        key: 'font-94',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText12.png'
      },
      {
        key: 'font-95',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText13.png'
      },
      {
        key: 'font-96',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText14.png'
      },
      {
        key: 'font-97',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText15.png'
      },
      {
        key: 'font-100',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText16.png'
      },
      {
        key: 'font-101',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText17.png'
      },
      {
        key: 'font-102',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText18.png'
      },
      {
        key: 'font-103',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText19.png'
      },
      {
        key: 'font-104',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText20.png'
      },
      {
        key: 'font-105',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText21.png'
      },
      {
        key: 'font-106',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText22.png'
      },
      {
        key: 'font-107',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText23.png'
      },
      {
        key: 'font-90',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/richText24.png'
      }
    ]
  },
  // {
  //   name: 'NgHeadline', // 组件名
  //   desc: '标题',
  //   icon: '',
  //   currStyleClass: '', // 当前选择的组件风格
  //   animate: '', // 图层的动画,可以支持多个动画
  //   events: [], // 事件配置数据，每个图层可以添加多个事件
  //   useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
  //   menuName: '标题',
  //   menuDesc: '',
  //   backBtn: false, // 返回按钮
  //   isBg: false, // 是否是选中背景
  //   isWire: true, // 是否是选中wire
  //   enabled: true, // 禁用拖拽 true 不禁用 false 禁用
  //   rotatable: false, // 可旋转
  //   rotation: 0, // 旋转角度
  //   scaleX: 1, // 缩放转换X
  //   scaleY: 1, // 缩放转换Y
  //   textContent: '标题文字标题字', // 文本内容
  //   link: { // 图片链接
  //     type: '', // page 网页 applet 小程序
  //     url: ''
  //   },
  //   styles: { // 组件样式
  //     position: 'static',
  //     top: '0',
  //     bottom: '0',
  //     left: '0',
  //     color: '#595959',
  //     fontFamily: '',
  //     fontSize: '.22rem',
  //     textAlign: 'left',
  //     fontStyle: '',
  //     fontWeight: '',
  //     textDecoration: '',
  //     letterSpacing: '',
  //     lineHeight: 1.5,
  //     height: '.44rem',
  //     width: '3.75rem',
  //     x: '0', // 位置 横轴
  //     y: '.06rem' // 位置 纵轴
  //   },
  //   boxBg: { // 背景颜色
  //     backgroundImage: '',
  //     backgroundColor: '',
  //     backgroundRepeat: 'repeat',
  //     backgroundSize: '100%',
  //     bgMode: 1, // 1 背景颜色   2 背景图
  //     width: '375px',
  //     height: '60px'
  //   },
  //   eboxValue: {
  //     top: 0,
  //     bottom: 0,
  //     left: 0,
  //     fontSize: 22,
  //     bgWidth: 375,
  //     bgHeight: 60,
  //     height: 44,
  //     width: 375,
  //     x: 0, // 位置 横轴
  //     y: 6 // 位置 纵轴
  //   },
  //   styleTypeMap: [ // 组件风格配置
  //     {
  //       key: 'style-one',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/title1.png'
  //     },
  //     {
  //       key: 'style-two',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/title2.png'
  //     },
  //     {
  //       key: 'style-three',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/title3.png'
  //     },
  //     {
  //       key: 'style-four',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/title4.png'
  //     },
  //     {
  //       key: 'style-five',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/title5.png'
  //     }
  //   ]
  // }
]
