import { getConfig } from '@/utils'

export default [
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', // 组件名
    desc: '电商组件',
    icon: '',
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: true, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '组件样式',
    menuDesc: '',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    textContent: '按钮', // 文本内容
    leftText: '加入购物车', // 按钮左侧文字
    rightText: '立即购买', // 按钮右侧文字
    url:'',
    actionType:1, // 交互类型 1 无交互 2 跳转目标链接 3 跳转外部链接
    clickType: 1, // 1 跳转网页 2 唤起弹窗
    position: 'none', // 组件设置——悬浮 none 无 top 顶部悬浮 bottom 底部悬浮
    link: { // 图片链接
      type: '', // page 网页 applet 小程序
      url: '',
      payMoney: '',
      payBtnText: '',
      poster_id: '',
      poster_name: '',
      qa_id: '',
    },
    styles: { // 组件样式
      position: 'fixed',
      top: '0',
      bottom: '0',
      left: '0',
      fontSize: '.16rem',
      btnColor: '#fff',
      btnBackgroundColor: '#4e90ff',
      borderColor: '#4e90ff',
      borderStyle: 'none',
      borderWidth: '.01rem',
      borderRadius: '.21rem',
      height: '.7rem',
      width: '3.75rem',
      x: '.955rem', // 位置 横轴
      y: '.09rem' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      background: '',
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      width: '375px',
      height: '70px'
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      fontSize: 16,
      opacity: 100, // 背景颜色透明度
      backgroundSize: 100, // 背景图片尺寸
      borderWidth: 1,
      borderRadius: 21,
      bgWidth: 375,
      bgHeight: 70,
      height: 70,
      width: 375,
      x: 95.5, // 位置 横轴
      y: 9 // 位置 纵轴
    },
    popup: { // 弹框内容
      qrcode: false, // 是否显示弹框二维码
      srcs: {
        height: 676,
        icId: '',
        size: 121856,
        imgurl: getConfig('NODE_OSS_URL') + '/assets/62_hd.jpg',
        width: 639
      },
      width: 280,
      height: 420,
      x: 26,
      y: 137
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'style-one',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-style-one.png',
      },
      {
        key: 'style-two',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-style-two.png',
      },
        {
        key: 'style-three',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-style-three.png',
      },
      {
        key: 'style-four',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/buynow-style-four.png',
      },
      
    ]
  }
]
