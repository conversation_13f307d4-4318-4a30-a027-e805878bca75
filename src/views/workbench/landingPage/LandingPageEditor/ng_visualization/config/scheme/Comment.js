import { getConfig } from '@/utils'

export default [
  {
    name: 'NgComment', // 组件名 评论
    desc: '评论',
    icon: '',
    currSetIndex: null, // 当前设置索引
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '基本样式',
    menuDesc: '',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    readNum: 452, // 阅读数
    seeNum: 452, // 观看数
    comment_code: null, // 选择评论code
    comment_id: null, // 选择评论ID
    commentList: [], // 评论列表
    styles: { // 组件样式
      position: 'static',
      top: '0',
      bottom: '0',
      left: '0',
      userNameColor: '#595959', // 用户名样式
      contentColor: '#595959', // 评论样式
      backgroundColor: '#fff',
      backgroundImage: '',
      borderBottom: '',
      borderBottomWidth: '',
      borderColor: '',
      paddingTop: '', // 上边距
      paddingBottom: '', // 下边距
      height: '3.37rem',
      width: '3.75rem',
      x: '0', // 位置 横轴
      y: '0' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      background: '',
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      opacity: 1,
      width: '375px',
      height: '337px'
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      btnRadius: 4, // 边框圆角
      paddingTop: 0,
      paddingBottom: 0,
      bgWidth: 375,
      bgHeight: 337,
      height: 337,
      width: 375,
      x: 0, // 位置 横轴
      y: 0 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'style-one',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/commit_styleone.png',
      }
    ]
  }
]
