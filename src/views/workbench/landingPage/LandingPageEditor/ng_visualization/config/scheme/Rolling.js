import { getConfig } from '@/utils'

export default [
  {
    name: 'Ng<PERSON><PERSON>ing', // 组件名
    desc: '跑马灯',
    icon: '',
    currSetIndex: null, // 当前设置索引
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    menuName: '基本样式',
    menuDesc: '',
    limitSize: 1, // 页面最多展示的组件
    useToolbar: true, // 是否使用工具栏  true 不使用
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    rollingList: [], // 滚动消息列表
    rollingRuleList: [{ id: 1, tag: '领取成功', content: '用户{客户名称}刚刚/1分钟前' }],
    position: 'absolute', // absolute跟随页面滑动 fixed固定顶部
    styles: {
      position: 'fixed',
      top: '.45rem',
      bottom: '0',
      left: '.15rem',
      height: '1.04rem',
      width: '2.44rem',
      x: '.15rem', // 位置 横轴
      y: '.45rem' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      width: '375px',
      height: '104px'
    },
    eboxValue: {
      top: 45,
      bottom: 0,
      left: 15,
      bgWidth: 375,
      bgHeight: 104,
      height: 104,
      width: 244,
      x: 15, // 位置 横轴
      y: 45 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'style-one',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/rolling_styleone.png',
      }
    ]
  }
]
