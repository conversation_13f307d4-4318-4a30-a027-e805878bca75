import { getConfig } from '@/utils'
export default [
  {
    name: 'NgImage', // 组件名  单图
    desc: '图片',
    icon: '',
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '单图',
    menuDesc: '双击添加模块和图片',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: true, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    link: { // 图片链接
      type: '', // page 网页 applet 小程序
      url: ''
    },
    srcs: {
      height: 450,
      icId: '',
      size: 125351,
      imgurl: getConfig('NODE_OSS_URL') + '/assets/h5/2ed9ede7ab0bb.png',
      width: 750
    },
    styles: { // 组件样式
      position: 'static',
      top: '0',
      bottom: '0',
      left: '0',
      color: '#576b95',
      fontSize: '15px',
      borderColor: '#000',
      borderStyle: 'none',
      borderWidth: '1px',
      borderRadius: '0',
      height: '2.25rem',
      width: '3.75rem',
      x: '0', // 位置 横轴
      y: '0' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      background: '',
      backgroundImage: '',
      backgroundColor: '#fff',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      opacity: 1,
      width: '375px',
      height: '260px'
    },
    // dragInfo: { w: 100, h: 100, x: 0, y: 0, rotateZ: 0 }, // 组件的位置、大小、旋转角度
    eboxValue: {
      opacity: 100, // 背景颜色透明度
      backgroundSize: 100, // 背景图片尺寸
      borderRadius: 0,
      fontSize: 15,
      bgWidth: 375,
      bgHeight: 260,
      height: 225,
      width: 375,
      x: 0, // 位置 横轴
      y: 0 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'style-one',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/imgSingleOne.png'
      }
    ]
  },
  // {
  //   name: 'NgCarousel', // 组件名
  //   desc: '轮播图',
  //   icon: '',
  //   currSetIndex: null, // 当前设置轮播索引
  //   currStyleClass: '', // 当前选择的组件风格 轮播类型
  //   animate: '', // 图层的动画,可以支持多个动画
  //   events: [], // 事件配置数据，每个图层可以添加多个事件
  //   useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
  //   first: false,
  //   menuName: '组图',
  //   menuDesc: '',
  //   // groupType: "",  // 轮播类型
  //   tagPosition: 'top', // 标签位置 top 顶部  bottom 底部   outside 外部
  //   backBtn: false, // 返回按钮
  //   isBg: false, // 是否是选中背景
  //   isWire: true, // 是否是选中wire
  //   enabled: true, // 禁用拖拽 true 不禁用 false 禁用
  //   rotatable: false, // 可旋转
  //   rotation: 0, // 旋转角度
  //   scaleX: 1, // 缩放转换X
  //   scaleY: 1, // 缩放转换Y
  //   configMap: { // 组件配置信息
  //     key: 'srcCarouselType',
  //     linksType: 'none', // 链接到
  //     maxlength: 20, // 轮播图最多上传数量
  //     value: [],
  //     valueMap: [],
  //     size: 2, // 2M
  //     accept: '.jpg,.jpeg,.png,.gif'
  //   },
  //   carouseList: [],
  //   autoplay: true, // 自动播放
  //   waitTime: 3, // 轮播间隔
  //   fixedScale: '9:16', // 图片比例
  //   styles: { // 组件样式
  //     position: 'static',
  //     top: '0',
  //     bottom: '0',
  //     left: '0',
  //     color: '#576b95',
  //     fontSize: '15px',
  //     imgsradius: '0px',
  //     height: '259px',
  //     paddingBottom: '0px',
  //     paddingTop: '0px',
  //     borderRadius: '0',
  //     objectFit: 'cover',
  //     height: '2.25rem',
  //     width: '3.75rem',
  //     x: '0', // 位置 横轴
  //     y: '0' // 位置 纵轴
  //   },
  //   boxBg: { // 背景颜色
  //     background: '',
  //     backgroundImage: '',
  //     backgroundColor: '',
  //     backgroundRepeat: 'repeat',
  //     backgroundSize: '100%',
  //     bgMode: 1, // 1 背景颜色   2 背景图
  //     opacity: 1,
  //     width: '375px',
  //     height: '225px'
  //   },
  //   // dragInfo: { w: 100, h: 100, x: 0, y: 0, rotateZ: 0 }, // 组件的位置、大小、旋转角度
  //   eboxValue: {
  //     top: 0,
  //     bottom: 0,
  //     left: 0,
  //     opacity: 100, // 背景颜色透明度
  //     backgroundSize: 100, // 背景图片尺寸
  //     imgsradius: 0,
  //     fontSize: 15,
  //     paddingBottom: 0,
  //     paddingTop: 0,
  //     typeKey: 1,
  //     bgWidth: 375,
  //     bgHeight: 225,
  //     height: 225,
  //     width: 375,
  //     x: 0, // 位置 横轴
  //     y: 0 // 位置 纵轴
  //   },
  //   styleTypeMap: [ // 组件风格配置
  //     {
  //       key: 'normal',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/imgGroupOne.png'
  //     },
  //     {
  //       key: 'carousel',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/imgGroupTwo.png'
  //     },
  //     {
  //       key: 'coverflow',
  //       value: getConfig('NODE_OSS_URL') + '/assets/h5/imgGroupThree.png'
  //     }
  //   ]
  // }
]
