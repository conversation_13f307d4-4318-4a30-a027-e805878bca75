import { getConfig } from '@/utils'
export default [
  {
    name: 'Ng<PERSON><PERSON>', // 组件名 表单
    desc: '表单',
    icon: '',
    currStyleClass: '', // 当前选择的组件风格
    currInputStyleClass: 'bottom', // 选择的文本框样式
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '基本样式',
    menuDesc: '双击添加模块和图片',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    autoplay: false, // 表单提交后跳转
    clickType: 1, // 1 跳转网页 2 唤起弹窗
    form_fields: {
      id: null,
      btnText: '立即提交',
      tipValue: '提交成功，感谢您的参与！',
      allow: false,
      elelist: []
    },
    sms_fields: {
      id: null, // 选择短信ID
      isOpen: false // 是否开启手机号短信验证
    },
    link: { // 图片链接
      type: '', // page 网页 applet 小程序
      url: ''
    },
    styles: { // 组件样式
      position: 'static',
      top: '0',
      bottom: '0',
      left: '0',
      fontSize: '.18rem',
      backgroundColor: '',
      backgroundImage: '',
      borderBottom: '',
      borderBottomWidth: '',
      height: '2.95rem',
      width: '3.75rem',
      x: '0', // 位置 横轴
      y: '0' // 位置 纵轴
    },
    boxBg: {
      background: '',
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 使用颜色 2 使用图片
      opacity: 1,
      width: '375px',
      height: '300px'
    },
    popup: { // 弹框内容
      qrcode: false, // 是否显示弹框二维码
      srcs: {
        height: 676,
        icId: '',
        size: 121856,
        imgurl: getConfig('NODE_OSS_URL') + '/assets/62_hd.jpg',
        width: 639
      },
      width: 280,
      height: 420,
      x: 26,
      y: 137
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      inputBorderList: [
        { name: '四周框线', key: 'range', value: getConfig('NODE_OSS_URL') + '/assets/h5/b2.png_noop.image', check: false },
        { name: '底部框线', key: 'bottom', value: getConfig('NODE_OSS_URL') + '/assets/h5/b1.png_noop.image', check: true }
      ],
      fontSize: 18,
      btnRadius: 4, // 边框圆角
      btnWidth: 0,
      bgWidth: 375,
      bgHeight: 300,
      height: 260,
      width: 375,
      x: 0, // 位置 横轴
      y: 0 // 位置 纵轴
    },
    txtColors: {
      desc: '#888888',
      option: '#595959',
      title: '#595959'
    },
    btnStyle: {
      backgroundColor: '#0188FB',
      radius: '0.04rem',
      color: '#FFFFFF',
      borderColor: '#FFFFFF',
      btnWidth: '0',
      borderStyle: 'none'
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 'style-one',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/form3.webp',
        textarea: 'bottom'
      },
      {
        key: 'style-two',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/form5.webp',
        textarea: 'range'
      },
      {
        key: 'style-three',
        value: getConfig('NODE_OSS_URL') + '/assets/h5/form2.webp',
        textarea: 'bottom'
      }
    ]
  }
]
