import { getConfig } from '@/utils'

export default [
  {
    name: 'NgFirstScreenVerify', // 组件名
    desc: '首屏验证组件',
    icon: '',
    currStyleClass: '', // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: true, // 是否使用工具栏  true 不使用 false 不使用
    allForbidToolbar: true, // 禁止任何工具栏
    menuName: '',
    menuDesc: '',
    limitSize: 99, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    rotatable: false, // 可旋转
    rotation: 0, // 旋转角度
    scaleX: 1, // 缩放转换X
    scaleY: 1, // 缩放转换Y
    verTips: '', // 文本内容
    verCodeFlag: false, // 验证码开关
    position: 'none', // 组件设置——悬浮 none 无 top 顶部悬浮 bottom 底部悬浮
    styles: { // 组件样式
      position: 'fixed',
      top: '0',
      bottom: '0',
      left: '0',
      fontSize: '.16rem',
      btnColor: '#fff',
      btnBackgroundColor: '#4e90ff',
      borderColor: '#4e90ff',
      borderStyle: 'none',
      borderWidth: '.01rem',
      borderRadius: '.21rem',
      height: '.7rem',
      width: '3.75rem',
      x: '.955rem', // 位置 横轴
      y: '.09rem' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      background: '',
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      width: '375px',
      height: '70px'
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      fontSize: 16,
      opacity: 100, // 背景颜色透明度
      backgroundSize: 100, // 背景图片尺寸
      borderWidth: 1,
      borderRadius: 21,
      bgWidth: 375,
      bgHeight: 70,
      height: 70,
      width: 375,
      x: 95.5, // 位置 横轴
      y: 9 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      
    ]
  }
]
