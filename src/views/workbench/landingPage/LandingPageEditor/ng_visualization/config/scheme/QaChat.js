import { getConfig, requireImg } from '@/utils'

export default [
  {
    name: 'NgQaChat', // 组件名  问答聊天
    desc: '问答聊天',
    icon: '',
    currStyleClass: null, // 当前选择的组件风格
    animate: '', // 图层的动画,可以支持多个动画
    events: [], // 事件配置数据，每个图层可以添加多个事件
    useToolbar: false, // 是否使用工具栏  true 不使用 false 不使用
    menuName: '',
    menuDesc: '',
    limitSize: 2, // 页面最多展示的组件
    backBtn: false, // 返回按钮
    isBg: false, // 是否是选中背景
    isWire: true, // 是否是选中wire
    enabled: true, // 禁用拖拽 true 不禁用 false 禁用
    page_name: '',
    page_title: '',
    style: '',
    isRepetAnswer: null, // 是否重复回答 1 是 2 否
    top: 10, // 展示位置
    ruleTitle: '', // 外显标题
    ruleContent: '', // 规则内容
    isRule: false, // 是否规则及提示
    isAutoReply: true, // 是否自动回复
    carousel_type: null, // 展现形式 1 轮播图形式 2 列表形式
    verCodeFlag: false, // 是否验证码
    verTips: '亲，如果您是为了做任务，到这一步说明您已经完成任务，请不要再点了，把机会留给真正需要的人，谢谢！！！如果您真的需要，请输入验证码，联系客服，添加客服注册免费领取奖品，名额有限，仅限前200！', // 验证码提示语
    welcomeList: [], // 欢迎语列表
    qaList: [], // 问答列表
    qaDataList: [], // 问答列表
    carouselList: [], // 轮播图列表
    allFontSize: [], // 全局字号
    fontSize: 14,
    custome_avatar: getConfig('NODE_OSS_URL') + '/assets/h5/qa_avatar_002.png', // 客服头像
    user_avatar: getConfig('NODE_OSS_URL') + '/assets/h5/qa_avatar_001.png', // 用户头像
    welcome_total: 3, // 欢迎语最大条数
    qa_total: 15, // 问答最大条数
    reply_total: 15, // 回复最大条数
    dialogue_total: 15, // 发送文本最大条数
    fontColor: '#296BEF',
    customer_btn: {
      type: 1, // 1悬浮组件 2动态悬浮组件
      style: 1, // 按钮风格1、2
      avatar: '', // 账号头像
      title: '', // 账号名称
      desc: '', // 账号描述
      text: '添加客服', // 按钮文案
      textColor: '#fff', // 按钮颜色
      bgColor: '#296BEF', // 按钮背景颜色
      fontSize: 14, // 按钮字体大小
      radius: 10 // 圆角
    },
    styles: { // 组件样式
      top: '0',
      bottom: '0',
      left: '0',
      color: '#333333',
      fontFamily: 'font-0',
      fontSize: '.22rem',
      textAlign: 'left',
      fontStyle: '',
      fontWeight: '',
      textDecoration: '',
      letterSpacing: '0',
      lineHeight: 1.5,
      height: '5.87rem',
      width: '3.75rem',
      x: '0', // 位置 横轴
      y: '0' // 位置 纵轴
    },
    boxBg: { // 背景颜色
      backgroundImage: '',
      backgroundColor: '',
      backgroundRepeat: 'repeat',
      backgroundSize: '100%',
      bgMode: 1, // 1 背景颜色   2 背景图
      width: '375px',
      height: '587px'
    },
    eboxValue: {
      top: 0,
      bottom: 0,
      left: 0,
      fontSize: 22,
      letterSpacing: 0,
      bgWidth: 375,
      bgHeight: 587,
      height: 587,
      width: 375,
      x: 0, // 位置 横轴
      y: 0 // 位置 纵轴
    },
    styleTypeMap: [ // 组件风格配置
      {
        key: 1,
        value: requireImg('h5/qa_01.png'),
        menuName: '按钮样式',
        menuDesc: '双击添加按钮样式',
      },
      {
        key: 2,
        value: requireImg('h5/qa_02.png'),
        menuName: '列表样式',
        menuDesc: '双击添加列表样式',
      }
    ]
  }
]
