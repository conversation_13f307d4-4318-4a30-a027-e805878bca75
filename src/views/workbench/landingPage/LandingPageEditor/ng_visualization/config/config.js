/**
 * @desc 组件配置文件输出出口
 */

import Image from './scheme/Image.js'
import Text from './scheme/Text.js'
import Button from './scheme/Button.js'
import Form from './scheme/Form.js'
import QaChat from './scheme/QaChat.js'
import BuyNow from './scheme/BuyNow.js'
import FirstScreenVerify from './scheme/FirstScreenVerify.js'
import Comment from './scheme/Comment.js'
import Rolling from './scheme/Rolling.js'

export default {
  Image,
  Text,
  Button,
  Form,
  QaChat,
  BuyNow,
  FirstScreenVerify,
  Comment,
  Rolling
}
