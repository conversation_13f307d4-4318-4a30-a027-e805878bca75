import { message } from 'ant-design-vue'
import { getConfig } from '@/utils'
/**
 * @description 深拷贝
 * @param {*} target
 * @returns
 */
const deepClone = (target) => {
  // if (target == null || target == "") return target;
  const type = typeof target
  let result
  if (type === 'object') {
    if (Array.isArray(target)) {
      result = []
      for (const i of target) {
        result.push(deepClone(i))
      }
    } else {
      result = target == null || target == '' ? target : {}
      for (const k in target) {
        result[k] = deepClone(target[k])
      }
    }
  } else {
    result = target
  }
  return result
}
const throttle = (delay) => {
  let run = false
  return function(callback = () => {}) {
    if (run) return false
    run = true
    callback()
    setTimeout(() => {
      run = false
    }, delay)
  }
}
/**
 * @description 复制文本
 * @param {*} text
 */
const copyText = (text) => {
  const hideInput = document.createElement('input')
  hideInput.value = text
  document.body.appendChild(hideInput)
  hideInput.select()
  document.execCommand('Copy')
  hideInput.style.display = 'none'
  hideInput.remove()
  message.success('复制成功')
}
/**
 * @description 获取链接参数
 * @param {*} item
 * @param {*} url
 * @returns
 */
const getUrlParams = (item, url) => {
  function decodeLocationSearch(url = '') {
    if (!location.search && !url) return {}
    if (url && !url.includes('?')) return {}
    const query = {}
    let _url = ''
    if (url) {
      _url = `?${url.split('?')[1]}`
    } else {
      _url = location.search
    }
    _url.slice(1).split('&').forEach(function(kv) {
      query[kv.split('=')[0]] = decodeURIComponent(kv.split('=')[1])
    })
    return query
  }
  if (item) {
    var svalue = (url || location.search).match(new RegExp('[\?\&]' + item + '=([^\&]*)(\&?)', 'i'))
    return svalue ? decodeURIComponent(svalue[1]) : ''
  } else {
    return decodeLocationSearch(url)
  }
}
/**
 * @description 链接对象参数拼接
 * @param {*} url
 * @param {*} obj
 * @returns
 */
const urlWithObj = (url = '', obj = {}) => {
  if (typeof obj !== 'object' || !Object.keys(obj).length) return url
  const _arr = []
  const _hasQuery = url && url.includes('?')
  if (_hasQuery) {
    const u_obj = getUrlParams('', url)
    Object.assign(obj, u_obj)
  }
  for (const k in obj) {
    _arr.push(`${k}=${obj[k]}`)
  }
  return `${url.split('?')[0]}?${_arr.join('&')}`
}
/**
 * @description 检验图片链接有效性
 * @param {*} src
 * @returns
 */
const checkImg = (src) => {
  if (!src) return ''
  const status = ['http', '//'].some(i => src.startsWith(i))
  return status ? src : ''
}
/**
 * @description 处理组件初始数据
 * @param {*} newVal
 * @returns
 */
const formatData = (newVal) => {
  if (['NgButton', 'NgBtnTop', 'NgBtnBottom', 'NgTelephone'].includes(newVal.name)) {
    if (newVal.currStyleClass == 'style-two') {
      newVal.eboxValue.borderRadius = 4
      newVal.eboxValue.width = 350
      newVal.eboxValue.x = 12.5
      newVal.styles.borderRadius = '.04rem'
      newVal.styles.width = '3.5rem'
      newVal.styles.x = '.125rem'
      if (['NgBtnTop', 'NgBtnBottom'].includes(newVal.name)) {
        const left = (375 - newVal.eboxValue.width) / 2
        newVal.styles.left = left / 100 + 'rem'
      }
    }

    if (newVal.currStyleClass == 'style-three') {
      newVal.textContent = getConfig('NODE_OSS_URL') + '/assets/h5/77aa9jv9_q76.png'
      newVal.eboxValue.borderWidth = 0
      newVal.eboxValue.borderRadius = 0
      newVal.eboxValue.width = 375
      newVal.eboxValue.bgHeight = 105
      newVal.eboxValue.height = 105
      newVal.eboxValue.x = 0
      newVal.eboxValue.y = 0
      newVal.styles.borderWidth = '0'
      newVal.styles.borderRadius = '0'
      newVal.styles.width = '3.75rem'
      newVal.styles.height = '1.05rem'
      newVal.styles.x = '0'
      newVal.styles.y = '0'
      newVal.boxBg.height = '105px'
      if (['NgBtnTop', 'NgBtnBottom'].includes(newVal.name)) newVal.styles.left = '0'
    }
  }
  if (['NgForm'].includes(newVal.name)) {
    if (newVal.currStyleClass == 'style-two') {
      newVal.currInputStyleClass = 'range'
      newVal.eboxValue.inputBorderList.forEach(v => {
        v.check = false
        if (v.key === newVal.currInputStyleClass) v.check = true
      })
    }
    if (newVal.currStyleClass == 'style-two' || newVal.currStyleClass == 'style-three') {
      newVal.eboxValue.bgHeight = 241
      newVal.eboxValue.height = 241
    }
  }
   if (['NgBuyNow'].includes(newVal.name)) {
    if (newVal.currStyleClass == 'style-four') {
      newVal.leftText = '不差钱，现价购买'
      newVal.rightText = '一键领券，立即购买'
    }else if(newVal.currStyleClass == 'style-two'){
      newVal.rightText = '参团购买'
    }
  }
}

/**
 * @description 根据DOM 计算高度
 * @param {*} src
 * @returns
 */
const calcHeight = (id, activeItems) => {
  const ele = document.querySelector(`#${id}-` + activeItems.uuid)
  const height = ele.offsetHeight
  activeItems.eboxValue.height = height
  activeItems.styles.height = height / 100 + 'rem'
  const bgH = activeItems.eboxValue.y + height
  if (bgH > activeItems.eboxValue.bgHeight) {
    activeItems.eboxValue.bgHeight = bgH
    activeItems.boxBg.height = bgH + 'px'
  }
}

/**
 * @description 计算组件高度
 * @param {*} src
 * @returns
 */
const calcModuleValue = (v) => {
  let uHeight = v.eboxValue.height
  if (['NgHeadline'].includes(v.name)) uHeight = (v.eboxValue.height + 10)
  if (['NgButton'].includes(v.name) && ['top', 'bottom'].includes(v.position)) {
    uHeight = v.eboxValue.height
  } else {
    if (['NgButton', 'NgTelephone'].includes(v.name)) uHeight = v.eboxValue.bgHeight
  }
  return uHeight
}


/**
 * 计算h5工具定位
 * @param newArr              Array  页面组件
 * @param data                Object 当前选中组件
 */
export function calcToolTop(newArr, data) {
  let maps = []
  newArr.forEach((v, i, arr) => {
    if (v.uuid == data.uuid) maps = arr.slice(0, i)
  })
  const mapt = maps.filter((v) => !hasUseToolbar(v))
  const nums = mapt.reduce((total, v) => total + calcModuleValue(v), 0)
  return nums + 'px'
}

export function hasUseToolbar(v, type) {
  if (type == 'button' && v.name == 'NgButton' && ['top', 'bottom'].includes(v.position)) {
    return true
  }
  if (v.name == 'NgButton') {
    return ['top', 'bottom'].includes(v.position) ? true : v.useToolbar
  }
  return v.useToolbar
}

export function getContainerHeight(ele, pic) {
  const container = document.querySelector(ele)
  if (!container) return Promise.resolve(0)

  const images = Array.from(container.querySelectorAll(pic))

  // 如果没有图片元素，直接返回容器高度
  if (images.length === 0) {
    return Promise.resolve(container.offsetHeight)
  }

  // 创建图片加载Promise数组
  const imageLoadPromises = images.map((img) => {
    return new Promise((resolve) => {
      img.querySelector('img').onload = function (e) {
        resolve(e.target.offsetHeight)
      }
      img.querySelector('img').error = function (e) {
        resolve(0)
      }
    })
  })

  // 等待所有图片加载完成
  return Promise.all(imageLoadPromises)
    .then((resp) => {
      return resp.reduce((sum, num) => sum + num, 0)
    })
    .catch((error) => {
      return container.offsetHeight
    })
}

export {
  deepClone,
  copyText,
  getUrlParams,
  urlWithObj,
  checkImg,
  throttle,
  formatData,
  calcHeight,
  calcModuleValue
}
