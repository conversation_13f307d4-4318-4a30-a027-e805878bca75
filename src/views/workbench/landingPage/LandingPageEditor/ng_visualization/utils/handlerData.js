/**
 * @description 数据清洗车间
 */

import configList from '@ng_visualization/config/config'
import { deepClone } from '@ng_visualization/utils'

/**
 * @description 为数据增添表单项供平台使用
 * @param {*} activityData
 * @returns
 */
export function setConfigMap(activityData) {
  if (!activityData || !activityData.pages || !activityData.pages.length) return activityData
  activityData.pages.forEach(i => {
    i.elements && i.elements.length && i.elements.forEach(j => {
      // 组件基础配置处理
      const midMap = configList[j.configCode] && deepClone(configList[j.configCode].configMap)
      for (const k in j.configInfo) {
        midMap[k].value = j.configInfo[k]
      }
      j.configMap = midMap
      j.configInfo && delete j.configInfo
    })
  })
  return activityData
}

/**
 * @description 为数据去掉表单项减少数据量，为前端提供更清晰的数据
 * @param {*} activityData
 * @returns
 */
export function removeConfigMap(activityData) {
  if (!activityData || !activityData.pages || !activityData.pages.length) return activityData
  activityData.pages.forEach(i => {
    i.elements.forEach(j => {
      // 组件基础配置处理
      const midInfo = {}
      for (const k in j.configMap) {
        midInfo[k] = j.configMap[k].value
      }
      j.configInfo = midInfo
      j.configMap && delete j.configMap
    })
  })
  return activityData
}
