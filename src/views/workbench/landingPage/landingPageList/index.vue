<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>落地页</div>
      </template>
      <template #extra>
        <div class="btn_group">
          <a-button v-auth="['addQAPage']" type="primary" @click="onJump('qa')" :disabled="pointData.balance <= 0"
            >新增问答页</a-button
          >
          <a-button
            v-auth="['addLandingPage']"
            type="primary"
            @click="onJump('landing')"
            :disabled="pointData.balance <= 0"
            >新增落地页</a-button
          >
        </div>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'name_alias'">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.name_alias }}</template>
                  <div class="text_overflow">{{ scope.record.name_alias }}</div>
                  <!-- <div class="text_overflow number-id">ID:{{ scope.record.code }}</div> -->
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'name'">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.name }}</template>
                  <div class="text_overflow">{{ scope.record.name }}</div>
                </a-tooltip>
              </template>

              <template v-if="scope.column.key === 'remarks'">
                <a-tooltip placement="topLeft" v-if="scope.record.remarks">
                  <template #title>{{ scope.record.remarks }}</template>
                  <div class="text_overflow_row2">{{ scope.record.remarks }}</div>
                </a-tooltip>
                <div v-else>--</div>
              </template>
              <template v-if="scope.column.key === 'type'">
                <div class="flex_align_center">
                  {{ statusType(scope.record.type) }}
                </div>
              </template>

              <template v-if="scope.column.key === 'creator'">
                <div style="min-width: 100px">{{ scope.record.creator }}</div>
              </template>
              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <a-button type="link" size="small" class="pa-0!" @click="onPreview(scope.record)">预览</a-button>
                    <a-button
                      v-auth="['pageCopy']"
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="onJump('copy', scope.record)"
                      >复制</a-button
                    >
                    <a-button
                      v-auth="['pageEdit']"
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="onJump('edit', scope.record)"
                      >编辑</a-button
                    >
                    <a-popconfirm
                      title="是否确认删除当前页面？"
                      placement="topRight"
                      :disabled="pointData.balance <= 0"
                      @confirm="delItem(scope.record)"
                    >
                      <a-button
                        v-auth="['pageDelete']"
                        type="link"
                        size="small"
                        class="pa-0! ml-10px"
                        :disabled="pointData.balance <= 0"
                        >删除</a-button
                      >
                    </a-popconfirm>
                  </div>
                </div>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>

    <a-modal
      v-model:open="data.dialogVisible"
      width="620px"
      :footer="null"
      :closable="false"
      :maskClosable="false"
      centered
      class="landing-preview-wrapper"
    >
      <Preview
        :info="data.info"
        :activity-id="data.info?.id"
        :page_name="data.info?.name"
        v-if="data.dialogVisible"
        @close="data.dialogVisible = false"
      />
    </a-modal>

    <a-modal
      v-model:open="data.qaVisible"
      width="890px"
      title="模板选择"
      :maskClosable="false"
      centered
      @ok="onQaConfirm"
    >
      <SelectQaType :type="data.qaType" @select="(val) => (data.qaType = val)" />
    </a-modal>
  </div>
</template>

<script setup>
  import Preview from '@/views/workbench/landingPage/LandingPageEditor/preview.vue'
  import SelectQaType from './components/SelectQaType.vue'
  import datas from './src/data'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  const {
    pageChange,
    searchForm,
    onJump,
    delItem,
    enable,
    searchConfig,
    data,
    statusType,
    getList,
    onPreview,
    onQaConfirm
  } = datas()
  getList()
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }

  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
</style>
