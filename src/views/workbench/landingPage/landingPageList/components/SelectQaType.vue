<template>
  <a-spin :spinning="state.spinning">
    <div class="flex-center" style="height: 620px">
      <div
        :class="['item', { active: state.qaType == 1 }]"
        :style="{ opacity: state.spinning ? 0 : 1 }"
        @click="onSelect(1)"
      >
        <img src="@/assets/images/h5/qa_01.png" class="icon" @load="onLoading" />
        <div class="select" v-if="state.qaType == 1">
          <CheckOutlined class="t" />
        </div>
      </div>
      <div
        :class="['item', { active: state.qaType == 2 }]"
        :style="{ opacity: state.spinning ? 0 : 1 }"
        @click="onSelect(2)"
      >
        <img src="@/assets/images/h5/qa_02.png" class="icon" @load="onLoading" />
        <div class="select" v-if="state.qaType == 2">
          <CheckOutlined class="t" />
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script setup>
  import { reactive } from 'vue'
  import { CheckOutlined } from '@ant-design/icons-vue'

  const props = defineProps({
    type: {
      type: Number,
      default: 1
    }
  })

  const emits = defineEmits(['select'])

  const state = reactive({
    qaType: props.type || 1,
    spinning: true
  })

  const onSelect = (type) => {
    state.qaType = type
    emits('select', state.qaType)
  }

  let timer = null
  const onLoading = () => {
    timer = setTimeout(() => {
      state.spinning = false
      clearTimeout(timer)
      timer = null
    }, 70)
  }
</script>

<style lang="scss" scoped>
  .item {
    position: relative;
    border: 2px solid transparent;
    margin-right: 30px;
    font-size: 0;
    border-radius: 5px;
    transition: all 0.3s;
    opacity: 0;
    cursor: pointer;
    &.active {
      border-color: #fe9d35;
    }
    &:last-child {
      margin-right: 0;
    }
    .icon {
      width: 290px;
      border-radius: 5px;
    }
    .select {
      position: absolute;
      right: 0;
      top: 0;
      padding: 1px 3px 2px;
      background-color: #fe9d35;
      border-radius: 0 0 0 4px;
    }
    .t {
      font-size: 12px;
      color: #fff;
      font-weight: 600;
    }
  }
</style>
