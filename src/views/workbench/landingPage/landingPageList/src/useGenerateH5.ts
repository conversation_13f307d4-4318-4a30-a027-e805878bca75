import { reactive } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { getConfig } from '@/utils'

export default function useGenerateH5() {
  const generateTempH5 = async (data: any) => {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: getConfig('NODE_API_URL') + '/templates',
        data
      })
        .then((resp) => {
          resolve(resp.data?.data)
        })
        .catch((err) => {
          console.log(err, 'err')
          message.error('请求失败，请稍后重试')
          reject('')
        })
    })
  }

  return {
    generateTempH5
  }
}
