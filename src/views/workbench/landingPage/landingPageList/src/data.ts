import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getH5List, setH5Del } from '../index.api'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入页面名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'code',
        value: undefined,
        props: {
          placeholder: '请输入ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'type',
        props: {
          placeholder: '请选择落地页类型',
          options: [
            { value: 1, label: '落地页' },
            { value: 2, label: '问答页' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        field: 'admin_ids',
        type: 'admin',
        value: undefined,
        span: 6,
        props: {
          mode: 'multiple',
          placeholder: '请选择负责人'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '页面名称',
        dataIndex: 'name_alias',
        key: 'name_alias',
        slot: true,
        width: 200
      },
      {
        title: '页面ID',
        dataIndex: 'code',
        key: 'code',
        slot: true,
        width: 200
      },
      {
        title: '页面标题',
        dataIndex: 'name',
        key: 'name',
        slot: true,
        width: 200
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        slot: true,
        width: 200
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        slot: true,
        width: 200
      },
      {
        title: '负责人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        slot: true,
        width: 200
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        slot: true,
        width: 200
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    dialogVisible: false,
    qaVisible: false,
    qaType: 1,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    }
  })
  const statusType = (val: string | number) => {
    let type = {
      1: '落地页',
      2: '问答页'
    }
    return (type as any)[val]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getH5List(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    data.params.admin_ids = v.formData.admin_ids ? v.formData.admin_ids.join(',') : ''
    data.params.page = 1
    getList()
  }

  const onQaConfirm = () => {
    console.log(data.qaType)
    router.push({ name: 'QaChatEditor', query: { module: data.qaType } })
  }

  const onJump = (type, item) => {
    console.log(type, item)
    data.info = item || {}
    if (type == 'qa') {
      // router.push({ name: 'QaChatEditor' })
      data.qaVisible = true
    } else if (type == 'landing') {
      router.push({ name: 'LandingPageEditor' })
    } else if (type == 'edit' || type == 'copy') {
      const name = item?.type == 2 ? 'QaChatEditor' : 'LandingPageEditor'
      router.push({ name, query: { id: item.id, type } })
    }
  }
  const delItem = async (row) => {
    try {
      await setH5Del({ id: row.id })
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }

  // 启用/禁用
  const enable = async (row) => {
    try {
      let resp = await setAddRole({ id: row.id, status: row.status === 1 ? 2 : 1 })
      message.success(resp.msg)
      getList()
    } catch (error) {
      console.error(error)
    }
  }

  const onPreview = (item: any) => {
    data.info = item
    data.dialogVisible = true
  }
  return {
    pageChange,
    searchForm,
    onJump,
    delItem,
    enable,
    searchConfig,
    data,
    statusType,
    getList,
    onPreview,
    onQaConfirm
  }
}
