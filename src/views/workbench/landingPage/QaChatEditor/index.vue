<template>
  <a-spin class="landing-spin" size="large" :spinning="false">
    <a-layout v-if="!state.fullscreenLoading" class="app_landing">
      <a-layout-header>
        <Header
          :bodyloading="state.bodyloading"
          :activity-data="store.getActivityData"
          @saveActivity="saveActivity"
          @toPreview="toPreview"
        />
      </a-layout-header>
      <a-layout>
        <!-- <a-layout-sider class="left-panel" width="400px">
        </a-layout-sider> -->
        <a-layout-content class="main-area scroll-edit-area">
          <MainEdit ref="mainEdit" />
        </a-layout-content>
        <a-layout-sider class="right-panel" width="905px">
          <RightAside ref="rightAside" />
        </a-layout-sider>
      </a-layout>
    </a-layout>
    <a-layout class="read-argee" v-if="!state.fullscreenLoading">
      <div :class="['read-content', { 'spin-blink': state.spinblink }]">
        <a-checkbox v-model:checked="store.getActivityData.checkoutProtocol"></a-checkbox>
        我已阅读并同意以下免责声明;广告落地页展示的所有信息由用户自行提供，其真实性、合法性、有效性由用户负责，斗量智投仅作为技术服务方授权用户使用域名，不对广告发布的后果提供任何保证，并不承担任何法律责任。请阅读并同意<span
          class="ant-btn-link"
          @click="state.dialogProtocolVisible = true"
          >《用户服务协议》</span
        >
      </div>
    </a-layout>

    <a-modal
      v-model:open="state.dialogVisible"
      width="620px"
      :footer="null"
      :closable="false"
      :maskClosable="false"
      centered
      class="landing-preview-wrapper"
    >
      <Preview
        :activity-id="state.activityId"
        :info="state.info"
        :page_name="store.getActivityData.title"
        v-if="state.dialogVisible"
        @close="state.dialogVisible = false"
      />
    </a-modal>

    <a-modal v-model:open="state.dialogProtocolVisible" title="用户协议" width="820px" :footer="null" centered>
      <div class="dialog-agree h-80vh overflow-y-auto">
        <div v-html="protocolText"></div>
      </div>
    </a-modal>
  </a-spin>
</template>

<script setup>
  import { onBeforeMount, reactive, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { cloneDeep } from 'lodash-es'
  import { v4 as uuidv4 } from 'uuid'
  import { message } from 'ant-design-vue'
  import html2canvas from 'html2canvas'
  import Header from './wrapper/Header/Index.vue'
  import MainEdit from './wrapper/Edit/Index.vue'
  import RightAside from './wrapper/RightAside/Index.vue'
  import Preview from '@/views/workbench/landingPage/LandingPageEditor/preview.vue'
  import { qaChatData } from './src/data.js'
  import config from './src/config.js'
  import { setAdH5Info, setAddAdH5 } from './index.api.ts'
  import { randomWord, getPageNameFromUrl, sleep } from '@/utils'
  import Cos from '@/utils/cos'
  import { protocolText } from '@/views/workbench/landingPage/landingPageList/src/protocol.ts'
  import useGenerateH5 from '@/views/workbench/landingPage/landingPageList/src/useGenerateH5.ts'
  import { activity as activityConfig, page as pageConfig } from '@ng_visualization/config/json_scheme.js'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()
  const { generateTempH5 } = useGenerateH5()
  const router = useRouter()
  const route = useRoute()

  const rightAside = ref(null)
  const state = reactive({
    isCopy: false,
    bodyloading: false,
    fullscreenLoading: false, // 数据加载icon
    dialogProtocolVisible: false, // 协议弹窗
    spinblink: false,
    timer: null,
    activityId: null, // 从列表跳转过来的ID
    adtype: null,
    dInnerHTML: '',
    cover_url: null,
    dialogVisible: false,
    storage_name: null,
    info: {}
  })

  onBeforeMount(() => {
    store.modifyQaData('reset')
    setActiveItems()
    initData()
  })

  const setActiveItems = () => {
    const datas = cloneDeep(qaChatData)
    const item = cloneDeep(config)[0] || {}
    item.uuid = uuidv4()
    delete item.styleTypeMap
    store.modifyActiveItems({ ...item, ...datas })
  }

  // 设置页面
  const setActivityData = () => {}

  const initData = async () => {
    try {
      state.fullscreenLoading = true
      state.activityId = route.query.id ? Number(route.query.id) : 0
      state.isCopy = route.query.type == 'copy'

      let actData = {}
      let jsonData = null
      console.log(state.activityId, 'state.activityId')
      if (state.activityId) {
        // 存在ID 编辑
        const res = await setAdH5Info({ id: state.activityId })
        jsonData = res && res.data.content
        state.info = (res && res.data) || {}
      }
      if (jsonData && jsonData != 'null') {
        actData = JSON.parse(jsonData)
      } else {
        actData = cloneDeep(activityConfig)
        const item = cloneDeep(pageConfig)
        item.uuid = uuidv4()
        item.elements = [store.getActiveItems]
        actData.author = 'qd'
        actData.pages.push(item)
      }
      if (!actData.qaModule) actData.qaModule = route.query.module || 2
      if (actData.pages[0].elements && actData.pages[0].elements.length) {
        actData.pages[0].elements.forEach((v) => {
          if (!v.qaModule && actData.qaModule) v.qaModule = actData.qaModule
          if (v.name == 'NgQaChat') {
            // todo 默认值
            const verCodeFlag = false // 是否验证码
            v.verTips = v?.verTips || store.defaultVerTips
            v.verCodeFlag = v.verCodeFlag || verCodeFlag
            if (v.qaDataList?.length > 0 && v.qaModule == '1') {
              v.qaDataList?.forEach((item) => {
                item.rowBtnStyle = item.rowBtnStyle || 4
                item.btnRadius = item.btnRadius || 6
              })
            }
            v.isRepetAnswer = v.isRepetAnswer || null
          }
          if (v.name == 'NgQaChat' && v.welcomeList.length) {
            const contextList = v.qaDataList[0].contextList
            const obj = contextList.find((v) => v.id)

            console.log('contextList--', contextList)
            if (v.qaDataList.length && !obj) {
              v.qaDataList[0].contextList = v.welcomeList
                .map((v) => {
                  return { ...v, pic: null }
                })
                .concat(contextList)
            }
          }
        })
      }
      console.log(actData, 'actData')
      store.modifyActivityData(actData)
      store.modifyActiveItems(store.getActivityData?.pages[0]?.elements[0] || {})
      store.modifyIsAllowedDrag()

      setTimeout(() => {
        state.fullscreenLoading = false
      }, 100)
    } catch (error) {
      console.log(error, 'sss')
    }
  }

  // 预览
  const toPreview = (data) => {
    if (data && data.cmd == 1) {
      saveActivity(data.cmd)
    } else {
      state.dialogVisible = true
    }
  }

  // 保存
  const saveActivity = async (desc) => {
    if (rightAside.value?.ngQaChat && rightAside.value.ngQaChat()?.validate) {
      await rightAside.value.ngQaChat().validate()
    }
    const params = paramsFunc()
    if (!params) return
    if (!store.getActivityData.checkoutProtocol) {
      state.spinblink = true
      const timer = setTimeout(() => {
        state.spinblink = false
        clearTimeout(timer)
      }, 1000)
      message.warning('请先阅读并勾选免责声明')
      return
    }
    state.bodyloading = true
    try {
      await dataURItoBlod()
      params.cover_url = state.cover_url
    } catch (error) {
      console.log(error)
    }
    try {
      const temp = await generateTempH5({
        isUpload: true,
        oldTemplatePath: state.activityId && state.info.h5_url ? getPageNameFromUrl(state.info.h5_url) : null,
        dataJson: params,
        pt: 'q'
      })
      if (temp.url) params.h5_url = temp.url
      const resp = await setAddAdH5(params)
      state.info = resp.data || {}
      state.activityId = resp.data.id
      state.bodyloading = false
      if (desc == 1) {
        state.dialogVisible = true
      } else {
        router.push({ name: 'LandingPageList' })
      }
    } catch (error) {
      console.log(error)
      state.bodyloading = false
    }
  }

  function paramsFunc() {
    const item = store.getActiveItems
    const { title, name_alias } = store.getActivityData
    if (state.isCopy) state.activityId = null
    const params = {
      name: title,
      content: JSON.stringify(store.getActivityData),
      fid: '',
      name_alias,
      remarks: '',
      type: 2,
      h5_url: state.activityId && state.info.h5_url ? state.info.h5_url : ''
    }
    if (state.activityId) params.id = state.activityId

    const validations = [
      { check: () => !params.name_alias, msg: '请输入问答页名称' },
      { check: () => !params.name, msg: '请输入页面标题' },
      { check: () => !item.custome_avatar, msg: '请上传老师头像' },
      { check: () => !item.user_avatar, msg: '请上传客户头像' }
      // {
      //   check: () => (item.welcomeList || []).some((v) => !v.content),
      //   msg: '请补全欢迎语信息'
      // }
    ]
    const status = validations.some(({ check, msg }) => {
      if (check()) {
        message.error(msg)
        return true
      }
    })
    if (status) return false
    if (!item.qaDataList || !item.qaDataList.length) {
      message.error('请至少添加一个对话组')
      return false
    }
    let index = 0
    const flag = item.qaDataList.every((x, i) => {
      const context = (x.contextList || []).some((v) => {
        return !v.content || v.content === '<p><br></p>'
      })
      const reply = (x.replyList || []).some((v) => {
        return !v.content || (v.clickStatus == 3 && !v.link) || (v.clickStatus == 4 && !v.otherReply)
      })
      if (context || reply) index = i + 1
      return context || reply ? false : true
    })
    if (!flag) {
      message.error(`【对话组${index}】请补充完整`)
      return false
    }

    return params
  }

  async function dataURItoBlod() {
    try {
      await sleep(200)
      let dataURI = await convertToImage(document.querySelector('.rendering-layer'))
      const fileNames = `${+new Date()}_${randomWord(12)}`
      let file = base64toFile(dataURI, fileNames)
      await Cos.upload(file, 'landingPage', false, (res) => {
        if (res.type == 'success') state.cover_url = res.content
      })
    } catch (error) {
      console.log(error)
    }
  }

  async function convertToImage(container, options = {}) {
    // 设置放大倍数
    const scale = window.devicePixelRatio

    // 传入节点原始宽高
    const width = 375 || container.offsetWidth
    const height = 585 || container.offsetHeight

    // html2canvas配置项
    const ops = {
      scale,
      width,
      height,
      backgroundColor: 'white',
      allowTaint: false,
      useCORS: true,
      proxy: '',
      ...options
    }
    const canvas = await html2canvas(container, ops)
    return canvas.toDataURL('image/png')
  }

  function base64toFile(dataurl, filename = 'file') {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const suffix = mime.split('/')[1]
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], `${filename}.${suffix}`, {
      type: mime
    })
  }
</script>

<style lang="scss" scoped></style>
