import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { hasUseToolbar } from '@ng_visualization/utils/index.js'

export function useDraggableResizable(props, emit) {
  // 响应式状态
  const top = ref(null)
  const left = ref(null)
  const width = ref(null)
  const height = ref(null)
  const rotate = ref(null)
  const resizing = ref(false)
  const dragging = ref(false)
  const rotating = ref(false)
  const handle = ref(null)
  const zIndex = ref(null)

  // 内部状态
  const state = {
    parentX: 0,
    parentW: 9999,
    parentY: 0,
    parentH: 9999,
    lastMouseX: 0,
    lastMouseY: 0,
    elmX: 0,
    elmY: 0,
    elmW: 0,
    elmH: 0,
    lastCenterX: 0,
    lastCenterY: 0,
    lastElmX: 0,
    lastElmY: 0,
    lastElmW: 0,
    lastElmH: 0,
    fixedXName: '',
    fixedYName: '',
    fixedX: 0,
    fixedY: 0
  }

  // 监听属性变化
  watch(() => props.activeItems.uuid, () => {
    initData()
  })

  watch(() => props.activeItems.eboxValue.x, (newVal) => {
    left.value = newVal
  })

  watch(() => props.activeItems.eboxValue.y, (newVal) => {
    top.value = newVal
  })

  watch(() => props.activeItems.eboxValue.width, (newVal) => {
    width.value = newVal
  })

  watch(() => props.activeItems.eboxValue.height, (newVal) => {
    height.value = newVal
  })

  watch(() => props.activeItems.rotation, (newVal) => {
    if (newVal >= 0) rotate.value = newVal % 360
  })

  // 方法
  const handleMove = (e) => {
    if (!props.activeItems.enabled || hasUseToolbar(props.activeItems)) return
    const { x: mouseX, y: mouseY } = getMouseCoordinate(e)
    
    if (resizing.value) {
      const rotateVal = rotate.value
      const widthDiff = mouseX - state.lastMouseX
      const heightDiff = mouseY - state.lastMouseY
      const c = Math.sqrt(Math.pow(widthDiff, 2) + Math.pow(heightDiff, 2))
      const angle = getAngle(widthDiff, heightDiff)
      const rad = (Math.PI / 180) * (angle - rotateVal)
      const diffY = Math.round(Math.sin(rad) * c)
      const diffX = Math.round(Math.cos(rad) * c)
      
      state.elmX = state.lastElmX
      state.elmY = state.lastElmY
      state.elmW = state.lastElmW
      state.elmH = state.lastElmH
      
      const [handleY, handleX] = handle.value
      if (handleX !== 'm') {
        state.elmW += diffX * (handleX === 'r' ? 1 : -1)
      }
      if (handleY !== 'm') {
        state.elmH += diffY * (handleY === 'b' ? 1 : -1)
      }
      
      if (state.elmW < props.minw) {
        state.elmW = props.minw
      }
      if (state.elmH < props.minh) {
        state.elmH = props.minh
      }
      
      fixedTo()
      left.value = Math.round(state.elmX / props.grid[0]) * props.grid[0]
      top.value = Math.round(state.elmY / props.grid[1]) * props.grid[1]
      width.value = Math.round(state.elmW / props.grid[0]) * props.grid[0]
      height.value = Math.round(state.elmH / props.grid[1]) * props.grid[1]
      
      emit('modifyResize', { x: left.value, y: top.value, w: width.value, h: height.value, type: 'resizing' })
    } else if (dragging.value) {
      state.elmX = state.lastElmX
      state.elmY = state.lastElmY
      state.elmW = state.lastElmW
      state.elmH = state.lastElmH
      
      const diffX = mouseX - state.lastMouseX
      const diffY = mouseY - state.lastMouseY
      
      state.elmX += diffX
      state.elmY += diffY
      
      if (props.axis === 'x' || props.axis === 'both') {
        left.value = Math.round(state.elmX / props.grid[0]) * props.grid[0]
      }
      if (props.axis === 'y' || props.axis === 'both') {
        top.value = Math.round(state.elmY / props.grid[1]) * props.grid[1]
      }
      
      // 边界检查
      if (top.value < 0) {
        const tp = props.activeItems.eboxValue.height - 3
        top.value = Math.abs(top.value) > tp ? -tp : top.value
      } else {
        const tp = props.activeItems.eboxValue.bgHeight - 5
        top.value = Math.abs(top.value) > tp ? tp : top.value
      }
      
      if (left.value < 0) {
        const lft = props.activeItems.eboxValue.width - 6
        left.value = Math.abs(left.value) > lft ? -lft : left.value
      } else {
        const lft = props.activeItems.eboxValue.bgWidth - 6
        left.value = Math.abs(left.value) > lft ? lft : left.value
      }
      
      emit('modifyResize', { x: left.value, y: top.value })
    } else if (rotating.value) {
      const y = mouseY - state.lastCenterY
      const x = mouseX - state.lastCenterX
      rotate.value = (getAngle(x, y) - 90) % 360
      emit('modifyRotate', { rotate: rotate.value })
    }
  }

  const handleDragDown = (handleVal, e) => {
    if (hasUseToolbar(props.activeItems)) return
    handle.value = handleVal
    if (e.stopPropagation) e.stopPropagation()
    if (e.preventDefault) e.preventDefault()
    
    if (handleVal === 'rot') {
      handleRotateDown(handleVal, e)
    } else {
      handleResizeDown(handleVal, e)
    }
  }

  const handleRotateDown = (handleVal, e) => {
    const docu = props.$refs[props.activeItems.name + props.activeItems.uuid][0] || {}
    const { top: rectTop, left: rectLeft, width: rectWidth, height: rectHeight } = docu.getBoundingClientRect()
    state.lastCenterX = window.pageXOffset + rectLeft + rectWidth / 2
    state.lastCenterY = window.pageYOffset + rectTop + rectHeight / 2
    rotating.value = true
  }

  const handleResizeDown = (handleVal, e) => {
    const fixed = {
      x: handleVal[1] === 'l' ? 'right' : 'left',
      y: handleVal[0] === 't' ? 'bottom' : 'top'
    }
    
    const rect = {
      top: top.value,
      right: left.value + width.value,
      bottom: top.value + height.value,
      left: left.value
    }
    
    const fixedCoordinate = rotatedPoint(rect, rotate.value, fixed)
    state.fixedXName = fixed.x
    state.fixedYName = fixed.y
    state.fixedX = fixedCoordinate.x
    state.fixedY = fixedCoordinate.y
    state.lastElmX = state.elmX
    state.lastElmY = state.elmY
    state.lastElmW = state.elmW
    state.lastElmH = state.elmH
    resizing.value = true
  }

  const elDownHandler = (ev) => {
    if (hasUseToolbar(props.activeItems)) return
    const target = ev.target || ev.srcElement
    reviewDimensions()
    state.elmX = left.value
    state.elmY = top.value
    
    if (props.activeItems.enabled) {
      state.lastElmX = state.elmX
      state.lastElmY = state.elmY
      state.lastElmW = state.elmW
      state.lastElmH = state.elmH
      dragging.value = true
    }
  }

  const reviewDimensions = () => {
    if (props.minw > width.value) width.value = props.minw
    if (props.minh > height.value) height.value = props.minh
    state.elmW = width.value
    state.elmH = height.value
    emit('modifyResize', { x: left.value, y: top.value, w: width.value, h: height.value, type: 'resizing' })
  }

  const handleDeselect = (e) => {
    if (hasUseToolbar(props.activeItems)) return
    const { x: mouseX, y: mouseY } = getMouseCoordinate(e)
    state.lastMouseX = mouseX
    state.lastMouseY = mouseY
  }

  const handleUp = (e) => {
    if (hasUseToolbar(props.activeItems)) return
    const { x: mouseX, y: mouseY } = getMouseCoordinate(e)
    state.lastMouseX = mouseX
    state.lastMouseY = mouseY
    handle.value = null
    
    if (resizing.value) {
      resizing.value = false
      emit('modifyResize', { x: left.value, y: top.value, w: width.value, h: height.value, type: 'resizing' })
    } else if (dragging.value) {
      dragging.value = false
    } else if (rotating.value) {
      rotating.value = false
    }
    
    state.elmX = left.value
    state.elmY = top.value
  }

  const getMouseCoordinate = (e) => {
    return {
      x: e.pageX || e.clientX + document.documentElement.scrollLeft,
      y: e.pageY || e.clientY + document.documentElement.scrollTop
    }
  }

  const fixedTo = () => {
    const rect = {
      top: state.elmY,
      right: state.elmX + state.elmW,
      bottom: state.elmY + state.elmH,
      left: state.elmX
    }
    
    const fixed = {
      x: state.fixedXName,
      y: state.fixedYName
    }
    
    const { x: fixedX, y: fixedY } = rotatedPoint(rect, rotate.value, fixed)
    const dX = Math.round(state.fixedX - fixedX)
    const dY = Math.round(state.fixedY - fixedY)
    state.elmX += dX
    state.elmY += dY
  }

  const rotatedPoint = (rect, rotateVal, point) => {
    const { top, right, bottom, left } = rect
    const rad = (Math.PI / 180) * rotateVal
    const cos = Math.cos(rad)
    const sin = Math.sin(rad)
    const originX = (right - left + 1) / 2 + left
    const originY = (bottom - top + 1) / 2 + top
    let x = rect[point.x]
    let y = rect[point.y]
    x -= originX
    y -= originY
    return {
      x: x * cos - y * sin + originX,
      y: x * sin + y * cos + originY
    }
  }

  const getAngle = (x, y) => {
    let theta = Math.atan2(y, x)
    theta = Math.round((180 / Math.PI) * theta)
    if (theta < 0) theta = 360 + theta
    return theta
  }

  const initData = () => {
    if (!props.activeItems.enabled || hasUseToolbar(props.activeItems)) return
    state.elmX = props.activeItems.eboxValue.x
    state.elmY = props.activeItems.eboxValue.y
    state.elmW = props.activeItems.eboxValue.width
    state.elmH = props.activeItems.eboxValue.height
  }

  // 生命周期钩子
  onMounted(() => {
    document.documentElement.addEventListener('mousemove', handleMove, true)
    document.documentElement.addEventListener('mousedown', handleDeselect, true)
    document.documentElement.addEventListener('mouseup', handleUp, true)
    initData()
  })

  onBeforeUnmount(() => {
    document.documentElement.removeEventListener('mousemove', handleMove, true)
    document.documentElement.removeEventListener('mousedown', handleDeselect, true)
    document.documentElement.removeEventListener('mouseup', handleUp, true)
  })

  return {
    top,
    left,
    width,
    height,
    rotate,
    resizing,
    dragging,
    rotating,
    handle,
    zIndex,
    handleMove,
    handleDragDown,
    elDownHandler,
    handleDeselect,
    handleUp
  }
}

// 默认属性
export const defaultProps = {
  minw: {
    type: Number,
    default: 50,
    validator: (val) => val >= 0
  },
  minh: {
    type: Number,
    default: 15,
    validator: (val) => val >= 0
  },
  grid: {
    type: Array,
    default: () => [1, 1]
  },
  axis: {
    type: String,
    default: 'both',
    validator: (val) => ['x', 'y', 'both'].indexOf(val) !== -1
  }
}
