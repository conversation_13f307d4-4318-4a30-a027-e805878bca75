<template>
  <div class="rendering-layer">
    <div v-if="store.getActiveItems.uuid && isEmpty" class="xr-brick-wrapper">
      <div class="stream-frame">
        <NgQaChat :item="store.getActiveItems" />
      </div>
    </div>
    <div v-else class="empty-page">
      <img :src="getConfig('NODE_OSS_URL') + '/assets/h5/h5-editor_empty.png'" alt="empty_page" width="289" />
      <p class="title">请配置对话组</p>
    </div>

    <div class="float-section top-float-section">
      <div class="float-items top-float-items" />
    </div>
  </div>
</template>

<script setup>
  import { computed, reactive } from 'vue'
  import NgQaChat from './QaH5.vue'
  import { getConfig } from '@/utils'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const isEmpty = computed(() => {
    return (
      (store.getActiveItems.carousel_type && store.getActiveItems.carouselList.length) ||
      store.getActiveItems.welcomeList.length ||
      store.getActiveItems.qaDataList.length
    )
  })
</script>

<style lang="scss" scoped>
  .rendering-layer {
    background: #ebeff1;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    min-height: 585px;
    // margin-bottom: 85px;

    // background-image:
    //   linear-gradient(45deg, #eee 25%, transparent 0, transparent 75%, #eee 0),
    //   linear-gradient(45deg, #eee 25%, transparent 0, transparent 75%, #eee 0) !important;
    background-position:
      0 0,
      8px 8px !important;
    background-size: 16px 16px !important;
  }
  .xr-brick-wrapper {
    .stream-frame {
      position: relative;
    }
  }

  .empty-page {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -32px;
    text-align: center;
    .title {
      font-size: 16px;
      color: #ccc;
      margin-top: 15px;
    }
    .subtitle {
      font-size: 12px;
      color: #d9d9d9;
      margin-top: 12px;
    }
  }

  .center-panel--slide .xr-brick-wrapper,
  .center-panel:not(.center-panel--slide) .rendering-layer {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
  }
</style>
