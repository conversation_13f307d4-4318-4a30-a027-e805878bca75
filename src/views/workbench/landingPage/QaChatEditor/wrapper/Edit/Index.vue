<template>
  <div class="center-panel-wrap">
    <div class="center-panel">
      <!-- 占位 -->
      <div class="top-blank" />
      <div class="content-block">
        <div class="edit-area">
          <!-- 数据渲染区 实际数据显示 -->
          <Rendering />
          <div v-show="store.qaIsShowVerify">
            <NgFirstScreenVerify :item="store.getActiveItems" :isShow="store.getActiveItems.verCodeFlag" />
          </div>

          <div class="screen-verrify-ruler" @click="onSelect" v-if="store.getActiveItems.verCodeFlag">
            {{ store.qaIsShowVerify ? '隐藏' : '展示' }}首屏验证
            <VerticalLeftOutlined />
          </div>
          <!-- 首屏提示 -->
          <!-- <div class="screen-ruler first-screen">
            <span>首屏</span>
            <el-tooltip content="以上内容首屏可见" placement="top-end"><i class="el-icon-question" /></el-tooltip>
          </div> -->
        </div>
      </div>
      <!-- 占位 -->

      <div class="bottom-blank" />
    </div>
  </div>
</template>

<script setup>
  import { watch, reactive } from 'vue'
  import { VerticalLeftOutlined } from '@ant-design/icons-vue'
  import Rendering from './components/Rendering.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  watch(
    () => store.getActiveItems.verCodeFlag,
    (val) => {
      store.qaIsShowVerify = val
    },
    { immediate: true, deep: true }
  )

  const onSelect = () => {
    store.qaIsShowVerify = !store.qaIsShowVerify
  }
</script>

<style lang="scss" scoped>
  .center-panel {
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .content-block {
    position: relative;
  }
  .top-blank,
  .bottom-blank {
    height: 85px;
  }
  .background,
  .bg-block {
    display: block;
  }
  .bg-block--hovered,
  .bg-block--selected,
  .bg-block:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  .edit-area {
    width: 375px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    user-select: none;
  }

  .screen-ruler {
    position: absolute;
    padding-top: 4px;
    border-top: 1px dashed #505f79;
    color: #505f79;
    opacity: 0.25;
    font-size: 12px;
    display: flex;
    align-content: center;
    justify-content: space-around;
    &.first-screen {
      top: 585px;
      left: -48px;
      width: 40px;
    }
  }
  .screen-verrify-ruler {
    position: absolute;
    top: 400px;
    left: -30px;
    width: 30px;
    padding: 10px 8px;
    border-radius: 8px 0px 0px 8px;
    box-shadow: -3px 0px 4px 0px rgba(181, 172, 163, 0.46);
    border: 1px solid #ffac54;
    border-right-width: none;
    background: #ffffff;
    color: #fe9d35;
    line-height: 17px;
    display: flex;
    align-content: center;
    justify-content: space-around;
    flex-direction: column;
    cursor: pointer;
  }
</style>
