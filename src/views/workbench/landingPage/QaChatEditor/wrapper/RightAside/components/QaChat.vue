<template>
  <div class="pages">
    <div class="header" v-if="lType != 2">{{ state.id ? '编辑' : '新增' }}</div>
    <div class="form">
      <a-form ref="ruleForm" :model="store.getActiveItems" label-width="0px">
        <a-form-item
          label=""
          name="qa_name_alias"
          :rules="[{ required: true, message: '请输入问答页名称' }]"
          v-if="lType != 2"
        >
          <div class="item flex-y-center">
            <label class="label lh-20px text-14px c-#313233">
              <span class="require">问答页名称：</span>
            </label>
            <div class="flex-1">
              <a-input
                v-model:value.trim="store.getActiveItems.qa_name_alias"
                :maxlength="20"
                show-count
                placeholder="请输入问答页名称"
                @keydown.space.prevent
                @change="onChangeInput('qa_name_alias')"
              />
            </div>
          </div>
        </a-form-item>

        <a-form-item
          label=""
          name="qa_title"
          :rules="[{ required: true, message: '请输入页面标题' }]"
          v-if="lType != 2"
        >
          <div class="item flex-y-center">
            <label class="label lh-20px text-14px c-#313233">
              <span class="require">页面标题：</span>
            </label>
            <div class="flex-1">
              <a-input
                v-model:value.trim="store.getActiveItems.qa_title"
                :maxlength="20"
                show-count
                placeholder="请输入页面标题"
                @keydown.space.prevent
                @change="onChangeInput('qa_title')"
              />
            </div>
          </div>
        </a-form-item>
        <a-form-item label="" name="verCode" v-if="lType != 2">
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 pt-1px">
              <span>验证码：</span>
            </label>
            <div class="">
              <a-switch class="block mb-6px" v-model:checked="store.getActiveItems.verCodeFlag" @change="onChangeVer" />
              <span class="c-#FE4D4F text-14px">注：开启后，用户将先进行验证码验证，成功后才可以浏览页面</span>
            </div>
          </div>
        </a-form-item>
        <a-form-item
          v-if="store.getActiveItems.verCodeFlag"
          label=""
          name="verTips"
          :rules="[{ required: true, message: '请输入温馨提示' }]"
        >
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 pt-1px">
              <span>温馨提示：</span>
            </label>
            <div class="flex-1">
              <a-textarea
                v-model:value.trim="store.getActiveItems.verTips"
                placeholder="请输入温馨提示"
                :maxlength="200"
                show-count
                :auto-size="{ minRows: 3, maxRows: 6 }"
                @keydown.space.prevent
              />
            </div>
          </div>
        </a-form-item>
        <a-form-item label="" name="isRepetAnswer" :rules="[{ required: true, message: '请选择是否允许重复回答' }]">
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 pt-1px">
              <span class="require">是否允许重复回答：</span>
            </label>
            <div class="flex-1">
              <a-radio-group v-model:value="store.getActiveItems.isRepetAnswer" :options="state.repeatOption" />
            </div>
          </div>
        </a-form-item>
        <a-form-item label="" name="carousel_type">
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 pt-1px">
              <span>展现形式：</span>
            </label>
            <div class="flex-1">
              <a-checkbox-group
                v-model:value="state.checkboxType"
                name="checkboxgroup"
                :options="state.carouselType"
                @change="onCheckbox"
              />
            </div>
          </div>
        </a-form-item>

        <a-form-item label="" name="carouselList" v-if="store.getActiveItems.carousel_type">
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 pt-8px">
              <span>轮播图：</span>
            </label>
            <div class="flex-1">
              <div class="flex-y-center">
                <div
                  class="uimgg-box mr-10px flex-center"
                  v-for="(v, index) in store.getActiveItems.carouselList"
                  :key="index"
                >
                  <img :src="v.pic" class="img" />
                  <!-- <DeleteOutlined class="delete-icon flex-center" @click="onCarouselDel(index)" /> -->
                  <img
                    src="@/assets/images/h5/delete.png"
                    class="delete-icon flex-center"
                    @click="onCarouselDel(index)"
                  />
                </div>
                <div
                  class="uimgg-box flex-center"
                  @click="uploadsImg('carousel', 5 - store.getActiveItems.carouselList.length, true)"
                  v-if="5 - store.getActiveItems.carouselList.length"
                >
                  <PlusOutlined class="plus" /><span>添加图片</span>
                </div>
              </div>
              <div class="text-12px c-#909090 lh-17px mt-6px">最多可上传5张，支持jpg/jpeg/png/gif格式；10M以内</div>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="" name="avatar" class="mb-0!">
          <div class="flex">
            <a-form-item label="" name="custome_avatar" :rules="[{ required: true, message: '请上传老师头像' }]">
              <div class="item flex">
                <label class="label lh-20px text-14px c-#313233 pt-8px">
                  <span class="require">老师头像：</span>
                </label>
                <div class="flex-1">
                  <div class="flex-y-center">
                    <div class="uimgg-box flex-center">
                      <img
                        v-if="store.getActiveItems.custome_avatar"
                        :src="store.getActiveItems.custome_avatar"
                        @click="uploadsImg('custome', 1)"
                        class="img"
                      />
                      <template v-else> <PlusOutlined class="plus" /><span>添加图片</span> </template>
                    </div>
                  </div>
                  <div class="text-12px c-#909090 lh-17px mt-6px whitespace-nowrap">
                    最多可上传1张，支持jpg/jpeg/png/gif格式; <br />
                    10M以内;
                  </div>
                </div>
              </div>
            </a-form-item>

            <a-form-item label="" name="user_avatar" :rules="[{ required: true, message: '请上传客户头像' }]">
              <div class="item flex">
                <label class="label lh-20px text-14px c-#313233 pt-8px">
                  <span class="require">客户头像：</span>
                </label>
                <div class="flex-1">
                  <div class="flex-y-center">
                    <div class="uimgg-box flex-center">
                      <img
                        v-if="store.getActiveItems.user_avatar"
                        :src="store.getActiveItems.user_avatar"
                        @click="uploadsImg('user', 1)"
                        class="img"
                      />
                      <template v-else> <PlusOutlined class="plus" /><span>添加图片</span> </template>
                    </div>
                  </div>
                  <div class="text-12px c-#909090 lh-17px mt-6px whitespace-nowrap">
                    最多可上传1张，支持jpg/jpeg/png/gif格式;<br />
                    10M以内;
                  </div>
                </div>
              </div>
            </a-form-item>
          </div>
        </a-form-item>

        <!-- <a-form-item label="">
          <div class="item flex">
            <label class="label lh-20px text-14px c-#313233 mt-8px">
              <span class="require">欢迎语：</span>
            </label>
            <div class="flex flex-column flex-1">
              <a-form-item
                v-for="(v, i) in store.getActiveItems.welcomeList"
                :key="v.id"
                :name="['welcomeList', i, 'content']"
                :rules="{ required: true, message: '请输入欢迎语' }"
                class="welcome-texta"
              >
                <div class="texta flex-1">
                  <a-textarea
                    v-model:value.trim="v.content"
                    placeholder="请输入欢迎语"
                    :maxlength="200"
                    @keydown.space.prevent
                  />
                  <CloseCircleOutlined class="close" v-if="i != 0" @click="onWelcomeDel(i)" />
                </div>
              </a-form-item>

              <div class="btns flex flex-1">
                <a-button
                  type="dashed"
                  class="w-full c-#313233!"
                  :disabled="store.getActiveItems.welcomeList.length == store.getActiveItems.welcome_total"
                  @click="onWelcomeAdd"
                >
                  +添加欢迎语
                </a-button>
              </div>
            </div>
          </div>
        </a-form-item> -->

        <div class="block-group" v-if="store.getActiveItems.qaDataList?.length">
          <div
            class="dialogue"
            v-for="(item, index) in store.getActiveItems.qaDataList"
            :key="index + 'x'"
            @click="onSetItem(item, index)"
          >
            <div class="headers ups">
              <div class="title text-16px lh-22px c-#313233">对话组{{ index + 1 }}</div>
              <div>
                <a-button :disabled="index == 0" @click.stop="onQaMove(item, index, 'up')">上移</a-button>
                <a-button
                  :disabled="store.getActiveItems.qaDataList.length == index + 1"
                  @click.stop="onQaMove(item, index, 'down')"
                >
                  下移
                </a-button>
                <a-button type="link" danger @click.stop="onQaMove(item, index, 'del')" class="mr-8px!">删除</a-button>
                <RightOutlined :class="['right-out-lined', { rotate: !item.fold }]" @click="item.fold = !item.fold" />
              </div>
            </div>
            <div v-show="!item.fold">
              <div class="rick ml-16px mr-16px flex-col">
                <div class="flex flex-1" v-for="(v, i) in item.contextList" :key="i">
                  <div class="flex rounded-8px p-16px mb-16px bg-#F7F9FC flex-1">
                    <div class="flex">
                      <label class="c-#323233 text-14px lh-20px mr-8px mt-8px">发送文本内容{{ i + 1 }}：</label>
                      <WangeditorV5
                        :text-content="v.content"
                        contentHeight="160px"
                        backgroundColor="#FFF"
                        borderRadius="8px"
                        :class="['qachatbx', lType == 2 ? 'qachatbx-l' : '']"
                        @wang="
                          (val) => {
                            v.content = val
                            onResetActive()
                            handleInputText()
                          }
                        "
                      />
                    </div>
                    <div class="flex ml-14px">
                      <label class="c-#323233 text-14px lh-20px mr-8px mt-8px flex-1">发送文本图片：</label>
                      <!-- <UploadH5
                          accept=".jpg,.png,.gif"
                          :max="1"
                          :size="20"
                          type="button"
                          use="landingPage"
                          @change="(val) => onUploadsItem(val, v)"
                        >
                      </UploadH5> -->
                      <div
                        class="w-70px! h-70px! flex flex-center bg-#fff! rounded-6px!"
                        @click="
                          () => {
                            contextImg = v
                            uploadsImg('contextImg', 1, true)
                          }
                        "
                      >
                        <img v-if="v.pic" :src="v.pic" class="w-100 h-100% rounded-6px!" />
                        <div class="flex flex-col flex-items-center" v-else>
                          <PlusOutlined class="plus" />
                          <div class="c-#acacac text-12px mt-4px ml-1px">添加图片</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <a-button class="delete-tag" type="link" danger @click.stop="onDelDialogue(item, i)">删除</a-button>
                </div>
              </div>
              <div class="btns flex flex-1">
                <a-button
                  type="dashed"
                  class="w-full"
                  :disabled="(store.getActiveItems.dialogue_total || 15) == item.contextList.length"
                  @click="onAddDialogue(item, index)"
                >
                  +添加发送内容
                </a-button>
              </div>
              <div class="flex ml-16px mb-12px">
                <label class="lh-20px w-155px text-left text-14px c-#313233 pt-8px">
                  <span
                    >延迟点击时长(秒)
                    <a-tooltip>
                      <template #title>
                        <div>达到设置的秒数后亮起回复选项</div>
                      </template>
                      <QuestionCircleFilled class="c-#888b8f"></QuestionCircleFilled>
                    </a-tooltip>
                    ：</span
                  >
                </label>
                <a-input-number
                  class="h-34px w-347px"
                  v-model:value="item.delayClick"
                  placeholder="请输入延迟点击时长"
                  type="number"
                  :min="0"
                  :max="99999"
                  @blur="
                    () => {
                      !item.delayClick && (item.delayClick = 0)
                    }
                  "
                />
              </div>
              <div v-if="route.query?.module == 1 || store.getActiveItems.qaModule == 1" class="mb-12px my-wrap">
                <div class="flex ml-16px">
                  <label class="lh-20px w-155px text-left text-14px c-#313233 pt-8px">
                    <span>选项设置：</span>
                  </label>
                  <a-radio-group
                    optionType="button"
                    v-model:value="item.rowBtnStyle"
                    style="margin-bottom: 16px"
                    :options="RowBtnStyleData"
                    @change="
                      () => {
                        onResetActive()
                        handleInputText()
                      }
                    "
                  >
                  </a-radio-group>
                </div>
                <div class="flex ml-16px">
                  <label class="lh-20px w-155px text-left text-14px c-#313233 pt-8px">
                    <span>圆角：</span>
                  </label>
                  <a-slider
                    :getTooltipPopupContainer="(triggerNode) => triggerNode.parentNode"
                    v-model:value="item.btnRadius"
                    class="btlider w-256px"
                    :min="0"
                    :max="100"
                    @afterChange="(val) => onResetActive()"
                  />
                </div>
              </div>
              <div class="reply-box">
                <div class="flex ml-16px mr-16px mb-16px flex" v-for="(v, i) in item.replyList" :key="i">
                  <div class="reply rounded-8px p-16px bg-#F7F9FC flex-1">
                    <div class="reply-item flex-y-center">
                      <div class="label">
                        <label class="whitespace-nowrap require">用户回复内容{{ i + 1 }}：</label>
                      </div>
                      <div class="flex-1">
                        <a-input
                          v-model:value.trim="v.content"
                          placeholder="请输入用户回复内容"
                          :maxlength="200"
                          @keydown.space.prevent
                          @change="
                            () => {
                              onResetActive()
                              handleInputText()
                            }
                          "
                        />
                      </div>
                    </div>
                    <div class="reply-item flex-y-center">
                      <div class="label">
                        <label class="require">会话状态：</label>
                      </div>
                      <div class="flex-1">
                        <!-- v-model:value="value" -->
                        <a-radio-group v-model:value="v.sessionStatus">
                          <a-radio v-for="x in state.seesstionList" :key="x.value" :value="x.value">{{
                            x.label
                          }}</a-radio>
                        </a-radio-group>
                      </div>
                    </div>
                    <div class="reply-item flex-y-center" v-if="v.sessionStatus == 1">
                      <div class="label">
                        <label class="require">用户点击交互：</label>
                      </div>
                      <div class="flex-1">
                        <a-radio-group v-model:value="v.clickStatus">
                          <a-radio v-for="x in state.userClickList" :key="x.value" :value="x.value">{{
                            x.label
                          }}</a-radio>
                        </a-radio-group>
                      </div>
                    </div>
                    <div class="reply-item flex-y-center" v-if="v.clickStatus == 3">
                      <div class="label">
                        <label class="require">跳转地址：</label>
                      </div>
                      <div class="flex-1">
                        <a-input v-model:value.trim="v.link" placeholder="请输入跳转地址" @keydown.space.prevent />
                      </div>
                    </div>
                    <div class="reply-item flex-y-center" v-if="v.sessionStatus == 1 && v.clickStatus == 4">
                      <div class="label">
                        <label class="require">跳转其他回答：</label>
                      </div>
                      <div class="flex-1">
                        <a-select
                          v-model:value="v.otherReply"
                          allowClear
                          class="w-full"
                          placeholder="请选择跳转其他回答"
                        >
                          <a-select-option
                            v-for="(x, i) in store.getActiveItems.qaDataList"
                            :key="i + 't'"
                            :value="x.id"
                            :disabled="index == i"
                          >
                            对话组{{ i + 1 }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                    <div class="reply-item flex" v-if="v.sessionStatus == 2">
                      <div class="label mt-8px">
                        <label>终止会话回复：</label>
                      </div>
                      <div class="flex-1">
                        <a-textarea
                          v-model:value.trim="v.endValue"
                          placeholder="请输入终止会话回复"
                          :row="3"
                          :maxlength="200"
                          @keydown.space.prevent
                        />
                      </div>
                    </div>
                  </div>
                  <a-button class="delete-tag" type="link" danger @click.stop="onDelReply(item, i)">删除</a-button>
                </div>
              </div>
              <div class="btns flex flex-1">
                <a-button
                  type="dashed"
                  class="w-full"
                  :disabled="store.getActiveItems.reply_total == item.replyList.length"
                  @click="onAddReply(item, index)"
                >
                  +添加回复内容
                </a-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <a-button type="primary" class="c-#fff!" :disabled="qaTotal <= 0" @click.stop="onAddDialogueGroup">
            新增对话组
          </a-button>
        </div>
      </a-form>
    </div>
    <a-modal v-model:open="dialog.visible" :title="dialog.title" :width="dialog.width" :footer="null" destroyOnClose>
      <MaterialLibrary
        :isH5Upload="dialog.isH5Upload"
        :size="dialog.size"
        :extension="'.jpg,jpeg,.png,.gif'"
        :fileSize="10 * 1024 * 1024"
        v-if="dialog.visible"
        type="image"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import { nextTick, onMounted, computed, reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { cloneDeep, debounce, isArray } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import {
    PlusOutlined,
    DeleteOutlined,
    CloseCircleOutlined,
    RightOutlined,
    QuestionCircleFilled
  } from '@ant-design/icons-vue'
  import WangeditorV5 from '@ng_visualization/components/WangeditorV5.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  const dialog = reactive({
    visible: false,
    title: '',
    width: '',
    type: '',
    size: 1,
    isH5Upload: false
  })
  const RowBtnStyleData = ref([
    {
      label: '一行一项',
      value: 1
    },
    {
      label: '一行两项',
      value: 2
    },
    {
      label: '一行三项',
      value: 3
    },
    {
      label: '一行四项',
      value: 4
    }
  ])
  const uploadsImg = (type, size, isH5Upload) => {
    dialog.type = type
    dialog.title = '图片素材'
    dialog.width = '80%'
    dialog.visible = true
    dialog.size = size
    dialog.isH5Upload = isH5Upload || false
  }
  const contextImg = ref({})
  const onEvent = (v) => {
    console.log(v, '问答')
    dialog.visible = false
    if (v?.data?.length) {
      const img = v.data
      switch (dialog.type) {
        case 'carousel':
          store.getActiveItems.carouselList = store.getActiveItems.carouselList.concat(
            img.map((item) => {
              if (isArray(item)) {
                return {
                  pic: item[0]?.original_image || '',
                  sensitiveImgs: item
                }
              } else {
                return {
                  pic: item.file_url,
                  width: item.width,
                  height: item.height,
                  size: item.file_size,
                  type: 'image/gif',
                  radio: item.width / item.height
                }
              }
            })
          )
          handleInputText(0)
          break
        case 'custome':
          store.getActiveItems.custome_avatar = img[0]?.file_url || ''
          delete store.getActiveItems.CustomeAvatarSensitiveImgs
          break
        case 'user':
          store.getActiveItems.user_avatar = img[0]?.file_url || ''
          delete store.getActiveItems.UserAvatarSensitiveImgs
          break
        case 'contextImg':
          if (isArray(img[0])) {
            ;(contextImg.value.pic = img[0][0]?.original_image || ''), (contextImg.value.textSensitiveImgs = img[0])
          } else {
            delete contextImg.value.textSensitiveImgs
            contextImg.value.pic = img[0]?.file_url || ''
            contextImg.value.fileInfo = img[0]
          }
          onResetActive()
          handleInputText(650)
          break
      }
    }
  }

  const store = useNgVisualizationStore()
  const route = useRoute()

  const props = defineProps({
    lType: {
      type: String,
      default: ''
    }
  })

  const ruleForm = ref(null)
  const state = reactive({
    loading: false,
    id: route.query.id,
    activityData: store.getActivityData,
    checkboxType: [],
    oldCheckboxType: null,
    seesstionList: [
      { value: 1, label: '允许会话' },
      { value: 2, label: '终止会话' }
    ],
    userClickList: [
      { value: 1, label: '继续会话' },
      { value: 2, label: '打开获客链接' },
      { value: 3, label: '网页跳转' },
      { value: 4, label: '跳转其他回答' }
    ],
    carouselType: [
      { label: '轮播图形式', value: 1 },
      { label: '列表形式', value: 2 }
    ],
    repeatOption: [
      { label: '允许重复回答', value: 1 },
      { label: '不允许重复回答', value: 2 }
    ]
  })

  // 可添加对话的条数
  const qaTotal = computed(() => {
    return store.getActiveItems.qa_total - store.getActiveItems.qaDataList.length
  })

  // 可添加回复的条数
  const replyTotal = computed(() => {
    // return store.getActiveItems.reply_total - store.getActiveItems.replyList.length
    return store.getActiveItems.reply_total
  })

  onMounted(() => {
    if (store.getActiveItems.carousel_type) {
      state.checkboxType = [store.getActiveItems.carousel_type]
      state.oldCheckboxType = store.getActiveItems.carousel_type
    }
    handleInputText(800)
  })

  // 验证码开关
  const onChangeVer = (val) => {
    console.log(val)
    if (val && !store.getActiveItems.verTips) {
      store.getActiveItems.verTips = store.defaultVerTips
    }
  }

  const onCheckbox = (checkedValues) => {
    console.log(
      checkedValues,
      cloneDeep(state.oldCheckboxType),
      cloneDeep(state.checkboxType),
      cloneDeep(store.getActiveItems),
      'checkbox'
    )
    setTimeout(() => {
      // 允许取消选中
      if (checkedValues.length === 0) {
        state.checkboxType = []
        store.getActiveItems.carousel_type = null
        state.oldCheckboxType = null
        return
      }

      // 处理单选逻辑
      const lastSelected = checkedValues.find((v) => v != state.oldCheckboxType)
      state.checkboxType = [lastSelected]
      store.getActiveItems.carousel_type = lastSelected
      state.oldCheckboxType = state.checkboxType.length ? state.checkboxType[0] : null
      handleInputText(300)
    }, 0)
  }

  const onChangeInput = (type) => {
    if (type == 'qa_name_alias') {
      store.getActivityData.name_alias = store.getActiveItems[type]
    } else if (type == 'qa_title') {
      store.getActivityData.title = store.getActiveItems[type]
    }
  }

  // 重置回复
  const onResetActive = () => {
    store.modifyQaActive(!store.getQaActive)
    // handleInputText(100)
  }

  const onSetItem = (item, index) => {
    store.modifyQaItemInfo({
      ...cloneDeep(item),
      subIndex: index,
      parIndex: item.id - 1
    })
  }

  // 问答自动回复
  const onAddDialogueGroup = (type) => {
    let group = {
      name: null,
      contextList: [
        {
          content: '',
          pic: null
        }
      ],
      rowBtnStyle: 4,
      btnRadius: 6, // 边框圆角
      delayClick: 0,
      replyList: [
        {
          content: '',
          sessionStatus: 1, // 回话状态 1.允许回话 2.终止会话
          clickStatus: 1, // 用户点击交互 1.继续会话 2.打开获客链接 3.网页跳转 4.跳转其他回答
          link: null,
          image: null,
          otherReply: null,
          endValue: null
        }
      ]
    }
    if (qaTotal.value) {
      store.getActiveItems.qaDataList.push({
        id: store.getActiveItems.qaDataList.length + 1,
        ...group
      })
      onResetActive()
      handleInputText()
    }
    store.qaIsShowVerify = false
  }

  // 问答操作
  const onQaMove = (item, index, type) => {
    if (type == 'up') {
      if (index > 0) {
        let temp = store.getActiveItems.qaDataList[index - 1]
        store.getActiveItems.qaDataList[index - 1] = store.getActiveItems.qaDataList[index]
        store.getActiveItems.qaDataList[index] = temp
      }
    } else if (type == 'down') {
      if (index < store.getActiveItems.qaDataList.length - 1) {
        let temp = store.getActiveItems.qaDataList[index + 1]
        store.getActiveItems.qaDataList[index + 1] = store.getActiveItems.qaDataList[index]
        store.getActiveItems.qaDataList[index] = temp
      }
    } else if (type == 'del') {
      if (store.getActiveItems.qaDataList.length <= 1) {
        return message.warning('至少保留一个对话组')
      }
      store.getActiveItems.qaDataList.splice(index, 1)
    }
    onResetActive()
  }

  // 添加回复
  const onAddReply = debounce((item, index) => {
    console.log(item, index)
    if (store.getActiveItems.reply_total >= item.replyList.length) {
      item.replyList.push({
        content: '',
        sessionStatus: 1,
        clickStatus: 1,
        link: null,
        image: null,
        otherReply: null,
        endValue: null
      })
      onResetActive()
    }
    console.log(cloneDeep(store.getActiveItems), 'sssssssssss')
  }, 100)

  // 删除回复
  const onDelReply = (item, index) => {
    if (item.replyList.length <= 1) {
      return message.warning('至少保留一个回复内容')
    }
    item.replyList.splice(index, 1)
    onResetActive()
    handleInputText(0)
  }

  // 删除对话
  const onDelDialogue = (item, index) => {
    if (item.contextList.length <= 1) {
      return message.warning('至少保留一项')
    }
    item.contextList.splice(index, 1)
    onResetActive()
    handleInputText(0)
  }

  // 添加对话
  const onAddDialogue = (item) => {
    item.contextList.push({
      content: '',
      pic: null
    })
    onResetActive()
  }

  // 删除欢迎语
  const onWelcomeDel = (index) => {
    store.getActiveItems.welcomeList.splice(index, 1)
  }

  // 添加欢迎语
  const onWelcomeAdd = () => {
    if (store.getActiveItems.welcomeList.length < store.getActiveItems.welcome_total) {
      store.getActiveItems.welcomeList.push({
        id: store.getActiveItems.welcomeList.length + 1,
        content: ''
      })
    }
  }

  const onUploadsItem = (val, item) => {
    if (isArray(val.content)) {
      item.pic = val.content[0]?.original_image || ''
      item.textSensitiveImgs = val.content
    } else {
      delete item.textSensitiveImgs
      item.pic = val.content
    }
    onResetActive()
  }

  // 上传
  const onUploads = debounce((val, type) => {
    let pic = val.content
    if (type == 'carousel') {
      if (isArray(val.content)) {
        store.getActiveItems.carouselList.push({
          pic: val.content[0]?.original_image || '',
          sensitiveImgs: val.content
        })
      } else {
        store.getActiveItems.carouselList.push({
          pic: val.content,
          width: val.fileInfo.width,
          height: val.fileInfo.height,
          size: val.file.size,
          type: val.fileInfo.type,
          radio: val.fileInfo.width / val.fileInfo.height
        })
      }
    }
    if (type == 'custome') {
      if (isArray(val.content)) {
        store.getActiveItems.CustomeAvatarSensitiveImgs = val.content
        pic = val.content[0]?.original_image || ''
      } else {
        delete store.getActiveItems.CustomeAvatarSensitiveImgs
      }
      store.getActiveItems.custome_avatar = pic
    }
    if (type == 'user') {
      if (isArray(val.content)) {
        store.getActiveItems.UserAvatarSensitiveImgs = val.content
        pic = val.content[0]?.original_image || ''
      } else {
        delete store.getActiveItems.UserAvatarSensitiveImgs
      }
      store.getActiveItems.user_avatar = val.content
    }
  }, 500)

  // 删除轮播图
  const onCarouselDel = (index) => {
    store.getActiveItems.carouselList.splice(index, 1)
    handleInputText(50)
  }

  function getCarouselHeight() {
    let totalHeight = 0
    let screenWidth = 375
    if (store.getActiveItems.carouselList?.length) {
      if (store.getActiveItems.carousel_type == 2) {
        store.getActiveItems.carouselList.forEach((item) => {
          let height = 0
          if (isArray(item?.sensitiveImgs)) {
            item.sensitiveImgs.forEach((v) => {
              if (v.fileInfo) height += (screenWidth / v.fileInfo.width) * v.fileInfo.height
            })
          } else {
            height = (screenWidth / item.width) * item.height || 0
          }
          totalHeight += height
        })
      } else {
        const newArr = store.getActiveItems.carouselList.map((item) => {
          let height = 0
          if (isArray(item?.sensitiveImgs)) {
            item.sensitiveImgs.forEach((v) => {
              if (v.fileInfo) height += (screenWidth / v.fileInfo.width) * v.fileInfo.height
            })
          } else {
            height = (screenWidth / item.width) * item.height || 0
          }
          return height
        })
        totalHeight = Math.max(...newArr)
      }
    }
    return totalHeight
  }

  function handleInputText(delay) {
    if (props.lType != '2') return
    const timer = setTimeout(() => {
      clearTimeout(timer)
      nextTick(() => {
        const totalHeight = getCarouselHeight()
        const ele = document.querySelector('#qachats-' + store.getActiveItems.uuid).querySelector('.dialog-box')
        console.log(ele?.offsetHeight, totalHeight, 'totalHeight')
        const height = !ele?.offsetHeight ? 585 : ele.offsetHeight + totalHeight + 110
        store.getActiveItems.eboxValue.height = height
        store.getActiveItems.styles.height = height / 100 + 'rem'
        const bgH = store.getActiveItems.eboxValue.y + height
        store.getActiveItems.eboxValue.bgHeight = bgH
        store.getActiveItems.boxBg.height = bgH + 'px'
      })
    }, delay || 50)
  }

  // 暴露表单引用给父组件
  defineExpose({
    validate: () => ruleForm.value?.validate()
  })
</script>

<style lang="scss" scoped>
  .pages {
    .header {
      width: 100%;
      height: 50px;
      padding: 18px 18px 0;
      border-bottom: 1px solid #eeeff3;
      font-weight: 500;
      font-size: 14px;
      color: #313233;
      line-height: 20px;
    }
    :deep(.my-wrap) {
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
      .ant-radio-group .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child,
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        border-color: #fe9d35;
        color: #fe9d35;
      }

      // .ant-radio-button-wrapper
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
        background-color: #fe9d35;
      }
      .ant-radio-button-wrapper:hover,
      .ant-radio-button-wrapper-checked:hover,
      .ant-radio-button-wrapper:hover::before {
        color: #fe9d35;
      }
      .ant-slider .ant-slider-track {
        background-color: #fe9d35;
      }
      .ant-slider .ant-slider-handle::after {
        box-shadow: 0 0 0 2px #fe9d35;
      }
      // .ant-slider-handle:focus::after {
      //   box-shadow: 0 0 0 4px #fe9d35;
      // }
    }

    .form {
      padding: 16px 32px 16px 26px;
      .item {
        // margin-bottom: 18px;
        .label {
          position: relative;
          width: 130px;
          // padding-right: 8px;
          // text-align: right;
        }
        .texta {
          position: relative;
          .close {
            display: none;
            position: absolute;
            right: -10px;
            top: -10px;
            padding: 5px;
            cursor: pointer;
            color: #ff4d4f;
          }
          &:hover {
            .close {
              display: block;
            }
          }
        }
      }
    }
    .block-group {
      .dialogue {
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e6e8ed;
        margin-bottom: 16px;
        .headers {
          padding: 24px 19px 16px 16px;
          :deep(.ant-btn) {
            height: 30px;
            padding: 2px 10px;
            margin-left: 16px;
            &.ant-btn-dangerous {
              background: #fff1f0;
              border-color: #ffccc7;
              color: #ff4d4f;
            }
          }
        }
        .btns {
          margin: 0px 52px 24px 16px;
        }
        .reply {
          .reply-item {
            margin-bottom: 18px;
            &:last-child {
              margin-bottom: 0;
            }
            .label {
              width: 120px;
              padding-right: 8px;
              font-size: 14px;
              color: #313233;
              line-height: 20px;
              // text-align: right;
            }
          }
        }
        .delete-tag {
          padding: 8px 4px;
          border: none;
          color: #ff4d4f;
        }
      }
      :deep(.ant-btn) {
        color: #313233;
        border-color: #d9d9d9;
      }
    }
    .uimgg-box {
      position: relative;
      width: 80px;
      height: 80px;
      background: #f7f9fc;
      border-radius: 4px;
      border: none;
      .plus {
        color: #a3a3a3;
        font-size: 23px;
      }
      span {
        margin-top: 7px;
        font-size: 12px;
        color: #a3a3a3;
        line-height: 16px;
      }
      .img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
      .delete-icon {
        position: absolute;
        right: 2px;
        top: 2px;
        width: 14px;
        height: 14px;
        margin-top: 0;
        cursor: pointer;
      }
    }
  }
  .require {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: -7px;
      top: 0px;
      font-size: 14px;
      color: #fe4d4f;
      line-height: 20px;
    }
  }
  .right-out-lined {
    font-size: 16px;
    color: #000;
    padding: 6px 0px 6px 0;
    cursor: pointer;
    transition: all 0.3s;
    &.rotate {
      transform: rotate(90deg);
      margin-top: -4px;
    }
  }
  :deep(.ant-form-item) {
    .ant-form-item-explain-error {
      margin-left: 130px;
    }
  }
  :deep(.welcome-texta) {
    .ant-form-item-explain-error {
      margin-left: 0;
    }
  }
  :deep(.qachatbx.wangeditor-v5__content) {
    border-radius: 8px;
    min-height: 200px;
    background: #fff;
    width: 405px;
    &.qachatbx-l {
      width: 286px;
    }
    .w-e-bar-item button {
      padding: 0 7px;
    }
    .w-e-text-container {
      background-color: #fff;
    }
  }
</style>
