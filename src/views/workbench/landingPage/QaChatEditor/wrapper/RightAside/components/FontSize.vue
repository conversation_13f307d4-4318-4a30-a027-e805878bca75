<template>
  <div id="elpopFontsize" class="input-inline flex-align" style="margin-left: 15px">
    <span class="left-l" style="padding-right: 3px">字号</span>
    <a-popover ref="popover" placement="bottom" trigger="focus" :overlayInnerStyle="{ padding: 0 }">
      <template #content>
        <div
          v-for="v in state.fontSizeList"
          :key="v"
          :class="['ropdown-item', { active: v == state.fontSize }]"
          @click="handleClickFontsize(v)"
        >
          {{ v }}
        </div>
      </template>
      <a-input-number
        v-model:value="state.fontSize"
        controls-position="right"
        size="medium"
        class="fix-input__number"
        :min="12"
        :max="288"
        :controls="false"
        @change="handleChange"
      />
    </a-popover>
  </div>
</template>

<script setup>
  import { reactive } from 'vue'

  const props = defineProps({
    size: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: [String, Number],
      default: null
    }
  })

  const emits = defineEmits(['inputText'])

  const state = reactive({
    fontSize: props.size,
    fontSizeList: [12, 14, 16, 18, 20, 22, 24]
  })

  function handleChange(val) {
    emits('inputText', { size: val, type: props.type })
  }
  function handleClickFontsize(val) {
    state.fontSize = val
    handleChange(val)
    // this.$refs.popover.doClose()
  }
</script>

<style lang="scss" scoped>
  .ropdown-item {
    width: 100px;
    font-size: 14px;
    color: #333;
    height: 30px;
    line-height: 30px;
    padding-left: 8px;
    cursor: pointer;
    &:hover {
      background-color: #eee;
    }
    &.active {
      background-color: rgba(78, 144, 255, 0.15);
    }
  }
</style>
