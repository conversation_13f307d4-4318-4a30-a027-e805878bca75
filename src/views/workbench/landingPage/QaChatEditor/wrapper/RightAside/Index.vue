<template>
  <div class="app-right-aside">
    <div :class="['panel-wrapper', { 'blank-panel': !store.getActiveItems.name }]">
      <div class="panel-body">
        <div v-if="store.getActiveItems.name" class="control-panel__inner">
          <NNgQaChat ref="qaChat" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { h, computed, ref } from 'vue'
  import NNgQaChat from './components/QaChat.vue'
  import { useNgVisualizationStore } from '@/store/ngVisualization.ts'
  const store = useNgVisualizationStore()

  const componentMap = {
    NgQaChat: NNgQaChat
  }
  const NCurrComponent = computed(() => {
    const componentName = store.getActiveItems.name
    return componentMap[componentName] || null // 添加默认值处理
  })

  const qaChat = ref(null)

  defineExpose({
    ngQaChat: () => qaChat.value
  })
</script>

<style lang="scss" scoped>
  .app-right-aside {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fff;
    .panel-wrapper {
      overflow-x: hidden;
    }
    .blank-panel {
      background-color: #f4f7f9;
    }
    .panel-body,
    .panel-wrapper {
      width: 100%;
      height: 100%;
    }
    .control-panel__inner {
      padding: 0;
    }
  }
</style>
