<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>表单管理</div>
      </template>
      <template #extra>
        <div class="btn_group">
          <a-button v-auth="['addForm']" type="primary" @click="onJump()" :disabled="pointData.balance <= 0"
            >新增表单</a-button
          >
        </div>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'name'">
                <a-tooltip>
                  <template #title>{{ scope.record.name }}</template>
                  <div class="text_overflow">{{ scope.record.name }}</div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'department_name'">
                <div style="min-width: 120px">{{ scope.record.department_name }}</div>
              </template>
              <template v-if="scope.column.key === 'desc'">
                <a-tooltip>
                  <template #title>{{ scope.record.desc }}</template>
                  <div class="text_overflow_row2">{{ scope.record.desc }}</div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'status'">
                <div class="flex_align_center" style="min-width: 100px">
                  <span class="round" :style="{ background: statusType(scope.record.status).color }"></span>
                  <span :class="{ font_red: scope.record.status === 2 }">
                    {{ statusType(scope.record.status).text }}
                  </span>
                </div>
              </template>
              <template v-if="scope.column.key === 'created_at_format'">
                <div style="min-width: 170px">{{ scope.record.created_at_format }}</div>
              </template>
              <template v-if="scope.column.key === 'creator'">
                <div style="min-width: 100px">{{ scope.record.creator }}</div>
              </template>
              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <a-button
                      v-auth="['editForm']"
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="onJump('edit', scope.record)"
                      >编辑</a-button
                    >
                    <a-popconfirm
                      title="是否确认删除当前表单？"
                      placement="topRight"
                      :disabled="pointData.balance <= 0"
                      @confirm="delItem(scope.record)"
                    >
                      <a-button
                        v-auth="['deleteForm']"
                        type="link"
                        size="small"
                        class="pa-0! ml-10px"
                        :disabled="pointData.balance <= 0"
                        >删除</a-button
                      >
                    </a-popconfirm>
                  </div>
                </div>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>
  </div>
</template>

<script setup>
  import datas from './src/data'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  const { pageChange, searchForm, onJump, delItem, enable, searchConfig, data, statusType, getList } = datas()
  getList()
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
</style>
