import http from '@/utils/request'
//时段报表
export const statement_account_latitude_hour = (data: any) => {
  return http('get', `/common/ad/v2/statement_account_latitude_hour`, data)
}
//企微列表
export const corpLatitudeApi = (data: any) => {
  return http('get', `/common/ad/statement_corp_latitude`, data)
}
//开口率分布
export const intervalStatApi = (data: any) => {
  return http('get', `/common/ad/chat_interval_stat`, data)
}
//企微，企微用户小时报表
export const latitudeHourApi = (data: any) => {
  return http('get', `/common/ad/statement_corp_latitude_hour`, data)
}
//企微用户报表接口
export const userLatitudeApi = (data: any) => {
  return http('get', `/common/ad/statement_user_latitude`, data)
}
export const fansFlowApi = (data: any) => {
  return http('get', `/common/fans_flow`, data)
}
