import { reactive } from 'vue'
import dayjs from 'dayjs'
export default function datas() {
  const searchList = reactive([
    {
      type: 'day',
      field: 'date',
      value: dayjs().format('YYYY-MM-DD'),
      props: {
        allowClear: false,
        disabledDate: (date: any) => {
          return dayjs(date).isAfter(dayjs(), 'day')
        }
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])

  const columns = reactive([
    { title: '时段', dataIndex: 'hour_str', width: 100, key: 'hour_str' },
    { title: '加粉数', dataIndex: 'fans_count', width: 100, key: 'fans_count' },
    {
      title: '加粉成本',
      dataIndex: 'fans_cost',
      width: 100,
      key: 'fans_cost'
    },
    {
      title: '开口数',
      dataIndex: 'open_talk_count',
      width: 120,
      key: 'open_talk_count'
    },
    {
      title: '开口率',
      dataIndex: 'open_talk_rate',
      width: 100,
      key: 'open_talk_rate'
    }
  ])
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  return { columns, searchList, actions }
}
