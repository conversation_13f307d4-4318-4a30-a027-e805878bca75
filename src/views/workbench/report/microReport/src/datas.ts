import { reactive, ref, computed } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { exportCreate, workListApi } from '@/api/common'
import { useDownloadCenter, usePoints } from '@/hooks'
import { mediaTypeSearch, getDatePresetsOptions } from '@/utils'
import { corpLatitudeApi, intervalStatApi, latitudeHourApi, userLatitudeApi } from '../index.api'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
const { pointData } = usePoints()
export default function datas() {
  const { goCenter } = useDownloadCenter()
  const schemas = ref([
    {
      field: 'media_type',
      type: 'select',
      value: '',
      props: {
        options: mediaTypeSearch,
        placeholder: '请选择媒体类型'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'corpid',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [],
        placeholder: '请选择企微'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      props: {
        presets: getDatePresetsOptions({ range: 'NearlyThirty' }),
        disabledDate: disabledDate
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = [
    { title: '企微名称', dataIndex: 'corp_name', key: 'corp_name', width: '200px', fixed: 'left' },
    { title: '成员数量', dataIndex: 'user_count', key: 'user_count', width: '120px' },
    // { title: '媒体类型', dataIndex: 'media_type', key: 'media_type', width: '100px' },
    { title: '加粉数', dataIndex: 'fans_count', width: '210px', key: 'fans_count' },
    { title: '加粉成本', dataIndex: 'fans_cost', width: '170px', key: 'fans_cost' },
    { title: '流速', dataIndex: 'flow_rate', key: 'flow_rate', width: '172px' },
    { title: '开口数', dataIndex: 'open_talk_count', key: 'open_talk_count', width: '168px' },
    { title: '开口率', dataIndex: 'open_talk_rate', key: 'open_talk_rate', width: '150px' },
    { title: '操作', dataIndex: 'handle', fixed: 'right', width: '200px', key: 'handle' }
  ]
  const columnsUser = [
    { title: '企微名称', dataIndex: 'corp_name', key: 'corp_name' },
    { title: '成员数量', dataIndex: 'user_count', key: 'user_count' },
    { title: '加粉数', dataIndex: 'fans_count', width: 120, key: 'fans_count' },
    { title: '加粉成本', dataIndex: 'fans_cost', width: 90, key: 'fans_cost' },
    { title: '流速', dataIndex: 'flow_rate', key: 'flow_rate', width: 120 },
    { title: '开口数', dataIndex: 'open_talk_count', key: 'open_talk_count', width: 120 },
    { title: '开口率', dataIndex: 'open_talk_rate', key: 'open_talk_rate', width: 120 },
    { title: '操作', dataIndex: 'handle', fixed: 'right', width: '120px', key: 'handle' }
  ]
  const openRangeData = ref({})
  const openTimeData = ref({})
  const hourNumData = ref({})
  const dates = ref()
  const state = reactive({
    sum: {} as any,
    query: {
      page: 1,
      page_size: 20,
      begin_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD')
    } as any,
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: null,
      media_type: []
    } as any,
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    defaultExpandedRowKeys: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'corpid',
      scroll: {
        scrollToFirstRowOnChange: true,
        x: 500
      },
      expandedRowKeys: [],
      dataSource: [],
      columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    },
    tableConfigOptions2: {
      bordered: false,
      loading: false,
      rowKey: 'adgroup_id',

      scroll: {
        scrollToFirstRowOnChange: false,
        x: 500
      },
      dataSource: [],
      columnsUser,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    },
    sumColumns: cloneDeep([{ checked: true, dataIndex: 'index' }, ...columns])
  })
  //顶部按钮置灰
  const isDisable = computed(() => {
    return !state.selectedRowKeys?.length || pointData.value.balance <= 0
  })

  //列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      state.tableConfigOptions.dataSource = []
      const resp = await corpLatitudeApi(state.query)
      let result = resp.data?.list || []
      result.forEach((v: any) => (v.showType = 1))
      state.tableConfigOptions.dataSource = result
      state.sum = resp.data?.sum || {}
      state.tableConfigOptions.pagination.total = resp.data?.total || resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1
      state.tableConfigOptions.expandedRowKeys = []
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const submitForm = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    }
    if (data.formData.created_at?.length && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.query.begin_time = data.formData.created_at[0]
      state.query.end_time = data.formData.created_at[1]
      state.query.created_at = undefined
    } else {
      state.query.begin_time = undefined
      state.query.end_time = undefined
      state.query.created_at = undefined
    }
    if (!data.status) {
      data.formData.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.query.begin_time = dayjs().format('YYYY-MM-DD')
      state.query.end_time = dayjs().format('YYYY-MM-DD')
    }
    state.query.page = 1
    if (data.type === 'export') {
      handleExportCreate()

      return false
    } else {
      getList()
    }
  }

  const pageChange = (pagination: any) => {
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  getList()
  //得到报表数据
  const getChildrenData = async (item) => {
    let params = {
      corpid: item.corpid,
      media_type: state.query?.media_type,
      page: 1,
      page_size: 1000,
      begin_time: state.query.begin_time,
      end_time: state.query.end_time
    }
    state.tableConfigOptions2.dataSource = []
    let resp = await userLatitudeApi(params)
    state.tableConfigOptions2.dataSource = resp.data?.list || []
    state.tableConfigOptions2.pagination.total = resp.data?.total || resp.data?.total_num || 0
    state.tableConfigOptions2.pagination.current = resp.data?.page || 1
    state.tableConfigOptions.dataSource.forEach((t) => {
      t.showType = 1
      if (t.corpid == item.corpid) {
        t.children = state.tableConfigOptions2.dataSource
        t.children.forEach((tt) => {
          tt.corp_name = ''
          tt.user_count = tt.user_name
          tt.showType = 2
        })
      }
    })
    console.log('ressss', resp, state.tableConfigOptions.dataSource)
  }
  const getWechatList = async () => {
    try {
      const { data } = await workListApi({ page: 1, page_size: 1000 })
      schemas.value.forEach((v) => {
        if (v.field === 'corpid') {
          v.props.options = data.list.map((item: any) => ({
            label: item.corp_name || '',
            value: item.corpid || ''
          }))
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getWechatList()
  //得到报表数据
  const getReportData = async (item) => {
    getChildrenData(item)
    let params = {
      corpid: item.corpid,
      media_type: state.query?.media_type,
      begin_time: state.query.begin_time,
      end_time: state.query.end_time
    }
    const openParmas = {
      // 开口时间分布 柱状图
      ...params,
      date: state.query.end_time
    }
    const hourParmas = {
      // 每小时加粉数及开口率
      ...params,
      date: state.query.end_time,
      only_chart: true
    }
    let [{ data: rangeData }, { data: openData }, { data: hourData }] = await Promise.all([
      intervalStatApi(params),
      intervalStatApi(openParmas),
      latitudeHourApi(hourParmas)
    ])
    openRangeData.value[item.corpid] = createOpenRangeData(rangeData)
    openTimeData.value[item.corpid] = createOpenTimeData(openData)
    hourNumData.value[item.corpid] = hourData.list && hourData.list.reverse((a, b) => a.hour - b.hour)
    console.log('hourData', hourData, hourNumData.value, item.corpid)
  }
  const createOpenRangeData = (refundData) => {
    console.log('222333', refundData)
    return {
      title: {
        text: '开口时间分布区间',
        left: 'center',
        textStyle: {
          fontSize: 16
        }
      },
      color: ['#EC907C', '#ECBE7A', '#B6A2DE'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(100, 100, 100, 0.72)',
        borderWidth: 0,
        textStyle: {
          color: '#FFFFFF'
        }
      },
      grid: {
        left: '10%',
        right: '10%'
      },

      series: [
        {
          name: '开口时间分布区间',
          type: 'pie',
          radius: ['0%', '60%'],
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}( {d}%)'
          },
          data: [
            {
              value:
                refundData['1min'].value +
                refundData['2min'].value +
                refundData['3min'].value +
                refundData['5min'].value,
              name: '5分钟'
            },
            {
              value: refundData['10min'].value + refundData['20min'].value + refundData['30min'].value,
              name: '30分钟'
            },
            { value: refundData['1hour'].value + refundData['>1hour'].value, name: '1小时' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }
  const createOpenTimeData = (payData) => {
    console.log('11111111,', payData)
    const timeKeys = ['1min', '2min', '3min', '5min', '10min', '20min', '1hour']
    const payTimeValues = timeKeys.map((key) => payData[key]?.value || 0)
    const payTimePer = timeKeys.map((key) => payData[key]?.per || 0)

    return {
      title: {
        text: '开口时间分布',
        left: 'center',
        textStyle: {
          fontSize: 16
        }
      },
      tooltip: {
        trigger: 'axis',
        // axisPointer: {
        //   type: 'shadow'
        // },
        backgroundColor: 'rgba(100, 100, 100, 0.72)',
        borderWidth: 0,
        textStyle: {
          color: '#FFFFFF',
          FontSize: '16px'
        }
      },
      color: ['#ECBE7A'],
      grid: {
        bottom: '9%',
        right: '3%'
      },
      xAxis: [
        {
          type: 'category',
          data: ['1分钟', '2分钟', '3分钟', '5分钟', '10分钟', '20分钟', '30分钟'],
          axisTick: {
            show: false
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false // 隐藏 y 轴的分隔线
          },
          splitArea: {
            show: true, // 显示 y 轴的分隔区域
            areaStyle: {
              color: ['#F9F9F9', '#fff'] // 设置 y 轴分隔区域的背景色为灰白相间
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: '60%',
          data: payTimeValues,
          label: {
            show: true,
            position: 'top',
            formatter: function (params) {
              console.log(params, 'params')
              let percentData = payTimePer[params.dataIndex]
              return params.data + '\n' + percentData
            }
          }
        }
      ]
    }
  }
  const showReport = (expand, item) => {
    const index = state.tableConfigOptions.expandedRowKeys.indexOf(item?.corpid)
    if (expand) {
      getReportData(item)
      if (index === -1) {
        // 如果 id 不在数组中，添加它
        state.tableConfigOptions.expandedRowKeys.push(item?.corpid)
      }
    } else {
      if (index > -1) {
        state.tableConfigOptions.expandedRowKeys.splice(index, 1)
      }
    }
  }
  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'invest_report_cory_latitude',
        params: JSON.stringify(state.query)
      })
      goCenter()
    } catch (error) {
      console.log(error)
    }
  }
  const onEvent = (data: { cmd: string }) => {
    state.dialog.visible = false
    getList()
  }
  const DIALOG_NAME: any = {
    quotaReport: {
      title: '扣量上报',
      width: 780
    },
    feedback: {
      title: '版位回传',
      width: 800
    },
    updataLink: {
      title: '更换链接',
      width: 600
    },
    callback_ratio: {
      title: '转化明细',
      width: 1170
    },
    PeriodReport: {
      title: `加粉时段报表`,
      width: 1170
    },
    flow_rate: {
      title: '',
      width: 600
    }
  }
  // 操作
  const handleChange = (type: any, record?: any) => {
    state.dialog.type = type
    state.dialog.data = record
    state.dialog.media_type = [record.media_type]
    if (type == 'PeriodReport') {
      if (record.showType == 1) {
        state.dialog.title = `加粉时段报表-${record.corp_name}`
      } else {
        state.dialog.title = `加粉时段报表-${record.corp_name}-${record.user_name}`
      }
    } else {
      state.dialog.title = DIALOG_NAME[type].title
    }

    state.dialog.width = DIALOG_NAME[type].width
    state.dialog.visible = true
  }
  // 日期选择
  const selectedDate = (val) => {
    dates.value = val
  }

  function disabledDate(current: any) {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  return {
    schemas,
    state,
    formConfig,
    columns,
    pageChange,
    onEvent,
    handleExportCreate,
    getList,
    submitForm,
    isDisable,
    handleChange,
    selectedDate,
    showReport,
    openRangeData,
    openTimeData,
    hourNumData
  }
}
