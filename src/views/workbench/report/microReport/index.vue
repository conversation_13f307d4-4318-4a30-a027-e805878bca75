<template>
  <div>
    <DesTablePage>
      <template #title>企微报表</template>
      <template #search>
        <SearchBaseLayout
          :data="schemas"
          :actions="formConfig"
          :showDateClear="false"
          @changeValue="submitForm"
          @selectedDate="selectedDate"
          :btnNames="isAuth('microReportExport') ? ['export'] : []"
        />
      </template>

      <template #tableWarp>
        <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange" @expand="showReport">
          <template #headerCell="{ scope }">
            <template v-if="scope.column.dataIndex == 'flow_rate'">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title>流速：当前时间往前推10分钟的加粉数据</template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.dataIndex === 'corp_name'">
              <div v-if="record.showType == 1">
                <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ record.corp_name }}</template>
                  <span class="goods_info_data_name">
                    {{ record.corp_name || '--' }}
                  </span>
                </a-tooltip>
                <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ record.corpid }}</template>
                  <span class="goods_info_data_name number-id" style="word-break: break-all"
                    >ID：{{ record.corpid }}</span
                  >
                </a-tooltip>
              </div>
            </template>

            <template v-if="column.dataIndex === 'user_count'">
              <div v-if="record.showType == 2">
                <a-tooltip :overlayInnerStyle="{ width: 'max-content' }" placement="topLeft">
                  <template #title>{{ record.user_count }}</template>
                  <span class="goods_info_data_name">
                    {{ record.user_count || '--' }}
                  </span>
                </a-tooltip>
                <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ record.user_id }}</template>
                  <span class="goods_info_data_name number-id" style="word-break: break-all"
                    >ID：{{ record.user_id }}</span
                  >
                </a-tooltip>
              </div>
            </template>

            <template v-if="column.dataIndex === 'open_talk_rate'">
              <span>{{ record.open_talk_rate || 0 }}%</span>
            </template>
            <template v-if="column.dataIndex === 'flow_rate'">
              <div class="cursor-pointer" @click="handleChange('flow_rate', record)">
                <span class="c-#52c41a">{{ record.flow_rate || 0 }}/</span>
                <span class="c-#B6AFAF">10min</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'ctr'">
              <span>{{ record.ctr || 0 }}%</span>
            </template>
            <template
              v-if="['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost'].includes(column.dataIndex)"
            >
              <span>¥{{ centsToYuan(record[column.dataIndex] || 0) }}</span>
            </template>
            <template v-if="column.dataIndex === 'fans_cost'">
              <span>¥{{ centsToYuan(record.fans_cost || 0) }}</span>
              <div
                class="flex-y-center text_overflow_row1"
                v-if="record.set_fans_cost && record.fans_cost > record.set_fans_cost"
              >
                <ExclamationCircleOutlined class="c-red font-size-12px" />
                <div class="c-red ml-4px font-size-12px h-12px line-height-12px">超出成本限制</div>
              </div>
            </template>

            <template v-if="column.dataIndex === 'handle'">
              <a-button class="h-auto pa-0" type="link" @click="handleChange('PeriodReport', record)"
                >时段报表</a-button
              >
            </template>
          </template>
          <template #expandedRowRender="{ scope: { record, column, index, indent, expanded } }">
            <div style="width: 100%">
              <div
                class="flex justify-between pa-6px"
                :class="isMobile ? 'flex-col echarts-mobile' : ''"
                v-if="expanded && hourNumData[record.corpid] && record.showType == 1"
              >
                <div class="item-echarts left pt-20px">
                  <Echarts
                    :id="'refundTime' + record.corpid"
                    :data="openRangeData[record.corpid]"
                    parentHeight="270px"
                    style="width: 100%; height: 100%"
                  />
                </div>
                <div class="item-echarts center">
                  <Echarts
                    :id="'payTime' + record.corpid"
                    parentHeight="270px"
                    :data="openTimeData[record.corpid]"
                    style="width: 100%; height: 100%"
                  />
                </div>
                <div class="item-echarts right">
                  <periodEchart
                    style="width: 100%; height: 100%"
                    :id="'orderNum' + record.corpid"
                    :data="hourNumData[record.corpid]"
                    type="children"
                  />
                </div>
              </div>
            </div>
          </template>

          <template #summary>
            <a-table-summary fixed>
              <a-table-summary-row>
                <template v-for="(item, index) in state.sumColumns">
                  <template v-if="['corp_name'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index"
                      >总计：{{ state.tableConfigOptions.pagination.total }}</a-table-summary-cell
                    >
                  </template>
                  <template v-else-if="['open_talk_rate'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="state.sum?.[item.dataIndex] && state.sum?.[item.dataIndex] !== 0"
                        >{{ state.sum[item.dataIndex] }}%</span
                      >
                    </a-table-summary-cell>
                  </template>
                  <template v-else-if="['fans_cost'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span>¥{{ centsToYuan(state.sum[item.dataIndex] || 0) }}</span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else-if="['flow_rate'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="!state.sum?.[item.dataIndex]"></span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else>
                    <a-table-summary-cell :index="index">{{ state.sum[item.dataIndex] }}</a-table-summary-cell>
                  </template>
                </template>
              </a-table-summary-row>
            </a-table-summary>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      :centered="true"
    >
      <PowderEchart v-if="state.dialog.type === 'flow_rate'" :data="state.dialog.data" />
      <PeriodReport
        v-if="state.dialog.type === 'PeriodReport'"
        :item="state.dialog.data"
        :time="state.query.end_time"
      />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import datas from './src/datas'
  import { mediaType2Content, centsToYuan } from '@/utils'
  import PeriodReport from './components/PeriodReport.vue'
  import { ExclamationCircleOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
  import periodEchart from './components/periodEchart.vue'
  import PowderEchart from './components/powderEchart.vue'
  import { useApp, useAuth } from '@/hooks'
  const { isMobile } = useApp()
  const { isAuth } = useAuth()
  const {
    schemas,
    state,
    formConfig,
    pageChange,
    submitForm,
    handleChange,
    selectedDate,
    showReport,
    openRangeData,
    openTimeData,
    hourNumData
  } = datas()
</script>
<style lang="scss" scoped>
  :deep(.ant-table-expanded-row-fixed) {
    // width: 100% !important;
    padding: 0;
  }
  :deep(.ant-table-row-level-1) {
    .ant-table-row-expand-icon {
      display: none;
    }
  }
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .welcome {
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-sizing: border-box;
    &:hover {
      background-color: #ffe7ce;
      color: #e87800;
      border: 1px solid #fe9d35;
    }
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
  .rotate {
    transform: rotate(-90deg); // 设置旋转效果为 90 度
  }
  .item-echarts {
    position: relative;
    padding: 16px 0 0px 0;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(198, 198, 198, 0.33);
    border-radius: 8px;
  }
  .left {
    width: 25%;
  }
  .center {
    width: 28%;
  }
  .right {
    width: 45%;
  }
  .echarts-mobile {
    .left {
      width: 100%;
    }
    .center {
      width: 100%;
    }
    .right {
      width: 100%;
    }
  }
</style>
