<template>
  <div class="flex" :class="[isMobile ? 'eachart_grid-wrapper1' : 'eachart_grid-wrapper']">
    <div class="echart-wrapper">
      <div class="mb-8px" :class="[isMobile ? 'flex-col' : 'flex flex-justify-between']">
        <h2 class="flex-y-center" :class="[isMobile && 'flex-col align-items-start']">
          <div class="mr-8px font-size-16px">加粉数-{{ data.showType == 1 ? data.corp_name : data.user_name }}</div>
        </h2>
      </div>
      <Echarts key="left" id="left_echarts" :data="state.setOption_1" parentHeight="270px" />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { reactive, watch } from 'vue'
  import { fansFlowApi } from '../index.api'
  import { useApp } from '@/hooks'
  import dayjs from 'dayjs'
  const { isMobile } = useApp()
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const state = reactive({
    horValue: 1,
    setOption_1: {},
    setOption_2: {},
    days: [],
    hours: [],
    historyDay: ''
  })
  const init = async () => {
    try {
      let params = {
        type: props.data.showType == 2 ? 5 : 4,
        value: props.data.showType == 2 ? props.data.user_id : props.data.corpid
      }
      let res: any = await fansFlowApi(params)
      if (res.code === 0) {
        console.log(res)
        leftEcharts(res.data.list)
      }
    } catch (err) {
      console.log(err)
    }
  }
  const leftEcharts = (data: any) => {
    let xAxisData = data.map((v: any) => {
      return dayjs(v.interval).format('HH:mm')
    })

    let yAxisData1 = data.map((v: any) => {
      return {
        value: v.fan_count
      }
    })
    state.setOption_1 = {
      legend: {
        top: 6,
        right: 1,
        itemHeight: 6,
        itemWidth: 16,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.name}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                  <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                  <span style="display: inline-block;width:52px;margin-right:16px">${v.seriesName}</span>
                  <span>${v.value}</span>
                  </div>
                  `
          })
          return html
        }
      },
      grid: {
        left: 1,
        right: 10,
        top: '16%',
        bottom: '1%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '加粉数',
          color: '#FE005A',
          type: 'line',
          smooth: true,
          symbolSize: 4,
          label: {
            show: true,
            position: 'top',
            formatter: function (params: any) {
              return params.value == 0 ? '' : params.value
            }
          },
          areaStyle: {
            normal: {
              color: 'rgba(254,0,90,0.1)'
            }
          },
          lineStyle: {
            normal: {
              color: '#FE005A'
            }
          },
          data: yAxisData1
        }
      ]
    }
  }

  watch(
    () => props.data,
    (val: any) => {
      if (val) {
        init()
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .eachart {
    padding: 24px;
    background: #fff;
    position: relative;
    border-radius: 16px;
  }
  .eachart_grid-wrapper {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* 默认1列 */
  }
  .eachart_grid-wrapper1 {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* 默认1列 */
  }
  .align-items-start {
    align-items: start;
  }
</style>
