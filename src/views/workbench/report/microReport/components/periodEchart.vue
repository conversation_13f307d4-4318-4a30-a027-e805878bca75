<template>
  <div class="echart-wrapper">
    <div class="title" :class="type === 'children' ? 'ml16px' : ''">
      <h2>
        <span>每小时加粉数及开口率</span>
        <span class="c-#888 font-size-12px ml-8px fw-400" v-if="time">统计时间：{{ time }}</span>
      </h2>
    </div>
    <div class="eachart">
      <Echarts
        key="right"
        :id="id"
        @legendselectchanged="legendselectchanged"
        :data="state.options"
        parentHeight="270px"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { reactive, watch } from 'vue'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: 'right_echarts'
    },
    time: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: '' // children or parent
    }
  })
  const state = reactive({
    options: {}
  })
  function transformToStackedOverlap(data1, data2) {
    // 首先确保两个数组长度相同
    if (data1.length !== data2.length) {
      throw new Error('Data arrays must have the same length')
    }

    // 创建结果数组
    const result1 = []
    const result2 = []

    for (let i = 0; i < data1.length; i++) {
      const item1 = data1[i]
      const item2 = data2[i]

      // 检查时间是否匹配
      if (item1.overview_hour !== item2.overview_hour) {
        throw new Error(`Time mismatch at index ${i}: ${item1.overview_hour} vs ${item2.overview_hour}`)
      }

      const val1 = item1.value
      const val2 = item2.value

      // 确定较小值和较大值
      let minVal, maxVal, minItem, maxItem
      if (val1 <= val2) {
        minVal = val1
        maxVal = val2
        minItem = item1
        maxItem = item2
      } else {
        minVal = val2
        maxVal = val1
        minItem = item2
        maxItem = item1
      }

      // 计算差值
      const diff = maxVal - minVal

      // 构建结果对象 - data1取较小值但保持原始type
      result1.push({
        value: minVal,
        overview_hour: minItem.overview_hour,
        type: minItem.type,
        open_talk_rate: minItem.open_talk_rate,
        fans_count: minItem.fans_count, // 加粉数
        open_talk_count: minItem.open_talk_count // 开口数
      })

      // data2取差值但使用另一个数据的type
      result2.push({
        value: diff,
        overview_hour: maxItem.overview_hour,
        type: maxItem.type,
        open_talk_rate: minItem.open_talk_rate,
        fans_count: maxItem.fans_count, // 加粉数
        open_talk_count: maxItem.open_talk_count // 开口数
      })
    }

    return { data1: result2, data2: result1 }
  }
  const legendselectchanged = (val: any) => {
    init(Object.values(val.selected).every((it) => it) ? false : true)
  }
  const init = (isSelected: boolean = false) => {
    let xAxisData = props.data && props.data.map((v: any) => v.hour_str)
    console.log('1123123', props.data)
    let data1 =
      props.data &&
      props.data.map((v: any) => {
        return {
          type: 'fans',
          value: v.fans_count,
          overview_hour: v.hour_str,
          fans_count: v.fans_count, // 加粉数
          open_talk_count: v.open_talk_count, // 开口数
          open_talk_rate: v.open_talk_rate // 开口率
        }
      })
    let data2 = props.data.map((v: any) => {
      return {
        type: 'open',
        value: v.open_talk_count,
        overview_hour: v.hour_str,
        fans_count: v.fans_count, // 加粉数
        open_talk_count: v.open_talk_count, // 开口数
        open_talk_rate: v.open_talk_rate // 开口率
      }
    })
    let yAxisData1 = isSelected ? data1 : transformToStackedOverlap(data1, data2).data1
    let yAxisData2 = isSelected ? data2 : transformToStackedOverlap(data1, data2).data2
    state.options = {
      legend: {
        right: 10,
        itemHeight: 4,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.data?.overview_hour}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">${v.seriesName}</span>
                <span>${v.data.type === 'fans' ? v.data.fans_count : v.data.open_talk_count}</span></div>`
          })
          if (params.length) {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;">
                  <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#C1FF62"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">开口率</span>
                <span>${params?.[0]?.data?.open_talk_rate || 0}%</span></div>`
          }
          return html
        }
      },
      grid: {
        left: 1,
        right: '1%',
        top: '14%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 'auto',
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            interval: 0,
            margin: 16,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          axisTick: {
            alignWithLabel: true, // 让刻度线与标签对齐
            interval: 'auto'
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '开口数',
          color: '#FF826F',
          type: 'bar',
          stack: '总量',
          z: 10,
          barMaxWidth: 20,
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: function (params) {
                // 根据数据值返回不同颜色
                const type = params.data.type

                return type == 'open' ? '#FF826F' : '#FF826F' // 橙色
              },
              barBorderRadius: 0
            }
          },
          data: yAxisData2
        },
        {
          name: '加粉数',
          color: '#FEB24E',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 20,
          z: 20,
          barGap: '10%',
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: function (params) {
                // 根据数据值返回不同颜色
                const type = params.data.type

                return type == 'open' ? '#FEB24E' : '#FEB24E' // 橙色
              },
              barBorderRadius: 0
            }
          },
          data: yAxisData1
        }
      ]
    }
  }
  watch(
    () => props.data,
    (val) => {
      init()
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .echart-wrapper {
    position: relative;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > h2 {
        font-weight: 500;
        font-size: 16px;
        color: #222122;
        line-height: 22px;
      }
      .btn {
        .ant-radio-button-wrapper {
          height: 24px;
          line-height: 24px;
        }
      }
    }
    .eachart {
      padding: 10px;
    }
  }
</style>
