<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>实时访客</div>
      </template>
      <template #search>
        <SearchBaseLayout
          :data="searchConfig.data"
          @changeValue="searchForm"
          :actions="searchConfig.options"
          :key="data.key"
        />
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.key == 'ad_name'">
              <div class="flex" style="align-items: flex-start; height: 40px">
                <div class="ad_platform_box mt-3px">
                  <img
                    class="ad_platform_img"
                    v-if="record.ad_platform == 1"
                    :src="requireImg('media/gdt.png')"
                    alt=""
                  />
                  <img
                    class="ad_platform_img"
                    v-if="record.ad_platform == 2"
                    :src="requireImg('media/cili.png')"
                    alt=""
                  />
                  <img
                    class="ad_platform_img"
                    v-if="record.ad_platform == 3"
                    :src="requireImg('media/jl.png')"
                    alt=""
                  />
                </div>
                <div v-if="record.ad_name.length > 15">
                  <a-tooltip>
                    <template #title>{{ record.ad_name }}</template>
                    <div class="text_overflow_row1 flex-1">{{ record.ad_name }}</div>
                  </a-tooltip>
                </div>
                <div class="flex-1" v-else>
                  <div class="line-clamp-2">{{ record.ad_name }}</div>
                </div>
              </div>
            </template>

            <template v-if="column.dataIndex === 'ad_account_name'">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ record.ad_account_name || '--' }}</template>
                  <div class="ad_platform_box">
                    <div class="text_overflow_row1">{{ record.ad_account_name || '--' }}</div>
                  </div>
                </a-tooltip>
                <span class="number-id" v-if="record.tr_user_id">ID：{{ record.tr_user_id }}</span>
                <div
                  class="c-red text_overflow_row1 cursor-pointer"
                  @click="adAuth(record.ad_platform)"
                  v-if="!record.ad_account_name && record.tr_account_id"
                >
                  <InfoCircleOutlined /> 账户未授权，请点击前往授权
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'isp_domain'">
              <div>{{ record.isp_domain || '--' }}</div>
            </template>
            <template v-if="column.dataIndex === 'ad_group_name'">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ record.ad_group_name || '--' }}</template>
                  <div class="flex-y-center">
                    <div class="goods_info_data_name">
                      {{ record.ad_group_name || '--' }}
                    </div>
                  </div>
                </a-tooltip>
                <span class="number-id" v-if="record.tr_ad_id != '__PROMOTION_ID__'">ID：{{ record.tr_ad_id }}</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'ad_h5_name'">
              <a-tooltip placement="topLeft" v-if="record.ad_h5_name">
                <template #title>{{ record.ad_h5_name || '--' }}</template>
                <div class="text_overflow_row1">{{ record.ad_h5_name || '--' }}</div>
              </a-tooltip>
              <div v-else>--</div>
            </template>
            <template v-if="column.dataIndex === 'ip'">
              <div class="c-#B6AFAF flex-y-center" v-if="record.is_blacklist">
                <span>{{ record.ip }}</span>
                <span class="tag-wrapper font-size-12px! line-height-16px!">IP黑名单</span>
              </div>
              <div v-else>{{ record.ip }}</div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
  </div>
</template>

<script setup>
  import { requireImg, callbackStatus2Content } from '@/utils'
  import datas from './data'
  import { InfoCircleOutlined } from '@ant-design/icons-vue'
  const {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    onSelectChange,
    adAuth,
    IpClick
  } = datas()
  getList()
</script>

<style lang="scss" scoped>
  .ad_platform_box {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .ad_platform_img {
      width: 15px;
      height: 15px;
      margin-right: 6px;
      display: block;
    }
  }
  .tag-wrapper {
    margin-left: 4px;
    flex: none;
    display: inline-block;
    padding: 2px 8px;
    font-size: 14px;
    color: #2b2b2b;
    border: 1px solid #d9d9d9;
    background: #f1f1f1;
    border-radius: 4px;
  }
</style>
