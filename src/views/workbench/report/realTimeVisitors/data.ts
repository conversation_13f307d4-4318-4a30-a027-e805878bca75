import { reactive, ref, createVNode } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { setRoleList } from './index.api'

import { mediaTypeSearch, getDatePresetsOptions } from '@/utils'
import { ad_ip_add } from '@/views/workbench/nameList/blacklist/index.api'
import dayjs from 'dayjs'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        field: 'ad_platform',
        type: 'select',
        value: '',
        props: {
          options: mediaTypeSearch,
          placeholder: '请选择媒体类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_account_name',
        value: undefined,
        props: {
          placeholder: '请输入账户名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_account_id',
        value: undefined,
        props: {
          placeholder: '请输入账户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_ad_group_name',
        value: undefined,
        props: {
          placeholder: '请输入计划名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_ad_group_id',
        value: undefined,
        props: {
          placeholder: '请输入计划ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'ad_link_name',
        value: undefined,
        props: {
          placeholder: '请输入投放链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'ad_h5_name',
        value: undefined,
        props: {
          placeholder: '请输入落地页名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'ip',
        value: undefined,
        props: {
          placeholder: '请输入IP'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'created_map',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        props: {
          presets: getDatePresetsOptions({ range: 'NearlyThirty' })
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1200
    },
    dataSource: [],
    columns: [
      {
        title: '投放链接',
        dataIndex: 'ad_name',
        key: 'ad_name',
        width: 100,
        slot: true
      },

      {
        title: '账户信息',
        dataIndex: 'ad_account_name',
        key: 'ad_account_name',
        slot: true,
        width: 160
      },
      {
        title: '计划信息',
        dataIndex: 'ad_group_name',
        key: 'ad_group_name',
        slot: true,
        width: 140
      },
      {
        title: '落地页',
        dataIndex: 'ad_h5_name',
        key: 'ad_h5_name',
        width: 100,
        slot: true
      },
      {
        title: 'IP',
        dataIndex: 'ip',
        key: 'ip',
        width: 210
      },
      {
        title: '地区',
        dataIndex: 'city',
        key: 'city',
        width: 120,
        slot: true
      },
      {
        title: '电信服务商',
        dataIndex: 'isp_domain',
        key: 'isp_domain',
        width: 100,
        slot: true
      },
      {
        title: '设备',
        dataIndex: 'device_model',
        key: 'device_model',
        width: 80,
        slot: true
      },
      {
        title: '访问时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 160,
        slot: true
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    selectedRowKeys: [],
    modalOpen: false,
    modalType: '',
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20,
      created_map: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    key: 0
  })
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await setRoleList(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    if (!v.status) {
      data.params.created_map = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      data.key++
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type, item) => {
    data.info = item
    data.modalType = type
    data.modalOpen = true
  }
  const delItem = async (row) => {
    try {
    } catch (error) {
      console.error(error)
    }
  }
  const handleOk = async () => {
    data.modalOpen = false
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    data.selectedRowKeys = selectedRowKeys
    console.log(data.selectedRowKeys)
  }
  const IpClick = (item: any) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否要将该IP拉入黑名单？'),
        async onOk() {
          await ad_ip_add({
            type: 1,
            ip: item.ip
          })
          message.success('拉黑成功')
          getList()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const ad_type = {
    3: {
      name: 'oceanEngine' //巨量引擎页
    },
    1: {
      name: 'GuangDian' //巨量引擎页
    }
  }
  // 跳转广告授权
  const adAuth = (type: number) => {
    router.push(ad_type[type])
  }
  return {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    searchConfig,
    data,
    getList,
    handleOk,
    onSelectChange,
    adAuth,
    IpClick
  }
}
