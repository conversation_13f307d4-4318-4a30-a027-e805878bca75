<template>
  <div class="echart-wrapper">
    <div class="title mb-10px">
      <h2>
        <span>{{ state.title }}</span>
      </h2>
      <a-button v-show="isMore" @click="goBack">返回</a-button>
    </div>
    <Echarts
      v-if="!isMore && state.list?.length"
      key="bottom"
      id="bottom_echarts"
      :data="state.options"
      parentHeight="270px"
      @echartClick="echartClick"
    />
    <Empty v-if="!state.list?.length && !isMore" />
    <Echarts v-if="isMore" id="account_single_rank" parentHeight="270px" :data="state.setOption" />
  </div>
</template>
<script setup lang="ts">
  import Empty from './empty.vue'
  import { reactive, watch, ref } from 'vue'
  import { centsToYuan } from '@/utils'
  import { accountWeekData, accountRank } from '../index.api'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const isMore = ref(false)
  const state = reactive({
    options: {},
    setOption: {},
    title: '账户消耗排行',
    list: [] as any
  })

  const init = () => {
    console.log('====')
    let echartData = [
      {
        name: '致奥ad-sq-宏硕百货商行-2',
        value1: 100,
        value2: 233
      },
      {
        name: '致奥ad-sq-宏硕百货商行-2',
        value1: 138,
        value2: 233
      }
    ]

    let xAxisData = echartData.map((v) => v.name)
    //  ["1", "2", "3", "4", "5", "6", "7", "8"]
    let yAxisData1 = echartData.map((v) => v.value1)
    // [100, 138, 350, 173, 180, 150, 180, 230]
    let yAxisData2 = echartData.map((v) => v.value2)
    state.options = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.name}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">${v.seriesName}</span>
                <span>${v.value}</span>
                </div>
                `
          })
          if (params.length) {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#C1FF62"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">开口率</span>
                <span>10%</span>
              </div>
              <div style="color: #fff;font-size: 12px;line-height: 17px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#FF753A"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">加粉数</span>
                <span>200</span>
              </div>`
          }
          return html
        }
      },
      grid: {
        left: '1%',
        right: '5%',
        top: '14%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            interval: 0,
            margin: 16,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          axisTick: {
            alignWithLabel: true, // 让刻度线与标签对齐
            interval: 'auto'
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '消耗金额',
          color: '#F4B776',
          type: 'bar',
          stack: '总量',
          z: 10,
          barMaxWidth: 310,
          barCategoryGap: '20%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: '#F4B776',
              barBorderRadius: 0
            }
          },
          data: yAxisData2
        },
        {
          name: '开口数',
          color: '#A17EE4',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 310,
          barGap: '20%',
          z: 2,
          barCategoryGap: '20%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: '#A17EE4'
            }
          },
          data: yAxisData1
        }
      ]
    }
  }
  function transformToStackedOverlap(data1, data2) {
    // 首先确保两个数组长度相同
    if (data1.length !== data2.length) {
      throw new Error('Data arrays must have the same length')
    }

    // 创建结果数组
    const result1 = []
    const result2 = []

    for (let i = 0; i < data1.length; i++) {
      const item1 = data1[i]
      const item2 = data2[i]

      // 检查时间是否匹配
      if (item1.overview_hour !== item2.overview_hour) {
        throw new Error(`Time mismatch at index ${i}: ${item1.overview_hour} vs ${item2.overview_hour}`)
      }

      const val1 = item1.value
      const val2 = item2.value

      // 确定较小值和较大值
      let minVal, maxVal, minItem, maxItem
      if (val1 <= val2) {
        minVal = val1
        maxVal = val2
        minItem = item1
        maxItem = item2
      } else {
        minVal = val2
        maxVal = val1
        minItem = item2
        maxItem = item1
      }

      // 计算差值
      const diff = maxVal - minVal

      // 构建结果对象 - data1取较小值但保持原始type
      result1.push({
        value: minVal,
        name: minItem.name,
        color: minItem.color,
        type: minItem.type,
        cost: minItem.cost, // 消耗金额
        fans_count: minItem.fans_count, // 加粉数
        open_talk_count: minItem.open_talk_count, // 开口数
        open_talk_rate: minItem.open_talk_rate, // 开口率
        account_id: minItem.account_id
      })

      // data2取差值但使用另一个数据的type
      result2.push({
        value: diff,
        name: maxItem.name,
        color: maxItem.color,
        type: maxItem.type,
        cost: maxItem.cost, // 消耗金额
        fans_count: maxItem.fans_count, // 加粉数
        open_talk_count: maxItem.open_talk_count, // 开口数
        open_talk_rate: maxItem.open_talk_rate, // 开口率
        account_id: maxItem.account_id
      })
    }

    return { data1: result1, data2: result2 }
  }
  // 账户排行数据
  const getAccountInfo = async () => {
    try {
      let params = {
        begin_time: props.data.begin_time,
        end_time: props.data.end_time,
        media_type: props.data.media_type,
        product_id: props.data.product_id
      }
      let res = await accountRank(params)
      let list = res.data.list || []
      state.list = list
      let x_data = []
      let yAxisData1: any = []
      let yAxisData2: any = []

      if (list && list.length > 0) {
        x_data = list.map((item: any) => item.account_name)
        let data1 = list.map((item: any) => ({
          value: centsToYuan(item.cost),
          type: 'xiaohao',
          color: '#F4B776',
          name: '消耗金额',
          cost: centsToYuan(item.cost), // 消耗金额
          fans_count: item.fans_count, // 加粉数
          open_talk_count: item.open_talk_count, // 开口数
          open_talk_rate: item.open_talk_rate, // 开口率
          account_id: item.account_id
        }))
        let data2 = list.map((item: any) => ({
          value: item.open_talk_count,
          type: 'kaikou',
          color: '#A17EE4',
          name: '开口数',
          cost: centsToYuan(item.cost), // 消耗金额
          fans_count: item.fans_count, // 加粉数
          open_talk_count: item.open_talk_count, // 开口数
          open_talk_rate: item.open_talk_rate, // 开口率
          account_id: item.account_id
        }))
        yAxisData1 = transformToStackedOverlap(data1, data2).data1
        yAxisData2 = transformToStackedOverlap(data1, data2).data2
      } else {
        x_data = []
        yAxisData1 = []
        yAxisData2 = []
      }

      state.options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              type: 'dashed',
              color: '#FE9D35'
            }
          },
          extraCssText:
            'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;white-space:normal;',
          formatter: function (params) {
            let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;word-break:break-word">${params[0]?.name}</div>`
            params.forEach((v) => {
              html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.data.color || v.color}"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">${v.data.name || v.seriesName}</span>
                <span>${v.data.type === 'xiaohao' ? v.data.cost : v.data.open_talk_count}</span>
                </div>
                `
            })
            if (params.length) {
              html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#C1FF62"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">开口率</span>
                <span>${params?.[0]?.data?.open_talk_rate}%</span>
              </div>
              <div style="color: #fff;font-size: 12px;line-height: 17px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#FF753A"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">加粉数</span>
                <span>${params?.[0]?.data?.fans_count}</span>
              </div>`
            }
            return html
          }
        },
        grid: {
          left: '1%',
          right: '1%',
          top: '14%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#242F57',
                fontSize: '12px'
              }
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false,
              onZero: false,
              lineStyle: {
                color: '#D9D9D9'
              }
            },
            data: x_data
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              interval: 0,
              margin: 16,
              textStyle: {
                color: '#242F57',
                fontSize: '12px'
              },
              formatter: function (value: any) {
                // 根据数值动态设置 Y 轴单位
                if (value >= 100000000) {
                  return value / 100000000 + '亿'
                } else if (value >= 10000000) {
                  return value / 10000000 + '千万'
                } else if (value >= 1000000) {
                  return value / 1000000 + '百万'
                } else if (value >= 10000) {
                  return value / 10000 + '万'
                } else {
                  return value
                }
              }
            },
            axisTick: {
              alignWithLabel: true, // 让刻度线与标签对齐
              interval: 'auto'
            },
            nameTextStyle: {
              color: '#666',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                type: 'solid',
                color: '#f3f3f3'
              }
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '开口数',
            color: '#A17EE4',
            type: 'bar',
            stack: '总量',
            barMaxWidth: 310,
            barGap: '20%',
            z: 20,
            barCategoryGap: '20%', // 可选的类目间间隔调整
            itemStyle: {
              normal: {
                color: function (params) {
                  // 根据数据值返回不同颜色
                  const type = params.data.type

                  return type == 'kaikou' ? '#A17EE4' : '#F4B776' // 橙色
                }
              }
            },
            data: yAxisData1
          },
          {
            name: '消耗金额',
            color: '#F4B776',
            type: 'bar',
            stack: '总量',
            z: 10,
            barMaxWidth: 310,
            barCategoryGap: '20%', // 可选的类目间间隔调整
            itemStyle: {
              normal: {
                color: function (params) {
                  // 根据数据值返回不同颜色
                  const type = params.data.type
                  return type == 'kaikou' ? '#A17EE4' : '#F4B776' // 橙色
                },
                barBorderRadius: 0
              }
            },
            data: yAxisData2
          }
        ]
      }
    } catch (error) {}
  }
  //账户排行点击事件
  const echartClick = (params: any) => {
    isMore.value = true
    state.title = params.name

    getAccountWeek(params.data.account_id)
  }
  //近一周的数据
  const getAccountWeek = async (account_id: string) => {
    try {
      let params = {
        begin_time: props.data.begin_time,
        end_time: props.data.end_time,
        media_type: props.data.media_type,
        account_id: account_id,
        product_id: props.data.product_id
      }
      state.setOption = {}
      let res = await accountWeekData(params)
      let list = res.data.list || []
      let x_data = list.map((item: any) => item.overview_date)
      let y_order_data = list.map((item: any) => centsToYuan(item.cost))
      let y_loss_data = list.map((item: any) => item.fans_count)
      state.setOption = {
        itemGap: 80,
        color: ['#FE005A', '#FFAF5B'],
        tooltip: {
          trigger: 'axis',
          borderWidth: '0',
          backgroundColor: 'rgba(100,100,100,0.72)',
          axisPointer: {
            type: 'cross',
            lineStyle: {
              color: '#FE9D35' // 指示器线条的颜色
            },
            label: {
              backgroundColor: '#FE9D35' // 指示器内容的背景颜色
            }
          },
          textStyle: {
            color: '#FFFFFF'
          }
        },
        grid: {
          left: '4%',
          right: '2%',
          bottom: '9%'
        },
        legend: {
          right: 0,
          itemHeight: 6,
          itemWidth: 16
        },
        xAxis: [
          {
            type: 'category',
            data: x_data,
            axisTick: {
              show: false // 显示标线
            },
            axisLine: {
              lineStyle: {
                width: 0
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisPointer: {
              lineStyle: {
                color: '#FE9D35' // 指示器线条的颜色
              },
              label: {
                backgroundColor: '#FE9D35' // 指示器内容的背景颜色
              }
            },
            splitLine: {
              show: false // 隐藏 y 轴的分隔线
            },
            splitArea: {
              show: true, // 显示 y 轴的分隔区域
              areaStyle: {
                color: ['#F9F9F9', '#fff'] // 设置 y 轴分隔区域的背景色为灰白相间
              }
            },
            axisLabel: {
              formatter: function (value: any) {
                // 根据数值动态设置 Y 轴单位
                if (value >= 100000000) {
                  return value / 100000000 + '亿'
                } else if (value >= 10000000) {
                  return value / 10000000 + '千万'
                } else if (value >= 1000000) {
                  return value / 1000000 + '百万'
                } else if (value >= 10000) {
                  return value / 10000 + '万'
                } else {
                  return value
                }
              }
            }
          }
        ],
        series: [
          {
            name: '消耗',
            type: 'line',
            tooltip: {
              valueFormatter: function (value) {
                return value
              }
            },
            data: y_order_data,
            areaStyle: {
              color: '#FE005A19'
            },
            smooth: true
          },
          {
            name: '加粉',
            type: 'line',
            tooltip: {
              valueFormatter: function (value) {
                return value
              }
            },
            data: y_loss_data,
            areaStyle: {
              color: '#FFF6ED'
            },
            smooth: true
          }
        ]
      }
    } catch (error) {}
  }
  //返回账户排行
  const goBack = () => {
    isMore.value = false
    state.title = '账户消耗排行'
  }
  watch(
    () => props.data,
    () => {
      getAccountInfo()
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .echart-wrapper {
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > h2 {
        font-weight: 500;
        font-size: 16px;
        color: #222122;
        line-height: 22px;
        margin-bottom: 0;
      }
      .btn {
        .ant-radio-button-wrapper {
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }
</style>
