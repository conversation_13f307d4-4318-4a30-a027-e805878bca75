<template>
  <div class="flex" :class="[isMobile ? 'eachart_grid-wrapper1' : 'eachart_grid-wrapper']">
    <div class="echart-wrapper">
      <div class="mb-8px" :class="[isMobile ? 'flex-col' : 'flex flex-justify-between']">
        <h2 class="flex-y-center" :class="[isMobile && 'flex-col align-items-start']">
          <div class="mr-8px font-size-16px">{{ data.currentView.desc }}/实时流速</div>
          <div class="c-#888 font-size-12px fw-400">
            统计时间：{{ state.params.begin_time }}~{{ state.params.end_time }}
          </div>
        </h2>
        <div class="btn">
          <a-radio-group v-model:value="state.horValue" button-style="solid" class="time-select">
            <template v-for="item in horTotal" :key="item.value">
              <a-radio-button :value="item.value" @click="onSwitch(item.value)">{{ item.name }}</a-radio-button>
            </template>
          </a-radio-group>
        </div>
      </div>
      <Echarts
        key="left"
        id="left_echarts"
        :data="state.setOption_1"
        parentHeight="270px"
        v-if="state.horValue === 1 ? state.hours?.length : state.days?.length"
      />
      <Empty v-else />
    </div>

    <div class="echart-wrapper" :class="[isMobile && 'mt-8px']">
      <div class="mb-8px" :class="[isMobile ? 'flex-col' : 'block']">
        <h2 class="flex-y-center" :class="[isMobile && 'flex-col align-items-start']">
          <div class="mr-8px font-size-16px">每小时加粉数及开口率</div>
          <div class="c-#888 font-size-12px fw-400">
            统计时间：{{ state.rightTime.begin_time }}~{{ state.rightTime.end_time }}
          </div>
        </h2>
      </div>
      <Echarts
        key="right"
        id="right_echarts"
        :data="state.setOption_2"
        parentHeight="270px"
        @legendselectchanged="legendselectchanged"
      />
      <!-- <Empty v-else /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
  import Empty from './empty.vue'
  import { reactive, watch } from 'vue'
  import { dayOverviewApi } from '../index.api'
  import { useApp } from '@/hooks'
  import { centsToYuan } from '@/utils'
  import dayjs from 'dayjs'
  import { cloneDeep } from 'lodash-es'
  const { isMobile } = useApp()
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const state = reactive({
    horValue: 1,
    setOption_1: {},
    setOption_2: {},
    days: [],
    hours: [],
    historyDay: '',
    params: {
      begin_time: props.data.begin_time,
      end_time: props.data.end_time,
      media_type: props.data.media_type,
      product_id: props.data.product_id,
      overview_type: 2
    } as {
      begin_time: string
      end_time: string
      media_type: number
      product_id: number
      overview_type: number
      product_id?: number
    },
    rightTime: {
      begin_time: cloneDeep(props.data.begin_time),
      end_time: cloneDeep(props.data.end_time)
    }
  })
  const horTotal = reactive([
    {
      name: '每小时',
      value: 1
    },
    {
      name: '每天',
      value: 2
    },
    {
      name: '7日',
      value: 3
    }
  ])
  const onSwitch = (val: number) => {
    state.horValue = val
    init()
  }
  const init = async () => {
    try {
      state.params.product_id = props.data.product_id
      state.params.media_type = props.data.media_type
      if (props.data.begin_time === props.data.end_time) {
        state.rightTime.begin_time = props.data.begin_time
        state.rightTime.end_time = props.data.end_time
        if (state.horValue === 1) {
          state.params.begin_time = props.data.begin_time
          state.params.end_time = props.data.end_time
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.hours)
            rightEcharts(state.hours)
          }
        } else if (state.horValue === 2) {
          state.params.begin_time = props.data.begin_time
          state.params.end_time = props.data.end_time
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.days)
            rightEcharts(state.hours)
          }
        } else {
          state.params.begin_time = dayjs(props.data.end_time).subtract(7, 'day').format('YYYY-MM-DD')
          state.params.end_time = dayjs(props.data.end_time).format('YYYY-MM-DD')
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            // state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            // state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.days)
          }
        }
      } else {
        if (state.horValue === 1) {
          state.params.begin_time = state.historyDay
          state.params.end_time = state.historyDay
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            // state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.hours)
            rightEcharts(state.hours)
          }
        } else if (state.horValue === 2) {
          state.params.begin_time = props.data.begin_time
          state.params.end_time = props.data.end_time
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.days)
          }
        } else {
          state.params.begin_time = dayjs(props.data.end_time).subtract(7, 'day').format('YYYY-MM-DD')
          state.params.end_time = dayjs(props.data.end_time).format('YYYY-MM-DD')
          let res: any = await dayOverviewApi(state.params)
          if (res.code === 0) {
            state.days = []
            state.hours = []
            state.days = res.data.days?.length ? res.data.days : []
            state.hours = res.data.hours?.length ? res.data.hours : []
            leftEcharts(state.days)
          }
        }
      }
    } catch (err) {
      console.log(err)
    }
  }
  const leftEcharts = (data: any) => {
    let xAxisData = data.map((v: any) => {
      if (state.horValue === 1) {
        return dayjs(v.overview_hour).format('HH:mm')
      } else {
        return v.overview_date
      }
    })
    let title = props.data.currentView.desc
    let yAxisData1 = data.map((v: any) => {
      return {
        value: ['cost', 'conversions_cost', 'fans_cost'].includes(props.data.currentView.key)
          ? centsToYuan(v[props.data.currentView.key])
          : v[props.data.currentView.key],
        overview_date: v.overview_date
      }
    })

    let yAxisData2 = data.map((v: any) => {
      return {
        value: v.fans_count,
        overview_date: v.overview_date
      }
    })
    state.setOption_1 = {
      legend: {
        top: 6,
        right: 1,
        itemHeight: 6,
        itemWidth: 16,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.name}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:${v.seriesName === '实时流速' ? 72 : 52}px;margin-right:16px">${v.seriesName === '实时流速' ? `实时流速(${state.horValue == 1 ? '小时' : state.horValue == 2 ? '每天' : '天'})` : v.seriesName} </span>
                <span>${v.value}</span>
                </div>
                `
          })
          return html
        }
      },
      grid: {
        left: 1,
        right: 10,
        top: '16%',
        bottom: '1%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        }
      ],
      series: [
        {
          name: title,
          color: '#FE005A',
          type: 'line',
          smooth: true,
          symbolSize: 4,
          label: {
            show: true,
            position: 'top',
            formatter: function (params: any) {
              return params.value == 0 ? '' : params.value
            }
          },
          areaStyle: {
            normal: {
              color: 'rgba(254,0,90,0.1)'
            }
          },
          lineStyle: {
            normal: {
              color: '#FE005A'
            }
          },
          data: yAxisData1
        },
        {
          name: '实时流速',
          color: '#FE9D35',
          type: 'line',
          smooth: true,
          symbolSize: 4,
          label: {
            show: true,
            position: 'top',
            formatter: function (params: any) {
              return params.value == 0 ? '' : params.value
            }
          },
          lineStyle: {
            normal: {
              color: '#FE9D35'
            }
          },
          data: yAxisData2
        }
      ]
    }
  }
  function transformToStackedOverlap(data1, data2) {
    // 首先确保两个数组长度相同
    if (data1.length !== data2.length) {
      throw new Error('Data arrays must have the same length')
    }

    // 创建结果数组
    const result1 = []
    const result2 = []

    for (let i = 0; i < data1.length; i++) {
      const item1 = data1[i]
      const item2 = data2[i]

      // 检查时间是否匹配
      if (item1.overview_hour !== item2.overview_hour) {
        throw new Error(`Time mismatch at index ${i}: ${item1.overview_hour} vs ${item2.overview_hour}`)
      }

      const val1 = item1.value
      const val2 = item2.value

      // 确定较小值和较大值
      let minVal, maxVal, minItem, maxItem
      if (val1 <= val2) {
        minVal = val1
        maxVal = val2
        minItem = item1
        maxItem = item2
      } else {
        minVal = val2
        maxVal = val1
        minItem = item2
        maxItem = item1
      }

      // 计算差值
      const diff = maxVal - minVal

      // 构建结果对象 - data1取较小值但保持原始type
      result1.push({
        value: minVal,
        overview_hour: minItem.overview_hour,
        color: minItem.color,
        name: minItem.name,
        type: minItem.type,
        open_talk_rate: minItem.open_talk_rate,
        fans_count: minItem.fans_count, // 加粉数
        open_talk_count: minItem.open_talk_count // 开口数
      })

      // data2取差值但使用另一个数据的type
      result2.push({
        value: diff,
        color: maxItem.color,
        name: maxItem.name,
        overview_hour: maxItem.overview_hour,
        type: maxItem.type,
        open_talk_rate: minItem.open_talk_rate,
        fans_count: maxItem.fans_count, // 加粉数
        open_talk_count: maxItem.open_talk_count // 开口数
      })
    }

    return { data1: result2, data2: result1 }
  }
  const rightEcharts = (data: any, isSelected: boolean = false) => {
    let xAxisData = data.map((v: any) => dayjs(v.overview_hour).format('HH:mm'))

    let data1 = data.map((v: any) => {
      return {
        type: 'fans',
        value: v.fans_count,
        color: '#FEB24E',
        name: '加粉数',
        overview_hour: v.overview_hour,
        fans_count: v.fans_count, // 加粉数
        open_talk_count: v.open_talk_count, // 开口数
        open_talk_rate: v.open_talk_rate // 开口率
      }
    })

    let data2 = data.map((v: any) => {
      return {
        type: 'open',
        value: v.open_talk_count,
        color: '#FF826F',
        name: '开口数',
        overview_hour: v.overview_hour,
        fans_count: v.fans_count, // 加粉数
        open_talk_count: v.open_talk_count, // 开口数
        open_talk_rate: v.open_talk_rate // 开口率
      }
    })
    let yAxisData1 = isSelected ? data1 : transformToStackedOverlap(data1, data2).data1
    let yAxisData2 = isSelected ? data2 : transformToStackedOverlap(data1, data2).data2
    state.setOption_2 = {
      legend: {
        right: 10,
        itemHeight: 4,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.data?.overview_hour}</div>`
          params.reverse().forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.data.color || v.color}"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">${v.data.name || v.seriesName}</span>
                <span>${v.data.type === 'fans' ? v.data.fans_count : v.data.open_talk_count}</span></div>`
          })
          if (params.length) {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;">
                  <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#C1FF62"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">开口率</span>
                <span>${params?.[0]?.data?.open_talk_rate || 0}%</span></div>`
          }
          return html
        }
      },
      grid: {
        left: 1,
        right: '2%',
        top: '14%',
        bottom: '1%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 'auto',
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            interval: 0,
            margin: 16,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          axisTick: {
            alignWithLabel: true, // 让刻度线与标签对齐
            interval: 'auto'
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '开口数',
          color: '#FF826F',
          type: 'bar',
          stack: '总量',
          z: 10,
          barMaxWidth: 20,
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: function (params) {
                // 根据数据值返回不同颜色
                const type = params.data.type

                return type == 'open' ? '#FF826F' : '#FEB24E' // 橙色
              },
              barBorderRadius: 0
            }
          },
          data: yAxisData2
        },
        {
          name: '加粉数',
          color: '#FEB24E',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 20,
          z: 20,
          barGap: '10%',
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: function (params) {
                // 根据数据值返回不同颜色
                const type = params.data.type

                return type == 'open' ? '#FF826F' : '#FEB24E' // 橙色
              },
              barBorderRadius: 0
            }
          },
          data: yAxisData1
        }
      ]
    }
  }
  const legendselectchanged = (val: any) => {
    rightEcharts(state.hours, Object.values(val.selected).every((it) => it) ? false : true)
  }
  watch(
    () => props.data,
    (val: any) => {
      if (val) {
        if (val.begin_time === val.end_time) {
          state.historyDay = val.begin_time
        }
        init()
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .eachart {
    padding: 24px;
    background: #fff;
    position: relative;
    border-radius: 16px;
  }
  .eachart_grid-wrapper {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 默认2列 */
    gap: 80px; /* 间距 */
  }
  .eachart_grid-wrapper1 {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* 默认1列 */
  }
  .align-items-start {
    align-items: start;
  }
  h2 {
    font-size: 16px;
    font-weight: 500;
    color: #222122;
    line-height: 22px;
  }
</style>
