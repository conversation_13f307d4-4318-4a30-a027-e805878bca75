<template>
  <div class="data-grid">
    <div v-for="(v, index) in props.list" :key="index" class="item" @click="clickItem(v)">
      <div class="bg flex justify-between">
        <!-- 未授权提示（仅cost字段显示） -->
        <a-tooltip v-if="v.key == 'cost' && unauth">
          <template #title>{{ unauth }}个账户未授权，广告消耗数据有误</template>
          <ExclamationCircleOutlined class="c-#e63030 mr-20px item-tips" />
        </a-tooltip>

        <!-- 左侧图标（桌面端显示） -->
        <div class="left flex-none mt8px" v-if="v.icon">
          <img :src="requireNewImg(v.icon)" class="h-30px" alt="" />
        </div>

        <!-- 右侧内容 -->
        <div class="right flex flex-col">
          <div class="flex items-center flex-justify-end">
            <span class="desc">{{ v.desc }}</span>
            <a-tooltip v-if="v.tips" :title="v.tips">
              <img class="tips" src="@/assets/lddImages/dataOverView/tips.png" alt="" />
            </a-tooltip>
          </div>
          <span class="num">
            <span v-if="['cost', 'conversions_cost', 'fans_cost'].includes(v.key)">
              {{ centsToYuan(v.value) }}
            </span>
            <span v-else>{{ v.value }}</span>
            <span v-if="v.key === 'flow_rate'">/10min</span>
            <!-- <span v-if="v.key === 'open_talk_rate'">%</span> -->
          </span>
          <span class="thanYesterday">
            <span class="diff_text"> 较昨日 </span>
            <template v-if="v.ratio != '-' && v.ratio != ''">
              <a-image
                height="8px"
                :preview="false"
                :src="requireNewImg(v.is_up ? 'dataOverView/up.png' : 'dataOverView/down.png')"
              />
              <span :class="['diff_num ml4px', v.is_up ? 'c-#ff4d4f' : 'c-#52C41A']">{{ v.ratio }}%</span>
            </template>
            <template v-else>
              <span class="emty">--</span>
            </template>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { requireNewImg, centsToYuan } from '@/utils'

  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  const emit = defineEmits(['event', 'clickItem'])
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    },
    unauth: {
      type: Number,
      default: 0
    }
  })
  let curItem = ref()
  const clickItem = (item) => {
    curItem.value = item.desc
    emit('clickItem', item)
  }
</script>

<style lang="scss" scoped>
  .data-grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 默认5列 */
    gap: 19px; /* 间距 */
  }

  @media (max-width: 1200px) {
    .data-grid {
      grid-template-columns: repeat(4, 1fr); /* 中等屏幕4列 */
    }
  }

  @media (max-width: 992px) {
    .data-grid {
      grid-template-columns: repeat(3, 1fr); /* 小屏幕3列 */
    }
  }

  @media (max-width: 576px) {
    .data-grid {
      grid-template-columns: repeat(1, 1fr); /* 超小屏幕2列 */
    }
  }

  .item {
    height: 110px;
    .bg {
      position: relative;
      height: 100%;
      padding: 16px 16px 16px 32px;
      box-sizing: border-box;

      background: #f7f9fc;
      box-shadow: 0px 0px 9px 0px rgba(188, 188, 188, 0.08);
      border-radius: 4px;
      border: 1px solid transparent;
      font-family:
        PingFangSC,
        PingFang SC;
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        transform: translate(-3px, -3px);
        background: #ffffff;
        box-shadow: 0px 0px 22px 0px rgba(145, 145, 145, 0.47);
        border-radius: 4px;
        border: 1px solid #dddddd;
      }

      .tips {
        width: 12px;
        height: 12px;
        margin-left: 4px;
      }
    }

    .diff_bg {
      padding: 8px 0 6px 10px;
      box-shadow: none;
      background: none;
      border: none;

      &:hover {
        background: #f3f3f3;
        border-radius: 4px;
        border: none;
        box-shadow: none;
        transform: translate(0, 0);
      }
    }

    .left,
    .right {
      font-size: 0;
    }

    .desc {
      font-weight: 400;
      font-size: 16px;
      color: #808080;
      line-height: 22px;
    }

    .num {
      margin-top: 4px;
      font-weight: 500;
      font-size: 20px;
      color: #666666;
      line-height: 1;
      text-align: right;
    }
    .thanYesterday {
      text-align: right;
      margin-top: 8px;
      .diff_text {
        font-weight: 400;
        font-size: 12px;
        color: #8d8d8d;
        line-height: 17px;
        margin-right: 4px;
      }
      .diff_num {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
      }
    }
  }

  .item-tips {
    position: absolute;
    left: 16px;
    top: 10px;
  }
  .emty {
    font-size: 16px;
    color: #a2a2a2;
  }
</style>
