import { reactive } from 'vue'
export default function datas() {
  // 流量
  const rateTimes = reactive([
    {
      name: '今日',
      value: 1
    },
    {
      name: '昨日',
      value: 2
    },
    {
      name: '最近7天',
      value: 3
    },
    {
      name: '最近30天',
      value: 5
    }
  ])

  const dataFlows = [
    { key: 'cost', desc: '消耗(元)', icon: 'dataOverView/data_01.png', value: '0', tips: '', is_up: 1, ratio: '' },
    {
      key: 'conversions_count',
      desc: '转化数',
      icon: 'dataOverView/data_02.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'conversions_cost',
      desc: '转化成本(元)',
      icon: 'dataOverView/data_03.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'fans_count',
      desc: '加粉数',
      icon: 'dataOverView/new_data_04.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'fans_cost',
      desc: '真实粉价',
      icon: 'dataOverView/new_data_05.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'open_talk_rate',
      desc: '开口率(%)',
      icon: 'dataOverView/new_data_06.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'flow_rate',
      desc: '实时流速',
      icon: 'dataOverView/new_data_07.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'cost_account_count',
      desc: '有消耗账户数',
      icon: 'dataOverView/data_08.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'running_account_count',
      desc: '在投账户数',
      icon: 'dataOverView/new_data_09.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    },
    {
      key: 'running_ad_count',
      desc: '在投计划数',
      icon: 'dataOverView/new_data_10.png',
      value: '0',
      tips: '',
      is_up: 1,
      ratio: ''
    }
  ]
  const horTotal = reactive([
    {
      name: '每小时',
      value: 1
    },
    {
      name: '每天',
      value: 2
    },
    {
      name: '近七天',
      value: 3
    }
  ])
  return {
    rateTimes,
    dataFlows,
    horTotal
  }
}
