import http from '@/utils/request'

//数据概览
export const statistics_data = (data: any) => {
  return http('get', `/admin/statistics/data`, data)
}
//图表
export const dayOverviewApi = (data: any) => {
  return http('get', `/common/ad/v2/day_overview`, data)
}
//部门列表
export const getUserDepList = (data: any) => {
  return http('get', `/admin/department/get_user_dep_list`, data)
}
//账户排行
export const accountRank = (data: any) => {
  return http('get', `/common/ad/v2/account_rank`, data)
}
//近一周账户排行
export const accountWeekData = (data: any) => {
  return http('get', `/common/ad/v2/account_week_data`, data)
}

//授权数量
export const unauthorized_count = (data: any) => {
  return http('get', `/admin/qq_dmp/unauthorized_count`, data)
}

//产品库
export const setPrductList = (data: any) => {
  return http('post', `/admin/product/list`, data)
}
