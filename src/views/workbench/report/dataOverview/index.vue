<template>
  <a-space direction="vertical" style="width: 100%" class="space-wrapper">
    <a-card class="dataOverView-wrapper" :class="isMobile ? 'card_M' : 'card_PC'">
      <div class="eachart">
        <div class="w-full flex mb-24px">
          <div class="flex-y-center justify-between w-100%" :class="[isMobile && 'block']">
            <div class="fw-600 font-size-16px c-#313232 w-30%">数据概览</div>
            <a-space :size="10" :direction="isMobile ? 'vertical' : 'horizontal'" class="w-full flex justify-end" wrap>
              <a-radio-group v-model:value="state.rateValue" button-style="solid" class="time-select">
                <a-radio-button
                  :value="item.value"
                  v-for="item in rateTimes"
                  :key="item.value"
                  @click="onSwitch(item.value)"
                  >{{ item.name }}</a-radio-button
                >
              </a-radio-group>
              <a-range-picker
                v-model:value="state.ratePick"
                class="picker w-240px"
                valueFormat="YYYY-MM-DD"
                @change="onTimePick($event, 'flow')"
                @calendarChange="calendarTime"
                :presets="getDatePresetsOptions()"
                :allowClear="false"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :disabled-date="isDisabled"
              />
              <a-select
                v-model:value="state.product_id"
                @change="changeProduct"
                allowClear
                placeholder="请选择产品库"
                class="w-200px"
                :options="state.productList"
                :show-search="true"
                :filterOption="onFiterOption"
              />
              <a-select
                v-model:value="state.media_type"
                @change="init"
                allowClear
                placeholder="请选择媒体类型"
                class="w-200px"
                :options="state.mediaTypeList"
              >
              </a-select>
            </a-space>
          </div>
        </div>
        <DataList @clickItem="clickItem" :list="state.dataList" :unauth="state.qq_unauthorized_count" />
      </div>
      <topEcharts
        class="eachart mt-16px"
        :data="{
          begin_time: state.ratePick[0],
          end_time: state.ratePick[1],
          media_type: state.media_type,
          product_id: state.product_id,
          currentView: state.currentView
        }"
      />
      <bottomEcharts
        class="eachart mt-16px"
        :data="{
          begin_time: state.ratePick[0],
          end_time: state.ratePick[1],
          media_type: state.media_type,
          product_id: state.product_id
        }"
      />
    </a-card>
  </a-space>
</template>

<script setup lang="ts">
  import topEcharts from './components/topEcharts.vue'
  import bottomEcharts from './components/bottomEcharts.vue'
  import { reactive, ref, onMounted } from 'vue'
  import { useApp } from '@/hooks'
  import { centsToYuan, mediaTypeSearch, getDatePresetsOptions } from '@/utils'
  import DataList from './components/DataList.vue'
  import datas from './src/datas'
  import { statistics_data, unauthorized_count, setPrductList } from './index.api'
  import { product_list } from '@/api/common'
  import dayjs from 'dayjs'
  import { cloneDeep } from 'lodash-es'
  const { isMobile } = useApp()
  const { rateTimes, dataFlows } = datas()
  const timeRef = ref(null)
  const state = reactive({
    rateValue: 1 as any, //近30天按钮组
    qq_unauthorized_count: 0,
    ratePick: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    title: '',
    horValue: 1,
    media_type: '',
    product_id: undefined,
    dataList: [] as any,
    productList: [] as any,
    mediaTypeList: mediaTypeSearch,
    curKey: 'cost',
    currentView: {
      desc: '消耗(元)',
      key: 'cost'
    } as any
  })
  const isDisabled = (time: any) => {
    let today = dayjs()
    return dayjs(time).isAfter(today, 'day')
  }

  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  //数据概览
  const getData = async () => {
    try {
      let params = {
        begin_time: state.ratePick[0],
        end_time: state.ratePick[1],
        media_type: state.media_type,
        product_id: state.product_id
      }
      state.dataList = dataFlows
      let res = await statistics_data(params)
      state.dataList.forEach((item: any) => {
        item.value = res.data?.[item.key]?.value || 0
        item.is_up = res.data?.[item.key]?.is_up || 0
        item.ratio = res.data?.[item.key]?.ratio || 0
      })
    } catch (error) {}
  }
  const changeProduct = (v: any, option: any) => {
    state.product_id = v

    if (v) {
      state.media_type = option.media_type
      state.mediaTypeList = state.mediaTypeList.map((item) => {
        return {
          ...item,
          disabled: item.value !== option.media_type
        }
      })
    } else {
      state.mediaTypeList = state.mediaTypeList.map((item) => {
        return {
          ...item,
          disabled: false
        }
      })
    }
    init()
  }
  const getPrductList = async () => {
    try {
      const res = await product_list({ page: 1, page_size: 999 })
      state.productList = (res.data.list || []).map((item) => {
        return {
          label: item.name,
          value: item.id,
          media_type: item.media_type
        }
      })
    } catch (error) {
      console.log(error)
    }
  }
  const getAuthCount = async () => {
    let res = await unauthorized_count({})
    state.qq_unauthorized_count = res.data || 0
  }

  //点击item
  const clickItem = (item: any) => {
    if (['cost_account_count', 'running_account_count', 'running_ad_count', 'flow_rate'].includes(item.key)) return
    state.desc_title = `${item.desc}/实时流速`
    state.curKey = item.key
    state.currentView = item
  }
  //切换日期，累计
  const onSwitch = (v: number) => {
    switch (v) {
      case 1:
        state.ratePick = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        init()
        break
      case 2:
        state.ratePick = [
          dayjs().subtract(1, 'days').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'days').format('YYYY-MM-DD')
        ]
        init()
        break
      case 3:
        state.ratePick = [dayjs().add(-7, 'd').format('YYYY-MM-DD'), dayjs().subtract(0, 'days').format('YYYY-MM-DD')]
        init()
        break
      case 4: //暂时不用近14天
        state.ratePick = [dayjs().add(-14, 'd').format('YYYY-MM-DD'), dayjs().subtract(0, 'days').format('YYYY-MM-DD')]
        init()
        break
      case 5:
        state.ratePick = [dayjs().add(-30, 'd').format('YYYY-MM-DD'), dayjs().subtract(0, 'days').format('YYYY-MM-DD')]
        init()
        break
    }
  }

  const onTimePick = (v, type) => {
    state.horValue = 1
    state.rateValue = null
    init()
  }

  const calendarTime = (date) => {
    const [minDate, maxDate] = date
    if (minDate && !maxDate) {
      timeRef.value = minDate //记录选中的首个日期
    } else {
      timeRef.value = null
    }
  }
  const init = async () => {
    await getData()
  }
  onMounted(() => {
    init()
    getPrductList()
  })
</script>

<style lang="scss" scoped>
  .space-wrapper {
    :deep(.card_PC) {
      // margin-top: 16px;
      border: none;
      background-color: transparent;
      border-radius: 0;
      padding: 0;
    }
    :deep(.time-select) {
      .ant-radio-button-wrapper {
        color: #8d8d8d;
        border-color: #e5e6e7;
        &.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          color: #fff;
          background: #fe9d35;
          border-color: #fe9d35;
        }
      }
    }
    :deep(.ant-radio-button-wrapper) {
      height: 30px;
      line-height: 30px;
    }
  }
  .card_M {
    :deep(.ant-card-head-wrapper) {
      flex-wrap: wrap;
      .ant-card-head-title {
        flex: 100%;
      }
    }
  }
  .eachart {
    padding: 20px;
    background: #fff;
    position: relative;
    border-radius: 16px;
  }
  .eachart_grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 默认5列 */
    gap: 19px; /* 间距 */
    .left,
    .right {
      position: relative;
    }
  }
  .dataOverView-wrapper {
    :deep(.ant-card-body) {
      padding: 0;
      &::before {
        display: block;
      }
    }
  }
  :deep(.ant-radio-button-wrapper) {
    padding-inline: 10px;
  }
</style>
