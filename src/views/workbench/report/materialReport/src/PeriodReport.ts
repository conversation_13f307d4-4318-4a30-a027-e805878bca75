import { reactive } from 'vue'
import dayjs from 'dayjs'
export default function datas() {
  const searchList = reactive([
    {
      type: 'day',
      field: 'date',
      value: dayjs().format('YYYY-MM-DD'),
      props: {
        allowClear: false,
        disabledDate: (date: any) => {
          return dayjs(date).isAfter(dayjs(), 'day')
        }
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])

  const columns = reactive([
    { title: '时间', dataIndex: 'hour_str', width: 100, key: 'hour_str' },
    { title: '消耗', dataIndex: 'cost', width: 100, key: 'cost', sorter: (a, b) => a.cost - b.cost },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 100,
      key: 'conversions_count',
      sorter: (a, b) => a.conversions_count - b.conversions_count
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 110,
      key: 'conversions_cost',
      sorter: (a, b) => a.conversions_cost - b.conversions_cost
    },
    {
      title: '实际加粉数',
      dataIndex: 'fans_count',
      width: 120,
      key: 'fans_count',
      sorter: (a, b) => a.fans_count - b.fans_count
    },
    {
      title: '实际加粉成本',
      dataIndex: 'fans_cost',
      width: 130,
      key: 'fans_cost',
      sorter: (a, b) => a.fans_cost - b.fans_cost
    },
    {
      title: '开口率',
      dataIndex: 'open_talk_rate',
      width: 100,
      key: 'open_talk_rate',
      sorter: (a, b) => a.open_talk_rate - b.open_talk_rate
    },
    {
      title: '点击数',
      dataIndex: 'valid_click_count',
      width: 100,
      key: 'valid_click_count',
      sorter: (a, b) => a.valid_click_count - b.valid_click_count
    },
    {
      title: '平均点击单价',
      dataIndex: 'click_avg_cost',
      width: 140,
      key: 'click_avg_cost',
      sorter: (a, b) => a.click_avg_cost - b.click_avg_cost
    },
    {
      title: '展示数',
      dataIndex: 'view_count',
      width: 100,
      key: 'view_count',
      sorter: (a, b) => a.view_count - b.view_count
    },
    {
      title: '千展费用',
      dataIndex: 'thousand_display_price',
      width: 110,
      key: 'thousand_display_price',
      sorter: (a, b) => a.thousand_display_price - b.thousand_display_price
    },
    { title: '点击率', dataIndex: 'ctr', width: 100, key: 'ctr', sorter: (a, b) => a.ctr - b.ctr },
    {
      title: '转化率',
      dataIndex: 'conversions_rate',
      width: 100,
      key: 'conversions_rate',
      sorter: (a, b) => a.conversions_rate - b.conversions_rate
    }
  ])
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  return { columns, searchList, actions }
}
