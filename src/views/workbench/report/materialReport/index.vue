<template>
  <div>
    <DesTablePage>
      <template #title>素材报表</template>
      <template #search>
        <SearchBaseLayout
          :data="schemas"
          :actions="formConfig"
          :showDateClear="false"
          @changeValue="submitForm"
          @selectedDate="selectedDate"
        />
      </template>

      <template #tableWarp>
        <!-- <div class="btn-group mb-16px">
          <a-button
            type="primary"
            v-auth="['projectReport_quotaReport']"
            ghost
            :disabled="isDisable"
            @click="handleChange('quotaReport')"
            >批量扣量上报</a-button
          >
          <a-button
            type="primary"
            v-auth="['projectReport_updataLink']"
            ghost
            :disabled="isDisable"
            @click="handleChange('updataLink')"
            >批量更换链接</a-button
          >
          <a-button
            type="primary"
            v-auth="['projectReport_feedback']"
            ghost
            :disabled="isDisable"
            @click="handleChange('feedback')"
            >批量设置版位回传</a-button
          >
        </div> -->
        <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
          <!-- :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" -->
          <template #headerCell="{ scope }">
            <template v-if="scope.column.dataIndex == 'flow_rate'">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title>流速：当前时间往前推10分钟的加粉数据</template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.dataIndex === 'name'">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ record.material_name }}</template>
                  <div class="flex-y-center">
                    <img
                      v-if="[1, 2, 3].includes(record.media_type)"
                      class="w-15px h-15px mr-4px"
                      :src="mediaType2Content(record.media_type)?.icon"
                    />
                    <span class="goods_info_data_name">
                      {{ record.material_name || '--' }}
                    </span>
                  </div>
                </a-tooltip>
              </div>
            </template>
            <template v-if="column.dataIndex === 'account_name'">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ record.account_name }}</template>
                  <span class="goods_info_data_name">
                    {{ record.account_name || '--' }}
                  </span>
                </a-tooltip>
                <span class="number-id">ID：{{ record.account_id }}</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'open_talk_rate'">
              <span>{{ record.open_talk_rate || 0 }}%</span>
            </template>
            <template v-if="column.dataIndex === 'flow_rate'">
              <div>
                <span class="c-#52c41a">{{ record.flow_rate || 0 }}/</span>
                <span class="c-#B6AFAF">10min</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'ctr'">
              <span>{{ record.ctr || 0 }}%</span>
            </template>
            <template
              v-if="
                ['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost', 'fans_cost'].includes(
                  column.dataIndex
                )
              "
            >
              <span>¥{{ centsToYuan(record[column.dataIndex] || 0) }}</span>
            </template>
            <template v-if="column.dataIndex === 'conversions_rate'">
              <span>{{ record.conversions_rate || 0 }}%</span>
            </template>
            <template v-if="column.dataIndex === 'callback_ratio'">
              <div @click="handleChange('callback_ratio', record)">
                <a-button class="h-auto pa-0" type="link" v-if="record.callback_ratio === 100">未扣量</a-button>
                <a-button class="h-auto pa-0" type="link" v-else>{{ record.callback_ratio }}%</a-button>
              </div>
            </template>
            <template v-if="column.dataIndex === 'bwsj'">
              <a-button
                class="h-auto pa-0"
                v-auth="['projectReport_feedback']"
                type="link"
                @click="handleChange('feedback', record)"
                >查看</a-button
              >
            </template>
            <template v-if="column.dataIndex === 'handle'">
              <a-button class="h-auto pa-0" type="link" @click="handleChange('preview', record)">预览</a-button>
              <!-- <a-button class="h-auto pa-0" type="link" @click="handleChange('PeriodReport', record)"
                >时段报表</a-button
              >
              <a-button
                class="h-auto pa-0"
                v-auth="['projectReport_quotaReport']"
                type="link"
                @click="handleChange('quotaReport', record)"
                >扣量上报</a-button
              >
              <a-button
                class="h-auto pa-0"
                v-auth="['projectReport_updataLink']"
                type="link"
                @click="handleChange('updataLink', record)"
                >更换链接</a-button
              > -->
              <!-- <a-button class="h-auto pa-0" type="link" @click="handleChange('callback', record)">手动回传</a-button> -->
            </template>
          </template>
          <template #summary>
            <a-table-summary fixed>
              <a-table-summary-row>
                <template v-for="(item, index) in state.sumColumns">
                  <template v-if="['ad_name'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index"
                      >总计：{{ state.tableConfigOptions.pagination.total }}</a-table-summary-cell
                    >
                  </template>
                  <template v-else-if="['open_talk_rate', 'ctr', 'conversions_rate'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="state.sum?.[item.dataIndex] && state.sum?.[item.dataIndex] !== 0"
                        >{{ state.sum[item.dataIndex] }}%</span
                      >
                    </a-table-summary-cell>
                  </template>
                  <template
                    v-else-if="
                      ['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost', 'fans_cost'].includes(
                        item.dataIndex
                      )
                    "
                  >
                    <a-table-summary-cell :index="index">
                      <span>¥{{ centsToYuan(state.sum[item.dataIndex] || 0) }}</span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else-if="['flow_rate', 'callback_ratio'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="!state.sum?.[item.dataIndex]"></span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else>
                    <a-table-summary-cell :index="index">{{ state.sum[item.dataIndex] }}</a-table-summary-cell>
                  </template>
                </template>
              </a-table-summary-row>
            </a-table-summary>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      centered
    >
      <div v-if="state.dialog.type === 'preview'" class="landing-preview-wrapper">
        <video :src="state.dialog.data.url" v-if="state.dialog.data.format == 'MP4'" controls></video>
        <a-image v-else :src="state.dialog.data.url" />
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import datas from './src/datas'
  import { mediaType2Content, centsToYuan } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  const {
    schemas,
    state,
    formConfig,
    pageChange,
    onEvent,
    onSelectChange,
    submitForm,
    isDisable,
    handleChange,
    selectedDate
  } = datas()
</script>
<style lang="scss" scoped>
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .welcome {
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-sizing: border-box;
    &:hover {
      background-color: #ffe7ce;
      color: #e87800;
      border: 1px solid #fe9d35;
    }
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #e6f4ff;
    color: #1677ff;
    border: 1px solid #bae0ff;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
</style>
