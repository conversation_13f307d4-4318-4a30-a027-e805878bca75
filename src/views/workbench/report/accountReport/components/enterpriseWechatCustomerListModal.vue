<template>
  <div>
    <SearchBaseLayout
      :data="searchConfig.data"
      @changeValue="searchForm"
      :actions="searchConfig.options"
      :btnNames="[isAuth(['enterpriseWechatCustomerExport']) ? 'special_export' : '']"
    >
      <template #btns>
        <a-button
          class="ml-8px"
          type="primary"
          @click="onShowDialog('batch')"
          :disabled="pointData.balance <= 0 || !data.selectedRowKeys.length"
          >批量手动回传</a-button
        >
      </template>
    </SearchBaseLayout>

    <TableZebraCrossing
      class="mt-24px"
      :data="data.tableConfigOptions"
      :rowSelection="{
        selectedRowKeys: data.selectedRowKeys,
        onChange: selectedChange,
        getCheckboxProps
      }"
      @change="pageChange"
    >
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'customer_name'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.customer_name }}</template>
            <div class="text_overflow">{{ scope.record.customer_name }}</div>
          </a-tooltip>
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.extend_user_id }}</template>
            <div class="text_overflow number-id">ID：{{ scope.record.extend_user_id }}</div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.key === 'customer_tag'">
          <div v-if="scope.record.customer_tag?.length > 15">
            <a-tooltip
              placement="top"
              :disabled="!scope.record.customer_tag"
              :overlayInnerStyle="{ width: 'max-content' }"
            >
              <template #title>{{ scope.record.customer_tag }}</template>
              <div class="text_overflow w-full">{{ scope.record.customer_tag || '--' }}</div>
            </a-tooltip>
          </div>
          <span v-else>{{ scope.record.customer_tag || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'is_chat'">
          <div class="flex-y-center">
            <span class="rounds" :style="{ background: chatEnumCls(scope.record.is_chat) }"></span>
            <span :style="{ color: chatEnumCls(scope.record.is_chat) }">
              {{ chatEnum(scope.record.is_chat) }}
            </span>
          </div>
        </template>
        <template v-if="scope.column.key === 'is_callback'">
          <div class="flex items-center">
            <div :class="callbackStatus2Content(scope.record.status)?.cls">
              {{ callbackStatus2Content(scope.record.status)?.label || '--' }}
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'ad_platform'">
          <div class="flex platform_content items-center">
            <img :src="mediaType2Content(scope.record.ad_platform)?.icon" alt="" />{{
              mediaType2Content(scope.record.ad_platform)?.label
            }}
          </div>
        </template>
        <template v-if="scope.column.key === 'tr_site'">
          <span>{{ scope.record.tr_site || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'customer_marker'">
          <div v-if="scope.record.customer_marker?.length > 15">
            <a-tooltip
              placement="top"
              :disabled="!scope.record.customer_marker"
              :overlayInnerStyle="{ width: 'max-content' }"
              v-if="scope.record.customer_marker"
            >
              <template #title>{{ scope.record.customer_marker }}</template>
              <div class="text_overflow w-full">{{ scope.record.customer_marker || '--' }}</div>
            </a-tooltip>
          </div>
          <span v-else>{{ scope.record.customer_marker || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'wx_user_id'">
          <div v-if="scope.record.wx_user_name.length">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.wx_user_name }}</template>
              <div class="text_overflow">{{ scope.record.wx_user_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>{{ scope.record.wx_user_name || '--' }}</div>
          <div v-if="scope.record.wx_user_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.wx_user_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.wx_user_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'wx_link_id'">
          <div v-if="scope.record.link_name.length">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.link_name }}</template>
              <div class="text_overflow">{{ scope.record.link_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>
            {{ scope.record.link_name || '--' }}
          </div>
          <div v-if="scope.record.wx_link_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.wx_link_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.wx_link_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'corp_name'">
          <div v-if="scope.record.corp_name.length">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corp_name }}</template>
              <div class="text_overflow">{{ scope.record.corp_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>{{ scope.record.corp_name || '--' }}</div>
          <div v-if="scope.record.corpid">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corpid }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.corpid }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <!-- 账户信息 -->
        <template v-if="scope.column.key === 'tr_user_id'">
          <div class="relative">
            <img class="absolute left-0px top-2px w-15px h-15px" :src="mediaType2Content(scope.record.ad_platform)?.icon" alt="" />
            <div class="ml-20px" v-if="scope.record.ad_account_name.length">
              <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                <template #title>{{ scope.record.ad_account_name }}</template>
                <div class="text_overflow">{{ scope.record.ad_account_name }}</div>
              </a-tooltip>
            </div>
            <div v-else>{{ scope.record.ad_account_name || '--' }}</div>
          </div>

          <div v-if="scope.record.tr_user_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.tr_user_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.tr_user_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <!-- 计划信息 -->
        <template v-if="scope.column.key === 'ad_code'">
          <div v-if="scope.record.ad_group_name.length">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.ad_group_name }}</template>
              <div class="text_overflow">{{ scope.record.ad_group_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>{{ scope.record.ad_group_name || '--' }}</div>
          <div v-if="scope.record.tr_ad_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.tr_ad_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.tr_ad_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'callback_at'">
          <span v-if="scope.record.is_callback == 1"> {{ scope.record.callback_at }}</span>
          <span v-else>--</span>
        </template>
        <template v-if="scope.column.key === 'action'">
          <div class="handle_btns">
            <div class="flex_align_center">
              <a-button
                type="link"
                size="small"
                class="pa-0!"
                :disabled="scope.record.is_callback == 1 || pointData.balance <= 0"
                @click="onShowDialog('single', scope.record)"
                >手动回传</a-button
              >
            </div>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
    <specialExport :exportsData="exportsData" @onEvent="onEvent" />
  </div>
</template>
<script setup lang="ts">
  import { reactive, onMounted } from 'vue'
  import { message } from 'ant-design-vue'
  import {
    callbackStatus2Content,
    callbackStatusParams,
    mediaType2Content,
    chatEnum,
    chatEnumCls,
    mediaType
  } from '@/utils'
  import {
    getListApi,
    manualCallbackApi
  } from '@/views/workbench/privateDomainManagement/enterpriseWechatCustomerList/index.api'
  import { workListApi, exportCreate } from '@/api/common'
  import { usePoints, useDownloadCenter, useAuth } from '@/hooks'
  const { isAuth } = useAuth()
  const { goCenter } = useDownloadCenter()
  const { pointData } = usePoints()
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    }
  })
  const searchConfig = reactive({
    data: [
      {
        field: 'corpid',
        type: 'select',
        value: undefined,
        span: 6,
        props: {
          options: [],
          placeholder: '请选择企微'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'link_name',
        value: undefined,
        props: {
          placeholder: '请输入获客链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'wx_link_id',
        value: undefined,
        props: {
          placeholder: '请输入获客链接ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'customer_name',
        value: undefined,
        props: {
          placeholder: '请输入客户名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'extend_user_id',
        value: undefined,
        props: {
          placeholder: '请输入客户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'wx_user_name',
        value: undefined,
        props: {
          placeholder: '请输入跟进客服名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'wx_user_id',
        value: undefined,
        props: {
          placeholder: '请输入跟进客服ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'ad_code',
        value: undefined,
        props: {
          placeholder: '请输入广告ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'tr_user_id',
        value: undefined,
        props: {
          placeholder: '请输入账户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'ad_platform',
        type: 'select',
        value: undefined,
        props: {
          options: [...mediaType],
          placeholder: '请选择广告平台'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'tr_material_id',
        value: undefined,
        props: {
          placeholder: '请输入素材ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'is_chat',
        value: undefined,
        props: {
          placeholder: '请选择回话状态',
          options: [
            {
              value: 1,
              label: '客户已发送消息'
            },
            {
              value: 2,
              label: '客户未发送消息'
            },
            {
              value: 3,
              label: '客户待通过申请'
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'status',
        value: undefined,
        props: {
          placeholder: '请选择回传状态',
          options: callbackStatusParams
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'is_deduct',
        value: undefined,
        props: {
          placeholder: '请选择是否扣量',
          options: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 2
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'date',
        field: 'created_at',
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 12,
      //   md: 8,
      //   lg: 8,
      //   xl: 8,
      //   xxl: 6
      // }
    }
  })
  const exportsData = reactive({
    visible: false,
    type: 'wx_customer_export',
    params: {}
  })
  const onEvent = ({ cmd, data }: { cmd: string; data?: any }) => {
    exportsData.visible = false
  }
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 2100,
      y: 240
    },
    dataSource: [],
    columns: [
      {
        title: '客户名称',
        dataIndex: 'customer_name',
        key: 'customer_name',
        width: 150
      },
      {
        title: '客户标签',
        dataIndex: 'customer_tag',
        key: 'customer_tag',
        width: 150
      },
      {
        title: '客户标记',
        dataIndex: 'customer_marker',
        key: 'customer_marker',
        width: 150
      },
      {
        title: '回话状态',
        dataIndex: 'is_chat',
        key: 'is_chat',
        width: 150
      },
      {
        title: '流量版位',
        dataIndex: 'tr_site',
        key: 'tr_site',
        width: 150
      },
      {
        title: '回传状态',
        dataIndex: 'is_callback',
        key: 'is_callback',
        width: 120
      },
      {
        title: '跟进企微',
        dataIndex: 'wx_user_id', //wx_user_name
        key: 'wx_user_id',
        width: 150
      },
      {
        title: '获客链接',
        dataIndex: 'wx_link_id', //link_name
        key: 'wx_link_id',
        width: 150
      },
      {
        title: '企微名称',
        dataIndex: 'corp_name', //corpid
        key: 'corp_name',
        width: 200
      },
      {
        title: '账户信息',
        dataIndex: 'tr_user_id', //ad_account_name
        key: 'tr_user_id',
        width: 200
      },
      {
        title: '计划信息',
        dataIndex: 'ad_code', //ad_name
        key: 'ad_code',
        width: 200
      },
      {
        title: '素材信息',
        dataIndex: 'tr_material_id', //ad_name
        key: 'tr_material_id',
        width: 200
      },
      // {
      //   title: '广告平台',
      //   dataIndex: 'ad_platform',
      //   key: 'ad_platform',
      //   width: 150
      // },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 150
      },
      {
        title: '点击时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 150
      },
      {
        title: '回传时间',
        dataIndex: 'callback_at',
        key: 'callback_at',
        width: 150
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 120,
        fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 10,
      tr_user_id: undefined
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    },
    selectedRowKeys: [],
    selectedRows: []
  })
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getListApi(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
      if (data.tableConfigOptions.dataSource.length >= 4) {
        data.tableConfigOptions.scroll.y = 300
      } else {
        delete data.tableConfigOptions.scroll.y
      }
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  //导出
  const handleExportCreate = async () => {
    try {
      let params = {
        ...data.params
      }
      await exportCreate({
        type: 'wx_customer_export',
        params: JSON.stringify(params)
      })
      goCenter()
    } catch (error) {
      console.log(error)
    }
  }
  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      customer_add_time: (v.formData.created_at && v.formData.created_at.join('_')) || undefined
    }
    delete data.params.created_at
    data.params.page = 1
    if (v.type === 'special_export') {
      // handleExportCreate()
      exportsData.visible = true
      exportsData.params = data.params
      return false
    } else {
      getList()
    }
  }
  const selectedChange = (selectedRowKeys: any, selectedRows: any) => {
    data.selectedRows = selectedRows
    data.selectedRowKeys = selectedRowKeys
  }
  const getCheckboxProps = (record: any) => {
    return {
      disabled: pointData.value.balance <= 0 || record.is_callback == 1
    }
  }
  const manualCallback = async (ad_conversion_ids: any) => {
    try {
      let params = {
        ad_conversion_ids: ad_conversion_ids
      }
      await manualCallbackApi(params)
      message.success('操作成功')
      getList()
      data.selectedRowKeys = []
    } catch (error) {}
  }
  const onShowDialog = (type: string, item?: any) => {
    data.info = item
    switch (type) {
      case 'batch':
        if (data.selectedRowKeys.length == 0) {
          message.warning('请选择回传数据')
          return
        }
        manualCallback(data.selectedRowKeys)
        break
      case 'single':
        manualCallback([item.id])
        break
    }
  }
  const getWechatList = async () => {
    try {
      const { data } = await workListApi({ page: 1, page_size: 1000 })
      searchConfig.data.forEach((v) => {
        if (v.field === 'corpid') {
          v.props.options = data.list.map((item: any) => ({
            label: item.corp_name || '',
            value: item.corpid || ''
          }))
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  onMounted(() => {
    getWechatList()
    // data.params.status = 4
    data.params.tr_user_id = props.item.account_id + ''
    searchConfig.data.forEach((v) => {
      if (v.field === 'tr_user_id') {
        v.value = props.item.account_id + ''
      }
      // if (v.field === 'status') {
      //   v.value = 4
      // }
    })
    getList()
  })
</script>
