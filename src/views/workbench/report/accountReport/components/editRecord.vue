<template>
  <div class="comp_wrapper">
    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'enable'"
          >{{ record.enable == 1 ? '开启' : record.enable == 2 ? '关闭' : '--' }}
        </template>
        <template v-if="column.key === 'deduction_type'">
          <div v-if="record.enable == 1">
            {{ record.deduction_type == 1 ? '账户' : record.deduction_type == 2 ? '计划' : record.deduction_type == 3 ? '素材' : '--' }}
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.key === 'callback_type'">
          <div v-if="record.enable == 1">
            {{ record.callback_type == 1 ? '普通回传' : record.callback_type == 2 ? '最优回传' : '--' }}
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.key === 'no_deduction_reset_type'">
          <div v-if="record.enable == 1">
            {{ reset_type(record.no_deduction_reset_type) }}--{{ record.no_deduction_order_num }}
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.key === 'conversion_rate'">
          <div v-if="record.enable == 1">
            {{ reset_type(record.conversion_reset_type) }}--{{ record.conversion_rate }}%
          </div>
          <div v-else>--</div>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>
<script setup lang="ts">
  import { get_deduction_report_log } from '../index.api.ts'
  import { onMounted, reactive } from 'vue'
  const emit = defineEmits(['event'])
  const props = defineProps(['data'])
  const state = reactive({
    rawDataSource: [],
    originDataSource: [],
    dialog: {
      visible: false,
      title: '扣量上报',
      width: 600,
      data: {}
    },
    query: {
      page: 1,
      page_size: 20
    }
  })

  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true
    },
    dataSource: [],
    loading: false,
    columns: [
      {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
        width: 100
      },
      {
        title: '扣量生效粒度',
        dataIndex: 'deduction_type',
        key: 'deduction_type',
        width: 120
      },
      {
        title: '回传策略',
        dataIndex: 'callback_type',
        key: 'callback_type',
        width: 180
      },
      {
        title: '前N单免扣',
        dataIndex: 'no_deduction_reset_type',
        key: 'no_deduction_reset_type',
        width: 180
      },
      {
        title: '转化上报比例',
        dataIndex: 'conversion_rate',
        key: 'conversion_rate',
        width: 150
      },
      {
        title: '操作人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        width: 180
      },
      {
        title: '操作时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      }
    ],
    rowKey: 'key',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total: any) => `共${total}条数据`
    }
  })
  const reset_type = (type: any) => {
    let types = {
      1: '按天重置',
      2: '按时重置',
      3: '不重置'
    }
    return types[type] || '--'
  }
  const getList = async () => {
    try {
      let params = {
        account_id: props.data.account_id,
        media_type: props.data.media_type,
        ...state.query
      }
      let { data } = await get_deduction_report_log(params)
      tableData.dataSource = data.logs || []
      tableData.pagination.total = data.total
    } catch (error) {
      console.log(error)
    }
  }

  //分页
  const pageChange = (pagination) => {
    tableData.pagination.current = pagination.current
    tableData.pagination.pageSize = pagination.pageSize
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    getList()
  }

  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .footer {
    margin-top: 10px;
    text-align: end;
  }
</style>
