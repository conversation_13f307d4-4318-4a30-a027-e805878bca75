<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :rules="ruleData" :labelCol="{ style: { width: '110px' } }">
      <a-form-item label="扣量上报状态" name="enable">
        <a-switch class="mr16px" v-model:checked="state.form.enable" />
      </a-form-item>
      <template v-if="state.form.enable">
        <a-form-item>
          <template #label><span>转化上报比例</span></template>
          <div class="flex-y-center">
            <a-form-item label="" name="ratio">
              <div class="w-310px!">
                <a-input-number v-model:value="state.form.ratio" :precision="0" :min="1" :max="100">
                  <template #addonBefore>将实际转化的</template>
                  <template #addonAfter>%上报至媒体平台</template>
                </a-input-number>
              </div>
            </a-form-item>
          </div>
        </a-form-item>
      </template>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { onMounted, reactive, ref } from 'vue'
  import { isArray } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import { oceanengine_batchedit } from '../index.api'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  const props = defineProps(['item', 'media_type'])
  const emits = defineEmits(['close', 'setRatio'])
  const ruleForm = ref(null)

  const state = reactive({
    loading: false,
    form: {
      enable: false,
      ratio: 100
    }
  })

  const ruleData = ref({
    enable: [{ required: true, message: '请选择扣量上报状态', trigger: ['change', 'blur'] }],
    ratio: [{ required: true, message: '请输入转化上报比例', trigger: ['change', 'blur'] }]
  })

  onMounted(() => {
    if (props.item && !isArray(props.item)) {
      const data = props.item || {}
      state.form = {
        enable: data.ratio > 0 ? true : false,
        ratio: data.ratio || 100
      }
    }
  })

  const close = () => {
    emits('close')
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const edit = async () => {
    try {
      state.loading = true
      let site_id = isArray(props.item) ? props.item : [props.item.key]
      let params = {
        ratio: state.form.enable ? state.form.ratio : 0,
        site_id: site_id
      }
      // await oceanengine_batchedit(params)
      emits('setRatio', params)
      // message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    // margin-top: 30px;
    text-align: end;
  }
</style>
