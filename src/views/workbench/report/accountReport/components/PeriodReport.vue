<template>
  <div>
    <SearchBaseLayout
      ref="searchFormDataRef"
      class="mb-24px"
      :data="searchList"
      @changeValue="changeValue"
      :actions="actions"
    />
  </div>
  <periodEchart :id="'periodEcharts'" :time="state.params.date" :data="state.echartsList" />
  <TableZebraCrossing :data="state.tableConfigOptions">
    <template #bodyCell="{ scope: { record, column } }">
      <template v-if="column.dataIndex === 'open_talk_rate'">
        <span>{{ record.open_talk_rate }}%</span>
      </template>
      <template v-if="column.dataIndex === 'ctr'">
        <span>{{ record.ctr }}%</span>
      </template>
      <template v-if="column.dataIndex === 'conversions_rate'">
        <span>{{ record.conversions_rate }}%</span>
      </template>
      <template
        v-if="
          ['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost', 'fans_cost'].includes(
            column.dataIndex
          )
        "
      >
        <span>¥{{ centsToYuan(record[column.dataIndex] || 0) }}</span>
      </template>
    </template>
    <template #summary>
      <a-table-summary fixed>
        <a-table-summary-row>
          <template v-for="(item, index) in state.tableConfigOptions.columns">
            <template v-if="['hour_str'].includes(item.dataIndex)">
              <a-table-summary-cell :index="index">总计</a-table-summary-cell>
            </template>
            <template v-else-if="['open_talk_rate', 'ctr', 'conversions_rate'].includes(item.dataIndex)">
              <a-table-summary-cell :index="index">{{ state.sum[item.dataIndex] }}%</a-table-summary-cell>
            </template>
            <template
              v-else-if="
                ['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost', 'fans_cost'].includes(
                  item.dataIndex
                )
              "
            >
              <a-table-summary-cell :index="index">
                <span>¥{{ centsToYuan(state.sum[item.dataIndex] || 0) }}</span>
              </a-table-summary-cell>
            </template>
            <template v-else>
              <a-table-summary-cell :index="index">{{ state.sum[item.dataIndex] }}</a-table-summary-cell>
            </template>
          </template>
        </a-table-summary-row>
      </a-table-summary>
    </template>
  </TableZebraCrossing>
</template>
<script setup lang="ts">
  import { statement_account_latitude_hour } from '../index.api'
  import { ref, reactive, watch } from 'vue'
  import datas from '../src/PeriodReport'
  import { message } from 'ant-design-vue'
  import { centsToYuan } from '@/utils'
  import dayjs from 'dayjs'
  import periodEchart from './periodEchart.vue'
  import { cloneDeep } from 'lodash-es'
  const { columns, searchList, actions } = datas()
  const props = defineProps(['item'])
  const searchFormDataRef = ref()
  const state = reactive({
    echartsList: [],
    params: {
      account_id: undefined,
      field: undefined,
      order: undefined,
      media_type: props.item.media_type,
      date: dayjs().format('YYYY-MM-DD')
    } as any,
    sum: {} as any,
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns,
      pagination: false
    }
  })
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      const res = await statement_account_latitude_hour({ ...state.params })
      state.tableConfigOptions.dataSource = res.data.list || []
      state.echartsList = cloneDeep(res.data.list)
        .reverse((a, b) => a.hour - b.hour)
        .map((it: any) => {
          return {
            overview_hour: it.hour_str,
            fans_count: it.fans_count,
            open_talk_count: it.open_talk_count,
            open_talk_rate: it.open_talk_rate
          }
        })
      state.sum = res.data?.summary
      if (res.data?.list?.length > 0) {
        state.tableConfigOptions.scroll.y = 240
      }
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  //筛选
  const changeValue = (data: any) => {
    state.params = { ...state.params, ...data.formData }
    if (!data.status) {
      state.params.date = dayjs().format('YYYY-MM-DD')
      data.formData.date = dayjs().format('YYYY-MM-DD')
    }
    getList()
  }
  watch(
    () => props.item.account_id,
    (val) => {
      if (val) {
        state.params.account_id = val + ''
        getList()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped></style>
