import { reactive, ref, computed, watch } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { exportCreate } from '@/api/common'
import { useDownloadCenter, usePoints } from '@/hooks'
import { mediaTypeSearch, getDatePresetsOptions } from '@/utils'
import { statement_account_latitude } from '../index.api'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import { useAuth, useApp } from '@/hooks'
const { isAuth } = useAuth()
const { pointData } = usePoints()
const { isMobile } = useApp()
export default function datas() {
  const { goCenter } = useDownloadCenter()
  const dates = ref()
  const schemas = ref([
    {
      field: 'media_type',
      type: 'select',
      value: '',
      props: {
        options: mediaTypeSearch,
        placeholder: '请选择媒体类型'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'account_name',
      type: 'input.text',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'account_id',
      type: 'input.text',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'remark',
      type: 'input.text',
      value: undefined,
      props: {
        placeholder: '请输入备注'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'is_running',
      type: 'select',
      value: undefined,
      props: {
        options: [{ label: '在投', value: 1 }],
        placeholder: '请选择投放状态'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      props: {
        presets: getDatePresetsOptions({ range: 'NearlyThirty' }),
        disabledDate: disabledDate
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = [
    { title: '账户信息', dataIndex: 'account_name', fixed: 'left', width: 300, key: 'account_name' },
    { title: '扣量上报', dataIndex: 'callback_ratio', width: 120, key: 'callback_ratio' },
    { title: '版位数据', dataIndex: 'bwsj', width: 100, key: 'bwsj' },
    { title: '消耗', dataIndex: 'cost', key: 'cost', width: 100, sorter: true },
    { title: '转化数', dataIndex: 'conversions_count', key: 'conversions_count', width: 100, sorter: true },
    { title: '转化成本', dataIndex: 'conversions_cost', key: 'conversions_cost', width: 100, sorter: true },
    { title: '实际加粉数', dataIndex: 'fans_count', key: 'fans_count', width: 110, sorter: true },
    { title: '实际加粉成本', dataIndex: 'fans_cost', key: 'fans_cost', width: 120, sorter: true },
    { title: '流速', dataIndex: 'flow_rate', key: 'flow_rate', width: 110 },
    { title: '开口率', dataIndex: 'open_talk_rate', key: 'open_talk_rate', width: 100, sorter: true },
    { title: '点击数', dataIndex: 'valid_click_count', key: 'valid_click_count', width: 100, sorter: true },
    { title: '平均点击单价', dataIndex: 'click_avg_cost', width: 120, key: 'click_avg_cost', sorter: true },
    { title: '展示数', dataIndex: 'view_count', width: 100, key: 'view_count', sorter: true },
    { title: '千展费用', dataIndex: 'thousand_display_price', width: 100, key: 'thousand_display_price', sorter: true },
    { title: '点击率', dataIndex: 'ctr', width: 100, key: 'ctr', sorter: true },
    { title: '转化率', dataIndex: 'conversions_rate', width: 100, key: 'conversions_rate', sorter: true },
    { title: '备注', dataIndex: 'remarks', width: 180, key: 'remarks' },
    { title: '操作', dataIndex: 'handle', fixed: 'right', width: 220, key: 'handle' }
  ]

  const state = reactive({
    is_running: false,
    sum: {} as any,
    query: {
      page: 1,
      page_size: 20,
      is_running: 0,
      begin_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD'),
      field: undefined,
      order: undefined
    } as any,
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: null,
      media_type: []
    } as any,
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'account_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 2100
      },
      dataSource: [],
      columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    },
    sumColumns: cloneDeep([{ checked: true, dataIndex: 'index' }, ...columns]),
    drawer: {
      visible: false,
      title: '扣量修改日志',
      width: isMobile.value ? '100%' : '80%',
      row: {},
      type: ''
    }
  })
  //顶部按钮置灰
  const isDisable = computed(() => {
    return !state.selectedRowKeys?.length || pointData.value.balance <= 0
  })
  const checkedChange = () => {
    state.query.page = 1
    state.query.is_running = state.is_running ? 1 : 0
    getList()
  }
  // 商品列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      state.query.is_running = state.query.is_running ? state.query.is_running : 0
      const resp = await statement_account_latitude(state.query)
      state.tableConfigOptions.dataSource = resp.data?.list || []

      state.tableConfigOptions.pagination.total = resp.data?.total || resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1
      state.sum = resp.data?.sum || {}

      console.log('state.tableConfigOptions.dataSource', state.tableConfigOptions.dataSource)

      if (state.selectionItem.length) {
        state.selectionItem = []
      }
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const submitForm = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    }
    if (data.formData.created_at?.length && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.query.begin_time = data.formData.created_at[0]
      state.query.end_time = data.formData.created_at[1]
      state.query.created_at = undefined
    } else {
      state.query.begin_time = undefined
      state.query.end_time = undefined
      state.query.created_at = undefined
    }
    if (!data.status) {
      data.formData.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.query.begin_time = dayjs().format('YYYY-MM-DD')
      state.query.end_time = dayjs().format('YYYY-MM-DD')
    }
    state.query.page = 1
    getList()
  }

  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('sorter', sorter)

    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    if (sorter) {
      state.query.field = sorter.field
      state.query.order = sorter.order == 'ascend' ? 'asc' : 'desc'
    }
    getList()
  }
  getList()

  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'wx_work_link',
        params: JSON.stringify(state.query)
      })
      goCenter()
    } catch (error) {
      console.log(error)
    }
  }
  const onEvent = (data: { cmd: string }) => {
    state.dialog.visible = false
    // if (data.cmd == 'close') {
    //   state.dialog.visible = false
    // } else {
    //   state.dialog.visible = false
    // }
    state.selectedRowKeys = []
    getList()
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log('selectedRows changed: ', selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
    data.batchSetData.isSelected = selectedRows.length > 0
  }
  const DIALOG_NAME: any = {
    quotaReport: {
      title: '扣量上报',
      width: 680
    },
    feedback: {
      title: '版位回传',
      width: 800
    },
    updataLink: {
      title: '更换链接',
      width: 600
    },
    callback_ratio: {
      title: '客户明细',
      width: 1170
    },
    PeriodReport: {
      title: '时段报表',
      width: 1170
    },
    flowRate: {
      title: '流速',
      width: 1070
    }
  }
  watch(
    () => state.selectedRowKeys,
    (n) => {
      data.batchSetData.isSelected = n.length > 0
    }
  )
  // 操作
  const handleChange = (type: any, record?: any) => {
    let containGdt = state.selectionItem?.some((it) => it?.media_type === 1)
    if (containGdt && ['updataLink', 'feedback'].includes(type)) {
      message.error('广点通暂不支持此操作')
      const juliangArr = state.selectionItem?.filter((it) => it.media_type === 3)?.map((it) => it.account_id)
      state.selectedRowKeys = juliangArr
    }
    if (!record && !state.selectedRowKeys.length) {
      return message.error('请至少选择一条数据')
    }

    state.dialog.type = type
    state.dialog.data = record ? record : state.selectedRowKeys
    state.dialog.media_type = record ? [record.media_type] : state.selectionItem.map((it: any) => it.media_type)
    state.dialog.title = DIALOG_NAME[type].title
    state.dialog.width = DIALOG_NAME[type].width
    state.dialog.visible = true
    if (type === 'quotaReport') {
      state.dialog.list = setMediaList(state.selectionItem)
    }
  }

  // 按媒体类型整理数组
  const setMediaList = (list: any) => {
    const result = list.reduce((acc, item) => {
      const existingGroup = acc.find((group) => group.media_type === item.media_type)

      if (existingGroup) {
        existingGroup.account_id_list += `,${item.account_id}`
      } else {
        acc.push({
          account_id_list: String(item.account_id),
          media_type: item.media_type
        })
      }

      return acc
    }, [])
    return result
  }

  const batchSetCallback = (item: any) => {
    handleChange(item.type)
  }
  const data = reactive({
    batchSetData: {
      isShow: isAuth(['accountReport_quotaReport', 'accountReport_updataLink', 'accountReport_feedback']),
      list: [
        {
          text: '批量扣量上报',
          auth: ['accountReport_quotaReport'],
          type: 'quotaReport'
        },
        {
          text: '批量更换链接',
          auth: ['accountReport_updataLink'],
          type: 'updataLink'
        },
        {
          text: '批量设置版位回传',
          auth: ['accountReport_feedback'],
          type: 'feedback'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    }
  })
  // 日期选择
  const selectedDate = (val) => {
    dates.value = val
  }

  function disabledDate(current: any) {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  const showDrawer = (type, row) => {
    state.drawer.visible = true
    state.drawer.row = row
    state.drawer.type = type
  }
  return {
    schemas,
    state,
    formConfig,
    columns,
    data,
    pageChange,
    onEvent,
    handleExportCreate,
    getList,
    onSelectChange,
    submitForm,
    isDisable,
    handleChange,
    selectedDate,
    checkedChange,
    showDrawer
  }
}
