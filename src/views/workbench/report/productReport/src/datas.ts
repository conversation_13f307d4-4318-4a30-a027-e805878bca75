import { reactive, ref, computed } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { exportCreate } from '@/api/common'
import { useDownloadCenter, usePoints } from '@/hooks'
import { mediaTypeSearch, getDatePresetsOptions } from '@/utils'
import { product_latitude } from '../index.api'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
const { pointData } = usePoints()
export default function datas() {
  const { goCenter } = useDownloadCenter()
  const schemas = ref([
    {
      field: 'media_type',
      type: 'select',
      value: '',
      props: {
        options: mediaTypeSearch,
        placeholder: '请选择媒体类型'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'product_name',
      type: 'input.text',
      value: undefined,
      props: {
        placeholder: '请输入产品名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },

    {
      type: 'date',
      field: 'created_at',
      value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      props: {
        presets: getDatePresetsOptions({ range: 'NearlyThirty' }),
        disabledDate: disabledDate
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = [
    { title: '产品名称', dataIndex: 'product_name', fixed: 'left', width: 200, key: 'product_name' },
    { title: '消耗', dataIndex: 'cost', key: 'cost', width: 100, sorter: true },
    // { title: '转化数', dataIndex: 'conversions_count', key: 'conversions_count', width: 100 },
    // { title: '转化成本', dataIndex: 'conversions_cost', key: 'conversions_cost', width: 100 },
    // { title: '转化率', dataIndex: 'conversions_rate', key: 'conversions_rate', width: 100 },
    { title: '加粉数', dataIndex: 'fans_count', key: 'fans_count', width: 110, sorter: true },
    { title: '加粉成本', dataIndex: 'fans_cost', key: 'fans_cost', width: 120, sorter: true },
    { title: '流速', dataIndex: 'flow_rate', key: 'flow_rate', width: 110 },
    { title: '预加粉', dataIndex: 'pre_fans_num', key: 'pre_fans_num', width: 110 },
    { title: '进度', dataIndex: 'pre_fans_progress', key: 'pre_fans_progress', width: 100 },
    { title: '开口率', dataIndex: 'open_talk_rate', key: 'open_talk_rate', width: 100, sorter: true }
    // { title: '操作', dataIndex: 'handle', fixed: 'right', width: 80, key: 'handle' }
  ]
  const dates = ref()
  const state = reactive({
    sum: {} as any,
    query: {
      page: 1,
      page_size: 20,
      begin_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD'),
      field: undefined,
      order: undefined
    } as any,
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: null,
      media_type: []
    } as any,
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'adgroup_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    },
    sumColumns: cloneDeep([...columns])
  })

  // 产品列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true

      const resp = await product_latitude(state.query)

      state.tableConfigOptions.dataSource = resp.data?.list || []
      state.sum = resp.data?.sum || {}
      state.tableConfigOptions.pagination.total = resp.data?.total || resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1

      console.log('state.tableConfigOptions.dataSource', state.tableConfigOptions.dataSource)

      if (state.selectionItem.length) {
        state.selectionItem = []
      }
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const submitForm = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    }
    if (data.formData.created_at?.length && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.query.begin_time = data.formData.created_at[0]
      state.query.end_time = data.formData.created_at[1]
      state.query.created_at = undefined
    } else {
      state.query.begin_time = undefined
      state.query.end_time = undefined
      state.query.created_at = undefined
    }
    if (!data.status) {
      data.formData.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.query.begin_time = dayjs().format('YYYY-MM-DD')
      state.query.end_time = dayjs().format('YYYY-MM-DD')
    }
    state.query.page = 1
    getList()
  }

  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    if (sorter) {
      state.query.field = sorter.field
      state.query.order = sorter.order == 'ascend' ? 'asc' : 'desc'
    }
    getList()
  }
  getList()

  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'wx_work_link',
        params: JSON.stringify(state.query)
      })
      goCenter()
    } catch (error) {
      console.log(error)
    }
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log('selectedRows changed: ', selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
  }
  const DIALOG_NAME: any = {
    preview: {
      title: '预览',
      width: 520
    }
    // feedback: {
    //   title: '版位回传',
    //   width: 800
    // },
    // updataLink: {
    //   title: '更换链接',
    //   width: 600
    // },
    // callback_ratio: {
    //   title: '转化明细',
    //   width: 1170
    // },
    // PeriodReport: {
    //   title: '时段报表',
    //   width: 1170
    // }
  }
  // 操作
  const handleChange = async (type: any, record?: any) => {
    if (!record && !state.selectedRowKeys.length) {
      return message.error('请至少选择一条数据')
    }
    let handleSelect = state.selectedRowKeys
    // if (['quotaReport'].includes(type) && !record) {
    //   handleSelect = state.selectionItem.map((it) => it.account_id)
    // }
    if (type === 'preview') {
      const data = await material_preview({
        material_id: record.material_id,
        // material_id: '7392180591779692553',
        media_type: record.media_type
      })
      state.dialog.data = data.data
    }

    state.dialog.type = type
    // state.dialog.data = record ? record : handleSelect
    state.dialog.media_type = record ? [record.media_type] : state.selectionItem.map((it: any) => it.media_type)
    state.dialog.title = DIALOG_NAME[type].title
    state.dialog.width = DIALOG_NAME[type].width
    state.dialog.visible = true
  }
  // 日期选择
  const selectedDate = (val) => {
    dates.value = val
  }

  function disabledDate(current: any) {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  return {
    schemas,
    state,
    formConfig,
    columns,
    pageChange,
    handleExportCreate,
    getList,
    onSelectChange,
    submitForm,
    handleChange,
    selectedDate
  }
}
