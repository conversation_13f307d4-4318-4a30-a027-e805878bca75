<template>
  <div class="echart-wrapper">
    <div class="title">
      <h2>
        <span>每小时加粉数及开口率</span>
        <span class="c-#888 font-size-12px ml-8px fw-400">统计时间：{{ time }}</span>
      </h2>
    </div>
    <Echarts key="right" :id="id" :data="state.options" parentHeight="270px" />
  </div>
</template>
<script setup lang="ts">
  import { reactive, watch } from 'vue'
  import dayjs from 'dayjs'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: 'right_echarts'
    },
    time: {
      type: String,
      default: ''
    }
  })
  const state = reactive({
    options: {}
  })

  const init = () => {
    console.log('====')
    let xAxisData = props.data.map((v: any) => v.overview_hour)
    let yAxisData1 = props.data.map((v: any) => {
      return {
        value: v.fans_count,
        overview_hour: v.overview_hour,
        fans_count: v.fans_count, // 加粉数
        open_talk_count: v.open_talk_count, // 开口数
        open_talk_rate: v.open_talk_rate // 开口率
      }
    })
    let yAxisData2 = props.data.map((v: any) => {
      return {
        value: v.open_talk_count,
        overview_hour: v.overview_hour,
        fans_count: v.fans_count, // 加粉数
        open_talk_count: v.open_talk_count, // 开口数
        open_talk_rate: v.open_talk_rate // 开口率
      }
    })
    state.options = {
      legend: {
        right: 10,
        itemHeight: 4,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
        formatter: function (params) {
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.data?.overview_hour}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">${v.seriesName}</span>
                <span>${v.value}</span></div>`
          })
          if (params.length) {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;">
                  <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:#C1FF62"></span>
                <span style="display: inline-block;width:48px;margin-right:16px">开口率</span>
                <span>${params?.[0]?.data?.open_talk_rate || 0}%</span></div>`
          }
          return html
        }
      },
      grid: {
        left: 1,
        right: '1%',
        top: '14%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 'auto',
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            interval: 0,
            margin: 16,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          axisTick: {
            alignWithLabel: true, // 让刻度线与标签对齐
            interval: 'auto'
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '开口数',
          color: '#FF826F',
          type: 'bar',
          stack: '总量',
          z: 10,
          barMaxWidth: 20,
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: '#FF826F',
              barBorderRadius: 0
            }
          },
          data: yAxisData2
        },
        {
          name: '加粉数',
          color: '#FEB24E',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 20,
          z: 20,
          barGap: '10%',
          barCategoryGap: '10%', // 可选的类目间间隔调整
          itemStyle: {
            normal: {
              color: '#FEB24E'
            }
          },
          data: yAxisData1
        }
      ]
    }
  }
  watch(
    () => props.data,
    (val) => {
      init()
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>
<style lang="scss" scoped>
  .echart-wrapper {
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > h2 {
        font-weight: 500;
        font-size: 16px;
        color: #222122;
        line-height: 22px;
      }
      .btn {
        .ant-radio-button-wrapper {
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }
</style>
