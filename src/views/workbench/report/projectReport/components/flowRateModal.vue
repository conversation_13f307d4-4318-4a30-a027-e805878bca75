<template>
  <div>
    <Echarts key="flowRate" id="flowRateModal" :data="state.setOption" parentHeight="270px" />
  </div>
</template>
<script setup lang="ts">
  import { reactive, onMounted } from 'vue'
  import { fans_flow } from '@/api/common'
  import dayjs from 'dayjs'
  const state = reactive({
    setOption: {}
  })
  const props = defineProps(['item', 'type'])
  const init = async () => {
    try {
      let params = {
        type: props.type,
        media_type: props.item.media_type
      } as any
      if (props.type === 2) {
        params.adgroup_id = props.item.adgroup_id
      }
      let res = await fans_flow(params)
      let data = res.data.list
      let xAxisData = data.map((v: any) => {
        return dayjs(v.interval).format('HH:mm:ss')
      })
      let yAxisData1 = data.map((v: any) => {
        return {
          value: v.fan_count,
          interval: v.interval
        }
      })

      state.setOption = {
        legend: {
          top: 6,
          right: 1,
          itemHeight: 6,
          itemWidth: 16,
          lineStyle: {
            width: 1
          },
          textStyle: {
            color: '#242F57',
            fontSize: 12,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              type: 'dashed',
              color: '#FE9D35'
            }
          },
          extraCssText:
            'background:rgba(100,100,100,0.72);border-radius: 8px;padding: 8px;width: 197px;border-color:transparent;',
          formatter: function (params) {
            let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.data?.interval || params[0]?.name}</div>`
            params.forEach((v) => {
              html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:52px;margin-right:16px">${v.seriesName} </span>
                <span>${v.value}</span>
                </div>
                `
            })
            return html
          }
        },
        grid: {
          left: 0,
          right: 10,
          top: '16%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: '#242F57',
                fontSize: '12px'
              }
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false,
              onZero: false,
              lineStyle: {
                color: '#D9D9D9'
              }
            },
            data: xAxisData
          }
        ],
        yAxis: [
          {
            type: 'value',
            // name: '加粉数',
            // nameTextStyle: {
            //   fontWeight: 'bold',
            //   color: 'red',
            //   align: 'left',
            //   fontSize: 14
            // },
            axisLabel: {
              textStyle: {
                color: '#242F57',
                fontSize: '12px'
              },
              formatter: function (value: any) {
                // 根据数值动态设置 Y 轴单位
                if (value >= 100000000) {
                  return value / 100000000 + '亿'
                } else if (value >= 10000000) {
                  return value / 10000000 + '千万'
                } else if (value >= 1000000) {
                  return value / 1000000 + '百万'
                } else if (value >= 10000) {
                  return value / 10000 + '万'
                } else {
                  return value
                }
              }
            },
            nameTextStyle: {
              color: '#666',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                type: 'solid',
                color: '#f3f3f3'
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '加粉数',
            color: '#FE005A',
            type: 'line',
            smooth: true,
            symbolSize: 4,
            label: {
              show: true,
              position: 'top',
              formatter: function (params: any) {
                return params.value == 0 ? '' : params.value
              }
            },
            areaStyle: {
              normal: {
                color: 'rgba(254,0,90,0.1)'
              }
            },
            lineStyle: {
              normal: {
                color: '#FE005A'
              }
            },
            data: yAxisData1
          }
        ]
      }
    } catch (err) {
      console.log(err)
    }
  }
  onMounted(() => {
    init()
  })
</script>
