<template>
  <SearchBaseLayout
    ref="searchFormDataRef"
    :data="banWeischemas"
    @changeValue="searchForm"
    :batchSetData="state.batchList"
  />

  <div class="mt-12px">
    <TableZebraCrossing
      :data="tableData"
      @change="pageChange"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'is_open'">
          <a-switch
            @change="(e) => changeSwitch(e, record)"
            v-model:checked="record.is_open"
            :checkedValue="1"
            :unCheckedValue="2"
          />
        </template>
        <template v-if="column.key === 'callback_ratio'">
          <div @click="handleChange(record)">
            <a-button class="h-auto pa-0" v-if="record.ratio === 100 || !record.ratio" type="link">未扣量</a-button>
            <a-button class="h-auto pa-0" type="link" v-else>{{ record.ratio }}%</a-button>
          </div>
        </template>
        <template v-if="column.key === 'open_talk_rate'">
          <div>{{ record.open_talk_rate }}%</div>
        </template>

        <template v-if="column.key === 'value'">
          <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
            <template #title>{{ record.value }}</template>
            <span class="text_overflow_row1">
              {{ record.value }}
            </span>
          </a-tooltip>
        </template>
      </template>
    </TableZebraCrossing>
    <!-- <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submit">确定</a-button>
    </div> -->
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      :centered="true"
      destroyOnClose
    >
      <Report
        :item="state.dialog.data"
        :media_type="state.dialog.media_type"
        @close="closeReport"
        @setRatio="setRatio"
      />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import {
    callback_position_list,
    callback_position_setting,
    setPositionRatio,
    setPositionSwitch
  } from '../index.api.ts'
  import { reactive, onMounted, createVNode, computed, ref } from 'vue'
  import Report from '../../accountReport/components/CallBackReport.vue'

  import { message, Modal } from 'ant-design-vue'
  import { DownOutlined, UpOutlined } from '@ant-design/icons-vue'
  import { isArray, cloneDeep } from 'lodash-es'
  const emit = defineEmits(['event'])
  const props = defineProps(['item', 'begin_time', 'end_time'])
  const batchSetCallback = (item: any) => {
    batchChange(item.type)
  }
  const state = reactive({
    rawDataSource: [],
    originDataSource: [],
    isSearch: false,
    dialog: {
      visible: false,
      title: '扣量上报',
      width: 600,
      data: {}
    },
    dropOpen: false,
    batchList: {
      isShow: true,
      list: [
        {
          text: '批量关闭',
          type: 'closeStatus'
        },
        {
          text: '批量开启',
          type: 'openStatus'
        },
        {
          text: '批量扣量上报',
          type: 'quotaReport'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    },
    selectedRowKeys: [],
    add_columns: [
      // {
      //   title: '点击数',
      //   dataIndex: 'valid_click_count',
      //   key: 'valid_click_count',
      //   width: 120,
      //   sorter: (a, b) => a.valid_click_count - b.valid_click_count
      // },
      {
        title: '订单数/加粉数',
        dataIndex: 'fans_count',
        key: 'fans_count',
        width: 180,
        sorter: (a, b) => a.fans_count - b.fans_count
      },
      {
        title: '访问数',
        dataIndex: 'visit_count',
        key: 'visit_count',
        width: 120,
        sorter: (a, b) => a.visit_count - b.visit_count
      },
      {
        title: '开口率',
        dataIndex: 'open_talk_rate',
        key: 'open_talk_rate',
        width: 120,
        sorter: (a, b) => a.open_talk_rate - b.open_talk_rate
      }
      // {
      //   title: '总GMV',
      //   dataIndex: 'GMV',
      //   key: 'GMV',
      //   width: 120,
      //   sorter: (a, b) => a.GMV - b.GMV
      // }
    ]
  })
  const banWeischemas = ref([
    {
      field: 'name',
      type: 'input.text',
      value: undefined,
      props: {
        placeholder: '请输入流量版位'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    }
  ])
  //顶部按钮置灰
  const isDisable = computed(() => {
    return !state.selectedRowKeys?.length
  })

  const searchForm = (v) => {
    state.isSearch = true
    if (v.status && v.formData?.name) {
      const result = state.rawDataSource.filter((item: any) => {
        return item?.value.includes(v.formData?.name)
      })
      state.originDataSource = result
    } else {
      state.originDataSource = state.rawDataSource
    }
    tableData.pagination.current = 1
    updateTableData()
    state.selectedRowKeys = []
  }
  const upDateOriginData = () => {
    state.originDataSource?.forEach((item: any) => {
      tableData.dataSource?.forEach((item2: any) => {
        if (item.key === item2.key) {
          item.is_open = item2.is_open
          item.ratio = item2.ratio
        }
      })
    })
  }
  const changeSwitch = (checked: any, record: any) => {
    setSwitch(checked === 1 ? true : false, [record.key])
  }
  const handleChange = (record: any) => {
    state.dialog.visible = true
    state.dialog.data = record
    // 这里可以添加逻辑来处理点击事件，比如打开一个编辑比例的弹窗
  }
  const batchChange = (type: string) => {
    if (type === 'quotaReport') {
      if (!state.selectedRowKeys.length) {
        message.warning('请先选择需要操作的版位')
        return
      }
      state.dialog.visible = true
      state.dialog.data = state.selectedRowKeys
    } else if (type === 'closeStatus' || type === 'openStatus') {
      Modal.confirm({
        title: `确认批量${type === 'closeStatus' ? '关闭' : '开启'}回传？`,
        content: `${type === 'closeStatus' ? '批量关闭后，所选广告对应版位均不再进行回传' : ''}`,
        onOk: () => {
          const isOpen = type === 'openStatus' ? true : false
          setSwitch(isOpen, state.selectedRowKeys)
        }
      })
    }
  }
  const setSwitch = async (sw: any, site_id: any) => {
    let account_id_list = isArray(props.item) ? props.item : [props.item.adgroup_id]
    const params = {
      resource_id: account_id_list?.map((item: any) => String(item)),
      resource_type: 2, // 1-账号 2-计划
      switch: sw,
      site_id: site_id
    }
    try {
      await setPositionSwitch(params)
      message.success('操作成功')
      tableData.dataSource?.forEach((item) => {
        if (site_id.includes(item.key)) {
          item.is_open = sw ? 1 : 2
        }
      })
      state.selectedRowKeys = []
    } catch (error) {
      console.error(error)
      if (site_id?.length === 1) {
        // 如果是单个版位操作失败，恢复原状态
        tableData.dataSource?.forEach((item) => {
          if (site_id.includes(item.key)) {
            item.is_open = sw ? 2 : 1
          }
        })
      }
    } finally {
      upDateOriginData()
    }
  }
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRowKeys = selectedRowKeys
    // state.selectionItem = selectedRows
    state.batchList.isSelected = selectedRows.length > 0
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true
    },
    dataSource: [],
    loading: false,
    columns: [
      {
        title: '回传状态',
        dataIndex: 'is_open',
        key: 'is_open',
        width: 180
      },
      {
        title: '扣量上报',
        dataIndex: 'callback_ratio',
        key: 'callback_ratio',
        width: 100
      },
      {
        title: '流量版位',
        dataIndex: 'value',
        key: 'value',
        width: 180
      }
    ],
    rowKey: 'key',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total: any) => `共${total}条数据`
    }
  })
  onMounted(() => {
    if (!isArray(props.item)) {
      tableData.columns.push(...state.add_columns)
    }
    getList()
  })
  const setRatio = async (data: any) => {
    let account_id_list = isArray(props.item) ? props.item : [props.item.adgroup_id]
    const params = {
      resource_id: account_id_list?.map((item: any) => String(item)),
      resource_type: 2, // 1-账号 2-计划
      ratio: data.ratio,
      site_id: data.site_id
    }
    await setPositionRatio(params)
    message.success('操作成功')
    tableData.dataSource?.forEach((item) => {
      if (data.site_id.includes(item.key)) {
        item.ratio = data.ratio
      }
    })
    state.selectedRowKeys = []
    state.dialog.visible = false
    upDateOriginData()
  }

  const getList = async () => {
    try {
      tableData.loading = true
      let adgroup_id = isArray(props.item) ? '' : String(props.item.adgroup_id)
      let params = {
        adgroup_id,
        change_type: 2,
        media_type: 3,
        begin_time: props.begin_time,
        end_time: props.end_time
      }
      let { data } = await callback_position_list(params)
      state.rawDataSource = data || []

      tableData.pagination.current = 1
      updateTableData()
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }

  // 获取当前页数据
  const getCurrentPageData = () => {
    const { current, pageSize } = tableData.pagination
    const start = (current - 1) * pageSize
    const end = start + pageSize
    if (state.isSearch) {
      return state.originDataSource.slice(start, end)
    } else {
      return state.rawDataSource.slice(start, end)
    }
  }
  // 更新表格显示数据
  const updateTableData = () => {
    tableData.dataSource = getCurrentPageData()
    tableData.pagination.total = state.isSearch ? state.originDataSource.length : state.rawDataSource.length
    if (tableData.dataSource.length > 0) {
      tableData.scroll.y = 350
    } else {
      delete tableData.scroll.y
    }
  }
  //分页
  const pageChange = (pagination) => {
    tableData.pagination.current = pagination.current
    tableData.pagination.pageSize = pagination.pageSize
    updateTableData()
  }
  const close = () => {
    emit('event', { type: 'feedback', status: false })
  }

  const closeReport = () => {
    state.dialog.visible = false
  }
</script>

<style lang="scss" scoped>
  .footer {
    margin-top: 10px;
    text-align: end;
  }
</style>
