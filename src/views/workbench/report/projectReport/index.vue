<template>
  <div>
    <DesTablePage>
      <template #title>计划报表</template>
      <template #search>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="schemas"
          :actions="formConfig"
          :showDateClear="false"
          @changeValue="submitForm"
          @selectedDate="selectedDate"
          @onOpenChange="onOpenChange"
          :batchSetData="data.batchSetData"
        />
      </template>

      <template #tableWarp>
        <div class="flex-y-center justify-end">
          <!-- <div class="btn-group">
            <a-button
              type="primary"
              v-auth="['projectReport_quotaReport']"
              ghost
              :disabled="isDisable"
              @click="handleChange('quotaReport')"
              >批量扣量上报</a-button
            >
            <a-button
              type="primary"
              v-auth="['projectReport_updataLink']"
              ghost
              :disabled="isDisable"
              @click="handleChange('updataLink')"
              >批量更换链接</a-button
            >
            <a-button
              type="primary"
              v-auth="['projectReport_feedback']"
              ghost
              :disabled="isDisable"
              @click="handleChange('feedback')"
              >批量设置版位回传</a-button
            >
          </div> -->

          <!-- <div>
            <a-checkbox v-model:checked="state.is_running" @change="checkedChange">在投</a-checkbox>
          </div> -->
        </div>

        <TableZebraCrossing
          :data="state.tableConfigOptions"
          @change="pageChange"
          :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
        >
          <template #headerCell="{ scope }">
            <template v-if="['callback_ratio', 'bwsj'].includes(scope.column.dataIndex)">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title>{{
                    '扣量配置优先级：账户报表 < 计划报表 < 账户报表-版位 < 计划报表-版位'
                  }}</template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
            <template v-if="scope.column.dataIndex == 'flow_rate'">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title>流速：当前时间往前推10分钟的加粉数据</template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.dataIndex === 'ad_name'">
              <div>
                <a-tooltip
                  :getPopupContainer="
                    (triggerNode: any) => triggerNode.parentNode?.parentNode?.parentNode || triggerNode.parentNode
                  "
                  :overlayInnerStyle="{ width: 'max-content' }"
                >
                  <template #title>{{ record.ad_name }}</template>
                  <div class="flex-y-center">
                    <img
                      v-if="[1, 2, 3].includes(record.media_type)"
                      class="w-15px h-15px mr-4px"
                      :src="mediaType2Content(record.media_type)?.icon"
                    />
                    <span class="goods_info_data_name">
                      {{ record.ad_name || '--' }}
                    </span>
                  </div>
                </a-tooltip>
                <div class="number-id ml-20px text_overflow">ID：{{ record.adgroup_id }}</div>
              </div>
              <span
                v-if="record.monitor_warning === 1"
                class="bg-#fe4d4f h-18px lh-18px c-#fff pl-4px pr-4px inline-block border-radius-2px"
              >
                <ExclamationCircleFilled class="font-size-12px" />
                <span class="ml-4px font-size-12px">计划定向与监测定向内容不一致</span>
              </span>
            </template>
            <template v-if="column.dataIndex === 'account_name'">
              <div>
                <a-tooltip
                  :getPopupContainer="
                    (triggerNode: any) => triggerNode.parentNode?.parentNode?.parentNode || triggerNode.parentNode
                  "
                  :overlayInnerStyle="{ width: 'max-content' }"
                  v-if="record.account_name"
                >
                  <template #title>{{ record.account_name }}</template>
                  <span class="goods_info_data_name">
                    {{ record.account_name || '--' }}
                  </span>
                </a-tooltip>
                <span v-else class="goods_info_data_name">--</span>
                <div class="number-id text_overflow">ID：{{ record.account_id }}</div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'open_talk_rate'">
              <span>{{ record.open_talk_rate || 0 }}%</span>
            </template>
            <template v-if="column.dataIndex === 'conversions_count'">
              <div class="cursor-pointer c-primary" @click="handleChange('callback_ratio', record)">
                {{ record.conversions_count || 0 }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'ctr'">
              <span>{{ record.ctr || 0 }}%</span>
            </template>
            <template
              v-if="['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost'].includes(column.dataIndex)"
            >
              <span>¥{{ centsToYuan(record[column.dataIndex] || 0) }}</span>
            </template>
            <template v-if="column.dataIndex === 'fans_cost'">
              <span>¥{{ centsToYuan(record.fans_cost || 0) }}</span>
              <div
                class="flex-y-center text_overflow_row1"
                v-if="record.set_fans_cost && record.fans_cost > record.set_fans_cost"
              >
                <ExclamationCircleOutlined class="c-red font-size-12px" />
                <div class="c-red ml-4px font-size-12px h-12px line-height-12px">超出成本限制</div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'conversions_rate'">
              <span>{{ record.conversions_rate || 0 }}%</span>
            </template>
            <template v-if="column.dataIndex === 'flow_rate'">
              <div class="cursor-pointer" @click="handleChange('flowRate', record)">
                <span class="c-#52c41a">{{ record.flow_rate || 0 }}/</span>
                <span class="c-#B6AFAF">10min</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'callback_ratio'">
              <div @click="handleChange('quotaReport', record)">
                <a-button
                  class="h-auto pa-0"
                  v-if="record.deduction_report?.conversion_rate === 100 || !record.deduction_report?.conversion_rate"
                  type="link"
                  >未扣量</a-button
                >
                <a-button class="h-auto pa-0" type="link" v-else
                  >{{ record.deduction_report?.conversion_rate }}%</a-button
                >
              </div>
              <a-button class="h-auto pa-0 ml-0" type="link" @click="showDrawer('editRecord', record)"
                >修改日志</a-button
              >
            </template>
            <template v-if="column.dataIndex === 'bwsj'">
              <a-button
                v-if="record.media_type !== 1"
                class="h-auto pa-0"
                v-auth="['projectReport_feedback']"
                type="link"
                @click="handleChange('feedback', record)"
                >查看</a-button
              >
              <span v-else>--</span>
            </template>
            <template v-if="column.dataIndex === 'handle'">
              <a-button class="h-auto pa-0" type="link" @click="handleChange('PeriodReport', record)"
                >时段报表</a-button
              >
              <a-button
                class="h-auto pa-0"
                v-auth="['projectReport_quotaReport']"
                type="link"
                @click="handleChange('quotaReport', record)"
                >{{
                  record.deduction_report?.conversion_rate === 100 || !record.deduction_report?.conversion_rate
                    ? '扣量上报'
                    : '扣量中'
                }}</a-button
              >
              <template v-if="record.media_type !== 1">
                <a-button
                  class="h-auto pa-0"
                  v-auth="['projectReport_updataLink']"
                  type="link"
                  @click="handleChange('updataLink', record)"
                  >更换链接</a-button
                >
              </template>
              <!-- <a-button class="h-auto pa-0" type="link" @click="handleChange('callback', record)">手动回传</a-button> -->
            </template>
          </template>
          <template #summary>
            <a-table-summary fixed>
              <a-table-summary-row>
                <template v-for="(item, index) in state.sumColumns">
                  <template v-if="['ad_name'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index"
                      >总计：{{ state.tableConfigOptions.pagination.total }}</a-table-summary-cell
                    >
                  </template>
                  <template v-else-if="['open_talk_rate', 'ctr', 'conversions_rate'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="state.sum?.[item.dataIndex] && state.sum?.[item.dataIndex] !== 0"
                        >{{ state.sum[item.dataIndex] }}%</span
                      >
                    </a-table-summary-cell>
                  </template>
                  <template
                    v-else-if="
                      ['click_avg_cost', 'thousand_display_price', 'cost', 'conversions_cost', 'fans_cost'].includes(
                        item.dataIndex
                      )
                    "
                  >
                    <a-table-summary-cell :index="index">
                      <span>¥{{ centsToYuan(state.sum[item.dataIndex] || 0) }}</span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else-if="['flow_rate', 'callback_ratio'].includes(item.dataIndex)">
                    <a-table-summary-cell :index="index">
                      <span v-show="!state.sum?.[item.dataIndex]"></span>
                    </a-table-summary-cell>
                  </template>
                  <template v-else>
                    <a-table-summary-cell :index="index">{{ state.sum[item.dataIndex] }}</a-table-summary-cell>
                  </template>
                </template>
              </a-table-summary-row>
            </a-table-summary>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      :class="[state.dialog.type === 'callback_ratio' ? 'body_pad_none' : '']"
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
      :centered="true"
    >
      <EnterpriseWechatCustomerListModal
        v-if="state.dialog.type === 'callback_ratio'"
        type="report"
        :item="state.dialog.data"
      />
      <QuotaReport
        v-if="state.dialog.type === 'quotaReport'"
        :item="state.dialog.data"
        :list="state.dialog.list"
        :media_type="state.dialog.media_type"
        @event="onEvent"
      />
      <Feedback
        v-if="state.dialog.type === 'feedback'"
        :begin_time="state.query.begin_time"
        :end_time="state.query.end_time"
        :item="state.dialog.data"
        @event="onEvent"
      />
      <UpdataLink v-if="state.dialog.type === 'updataLink'" :item="state.dialog.data" @event="onEvent" />
      <PeriodReport v-if="state.dialog.type === 'PeriodReport'" :item="state.dialog.data" />
      <FlowRateModal v-if="state.dialog.type === 'flowRate'" :item="state.dialog.data" :type="2" />
    </a-modal>
    <a-drawer
      v-model:open="state.drawer.visible"
      :title="state.drawer.title"
      :width="state.drawer.width"
      placement="right"
      destroyOnClose
      @close="
        () => {
          state.drawer.visible = false
          getList()
        }
      "
    >
      <EditRecord :data="state.drawer.row" />
    </a-drawer>
  </div>
</template>
<script setup lang="ts">
  import datas from './src/datas'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { mediaType2Content, centsToYuan } from '@/utils'
  // import EnterpriseWechatCustomerListModal from './components/enterpriseWechatCustomerListModal.vue'
  import EnterpriseWechatCustomerListModal from '@/views/workbench/privateDomainManagement/enterpriseWechatCustomerList/index.vue'
  import FlowRateModal from './components/flowRateModal.vue'
  import QuotaReport from './components/QuotaReport.vue'
  import Feedback from './components/Feedback.vue'
  import UpdataLink from './components/UpdataLink.vue'
  import PeriodReport from './components/PeriodReport.vue'
  import { ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
  import EditRecord from './components/editRecord.vue'

  const {
    schemas,
    state,
    formConfig,
    data,
    pageChange,
    onEvent,
    onSelectChange,
    submitForm,
    isDisable,
    handleChange,
    checkedChange,
    getList,
    showDrawer
  } = datas()

  import { ref } from 'vue'
  import dayjs from 'dayjs'
  const searchFormDataRef = ref()
  const dates = ref()
  let oldDate: any = undefined
  // 打开日期选择弹窗
  const onOpenChange = (open: any) => {
    if (open) {
      oldDate = JSON.parse(JSON.stringify(searchFormDataRef.value.formData.created_at))
      dates.value = []
      searchFormDataRef.value.formData.created_at = []
    } else {
      const hasDate = dates.value.every((item: any) => item)
      if (!dates.value.length || !hasDate) searchFormDataRef.value.formData.created_at = oldDate
    }
  }
  // 日期选择
  const selectedDate = (val: any) => {
    dates.value = val
  }
  const disabledDate = (current: any) => {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  const shopForm = schemas.value.find((item: any) => item.field == 'created_at')
  if (shopForm && shopForm.props) {
    shopForm.props.disabledDate = disabledDate
  }
</script>
<style lang="scss" scoped>
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .welcome {
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-sizing: border-box;
    &:hover {
      background-color: #ffe7ce;
      color: #e87800;
      border: 1px solid #fe9d35;
    }
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
  .light {
    display: none;
  }
  .ant-btn-default:not(:disabled):hover {
    .light {
      display: block;
    }
    .default {
      display: none;
    }
  }
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: #fffaf4;
    color: var(--primary-color);
  }
  .border-radius-2px {
    border-radius: 2px;
  }
</style>
