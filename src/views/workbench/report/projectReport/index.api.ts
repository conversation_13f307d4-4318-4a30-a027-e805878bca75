import http from '@/utils/request'

//计划报表列表
export const statement_ad_latitude = (data: any) => {
  return http('get', `/common/ad/v2/statement_ad_latitude`, data)
}
/**
 * 扣量上报
 *
 */
export const oceanengine_batchedit = (data: any) => {
  return http('post', `/admin/ad/oceanengine_batchedit`, data)
}
/**
 * 获取详情
 *
 */
export const oceanengine_info = (data: any) => {
  return http('post', `/admin/ad/oceanengine_info`, data)
}
/**
 * 更换链接
 *
 */
export const updata_link = (data: any) => {
  return http('post', `/admin/change-link/add`, data)
}
/**
 * 投放链接列表
 *
 */
export const ad_link_list = (data: any) => {
  return http('post', `/admin/ad_link/list`, data)
}
/**
 * 版位回传
 *
 */
export const callback_position_list = (data: any) => {
  return http('post', `/admin/ad/callback_position_list`, data)
}
/**
 * 开启关闭版位回传
 *
 */
export const callback_position_setting = (data: any) => {
  return http('post', `/admin/ad/callback_position_setting`, data)
}

//时段报表
export const statement_account_latitude_hour = (data: any) => {
  return http('get', `/common/ad/v2/statement_account_latitude_hour`, data)
}
//版位回传-扣量上报
export const setPositionRatio = (data: any) => {
  return http('post', `/admin/ad/set_position_ratio`, data)
}

//版位回传-开关上报
export const setPositionSwitch = (data: any) => {
  return http('post', `/admin/ad/set_position_switch`, data)
}

// 扣量上报变更记录
export const get_deduction_report_log = (data: any) => {
  return http('post', `/admin/ad/get_deduction_report_log`, data)
}
