<template>
  <div>
    <SearchBaseLayout
      ref="searchFormDataRef"
      :data="searchData"
      @changeValue="searchForm"
      :actions="{
        foldNum: 0
      }"
      class="m-b-16px"
    />
    <div class="m-b-10px justify-between flex-y-center">
      <a-button type="primary" @click="submit('all')"> 全部同步 </a-button>
      <a-checkbox v-model:checked="state.checkedNew" @change="checkedNew">只展示可新增账户</a-checkbox>
    </div>
    <TableZebraCrossing
      @change="pageChange"
      :data="state.tableConfigOptions"
      :row-selection="{
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: onGetCheckboxProps
      }"
    >
      <template #bodyCell="{ scope: { record, column } }"> </template>
    </TableZebraCrossing>
    <div class="flex flex-justify-end m-t-2">
      <a-button @click="() => emits('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.saveLoading" @click="submit('')">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="tsx">
  import { uniqBy } from 'lodash'
  import { reactive, ref, watch, onMounted, nextTick } from 'vue'
  import { message } from 'ant-design-vue'
  import { getChildInfo, getOrglist, addChildAccount } from '../index.api'

  const props = defineProps(['from', 'item'])
  const emits = defineEmits(['event'])
  const searchFormDataRef = ref()
  const searchData = reactive([
    {
      type: 'select',
      field: 'id',
      value: undefined,
      props: {
        placeholder: '请选择组织账户',
        options: [],
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },

    // {
    //   type: 'select',
    //   field: 'company_name',
    //   value: undefined,
    //   props: {
    //     placeholder: '请选择主体名称',
    //     options: []
    //   },
    // layout: {
    //       xs: 24,
    //       sm: 12,
    //       md: 8,
    //       lg: 8,
    //       xl: 8,
    //       xxl: 4
    //     }
    // },
    {
      type: 'input.text',
      field: 'keywords',
      value: undefined,
      props: {
        placeholder: '请输入关键词'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])

  const state = reactive({
    saveLoading: false,
    checkedNew: false,
    orgList: [],
    activeIds: [], //ids
    product_list: [],
    query: {
      page: 1,
      page_size: 10,
      keywords: ''
    },
    selectedRowKeys: [],
    shop_list: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'token_account_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 600
      },
      dataSource: [],
      columns: [
        {
          title: '账户编号',
          key: 'token_account_id',
          dataIndex: 'token_account_id',
          width: 200
        },
        {
          title: '账户名',
          key: 'account_name',
          dataIndex: 'account_name',
          width: 240,
          ellipsis: true
        }
        // {
        //   title: '主体名称',
        //   key: 'company_name',
        //   dataIndex: 'company_name'
        // }
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 5,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    // 选中的数据
    selectionRowsPlus: []
  })
  const checkedNew = () => {
    getList()
  }
  // 组织账户列表
  const getOrgInfo = async () => {
    try {
      const res = await getOrglist({ type: 1 })
      state.activeIds = (res.data?.list || []).map((item) => item.token_account_id)
      const shopForm = searchData.find((item) => item.field == 'id')
      if (shopForm && shopForm.props) {
        if (props.from == 'ad') {
          shopForm.props.disabled = true
          if (searchFormDataRef?.value?.formData) {
            searchFormDataRef.value.formData.id = props.item.id
          }
        }
        shopForm.props.options = (res.data?.list || []).map((v) => {
          return {
            value: v.id,
            label: v.account_name || v.token_account_id
          }
        })

        await getList()
      }
    } catch (error) {
      console.error(error)
    }
  }
  getOrgInfo()

  const onSelect = (record, selected) => {
    selected
      ? state.selectionRowsPlus.push(record)
      : state.selectionRowsPlus.splice(
          state.selectionRowsPlus.findIndex((x) => x.token_account_id === record.token_account_id),
          1
        )
  }
  const onSelectAll = (selected, selectedRows, changeRows) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
    state.selectionRowsPlus = selected
      ? state.selectionRowsPlus.concat(changeRows)
      : state.selectionRowsPlus.filter((x) => !changeRows.find((i) => i.token_account_id === x.token_account_id))
  }

  async function submit(type) {
    try {
      if (!state.selectedRowKeys.length && !type) {
        return message.warning('请选择账户～～')
      }
      state.saveLoading = true
      await addAccount(type)
    } catch (error) {
      console.error(error)
    } finally {
      state.saveLoading = false
    }
  }

  const addAccount = async (type) => {
    try {
      let ids
      if (type) {
        ids = state.tableConfigOptions.dataSource.map((item) => item.token_account_id)
      } else {
        ids = uniqBy(state.selectedRowKeys)
      }
      let params = {
        token_account_ids: ids
      }
      await addChildAccount(params)
      emits('event', { cmd: 'refresh' })
    } catch (error) {}
  }

  async function getList() {
    try {
      state.tableConfigOptions.loading = true

      let params = null
      params = {
        oceanengine_id: state.query.id || props.item.id
      }

      let res = await getChildInfo(params)
      if (state.checkedNew) {
        state.tableConfigOptions.dataSource = res.data?.list.filter((item) => !item.is_lock)
      } else {
        state.tableConfigOptions.dataSource = res.data?.list || []
      }
      const { keywords } = state.query

      const dataSource = state.tableConfigOptions.dataSource
      const filteredData = dataSource.filter((item) => {
        const isKeywordsMatched = !keywords || item.account_name.includes(keywords)
        return isKeywordsMatched
      })

      state.tableConfigOptions.dataSource = filteredData
      state.tableConfigOptions.pagination.total = state.tableConfigOptions.dataSource.length || 0
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    // state.query.page = pagination.current
    // state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
  }

  const onSelectChange = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
  }
  const onGetCheckboxProps = (record) => ({
    // disabled: state.activeIds.includes(record.token_account_id),
    // name: record.token_account_id
  })
  const searchForm = (v) => {
    console.log(v)
    if (v.status) {
      state.query = {
        ...state.query,
        ...v.formData
      }
    } else {
      state.query = {}
    }

    getList()
  }
</script>
