<template>
  <div class="tab-warp">
    <a-tabs v-model:activeKey="activeName" @change="handleChange">
      <a-tab-pane key="ACCOUNT_ROLE_TYPE_ADVERTISER" tab="广告账户"></a-tab-pane>
      <a-tab-pane key="ACCOUNT_ROLE_TYPE_AGENCY" tab="代理商"></a-tab-pane>
      <a-tab-pane key="ACCOUNT_ROLE_TYPE_BUSINESS_MANAGER" tab="业务单元"></a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { getSchema } from '@/utils'
  const props = defineProps(['active'])
  const activeName = ref(props.active)
  const emits = defineEmits(['change'])
  function handleChange(val) {
    emits('change', val)
  }
  watch(
    () => props.active,
    (vld) => {
      activeName.value = vld
    },
    { immediate: true }
  )
</script>

<style scoped lang="scss">
  .tab-warp {
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
</style>
