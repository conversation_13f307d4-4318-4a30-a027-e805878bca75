<template>
  <div>
    <SearchBaseLayout
      ref="searchFormDataRef"
      :data="searchList"
      @changeValue="changeValue"
      :actions="actions"
      :showDateClear="false"
    >
      <template #btns>
        <a-button class="ml-8px mr-8px" type="primary" @click="handleAsync"> 同步广告账户 </a-button>
        <a-button type="primary" ghost :disabled="isDisable" @click="handleChange('add')"> 添加 </a-button>
      </template>
    </SearchBaseLayout>

    <TableZebraCrossing
      :data="tableData"
      @change="pageChange"
      class="mt-16px"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: (record) => {
          return {
            disabled: record.is_lock
          }
        }
      }"
    >
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'is_lock'">
          <div :class="record.is_lock ? 'success-tag' : 'disable-tag'">{{ record.is_lock ? '已添加' : '未添加' }}</div>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>
<script setup lang="ts">
  import { getProxyChildInfo, getUnitChildInfo, addProxyAccount, addUnitAccount } from '../index.api'
  import { ref, reactive, computed } from 'vue'
  import datas from './src/ViewAdAccount.ts'
  import { message } from 'ant-design-vue'
  import { useTheme } from '@/hooks'
  const { themeVar } = useTheme()
  const { columns, searchList } = datas()
  const props = defineProps(['item', 'from'])
  const emits = defineEmits(['event'])
  const searchFormDataRef = ref()
  const state = reactive({
    active: 1,
    can_add: false,
    params: {
      corporation_name: undefined,
      account_id: undefined,
      can_add: undefined
    },
    selectedRowKeys: [],
    selectedRows: [],
    // 存储原始数据用于前端分页
    rawDataSource: []
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 6,
      xxl: 4
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true
    },
    dataSource: [],
    loading: false,
    columns: columns,
    rowKey: 'account_id',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  const isDisable = computed(() => {
    return state.selectedRowKeys.length === 0
  })
  // 获取当前页数据
  const getCurrentPageData = () => {
    const { current, pageSize } = tableData.pagination
    const start = (current - 1) * pageSize
    const end = start + pageSize
    return state.rawDataSource.slice(start, end)
  }
  // 更新表格显示数据
  const updateTableData = () => {
    tableData.dataSource = getCurrentPageData()
    tableData.pagination.total = state.rawDataSource.length
  }
  const changeCan_add = () => {
    getList()
  }
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys', selectedRowKeys)

    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }

  const getList = async () => {
    try {
      tableData.loading = true
      let params = {
        id: props.item.id
      }
      let res = {}
      if (props.from === 'ACCOUNT_ROLE_TYPE_AGENCY') {
        //代理商
        res = await getProxyChildInfo(params)
      } else if (props.from === 'ACCOUNT_ROLE_TYPE_BUSINESS_MANAGER') {
        //业务单元
        res = await getUnitChildInfo(params)
      }

      let dataSource =
        state.params.can_add == 1
          ? res.data?.list.filter((item: { is_lock: boolean }) => item.is_lock)
          : state.params.can_add == 2
            ? res.data?.list.filter((item: { is_lock: boolean }) => !item.is_lock)
            : res.data?.list || []

      const { corporation_name, account_id } = state.params
      state.rawDataSource = dataSource.filter((item: { corporation_name: any; account_id: any }) => {
        if (!corporation_name && !account_id) return true
        const isKeywordsMatched = !corporation_name || item.corporation_name.includes(corporation_name)
        const isTokenAccountIdMatched = !account_id || String(item.account_id).includes(String(account_id))
        return isKeywordsMatched && isTokenAccountIdMatched
      })

      // 4. 重置分页到第一页
      tableData.pagination.current = 1
      updateTableData()
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }
  getList()
  //分页
  const pageChange = (pagination) => {
    tableData.pagination.current = pagination.current
    tableData.pagination.pageSize = pagination.pageSize
    updateTableData()
  }

  //筛选
  const changeValue = (v) => {
    console.log('筛选条件', v)
    if (v.status) {
      state.params = {
        ...v.formData
      }
    } else {
      state.params = {
        corporation_name: undefined,
        account_id: undefined
      }
      state.selectedRowKeys = []
      state.can_add = false
    }
    getList()
  }
  //同步广告账户
  const handleAsync = async () => {
    try {
      state.selectedRowKeys = []
      state.can_add = false
      await getList()
      message.success('同步成功')
    } catch (error) {
      console.error(error)
    } finally {
      tableData.loading = false
    }
  }
  //批量操作
  const handleChange = (type: string) => {
    switch (type) {
      case 'add':
        add()
        break
      case 'remove':
        getList()
        break
      default:
        break
    }
  }
  const add = async () => {
    try {
      let params = {
        id: props.item.id,
        account_ids: state.selectedRowKeys
      }
      if (props.from === 'ACCOUNT_ROLE_TYPE_AGENCY') {
        //代理商
        await addProxyAccount(params)
      } else if (props.from === 'ACCOUNT_ROLE_TYPE_BUSINESS_MANAGER') {
        //业务单元
        await addUnitAccount(params)
      }
      state.selectedRowKeys = []
      state.selectedRows = []
      // emits('event', { type: 'AdAccount', status: true })
      message.success('添加成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }
</script>

<style lang="scss" scoped>
  .timer_search {
    padding: 0 0 20px 0;
    box-sizing: border-box;
    justify-content: flex-end;
    flex-wrap: wrap;
    .search_picker {
      border-radius: 6px;
      height: 30px;
      background-color: #f0f2f6;
      margin-left: 20px;
    }
  }
  .product_id {
    margin-top: v-bind('themeVar.marginSmall');
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    font-family: PingFang SC;
    color: #404040;
    line-height: 20px;
  }
  .product_name {
    font-size: 14px;
    color: v-bind('themeVar.infoColor');
    line-height: 24px;
    cursor: pointer;
  }
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    @include set_font_config(--font-size-mini, --text-color-gray);
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    color: v-bind('themeVar.textColorGray');
    line-height: 16px;
  }
</style>
