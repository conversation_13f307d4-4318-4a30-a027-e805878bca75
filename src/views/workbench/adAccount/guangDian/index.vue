<template>
  <DesTablePage>
    <template #title> 广点通 </template>
    <template #extra
      ><a-button
        type="primary"
        v-auth="['adAccountGuangDianAdd']"
        :disabled="pointData.balance <= 0"
        @click="handleChange(data.type)"
      >
        新增
      </a-button></template
    >
    <template #action>
      <TabList :active="data.type" @change="tabsChange" />
      <SearchBaseLayout
        ref="searchFormDataRef"
        :data="adSearchList"
        @changeValue="changeValue"
        :actions="actions"
        :batchSetData="data.type == 'ACCOUNT_ROLE_TYPE_ADVERTISER' ? data.batchSetData : []"
      >
        <template #btns>
          <div>
            <a-button
              class="ml-8px mr-8px"
              type="primary"
              ghost
              v-if="data.type !== 'ACCOUNT_ROLE_TYPE_ADVERTISER'"
              v-auth="['adAccountGuangDianRemark']"
              :disabled="isDisable"
              @click="handleChange('remark')"
              >备注</a-button
            >
            <!-- <template v-if="data.type == 'ACCOUNT_ROLE_TYPE_ADVERTISER'">
              <a-button
                type="primary"
                ghost
                v-auth="['adAccountGuangDianReport']"
                :disabled="isDisable"
                @click="handleChange('quotaReport')"
              >
                扣量上报
              </a-button>
            </template> -->
          </div>
        </template>
      </SearchBaseLayout>
    </template>

    <template #tableWarp>
      <TableZebraCrossing :data="tableData" @change="pageChange" :row-selection="rowSelectionConfig">
        <template #bodyCell="{ scope: { record, column } }">
          <template v-if="column.key === 'account_name'">
            <a-tooltip placement="top" v-if="record.account_name">
              <template #title>{{ record.account_name }}</template>
              <div class="text_overflow">{{ record.account_name }}</div>
            </a-tooltip>
            <span v-else class="text_overflow">--</span>
          </template>
          <template v-if="column.key === 'oceanengine_name'">
            <a-tooltip placement="top" v-if="record.oceanengine_name">
              <template #title>{{ record.oceanengine_name }}</template>
              <div class="text_overflow">{{ record.oceanengine_name }}</div>
            </a-tooltip>
            <span v-else class="text_overflow">--</span>
          </template>

          <template v-if="column.key === 'parent_account_name'">
            <a-tooltip placement="top" v-if="record.parent_account_name">
              <template #title>{{ record.parent_account_name }}</template>
              <div class="text_overflow">{{ record.parent_account_name }}</div>
            </a-tooltip>
            <span v-else class="text_overflow">--</span>
          </template>
          <template v-if="column.key === 'remarks'">
            <div class="flex items-center">
              <a-tooltip placement="top" v-if="record.remarks">
                <template #title>{{ record.remarks }}</template>
                <div class="text_overflow">
                  {{ record.remarks }}
                </div>
              </a-tooltip>
              <span v-else class="text_overflow">--</span>
              <EditOutlined
                class="c-#FE9D35 ml4px"
                v-auth="['adAccountGuangDianRemark']"
                @click="handleChange('remark', record)"
              />
            </div>
          </template>
          <template v-if="column.dataIndex === 'empower_status'">
            <div class="flex-y-center">
              <div :class="empowerStatusClsEnum(record.empower_status)">
                {{ record.empower_status == 1 ? '有效' : '失效' }}
              </div>
            </div>
          </template>
          <template v-if="column.key === 'material_auth'">
            <div class="flex-y-center">
              <div :class="materialAuthClsEnum(record.material_auth)">
                {{ record.material_auth == 2 ? '已授权' : '未授权' }}
              </div>
            </div>
          </template>

          <template v-if="column.key === 'created_at'">
            <div class="flex-y-center">
              <div>
                {{ formatDate(record.created_at * 1000) }}
              </div>
            </div>
          </template>
          <template v-if="column.key === 'operation'">
            <template v-if="data.type !== 'ACCOUNT_ROLE_TYPE_ADVERTISER'">
              <a-button
                size="small"
                v-auth="['adAccountGuangDianView']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('viewAdAccount', record)"
                >查看广告账户</a-button
              >
            </template>
            <template v-else>
              <!-- <a-button
                size="small"
                v-auth="['adAccountOceanUpdataLink']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('updataLink', record)"
                >更换链接</a-button
              > -->
              <a-button
                size="small"
                v-auth="['adAccountGuangDianReport']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('quotaReport', record)"
                >扣量上报</a-button
              >
            </template>
          </template>
        </template>
      </TableZebraCrossing>
      <a-modal
        v-model:open="data.dialog.visible"
        wrapClassName="modal-custom-style"
        :title="data.dialog.title"
        :width="data.dialog.width"
        destroyOnClose
        :footer="null"
        :centered="true"
      >
        <EditRemark v-if="data.dialog.type === 'remark'" :item="data.dialog.item" @event="onEvent" />

        <ViewAdAccount
          v-if="data.dialog.type === 'viewAdAccount'"
          :item="data.dialog.item"
          :from="data.type"
          @event="onEvent"
        />
        <QuotaReport v-if="data.dialog.type === 'quotaReport'" :item="data.dialog.item" @event="onEvent" />
      </a-modal>
    </template>
  </DesTablePage>
</template>
<script setup name="AdDmp" lang="ts">
  import TabList from './components/TabList.vue'
  import EditRemark from './components/EditRemark.vue'
  import AddMainAccount from './components/AddMainAccount.vue'
  import ViewAdAccount from './components/ViewAdAccount.vue'
  import QuotaReport from './components/QuotaReport.vue'
  import Feedback from './components/Feedback.vue'
  import UpdataLink from './components/UpdataLink.vue'

  import { computed, ref, onMounted } from 'vue'
  import datas from './src/datas.ts'
  import { message } from 'ant-design-vue'
  import { EditOutlined } from '@ant-design/icons-vue'
  import { empowerStatusClsEnum, materialAuthClsEnum, formatDate } from '@/utils'
  import { usePoints } from '@/hooks'
  import { useRouter, useRoute } from 'vue-router'
  const { pointData } = usePoints()
  const {
    adSearchList,
    butlerColumns,
    adColumns,
    data,
    actions,
    tableData,
    changeValue,
    pageChange,
    onEvent,
    rowSelectionConfig,
    handleChange
  } = datas()
  const searchFormDataRef = ref()
  const route = useRoute()

  const isDisable = computed(() => {
    return !data.selectedRowKeys?.length || pointData.value.balance <= 0
  })

  const tabsChange = (val) => {
    data.type = val

    if (val == 'ACCOUNT_ROLE_TYPE_ADVERTISER') {
      tableData.columns = butlerColumns
    } else {
      tableData.columns = butlerColumns.filter((item) => item.key != 'parent_account_name')
    }
    searchFormDataRef.value.changeFormValue(false)
  }

  onMounted(() => {
    tabsChange(route.query.scan || 'ACCOUNT_ROLE_TYPE_ADVERTISER')
    if (route.query?.msg) {
      message.error(route.query.msg)
    }
  })
</script>
<style lang="scss" scoped>
  :deep(.ant-card-body) {
    padding-top: 4px;
  }
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
</style>
