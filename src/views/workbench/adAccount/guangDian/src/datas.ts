import { reactive, ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getOrglist, adCallback, delBatchAccount, setAuthUrl } from '../index.api.ts'
import { usePoints, useAuth } from '@/hooks'
const { isAuth } = useAuth()
export default function datas() {
  const route = useRoute()
  const router = useRouter()
  const searchFormDataRef = ref()

  const adSearchList = reactive([
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'token_account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])

  const butlerColumns = reactive([
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 160,
      ellipsis: true
    },
    {
      title: '账号ID',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      width: 140,
      ellipsis: true
    },
    {
      title: '授权来源',
      dataIndex: 'parent_account_name',
      key: 'parent_account_name',
      width: 100
    },
    {
      title: '授权状态',
      dataIndex: 'empower_status',
      key: 'empower_status',
      width: 100
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      ellipsis: true
    },
    {
      title: '授权用户',
      dataIndex: 'creator',
      key: 'creator',
      width: 140
    },
    {
      title: '授权时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 140
    }
  ])
  const DIALOG_NAME: any = {
    remark: {
      title: '备注',
      width: 600
    },
    butlerAdd: {
      title: '新增主账户',
      width: 520
    },
    adAdd: {
      title: '新增主账户',
      width: 520
    },
    viewAdAccount: {
      title: '查看广告账户',
      width: 900
    },
    quotaReport: {
      title: '扣量上报',
      width: 680
    },
    feedback: {
      title: '版位回传',
      width: 600
    },
    updataLink: {
      title: '更换链接',
      width: 600
    }
  }

  const batchSetCallback = (obj: any) => {
    handleChange(obj.type)
  }
  const handleChange = async (type: any, record?: any) => {
    console.log('record', type, record)
    let hasSelectedData = data.selectedRowKeys
    if (['quotaReport', 'updataLink', 'feedback'].includes(type)) {
      console.log('quotaReport', 'updataLink', 'feedback')
      hasSelectedData = data.selected_token_account_id
    }

    if (
      ['ACCOUNT_ROLE_TYPE_ADVERTISER', 'ACCOUNT_ROLE_TYPE_AGENCY', 'ACCOUNT_ROLE_TYPE_BUSINESS_MANAGER'].includes(type)
    ) {
      try {
        const res = await setAuthUrl({ scan: type })
        window.open(res.data.url, '_blank')
      } catch (error) {
        console.error(error)
      }
    }

    if (type === 'remove') {
      handleRemove(hasSelectedData)
      return
    }
    // if (type === 'updataLink') {
    //   hasSelectedData
    // }
    data.dialog.type = type
    data.dialog.item = record ? record : hasSelectedData
    data.dialog.title = DIALOG_NAME[type].title
    data.dialog.width = DIALOG_NAME[type].width
    data.dialog.visible = true
  }
  const data = reactive({
    type: 'ACCOUNT_ROLE_TYPE_ADVERTISER', // 账户类型
    params: {
      page: 1,
      page_size: 20
    },
    batchSetData: {
      isShow: isAuth(['adAccountGuangDianRemark', 'adAccountGuangDianReport']),
      list: [
        {
          text: '备注',
          auth: ['adAccountGuangDianRemark'],
          type: 'remark'
        },
        {
          text: '扣量上报',
          auth: ['adAccountGuangDianReport'],
          type: 'quotaReport'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    },
    selectedRowKeys2: [], // 表格选择的Item
    selectedRowKeys: [],
    selected_token_account_id: [],
    dialog: {
      title: '',
      type: '',
      visible: false,
      width: null as any,
      item: null as any
    }
  })
  watch(
    () => data.selectedRowKeys,
    (n) => {
      data.batchSetData.isSelected = n.length > 0
    }
  )
  const actions = {
    foldNum: 0
    // layout: {
    //   xs: 24,
    //   sm: 12,
    //   md: 8,
    //   lg: 8,
    //   xl: 6,
    //   xxl: 4
    // }
  }
  // 表格数据
  const tableData = reactive({
    bordered: false,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1600
    },
    rowKey: 'id',
    dataSource: [],
    loading: false,
    columns: butlerColumns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })

  const onSelectChange = (selectedRowKeys, selectedRows) => {
    data.selectedRowKeys = selectedRowKeys
    data.selected_token_account_id = selectedRows.map((it) => it.token_account_id)
  }

  const rowSelectionConfig = computed(() => {
    return {
      selectedRowKeys: data.selectedRowKeys,
      onChange: onSelectChange
    }
  })

  const changeValue = (v) => {
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData,
        token_account_id: Number(v.formData.token_account_id) || undefined
      }
      data.params.page = 1
      getList()
    } else {
      data.params = {
        page: 1,
        page_size: 10
      }
      tableData.pagination.pageSize = 10

      data.selectedRowKeys = []

      getList()
    }
  }
  // 获取广告列表
  const getList = async () => {
    try {
      tableData.loading = true
      const params = {
        ...data.params,
        account_role_type: data.type
      }
      const res = await getOrglist(params)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data.page || 1
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  // 获取广告回调code
  const getadCallback = async () => {
    try {
      let { auth_code, state } = route.query
      if (!auth_code) return getList()
      const res = await adCallback({ auth_code: auth_code, state: state })
      await getList()
      if (res.code === 0) {
        if (!res.data || res.data?.length > 0) {
          message.warning('授权失败')
        } else {
          message.success('授权成功')
        }
      }
      router.replace({
        name: 'oceanEngine'
      })
    } catch (error) {
      router.replace({
        name: 'oceanEngine'
      })
    }
  }

  //移除
  const handleRemove = async (ids) => {
    try {
      tableData.loading = true
      await delBatchAccount({ ids: ids })
      message.success('删除成功')
      data.selectedRowKeys = []
      data.selectedRowKeys2 = []
      getList()
    } catch (error) {
      console.error(error)
    }
    console.log(ids, 'ids')
  }
  const tabsChange = (val) => {
    data.type = val
    if (val == 'ACCOUNT_ROLE_TYPE_ADVERTISER') {
      tableData.columns = butlerColumns.filter((item) => item.key != 'oceanengine_name')
    } else {
      tableData.columns = butlerColumns
    }
    searchFormDataRef.value.changeFormValue(false)
  }

  const onEvent = (e) => {
    if (e?.type === 'adAdd') {
      handleChange('viewAdAccount', { id: e.id })
      return
    }
    if (e?.type === 'viewAdAccount') {
      data.type = 'ACCOUNT_ROLE_TYPE_ADVERTISER'
    }
    data.dialog = {
      title: '',
      type: '',
      visible: false,
      width: null,
      item: null
    }
    data.selectedRowKeys = []
    getList()
  }

  return {
    adSearchList,
    butlerColumns,
    data,
    actions,
    tableData,
    changeValue,
    pageChange,
    getadCallback,
    tabsChange,
    onEvent,
    rowSelectionConfig,
    handleChange
  }
}
