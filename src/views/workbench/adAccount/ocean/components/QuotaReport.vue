<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :rules="ruleData" :labelCol="{ style: { width: '110px' } }">
      <a-form-item label="扣量上报状态" name="enable">
        <a-switch class="mr16px" v-model:checked="state.form.enable" />
      </a-form-item>
      <template v-if="state.form.enable">
        <a-form-item label="扣量生效粒度" name="deduction_type">
          <a-radio-group v-model:value="state.form.deduction_type">
            <a-radio :value="1">账户</a-radio>
            <a-radio :value="2">计划</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="callback_type">
          <template #label>
            <span>回传策略</span>
            <a-tooltip :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>
                <div>普通回传：按照转化上报比例进行回传</div>
                <div>最优回传：按照优先发送消息进行回传</div>
              </template>
              <QuestionCircleFilled class="ml-3px c-#c5c6cc" />
            </a-tooltip>
          </template>
          <a-radio-group v-model:value="state.form.callback_type">
            <a-radio :value="1">普通回传</a-radio>
            <a-radio :value="2">最优回传</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item>
          <template #label><span>前N单免扣</span></template>
          <div :class="['flex-y-center resetBox', isMobile && 'flex-wrap']">
            <a-form-item label="" name="no_deduction_reset_type">
              <a-select
                v-model:value="state.form.no_deduction_reset_type"
                :options="TYPE"
                placeholder="请选择"
                class="w-120px!"
              />
            </a-form-item>
            <a-form-item class="ml-10px mr-10px w-34px"> 重置 </a-form-item>
            <a-form-item label="" name="no_deduction_order_num">
              <div class="w-220px!">
                <a-input-number
                  v-model:value="state.form.no_deduction_order_num"
                  :precision="0"
                  :min="0"
                  :max="9999999"
                >
                  <template #addonBefore>账户</template>
                  <template #addonAfter>单不扣回传</template>
                </a-input-number>
              </div>
            </a-form-item>
          </div>
        </a-form-item>
        <a-form-item>
          <template #label><span>转化上报比例</span></template>
          <div :class="['flex-y-center resetBox', isMobile && 'flex-wrap']">
            <a-form-item label="" name="conversion_reset_type">
              <a-select
                v-model:value="state.form.conversion_reset_type"
                :options="TYPE"
                placeholder="请选择"
                class="w-120px!"
              />
            </a-form-item>
            <a-form-item class="ml-10px mr-10px w-34px"> 重置 </a-form-item>
            <a-form-item label="" name="conversion_rate">
              <div class="w-310px!">
                <a-input-number v-model:value="state.form.conversion_rate" :precision="0" :min="1" :max="100">
                  <template #addonBefore>将实际转化的</template>
                  <template #addonAfter>%上报至媒体平台</template>
                </a-input-number>
              </div>
            </a-form-item>
          </div>
        </a-form-item>
        <a-form-item name="sales_volume">
          <template #label>
            <span>销售额上报</span>
            <a-tooltip>
              <template #title>加粉业务无需填写</template>
              <QuestionCircleFilled class="ml-3px c-#c5c6cc" />
            </a-tooltip>
          </template>
          <div class="flex-y-center">
            <a-input-number
              v-model:value="state.form.sales_volume"
              :precision="2"
              :min="0"
              :max="999999999"
              placeholder="请输入销售金额"
              class="w-full!"
            >
            </a-input-number>
          </div>
        </a-form-item>
      </template>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import { isArray } from 'lodash-es'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { oceanengine_batchedit, oceanengine_info } from '../index.api'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const props = defineProps(['item'])
  const emits = defineEmits(['event'])
  const ruleForm = ref(null)

  const TYPE = [
    { value: 1, label: '按天重置' },
    { value: 2, label: '按时重置' },
    { value: 3, label: '不重置' }
  ]

  const state = reactive({
    loading: false,
    form: {
      enable: false,
      deduction_type: 2,
      callback_type: 1,
      no_deduction_reset_type: 3,
      no_deduction_order_num: 5,
      conversion_reset_type: 1,
      conversion_rate: 100,
      sales_volume: undefined
    }
  })

  const ruleData = ref({
    enable: [{ required: true, message: '请选择扣量上报状态', trigger: ['change', 'blur'] }],
    deduction_type: [{ required: true, message: '请选择扣量生效粒度', trigger: ['change', 'blur'] }]
  })

  onMounted(() => {
    if (props.item && !isArray(props.item)) {
      // initData()
      const data = props.item.deduction_report || {}
      state.form = {
        enable: data.enable,
        deduction_type: data.deduction_type || 2,
        callback_type: data.callback_type || 1,
        no_deduction_reset_type: data.no_deduction_reset_type || 3,
        no_deduction_order_num: data.no_deduction_order_num || 0,
        conversion_reset_type: data.conversion_reset_type || 1,
        conversion_rate: data.conversion_rate || 100,
        sales_volume: data.sales_volume || undefined
      }
      console.log(JSON.parse(JSON.stringify(state.form)), '121212')
    }
  })
  const initData = async () => {
    try {
      let params = {
        account_id: String(props.item.token_account_id)
      }
      const { data } = await oceanengine_info(params)
      console.log(data)
    } catch (error) {
      console.error(error)
    }
  }
  const close = () => {
    emits('event', { type: 'quotaReport', status: false })
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const edit = async () => {
    try {
      state.loading = true
      let account_id_list = isArray(props.item) ? props.item.join(',') : String(props.item.token_account_id)
      let params = {
        account_id_list,
        deduction_report: state.form,
        type: 8,
        change_type: 1,
        media_type: 3
      }
      await oceanengine_batchedit(params)
      emits('event', { type: 'quotaReport', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    // margin-top: 30px;
    text-align: end;
  }
  :deep(.resetBox .ant-form-item) {
    margin-bottom: 0px;
  }
</style>
