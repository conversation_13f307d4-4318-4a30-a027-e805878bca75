<template>
  <div>
    <a-textarea
      class="recover-content"
      v-model:value="state.remark"
      placeholder="请输入备注"
      :autoSize="false"
      :rows="4"
      show-count
      :maxlength="100"
    />
    <div class="btn">
      <AButton @click="close">取消</AButton>
      <AButton type="primary" :loading="state.loading" @click="ok">确定</AButton>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue'
  import { oceanengine_batchedit } from '../index.api'
  import { message } from 'ant-design-vue'
  import { isArray } from 'lodash-es'
  interface Props {
    item: any | any[]
  }
  interface Emits {
    (event: 'event', payload: { type: 'editRemark'; status: boolean }): void
  }
  const props = defineProps<Props>()
  const emits = defineEmits<Emits>()
  const state = reactive({
    loading: false,
    remark: props.item && !isArray(props.item) ? props.item.remarks : ''
  })
  const close = () => {
    emits('event', { type: 'editRemark', status: false })
  }
  const ok = async () => {
    try {
      // if (!state.remark) {
      //   message.error('请输入备注')
      //   return
      // }
      state.loading = true
      let params = {
        ids: isArray(props.item) ? props.item : [props.item.id],
        type: 2,
        remark: state.remark.trim()
      }
      await oceanengine_batchedit(params)
      emits('event', { type: 'editRemark', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>

<style scoped lang="scss">
  .btn {
    margin-top: 30px;
    text-align: end;
  }
</style>
