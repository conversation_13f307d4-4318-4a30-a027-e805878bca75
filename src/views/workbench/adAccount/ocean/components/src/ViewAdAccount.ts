import { reactive } from 'vue'
export default function datas() {
  const searchList = reactive([
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称',
        disabled: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'token_account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'can_add',
      type: 'select',
      value: undefined,
      props: {
        options: [
          { label: '已添加', value: 1 },
          { label: '未添加', value: 2 }
        ],
        placeholder: '请选择添加状态'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])

  const columns = reactive([
    {
      title: '添加状态',
      dataIndex: 'is_lock',
      key: 'is_lock',
      width: 100
    },
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 150
    },
    {
      title: '账户ID',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      ellipsis: true,
      width: 100
    }
  ])

  const statusData = {
    0: '未转化',
    1: '未转化',
    2: '已转化',
    3: '转化失败',
    4: '已转化未上报',
    5: '手动上报',
    6: '转化黑名单',
    7: '云盾'
  }
  return { columns, searchList, statusData }
}
