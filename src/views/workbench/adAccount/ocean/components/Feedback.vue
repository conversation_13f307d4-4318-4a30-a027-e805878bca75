<template>
  <div>
    <div class="mt-24px mb-16px">
      <a-button type="primary" ghost :disabled="isDisable" @click="handleChange(1)"> 开启(回传) </a-button>
      <a-button type="primary" ghost :disabled="isDisable" @click="handleChange(2)"> 关闭(不回传) </a-button>
    </div>
    <TableZebraCrossing
      :data="tableData"
      @change="pageChange"
      class="mt-16px"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange
      }"
    >
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'is_open'">
          <a-switch v-model:checked="record.is_open" :checkedValue="1" :unCheckedValue="2" />
        </template>
      </template>
    </TableZebraCrossing>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { callback_position_list, callback_position_setting } from '../index.api.ts'
  import { reactive, computed, createVNode } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { isArray } from 'lodash-es'
  const emit = defineEmits(['event'])
  const props = defineProps(['item'])
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
    rawDataSource: []
  })
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true
    },
    dataSource: [],
    loading: false,
    columns: [
      {
        title: '回传状态',
        dataIndex: 'is_open',
        key: 'is_open',
        width: 180
      },
      {
        title: '流量版位',
        dataIndex: 'value',
        key: 'value',
        width: 180
      }
    ],
    rowKey: 'key',
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  const isDisable = computed(() => {
    return state.selectedRowKeys.length === 0
  })
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const getList = async () => {
    try {
      tableData.loading = true
      let account_id = isArray(props.item) ? '' : String(props.item.token_account_id)
      let params = {
        account_id,
        change_type: 1,
        media_type: 3
      }
      let { data } = await callback_position_list(params)
      state.rawDataSource = data || []
      tableData.pagination.current = 1
      updateTableData()
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }
  getList()
  // 获取当前页数据
  const getCurrentPageData = () => {
    const { current, pageSize } = tableData.pagination
    const start = (current - 1) * pageSize
    const end = start + pageSize
    return state.rawDataSource.slice(start, end)
  }
  // 更新表格显示数据
  const updateTableData = () => {
    tableData.dataSource = getCurrentPageData()
    tableData.pagination.total = state.rawDataSource.length
    if (tableData.dataSource.length > 8) {
      tableData.scroll.y = 350
    } else {
      delete tableData.scroll.y
    }
  }
  //分页
  const pageChange = (pagination) => {
    tableData.pagination.current = pagination.current
    tableData.pagination.pageSize = pagination.pageSize
    updateTableData()
  }
  const handleChange = async (type) => {
    try {
      state.rawDataSource.forEach((item) => {
        if (state.selectedRowKeys.includes(item.key)) {
          item.is_open = type
        }
      })
      state.selectedRowKeys = []
      state.selectedRows = []
    } catch (error) {
      console.error(error)
    }
  }
  const close = () => {
    emit('event', { type: 'feedback', status: false })
  }
  const submit = async () => {
    try {
      let account_id_list = isArray(props.item) ? props.item.join(',') : String(props.item.token_account_id)
      let close_callback_position_setting = state.rawDataSource
        .filter((item) => item.is_open === 2)
        .map((item) => item.key)
        .join(',')
      let open_callback_position_setting = state.rawDataSource
        .filter((item) => item.is_open === 1)
        .map((item) => item.key)
        .join(',')

      let params = {
        account_id_list,
        close_callback_position_setting,
        open_callback_position_setting,
        change_type: 1,
        media_type: 3
      }

      if (close_callback_position_setting.length == 0) {
        await callback_position_setting(params)
        message.success('操作成功')
        emit('event', { type: 'feedback', status: true })
        return
      }

      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '关闭后，所选广告对应版位均不在进行回传'),
        async onOk() {
          try {
            await callback_position_setting(params)
            message.success('操作成功')
            emit('event', { type: 'feedback', status: true })
          } catch (error) {
            console.error(error)
          } finally {
            Modal.destroyAll()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
</script>

<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
