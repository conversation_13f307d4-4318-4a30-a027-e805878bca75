<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :labelCol="{ style: { width: '110px' } }">
      <a-form-item
        label="选择投放链接："
        name="deployment_link"
        :rules="[{ required: true, message: '请选择投放链接' }]"
      >
        <a-select
          placeholder="请选择投放链接"
          label-in-value
          :field-names="{ label: 'name', value: 'id' }"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.deployment_link"
          :options="state.scopeOptions"
          @change="changeSelectLink"
        ></a-select>
      </a-form-item>
      <a-form-item label="选择域名：" name="domain_id" :rules="[{ required: true, message: '请选择域名' }]">
        <a-select
          placeholder="请选择域名"
          label-in-value
          :field-names="{ label: 'domain', value: 'id' }"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.domain_id"
          :options="state.domainNameOptions"
          @change="changeDomain"
        ></a-select>
      </a-form-item>
      <a-form-item label="直达链接：" :required="state.form.link_type.includes(1)" class="mb0">
        <div class="flex items-center">
          <a-form-item name="direct_domain" :rules="getRules('direct_domain')" class="w30% mr4px">
            <a-input v-model:value.trim="state.form.direct_domain" placeholder="请输入域名" />
          </a-form-item>
          <a-form-item name="direct_ad_url" :rules="getRules('direct_ad_url')" class="flex-1">
            <a-input v-model:value.trim="state.form.direct_ad_url" placeholder="请输入直达链接" />
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="落地页链接：" :required="state.form.link_type.includes(3)" class="mb0">
        <div class="flex items-center">
          <a-form-item name="land_domain" :rules="getRules('land_domain')" class="w30% mr4px">
            <a-input v-model:value.trim="state.form.land_domain" placeholder="请输入域名" />
          </a-form-item>
          <a-form-item name="land_url" :rules="getRules('land_url')" class="flex-1">
            <a-input v-model:value.trim="state.form.land_url" placeholder="请输入落地页链接" />
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="展示监测链接：" class="mb0">
        <div class="flex items-center">
          <a-form-item
            name="show_domain"
            :rules="[{ required: true, validator: showDomainValid, trigger: ['change', 'blur'] }]"
            class="w30% mr4px"
          >
            <a-input v-model:value.trim="state.form.show_domain" placeholder="请输入域名" />
          </a-form-item>
          <a-form-item
            name="show_monitor_url"
            :rules="[{ required: true, validator: showMonitorUrlValid, trigger: ['change', 'blur'] }]"
            class="flex-1"
          >
            <a-input v-model:value.trim="state.form.show_monitor_url" placeholder="请输入展示监测链接" />
          </a-form-item>
        </div>
      </a-form-item>

      <a-form-item label="点击监测链接：" :required="state.form.link_type.includes(2)" class="mb0">
        <div class="flex items-center">
          <a-form-item name="click_domain" :rules="getRules('click_domain')" class="w30% mr4px">
            <a-input v-model:value.trim="state.form.click_domain" placeholder="请输入域名" />
          </a-form-item>
          <a-form-item name="click_monitor_url" :rules="getRules('click_monitor_url')" class="flex-1">
            <a-input v-model:value.trim="state.form.click_monitor_url" placeholder="请输入点击监测链接" />
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="仅替换：" name="link_type" :rules="[{ required: true, message: '请选择替换链接' }]">
        <a-checkbox-group
          v-model:value="state.form.link_type"
          :options="state.replaceLinkGroup"
          @change="handleLinkTypeChange"
        />
      </a-form-item>
      <a-form-item label="替换范围：" class="change_type">
        <a-radio-group v-model:value="state.form.change_type" :options="state.replaceRangeGroup" />
        <div class="dashed_box" v-if="state.form.change_type === 2">
          <a-form-item
            label="广告ID："
            name="ad_list"
            class="ad_list"
            :rules="[{ required: true, message: '请输入广告ID' }]"
          >
            <a-textarea
              v-model:value="state.form.ad_list"
              placeholder="支持输入多条广告ID，删除状态的广告不生效，请用换行隔开"
              :rows="4"
              @change="change"
            />
          </a-form-item>
        </div>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { reactive, ref, nextTick, computed } from 'vue'
  import { updata_link, ad_link_list, domain_list } from '../index.api'
  import { message } from 'ant-design-vue'
  import { isArray } from 'lodash-es'
  import { useApp } from '@/hooks'
  const { useInfo } = useApp()
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])
  const ruleForm = ref(null)

  const state = reactive({
    loading: false,
    form: {
      deployment_link: undefined, //投放链接
      domain_id: undefined, //域名-
      domainName: '', //域名
      direct_domain: '', //直达链接域名
      direct_ad_url: '', //直达链接
      land_domain: '', //落地页域名
      land_url: '', //落地页链接
      show_domain: '', //展示监测链接域名
      show_monitor_url: '', //展示监测链接
      click_domain: '', //点击监测链接域名
      click_monitor_url: '', //点击监测链接
      link_type: [1, 2, 3], //仅替换
      change_type: 1, //替换范围
      ad_list: '', //广告id
      ad_list_array: []
    },
    scopeOptions: [], // 投放链接
    domainNameOptions: [], // 域名
    replaceLinkGroup: [
      { label: '直达链接', value: 1 },
      { label: '监测链接', value: 2 },
      { label: '落地页链接', value: 3 }
    ],
    replaceRangeGroup: [
      { label: '全量广告替换', value: 1 },
      { label: '部分广告替换', value: 2 }
    ]
  })
  const showDomainValid = (rule, value) => {
    if ((value === '' && state.form.show_monitor_url === '') || value) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入域名')
    }
  }
  const showMonitorUrlValid = (rule, value) => {
    if ((value === '' && state.form.show_domain === '') || value) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入展示监测链接')
    }
  }
  // const direct_ad_url = computed(() => {
  //   if (!state.form.direct_ad_url) return ''
  //   if (state.form.direct_ad_url && !state.form.domainName) return `${state.form.direct_ad_url}`
  //   if (state.form.direct_ad_url && state.form.domainName) return `${state.form.domainName}${state.form.direct_ad_url}`
  // })
  const land_url = computed(() => {
    if (!state.form.land_url) return ''
    if (state.form.land_url && !state.form.domainName) return `${state.form.land_url}`
    if (state.form.land_url && state.form.domainName) return `${state.form.domainName}${state.form.land_url}`
  })
  // const show_monitor_url = computed(() => {
  //   if (!state.form.show_monitor_url) return ''
  //   if (state.form.show_monitor_url && !state.form.domainName) return `${state.form.show_monitor_url}`
  //   if (state.form.show_monitor_url && state.form.domainName) return `${state.form.domainName}${state.form.show_monitor_url}`
  // })

  const click_monitor_url = computed(() => {
    if (!state.form.click_monitor_url) return ''
    if (state.form.click_monitor_url && !state.form.domainName) return `${state.form.click_monitor_url}`
    if (state.form.click_monitor_url && state.form.domainName)
      return `${state.form.domainName}${state.form.click_monitor_url}`
  })
  // 动态获取校验规则
  const getRules = (field) => {
    const fieldMap = {
      direct_domain: 1,
      direct_ad_url: 1,
      click_domain: 2,
      click_monitor_url: 2,
      land_domain: 3,
      land_url: 3
    }
    const isRequired = state.form.link_type.includes(fieldMap[field])
    return [
      {
        required: isRequired,
        message: `请输入${getFieldLabel(field)}`,
        trigger: 'blur'
      }
    ]
  }
  const getFieldLabel = (field) => {
    const labels = {
      direct_domain: '域名',
      direct_ad_url: '直达链接',
      click_domain: '域名',
      click_monitor_url: '点击监测链接',
      land_domain: '域名',
      land_url: '落地页链接'
    }
    return labels[field] || ''
  }
  const initData = async () => {
    try {
      let params = {
        page: 1,
        page_size: 9999,
        company_id: useInfo.value?.company_id,
        media_type: 0,
        status: 0,
        name: undefined
      }
      const [res, record] = await Promise.all([
        ad_link_list(params),
        domain_list({ page: 1, page_size: 9999, company_id: useInfo.value?.company_id })
      ])
      state.scopeOptions = res.data.list || []
      state.domainNameOptions = record.data.list || []
    } catch (error) {
      console.error(error)
    }
  }
  initData()

  //选择仅替换以后校验必填链接
  const handleLinkTypeChange = async () => {
    try {
      if (!ruleForm.value) return
      nextTick(() => {
        const validat = [
          'direct_domain',
          'direct_ad_url',
          'click_domain',
          'click_monitor_url',
          'land_domain',
          'land_url'
        ]
        ruleForm.value.validateFields(validat)
      })
    } catch (error) {
      console.error('校验失败:', error)
    }
  }
  const changeSelectLink = (e) => {
    const { option } = e
    const { h5_url: landPath, detection_url: clickPath } = option.ad_links || {}
    state.form.land_url = landPath
    state.form.click_monitor_url = clickPath
  }
  const changeDomain = (e) => {
    const { label: domain } = e
    let { link_type, show_domain, show_monitor_url } = state.form
    state.form = {
      ...state.form,
      direct_domain: link_type.includes(1) ? domain : '',
      land_domain: link_type.includes(3) ? domain : '',
      show_domain: show_domain || show_monitor_url ? domain : '',
      click_domain: link_type.includes(2) ? domain : ''
    }
  }
  const change = () => {
    state.form.ad_list_array = state.form.ad_list
      .split('\n') // 按换行拆分
      .map((id) => id.trim()) // 去除每行的前后空格
      .filter((id) => id.length > 0) // 过滤掉空行
  }
  const close = () => {
    emit('event', { type: 'updataLink', status: false })
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      edit()
    })
  }

  const edit = async () => {
    try {
      state.loading = true
      let ad_list = state.form.change_type == 1 ? [] : state.form.ad_list_array
      let { direct_ad_url, show_monitor_url, link_type, change_type } = state.form
      let params = {
        media_type: 3,
        ids: isArray(props.item) ? props.item : [props.item.id],
        direct_ad_url, //直达链接
        land_url: land_url.value, //落地页链接
        show_monitor_url: show_monitor_url, //展示监测链接
        click_monitor_url: click_monitor_url.value, //点击监测链接
        link_type, //仅替换
        change_type, //替换范围
        ad_list //广告id
      }
      await updata_link(params)
      emit('event', { type: 'updataLink', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    margin-top: 30px;
    text-align: end;
  }
  .change_type {
    :deep(.ant-row) {
      display: flex;
      align-items: baseline;
    }
    .ad_list {
      :deep(.ant-row) {
        display: flex;
        align-items: start !important;
      }
    }
  }
  .dashed_box {
    border: 1px dashed #d9d9d9;
    padding: 8px;
    margin-top: 10px;
    border-radius: 4px;
  }
</style>
