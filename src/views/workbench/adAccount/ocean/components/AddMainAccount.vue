<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :labelCol="{ style: { width: '110px' } }">
      <a-form-item
        name="account_name"
        :rules="[{ required: true, message: '请输入名称' }]"
        v-if="props.type === 'butlerAdd'"
      >
        <template #label>
          <div class="flex-align">
            <span>管家账号</span>
            <a-tooltip>
              <template #title> 建议填写巨量纵横管家账号 </template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-input
          v-model:value.trim="state.form.account_name"
          placeholder="自定义名称,用于识别身份"
          :maxlength="100"
          showCount
        />
      </a-form-item>
      <a-form-item label="管家账号：" name="account_name" :rules="[{ required: true, message: '请输入名称' }]" v-else>
        <a-select
          placeholder="请选择事件范围"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.account_name"
          :options="state.scopeOptions"
        ></a-select>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup name="GroupEdit" lang="ts">
  import { onMounted, reactive, ref } from 'vue'
  // import datas from '../src/group'
  import { setAuthUrl, getOrglist } from '../index.api'
  import { message } from 'ant-design-vue'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  // const { rules } = datas()
  const props = defineProps(['type'])
  const emit = defineEmits(['event'])
  const ruleForm = ref(null)

  const state = reactive({
    loading: false,
    form: {
      account_name: undefined
    },
    scopeOptions: []
  })
  onMounted(() => {
    if (props.type === 'adAdd') initData()
  })
  const initData = async () => {
    try {
      let params = {
        page: 1,
        page_size: 9999,
        type: 1
      }
      const res = await getOrglist(params)
      state.scopeOptions = (res.data.list || []).map((item) => {
        return {
          label: item.account_name,
          value: item.id
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const close = () => {
    emit('event', { cmd: 'close', type: props.type })
  }

  const submitForm = (formEl) => {
    formEl.validate().then(() => {
      if (props.type === 'butlerAdd') {
        butlerAdd()
      } else {
        adAdd()
      }
    })
  }
  //新增
  const butlerAdd = async () => {
    try {
      state.loading = true
      let params = {
        ...state.form
      }
      const res = await setAuthUrl(params)
      window.open(res.data.url, '_blank')
      emit('event', { type: 'butlerAdd' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  const adAdd = async () => {
    try {
      state.loading = true
      emit('event', { type: 'adAdd', id: state.form.account_name })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    margin-top: 30px;
    text-align: end;
  }
</style>
