import http from '@/utils/request'

/**
 * 获取广告列表
 * https://www.apifox.cn/link/project/2014698/apis/api-65300277
 */
export const setList = (data) => {
  return http('get', `/admin/ad/dmp_list`, data)
}

export const agnetism_list = (data) => {
  return http('get', `/admin/ad/magnetism_list`, data)
}

/**
 * 获取授权链接
 * https://www.apifox.cn/link/project/2014698/apis/api-62713590
 */
export const setAuthUrl = (data) => {
  return http('get', `/admin/ad/oceanengine_generate_auth_url`, data)
}
/**
 * 获取主体列表
 */
export const getCompanyInfo = (data) => {
  return http('get', `/admin/ad/get_company_info`, data)
}

/**
 * 广告授权回调
 * https://www.apifox.cn/link/project/2014698/apis/api-********
 */
export const adCallback = (data) => {
  return http('get', `/admin/ad/oceanengine_callback`, data)
}
export const magnetism_callback = (data) => {
  return http('get', `/admin/ad/magnetism_callback`, data)
}
/**
 * 删除账号
 *
 */
export const delAccount = (data) => {
  return http('post', `/admin/ad/oceanengine_delete`, data)
}
/**
 * 批量删除账号
 *
 */
export const delBatchAccount = (data) => {
  return http('post', `/admin/ad/oceanengine_batch_delete`, data)
}

/**
 * 获取子账号信息
 *
 */
export const getChildInfo = (data) => {
  return http('get', `/admin/ad/get_oceanengine_children`, data)
}
/**
 * 添加子账号
 *
 */
export const addChildAccount = (data) => {
  return http('post', `/admin/ad/add_oceanengine_children`, data)
}
/**
 * 获取组织/广告主列表
 *
 */
export const getOrglist = (data: any) => {
  return http('get', `/admin/ad/get_oceanengine_list`, data)
}
/**
 * 授权检测
 *
 */
export const getAuthCheck = (data) => {
  return http('get', `/admin/ad/get_oceanengine_empower`, data)
}
export const manual_batch_ad_sync = (data) => {
  return http('post', `/admin/ad/manual_batch_ad_sync`, data)
}
/**
 * 扣量上报
 *
 */
export const oceanengine_batchedit = (data) => {
  return http('post', `/admin/ad/oceanengine_batchedit`, data)
}
/**
 * 获取详情
 *
 */
export const oceanengine_info = (data) => {
  return http('post', `/admin/ad/oceanengine_info`, data)
}
/**
 * 更换链接
 *
 */
export const updata_link = (data) => {
  return http('post', `/admin/change-link/add`, data)
}
/**
 * 投放链接列表
 *
 */
export const ad_link_list = (data) => {
  return http('post', `/admin/ad_link/list`, data)
}
/**
 * 域名列表
 *
 */
export const domain_list = (data) => {
  return http('post', `/admin/domain/list`, data)
}
/**
 * 版位回传
 *
 */
export const callback_position_list = (data) => {
  return http('post', `/admin/ad/callback_position_list`, data)
}
/**
 * 开启关闭版位回传
 *
 */
export const callback_position_setting = (data) => {
  return http('post', `/admin/ad/callback_position_setting`, data)
}
