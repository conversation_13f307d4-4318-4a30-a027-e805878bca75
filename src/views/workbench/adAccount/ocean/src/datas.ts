import { reactive, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getOrglist, adCallback, delBatchAccount } from '../index.api.ts'
import { usePoints, useAuth } from '@/hooks'
const { isAuth } = useAuth()
export default function datas() {
  const route = useRoute()
  const router = useRouter()
  const searchFormDataRef = ref()
  const butlerSearchList = reactive([
    {
      type: 'input.text',
      field: 'token_account_id',
      value: undefined,
      props: {
        placeholder: '请输入管家账号ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入管家账号名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const adSearchList = reactive([
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'token_account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'oceanengine_name',
      value: undefined,
      props: {
        placeholder: '请输入管家账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])

  const butlerColumns = reactive([
    {
      title: '管家账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 160,
      ellipsis: true
    },
    {
      title: '管家账号ID',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      width: 140,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'empower_status',
      key: 'empower_status',
      width: 100
    },

    {
      title: '敏感物料授权',
      dataIndex: 'material_auth',
      key: 'material_auth',
      width: 120
    },
    {
      title: '授权用户',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 140
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      ellipsis: true
    },

    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 100
    }
  ])
  const adColumns = reactive([
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 160,
      ellipsis: true
    },
    {
      title: '账户ID',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      width: 160
    },
    {
      title: '管家账号名称',
      dataIndex: 'oceanengine_name',
      key: 'oceanengine_name',
      width: 160,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'empower_status',
      key: 'empower_status',
      width: 100
    },
    {
      title: '添加人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 140
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200
    },

    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ])

  const data = reactive({
    type: 'butler', // 账户类型
    params: {
      page: 1,
      page_size: 20
    },
    selectedRowKeys2: [], // 表格选择的Item
    selectedRowKeys: [],
    selected_token_account_id: [],
    dialog: {
      title: '',
      type: '',
      visible: false,
      width: null as any,
      item: null as any
    },
    dropOpen: false,
    batchList: [
      {
        text: '移除',
        auth: ['adAccountOceanRemove'],
        type: 'remove'
      },
      {
        text: '扣量上报',
        auth: ['adAccountOceanQuotaReport'],
        type: 'quotaReport'
      },
      {
        text: '更换链接',
        auth: ['adAccountOceanUpdataLink'],
        type: 'updataLink'
      },
      {
        text: '设置版位回传',
        auth: ['adAccountOceanFeedback'],
        type: 'feedback'
      }
    ]
  })
  const actions = {
    foldNum: 0
    // layout: {
    //   xs: 24,
    //   sm: 12,
    //   md: 8,
    //   lg: 8,
    //   xl: 8,
    //   xxl: 4
    // }
  }
  // 表格数据
  const tableData = reactive({
    bordered: false,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1000
    },
    rowKey: 'id',
    dataSource: [],
    loading: false,
    columns: butlerColumns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  // 上架状态颜色判断
  const colorType = (val: string | number) => {
    let status = {
      1: '#52C41A',
      2: '#FF4D4F'
    }
    return status[val]
  }
  // 上架状态颜色判断
  const materialColorType = (val: string | number) => {
    let status = {
      2: '#52C41A',
      1: '#FF4D4F'
    }
    return status[val]
  }
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    data.selectedRowKeys = selectedRowKeys
    state.remarkBatch.isSelected = selectedRows.length > 0
  }
  const onSelectChange2 = (selectedRowKeys, selectedRows) => {
    data.selectedRowKeys2 = selectedRowKeys
    data.selected_token_account_id = selectedRows.map((it) => it.token_account_id)
    state.batchSetData.isSelected = selectedRows.length > 0
  }
  // 当前显示引导
  const rowSelectionConfig = computed(() => {
    return {
      selectedRowKeys: data.type === 'butler' ? data.selectedRowKeys : data.selectedRowKeys2,
      onChange: data.type === 'butler' ? onSelectChange : onSelectChange2
    }
  })
  // 批量操作权限判断
  const auth = computed(() => {
    return data.type == 'ad'
      ? isAuth([
          'adAccountOceanRemark',
          'adAccountOceanRemove',
          'adAccountOceanQuotaReport',
          'adAccountOceanUpdataLink',
          'adAccountOceanFeedback'
        ])
      : isAuth(['adAccountOceanRemark'])
  })
  const changeValue = (v) => {
    console.log(v, 'v.formData')
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData,
        token_account_id: Number(v.formData.token_account_id) || undefined,
        admin_id: !v.formData.admin_id ? '' : v.formData.admin_id.join(',')
      }
      data.params.page = 1
      getList()
    } else {
      data.params = {
        page: 1,
        page_size: 20
      }
      tableData.pagination.pageSize = 20
      if (data.type == 'butler') {
        data.selectedRowKeys = []
      } else {
        data.selectedRowKeys2 = []
      }
      getList()
    }
  }
  // 获取广告列表
  const getList = async () => {
    try {
      tableData.loading = true
      const params = {
        ...data.params,
        type: data.type == 'butler' ? 1 : 2
      }
      const res = await getOrglist(params)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data.page || 1
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  // 获取广告回调code
  const getadCallback = async () => {
    try {
      console.log(route, '路由参数')
      let { auth_code, state } = route.query
      if (!auth_code) return getList()
      const res = await adCallback({ auth_code: auth_code, state: state })
      await getList()
      if (res.code === 0) {
        if (!res.data || res.data?.length > 0) {
          message.warning('部分组织授权失败')
        } else {
          message.success('授权成功')
        }
      }
      router.replace({
        name: 'oceanEngine'
      })
    } catch (error) {
      router.replace({
        name: 'oceanEngine'
      })
    }
  }

  const DIALOG_NAME: any = {
    remark: {
      title: '备注',
      width: 600
    },
    butlerAdd: {
      title: '新增主账户',
      width: 520
    },
    adAdd: {
      title: '新增主账户',
      width: 520
    },
    viewAdAccount: {
      title: '查看广告账户',
      width: 900
    },
    quotaReport: {
      title: '扣量上报',
      width: 680
    },
    feedback: {
      title: '版位回传',
      width: 600
    },
    updataLink: {
      title: '更换链接',
      width: 600
    }
  }
  const handleChange = (type: any, record?: any) => {
    let hasSelectedData = data.type === 'butler' ? data.selectedRowKeys : data.selectedRowKeys2
    if (['quotaReport', 'updataLink', 'feedback'].includes(type)) {
      console.log('quotaReport', 'updataLink', 'feedback')
      hasSelectedData = data.selected_token_account_id
    }
    if (!['butlerAdd', 'adAdd'].includes(type) && !record && !hasSelectedData.length) {
      return message.error('请至少选择一条数据')
    }

    if (type === 'remove') {
      handleRemove(hasSelectedData)
      return
    }
    // if (type === 'updataLink') {
    //   hasSelectedData
    // }
    data.dialog.type = type
    data.dialog.item = record ? record : hasSelectedData
    data.dialog.title = DIALOG_NAME[type].title
    data.dialog.width = DIALOG_NAME[type].width
    data.dialog.visible = true
  }
  //移除
  const handleRemove = async (ids) => {
    try {
      tableData.loading = true
      await delBatchAccount({ ids: ids })
      message.success('删除成功')
      data.selectedRowKeys = []
      data.selectedRowKeys2 = []
      getList()
    } catch (error) {
      console.error(error)
    }
    console.log(ids, 'ids')
  }
  const batchSetCallback = (item: any) => {
    handleChange(item.type)
  }
  const state = reactive({
    batchSetData: {
      isShow: auth,
      list: [
        {
          text: '备注',
          auth: ['adAccountOceanRemark'],
          type: 'remark'
        },
        {
          text: '移除',
          auth: ['adAccountOceanRemove'],
          type: 'remove'
        },
        {
          text: '扣量上报',
          auth: ['adAccountOceanQuotaReport'],
          type: 'quotaReport'
        },
        {
          text: '更换链接',
          auth: ['adAccountOceanUpdataLink'],
          type: 'updataLink'
        },
        {
          text: '设置版位回传',
          auth: ['adAccountOceanFeedback'],
          type: 'feedback'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    },
    remarkBatch: {
      isShow: auth,
      list: [
        {
          text: '备注',
          auth: ['adAccountOceanRemark'],
          type: 'remark'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    }
  })
  // const tabsChange = (val) => {
  //   data.type = val
  //   if (val == 'ad') {
  //     tableData.columns = columns.filter((item) => item.key != 'oceanengine_name')
  //   } else {
  //     tableData.columns = columns
  //   }
  //   searchFormDataRef.value.changeFormValue(false)
  // }
  const onEvent = (e) => {
    if (e?.type === 'adAdd') {
      handleChange('viewAdAccount', { id: e.id })
      return
    }
    if (e?.type === 'viewAdAccount') {
      data.type = 'ad'
    }
    data.dialog = {
      title: '',
      type: '',
      visible: false,
      width: null,
      item: null
    }
    data.type == 'butler' ? (data.selectedRowKeys = []) : (data.selectedRowKeys2 = [])
    getList()
  }
  const visibilitychange = () => {
    document.addEventListener('visibilitychange', function () {
      if (document.visibilityState === 'visible') {
        getadCallback()
      }
    })
  }
  return {
    butlerSearchList,
    adSearchList,
    butlerColumns,
    adColumns,
    data,
    actions,
    tableData,
    changeValue,
    pageChange,
    getadCallback,
    visibilitychange,
    onEvent,
    rowSelectionConfig,
    handleChange,
    colorType,
    materialColorType,
    state
  }
}
