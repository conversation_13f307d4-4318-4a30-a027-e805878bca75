<template>
  <DesTablePage>
    <template #title> 巨量引擎 </template>
    <template #extra
      ><a-button
        type="primary"
        v-auth="['adAccountOceanAdd']"
        :disabled="pointData.balance <= 0"
        @click="handleChange(data.type === 'butler' ? 'butlerAdd' : 'adAdd')"
      >
        新增
      </a-button></template
    >
    <template #action>
      <TabList :active="data.type" @change="tabsChange" />
      <SearchBaseLayout
        ref="searchFormDataRef"
        :data="searchList"
        @changeValue="changeValue"
        :actions="actions"
        :batchSetData="data.type == 'ad' ? state.batchSetData : state.remarkBatch"
      />
    </template>

    <template #tableWarp>
      <!-- <div class="mt-24px mb-16px">
        <a-button
          type="primary"
          ghost
          v-auth="['adAccountOceanRemark']"
          :disabled="isDisable"
          @click="handleChange('remark')"
          >备注</a-button
        >
        <template v-if="data.type == 'ad'">
          <a-button
            type="primary"
            ghost
            v-auth="['adAccountOceanRemove']"
            :disabled="isDisable"
            @click="handleChange('remove')"
          >
            移除
          </a-button>
          <a-button
            type="primary"
            ghost
            v-auth="['adAccountOceanQuotaReport']"
            :disabled="isDisable"
            @click="handleChange('quotaReport')"
          >
            扣量上报
          </a-button>
          <a-button
            type="primary"
            ghost
            v-auth="['adAccountOceanUpdataLink']"
            :disabled="isDisable"
            @click="handleChange('updataLink')"
          >
            更换链接
          </a-button>
          <a-button
            type="primary"
            ghost
            v-auth="['adAccountOceanFeedback']"
            :disabled="isDisable"
            @click="handleChange('feedback')"
          >
            设置版位回传
          </a-button>
        </template>
      </div> -->
      <TableZebraCrossing :data="tableData" @change="pageChange" :row-selection="rowSelectionConfig">
        <template #bodyCell="{ scope: { record, column } }">
          <template v-if="column.key === 'account_name'">
            <a-tooltip placement="top" v-if="record.account_name">
              <template #title>{{ record.account_name }}</template>
              <div class="text_overflow">{{ record.account_name }}</div>
            </a-tooltip>
            <span v-else class="text_overflow">--</span>
          </template>
          <template v-if="column.key === 'oceanengine_name'">
            <a-tooltip placement="top" v-if="record.oceanengine_name">
              <template #title>{{ record.oceanengine_name }}</template>
              <div class="text_overflow">{{ record.oceanengine_name }}</div>
            </a-tooltip>
            <span v-else class="text_overflow">--</span>
          </template>
          <template v-if="column.key === 'remarks'">
            <div class="flex items-center">
              <a-tooltip placement="top" v-if="record.remarks">
                <template #title>{{ record.remarks }}</template>
                <div class="text_overflow">
                  {{ record.remarks }}
                </div>
              </a-tooltip>
              <span v-else class="text_overflow">--</span>
              <EditOutlined class="c-#FE9D35 ml4px" @click="handleChange('remark', record)" />
            </div>
          </template>
          <template v-if="column.dataIndex === 'empower_status'">
            <div class="flex-y-center">
              <div :class="empowerStatusClsEnum(record.empower_status)">
                {{ record.empower_status == 1 ? '有效' : '失效' }}
              </div>
            </div>
          </template>
          <template v-if="column.key === 'material_auth'">
            <div class="flex-y-center">
              <div :class="materialAuthClsEnum(record.material_auth)">
                {{ record.material_auth == 2 ? '已授权' : '未授权' }}
              </div>
            </div>
          </template>
          <template v-if="column.key === 'operation'">
            <template v-if="data.type === 'butler'">
              <a-button
                size="small"
                v-auth="['adAccountOceanViewAdAccount']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('viewAdAccount', record)"
                >查看广告账户</a-button
              >
            </template>
            <template v-else>
              <a-button
                size="small"
                v-auth="['adAccountOceanUpdataLink']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('updataLink', record)"
                >更换链接</a-button
              >
              <a-button
                size="small"
                v-auth="['adAccountOceanQuotaReport']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('quotaReport', record)"
                >扣量上报</a-button
              >
              <a-button
                size="small"
                v-auth="['adAccountOceanFeedback']"
                class="pa-0!"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="handleChange('feedback', record)"
                >版位回传</a-button
              >
            </template>
          </template>
        </template>
      </TableZebraCrossing>
      <a-modal
        v-model:open="data.dialog.visible"
        wrapClassName="modal-custom-style"
        :title="data.dialog.title"
        :width="data.dialog.width"
        destroyOnClose
        :footer="null"
        :centered="true"
      >
        <EditRemark v-if="data.dialog.type === 'remark'" :item="data.dialog.item" @event="onEvent" />
        <AddMainAccount
          v-if="['butlerAdd', 'adAdd'].includes(data.dialog.type)"
          :type="data.dialog.type"
          @event="onEvent"
        />
        <ViewAdAccount
          v-if="data.dialog.type === 'viewAdAccount'"
          :item="data.dialog.item"
          :from="data.type"
          @event="onEvent"
        />
        <QuotaReport v-if="data.dialog.type === 'quotaReport'" :item="data.dialog.item" @event="onEvent" />
        <Feedback v-if="data.dialog.type === 'feedback'" :item="data.dialog.item" @event="onEvent" />
        <UpdataLink v-if="data.dialog.type === 'updataLink'" :item="data.dialog.item" @event="onEvent" />
      </a-modal>
    </template>
  </DesTablePage>
</template>
<script setup name="AdDmp" lang="ts">
  import TabList from './components/TabList.vue'
  import EditRemark from './components/EditRemark.vue'
  import AddMainAccount from './components/AddMainAccount.vue'
  import ViewAdAccount from './components/ViewAdAccount.vue'
  import QuotaReport from './components/QuotaReport.vue'
  import Feedback from './components/Feedback.vue'
  import UpdataLink from './components/UpdataLink.vue'

  import { computed, ref, onMounted } from 'vue'
  import datas from './src/datas.ts'
  import { EditOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue'
  import { empowerStatusClsEnum, materialAuthClsEnum } from '@/utils'
  import { usePoints } from '@/hooks'

  const { pointData } = usePoints()
  const {
    butlerSearchList,
    adSearchList,
    butlerColumns,
    adColumns,
    data,
    actions,
    tableData,
    changeValue,
    pageChange,
    getadCallback,
    visibilitychange,
    onEvent,
    rowSelectionConfig,
    handleChange,
    colorType,
    materialColorType,
    state
  } = datas()
  const searchFormDataRef = ref()
  const searchList = computed(() => {
    return data.type == 'butler' ? butlerSearchList : adSearchList
  })
  const isDisable = computed(() => {
    return (
      (data.type == 'butler' ? !data.selectedRowKeys?.length : !data.selectedRowKeys2?.length) ||
      pointData.value.balance <= 0
    )
  })
  const tabsChange = (val) => {
    data.type = val
    tableData.columns = val == 'butler' ? butlerColumns : adColumns
    searchFormDataRef.value.changeFormValue(false)
    state.batchSetData.isSelected = false
    state.remarkBatch.isSelected = false
  }
  getadCallback()
  onMounted(() => {
    visibilitychange()
  })
</script>
<style lang="scss" scoped>
  :deep(.ant-card-body) {
    padding-top: 4px;
  }
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .light {
    display: none;
  }
  .ant-btn-default:not(:disabled):hover {
    .light {
      display: block;
    }
    .default {
      display: none;
    }
  }
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: #fffaf4;
    color: var(--primary-color);
  }
</style>
