<template>
  <div>
    <DesTablePage>
      <template #title>获客链接</template>
      <template #extra>
        <a-button
          type="primary"
          v-auth="['WechatAddLink']"
          :disabled="pointData.balance <= 0"
          @click="onShowDialog('add')"
          >新增链接</a-button
        >
        <a-button v-auth="['gainCustomerLinkManagementExport']" @click="handleExportCreate">导出</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
      </template>
      <template #tableWarp>
        <div class="mb-16px">
          <span class="c-#656D7D"
            >本日加粉人数：<span class="c-#B46007 mr-8px">{{ state.fansTotal.day_num }}</span>
          </span>
          <span class="c-#656D7D"
            >本周加粉人数：<span class="c-#B46007 mr-8px">{{ state.fansTotal.week_num }}</span>
          </span>
          <span class="c-#656D7D"
            >本月加粉人数：<span class="c-#B46007 mr-8px">{{ state.fansTotal.month_num }}</span>
          </span>
        </div>
        <TableZebraCrossing
          :data="state.tableConfigOptions"
          @change="pageChange"
          :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
        >
          <template #headerCell="{ scope: { column } }">
            <div>
              <span>{{ column.title }}</span>
              <a-tooltip>
                <template #title>{{ column.tips }}</template>
                <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
              </a-tooltip>
            </div>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.dataIndex === 'title'">
              <div class="flex goods_info">
                <div class="goods_info_data">
                  <a-tooltip
                    popper-class="toolt"
                    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                    placement="topLeft"
                  >
                    <template #title>{{ record.corp_name }}</template>
                    <span class="goods_info_data_name">
                      {{ record.corp_name }}
                    </span>
                  </a-tooltip>
                  <span class="number-id">ID：{{ record.corp_id }}</span>
                  <div class="flex-y-center" v-if="record.fail_status !== 0">
                    <ExclamationCircleOutlined class="c-red font-size-12px text_overflow_row1" />
                    <div class="c-red ml-4px font-size-12px h-12px line-height-12px">
                      {{ record.fail_status === 1 ? '成员异常,请尽快处理' : '成员全部异常，请尽快处理' }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'link_name'">
              <div class="flex goods_info">
                <div class="goods_info_data">
                  <a-tooltip
                    popper-class="toolt"
                    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                    placement="topLeft"
                  >
                    <template #title>{{ record.link_name }}</template>
                    <span class="goods_info_data_name">
                      {{ record.link_name }}
                    </span>
                  </a-tooltip>
                  <a-tooltip
                    popper-class="toolt"
                    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                    placement="topLeft"
                  >
                    <template #title>{{ record.link }}</template>
                    <div class="flex-y-center">
                      <span class="goods_info_data_name">
                        {{ record.link }}
                      </span>
                      <CopyOutlined class="c-primary" @click="copy(record.link)" />
                    </div>
                  </a-tooltip>
                  <span class="number-id">ID：{{ record.link_id }}</span>
                </div>
              </div>
            </template>

            <template v-if="column.dataIndex === 'on_sale'">
              <span :class="[record.on_sale == 1 ? 'online' : 'offline']" class="item-tag">{{
                record.on_sale == 1 ? '上线' : '下线'
              }}</span>
            </template>
            <template v-if="column.dataIndex === 'remark'">
              <div class="flex">
                <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                  <template #title>{{ record.remark || '--' }}</template>
                  <div class="text_overflow">{{ record.remark || '--' }}</div>
                </a-tooltip>
                <EditOutlined
                  class="c-primary w-12px ml-6px cursor-pointer"
                  v-auth="['WechatRemark']"
                  @click="onShowDialog('remark', record)"
                />
              </div>
            </template>
            <template v-if="column.dataIndex === 'wx_user_num'">
              <div class="flex-y-center">
                <span class="cursor-pointer c-primary" @click="onShowDialog('user', record)">{{
                  record.wx_user_num
                }}</span>
                <!-- <ReloadOutlined class="c-primary w-12px ml-6px cursor-pointer" @click="updateUser(record)" /> -->
              </div>
            </template>
            <template v-if="column.dataIndex === 'welcome'">
              <span
                class="item-tag welcome font-size-14px! cursor-pointer"
                v-auth="['WechatWelcome']"
                @click="onShowDialog('welcome', record)"
                >欢迎语</span
              >
            </template>
            <template v-if="column.dataIndex === 'tag'">
              <span
                class="item-tag welcome font-size-14px! cursor-pointer"
                v-auth="['WechatLabel']"
                @click="onShowDialog('label', record)"
                >客户标签</span
              >
            </template>
            <template v-if="column.dataIndex === 'month_num'">
              <div class=" ">
                <span class="c-primary cursor-pointer" @click="onShowDialog('month', record)">{{
                  record.month_num
                }}</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'group_list'">
              <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                <template #title>
                  {{
                    record.group_list?.length > 0 ? record.group_list.map((it: any) => it.name).join(',') : '--'
                  }}</template
                >
                <div class="text_overflow_row3">
                  {{ record.group_list?.length > 0 ? record.group_list.map((it: any) => it.name).join(',') : '--' }}
                </div>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'week_num'">
              <div class=" ">
                <span class="c-primary cursor-pointer" @click="onShowDialog('week', record)">{{
                  record.week_num
                }}</span>
              </div>
            </template>

            <template v-if="column.dataIndex === 'day_num'">
              <div class=" ">
                <span class="c-primary cursor-pointer" @click="onShowDialog('day', record)">{{ record.day_num }}</span>
              </div>
            </template>

            <template v-if="column.dataIndex === 'status'">
              <div class="flex-y-center">
                <span class="round" :style="{ background: colorType(record.status) }"></span>
                <div class="ml-4px">{{ record.status == 1 ? '正常' : '异常' }}</div>
              </div>
            </template>

            <template v-if="column.dataIndex === 'handle'">
              <a-button
                class="h-auto pa-0"
                v-auth="['WechatEditLink']"
                type="link"
                :disabled="pointData.balance <= 0"
                @click="onShowDialog('edit', record)"
                >编辑</a-button
              >

              <a-popconfirm
                title="请确认是否删除当前链接？"
                :disabled="pointData.balance <= 0"
                placement="topRight"
                @confirm="onDel(record.id, '')"
              >
                <a-button class="h-auto pa-0" v-auth="['WechatDelLink']" :disabled="pointData.balance <= 0" type="link"
                  >删除</a-button
                >
              </a-popconfirm>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <SetWelcome v-if="state.dialog.type == 'welcome'" :item="state.item" @event="onEvent" />
      <FansList
        v-if="['month', 'week', 'day'].includes(state.dialog.type)"
        :item="state.item"
        :type="state.dialog.type"
        @event="onEvent"
      />
      <UserList v-if="['user'].includes(state.dialog.type)" :item="state.item" @event="onEvent" />
      <SetLabel v-if="['label'].includes(state.dialog.type)" :item="state.item" @event="onEvent" />
      <Remark v-if="['remark'].includes(state.dialog.type)" :item="state.item" @event="onEvent" />
      <GainCustomerLinkModal v-if="['edit', 'add'].includes(state.dialog.type)" :item="state.item" @event="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'gainCustomerLinkManagement' })
  import { QuestionCircleFilled, EditOutlined, ExclamationCircleOutlined, CopyOutlined } from '@ant-design/icons-vue'
  import SetWelcome from './components/SetWelcome.vue'
  import FansList from './components/FansList.vue'
  import UserList from './components/UserList.vue'
  import SetLabel from './components/SetLabel.vue'
  import Remark from './components/Remark.vue'
  import GainCustomerLinkModal from './components/GainCustomerLinkModal.vue'
  import datas from './src/data'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  import { copy } from '@/utils'
  const {
    schemas,
    state,
    formConfig,
    colorType,
    pageChange,
    onShowDialog,
    onDel,
    onEvent,
    onSelectChange,
    submitForm,
    updateUser,
    handleExportCreate
  } = datas()
</script>
<style lang="scss" scoped>
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .welcome {
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-sizing: border-box;
    &:hover {
      background-color: #ffe7ce;
      color: #e87800;
      border: 1px solid #fe9d35;
    }
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
</style>
