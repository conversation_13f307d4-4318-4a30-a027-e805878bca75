<template>
  <div class="comp_dialog_sold_out">
    <a-form ref="ruleFormRef" :model="form" :rules="rules">
      <a-form-item label="备注：">
        <a-textarea
          :auto-size="{ minRows: 7 }"
          v-model:value="form.remark"
          :maxlength="200"
          :show-count="true"
          placeholder="请输入备注内容"
        ></a-textarea>
      </a-form-item>
    </a-form>
    <div style="text-align: right">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="state.loading">保存</a-button>
    </div>
  </div>
</template>
<script lang="tsx" setup name="Remark">
  import { reactive, ref } from 'vue'
  import { save_remark } from '../index.api'
  import { message } from 'ant-design-vue'
  const emit = defineEmits(['event'])
  const state = reactive({
    loading: false
  })

  const rules = {
    remark: [{ required: true, message: '请输入备注内容', trigger: ['blur', 'change'] }]
  }
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    // 审核不通过时的type
    type: [String, Number]
  })
  const form = reactive({
    id: props.item.id,
    remark: props.item.remark
  })
  const ruleFormRef = ref()

  async function submitForm() {
    try {
      state.loading = true
      if (!(await ruleFormRef.value.validate())) return
      const params = {
        id: form.id,
        remark: form.remark
      }
      await save_remark(params)
      message.success('保存成功')
      emit('event', { cmd: 'success' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>

<style lang="scss" scoped>
  .item-link {
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
</style>
