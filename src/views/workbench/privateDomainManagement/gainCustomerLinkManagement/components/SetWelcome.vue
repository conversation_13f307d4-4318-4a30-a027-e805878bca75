<template>
  <div class="comp_dialog_sold_out">
    <a-form :model="form" :labelCol="{ style: { width: '62px' } }">
      <a-form-item label="欢迎语：" name="">
        <a-textarea
          :auto-size="{ minRows: 7 }"
          v-model:value="form.remark"
          :maxlength="200"
          showCount
          placeholder="请输入欢迎语"
        ></a-textarea>
        <div class="">
          <a-button type="link" class="p-0!" @click="addName">插入客户昵称</a-button>
        </div>
      </a-form-item>

      <div class="link-list">
        <div v-for="(item, index) in form.welcome_link" :key="index" class="flex-y-center">
          <div class="item-link text_overflow">
            <a-tooltip>
              <template #title>{{ item.link }}</template>
              {{ item.link }}
            </a-tooltip>
          </div>
          <a-button type="link" danger class="c-#FE4D4F mb-10px" @click="delLink(index)">删除</a-button>
        </div>
      </div>

      <!-- <a-input v-model:value="form.link" class="w-480px" placeholder="请输入链接，以http或https开头" /> -->
      <!-- </a-form-item> -->
    </a-form>
    <a-button type="primary" :disabled="form.welcome_link.length > 4" @click="addLink">添加链接</a-button>
    <div style="text-align: right">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="state.loading">保存</a-button>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      @ok="okModal"
      destroyOnClose
    >
      <a-form ref="ruleFormRef" :model="forms" :rules="rules" :labelCol="{ style: { width: '62px' } }">
        <a-form-item label="链接：" name="link">
          <a-input v-model:value="forms.link" placeholder="请输入链接，以http或https开头" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="tsx" setup name="DialogSoldOut">
  import { reactive, ref } from 'vue'
  import { save_welcome_txt } from '../index.api'
  import { message } from 'ant-design-vue'
  const emit = defineEmits(['event'])
  const state = reactive({
    loading: false,
    dialog: {
      visible: false,
      title: '添加链接',
      width: 500,
      type: 'welcome'
    }
  })

  const rules = {
    remark: [{ required: true, message: '请输入欢迎语', trigger: ['blur', 'change'] }],
    link: [
      { required: true, message: '请输入链接', trigger: ['change', 'blur'] },
      {
        pattern: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
        message: '请输入正确的链接',
        trigger: ['change', 'blur']
      }
    ]
  }
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    // 审核不通过时的type
    type: [String, Number]
  })
  const form = reactive({
    id: props.item.id,
    remark: props.item.welcome_txt,
    link: '',
    welcome_link: props.item.welcome_link
      ? props.item.welcome_link.split(',').map((item) => {
          return {
            link: item
          }
        })
      : ([] as Array<{ link: string }>)
  })
  const forms = reactive({
    link: undefined
  })
  const ruleFormRef = ref()
  const addLink = async () => {
    state.dialog.visible = true
    forms.link = undefined
  }

  const okModal = async () => {
    if (!(await ruleFormRef.value.validate('link'))) return
    form.welcome_link.push({
      link: forms.link
    })
    state.dialog.visible = false
  }

  const addName = () => {
    const textToAdd = '{客户昵称}'
    if (form.remark.length + textToAdd.length <= 200) {
      form.remark += textToAdd
    } else {
      // 可以选择在这里给用户一个提示，例如：
      // 或者使用 message 组件提示用户
      message.warning('已达到最大字数限制')
    }
  }
  const delLink = (index: number) => {
    form.welcome_link.splice(index, 1)
  }
  async function submitForm() {
    try {
      state.loading = true

      // if (!(await ruleFormRef.value.validate())) return
      const params = {
        id: props.item.id,
        welcome_txt: form.remark,
        welcome_link: form.welcome_link.map((item) => item.link).join(',')
      }
      await save_welcome_txt(params)
      message.success('保存成功')
      emit('event', { cmd: 'success' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>

<style lang="scss" scoped>
  .comp_dialog_sold_out {
    color: #404040;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-bottom: 20px;
    }
    .tip {
      font-size: 12px;
      color: #85878a;
    }
  }
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
</style>
