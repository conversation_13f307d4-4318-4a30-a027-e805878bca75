<template>
  <div class="form-scroll-wrapper">
    <a-form
      class="form-scroll-box"
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width: 128px;' }"
      autocomplete="off"
      :colon="true"
      :rules="rules"
    >
      <a-form-item label="企微" name="corpid">
        <a-select
          :disabled="!!item?.id"
          :show-search="true"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.corpid"
          :options="state.enterpriseWechatList"
          :filter-option="filterOption"
          :fieldNames="{ value: 'corpid', label: 'corp_name' }"
          placeholder="请选择企微"
          allowClear
          @change="enterpriseWechatListChange"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="生成方式" name="gen_method">
        <a-radio-group v-model:value="state.form.gen_method" :disabled="!!item?.id">
          <a-radio :value="1"><span class="span">单个生成</span></a-radio>
          <a-radio :value="2"><span class="span">批量生成</span></a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="生成规则" name="rules" v-if="state.form.gen_method === 2">
        <a-select
          disabled
          :show-search="true"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.rules"
          :options="[{ label: '一个企微成员生成一个获客链接', value: 1 }]"
          :filter-option="filterOption"
          placeholder="一个企微成员生成一个获客链接"
          allowClear
        >
        </a-select>
      </a-form-item>
      <a-form-item label="成员" name="wx_user_id">
        <a-select
          :show-search="true"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.wx_user_id"
          :filter-option="filterOption"
          mode="multiple"
          placeholder="请选择企微成员，逗号分割可搜索多个"
          allowClear
          :disabled="!state.form.corpid"
        >
          <a-select-option
            v-for="item in state.memberList"
            :disabled="item.wx_status !== 1"
            :key="item.user_id"
            :value="item.user_id"
          >
            <div class="flex-y-center">
              <span>{{ item.name }} </span>
              <ExclamationCircleOutlined v-show="item.wx_status !== 1" class="ml-4px c-red font-size-12px" />
              <span class="ml-4px font-size-12px c-red" v-show="item.wx_status !== 1">成员异常</span>
            </div>
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="auth_on_sale">
        <template #label>
          <div class="flex-y-center">
            <span>自动上线/下线</span>
            <a-tooltip>
              <template #title>上线：可通过落地页正常添加；下线：不可通过落地页添加</template>
              <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
            </a-tooltip>
          </div>
        </template>
        <a-radio-group v-model:value="state.form.auth_on_sale" @change="authOnSaleChange">
          <a-radio :value="1"><span class="span">不设置</span></a-radio>
          <a-radio :value="2"><span class="span">按时间段</span></a-radio>
          <a-radio :value="3"><span class="span">按成功添加企微次数</span></a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item name="checkCycle" label="检查周期" required v-if="state.form.auth_on_sale === 3">
        <a-radio v-model:checked="state.form.checkCycle" :value="1">每天</a-radio>
      </a-form-item>
      <a-form-item name="time_str" class="period-item" v-if="[2, 3].includes(state.form.auth_on_sale)">
        <div class="ml-36px mt--14px">
          <SelectTime
            :key="state.form.auth_on_sale"
            v-model="state.form.time_str"
            @change="selectTimeChange"
          ></SelectTime>
        </div>
      </a-form-item>
      <a-form-item
        name="off_sale_rule"
        class="down-rules-count-wrapper"
        label="下线规则"
        v-if="state.form.auth_on_sale === 3"
      >
        <div class="flex-y-center">
          <span>成功添加企业微信</span>
          <a-input-number
            v-model:value.trim="state.form.off_sale_rule"
            :controls="false"
            :precision="0"
            :min="1"
            placeholder="请输入"
          />
          <span>次后自动下线</span>
        </div>
      </a-form-item>
      <a-form-item name="add_fans_type" label="加粉限制">
        <a-checkbox v-model:checked="state.form.add_fans_type">同一获客链接限制用户多次添加</a-checkbox>
      </a-form-item>
      <a-form-item name="on_sale">
        <template #label>
          <div class="flex-y-center">
            <span>上下线</span>
            <a-tooltip>
              <template #title>开启为上线，关闭为下线</template>
              <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
            </a-tooltip>
          </div>
        </template>
        <a-switch v-model:checked="state.form.on_sale" :checkedValue="1" :unCheckedValue="2" />
      </a-form-item>
      <a-form-item name="weight">
        <template #label>
          <div class="flex-y-center">
            <span>权重</span>
            <a-tooltip>
              <template #title>数字越大，进粉的几率越大</template>
              <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
            </a-tooltip>
          </div>
        </template>
        <a-select
          :show-search="true"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.weight"
          placeholder="请选择权重"
          allowClear
        >
          <a-select-option :value="item" v-for="item in 10">{{ item }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="link_name" label="名称">
        <a-textarea
          id="textarea"
          ref="text_content"
          class="flex-1"
          :maxLength="50"
          :rows="3"
          placeholder="请输入链接名称"
          v-model:value="state.form.link_name"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <template #extra>
          <a-button
            type="link"
            class="p-0! h-auto"
            :disabled="dataDisabled"
            @click.stop="onAddeventSign({ Name: '日期', Value: 'date' })"
            >{日期}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="nameDisabled"
            @click.stop="onAddeventSign({ Name: '企微成员名称', Value: 'name' })"
            >{企微成员名称}</a-button
          >
        </template>
      </a-form-item>
    </a-form>
    <div class="text-right mr-20px">
      <AButton @click="emits('event', { cmd: 'close' })">取消</AButton>
      <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import type { Rule } from 'ant-design-vue/es/form'
  import { QuestionCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { reactive, ref, onMounted, computed } from 'vue'
  import { debounce } from 'lodash-es'
  import dayjs from 'dayjs'
  import {
    getEnterpriseWechatList as getEnterpriseWechatListApi,
    saveGainCustomerLink,
    getMemeberList as getMemeberListApi,
    getLinkInfo
  } from '../index.api'
  import { message } from 'ant-design-vue'
  const rules: Record<string, Rule[]> = {
    corpid: [{ required: true, message: '请选择企微', trigger: ['blur', 'change'] }],
    gen_method: [{ required: true, message: '请选择生成方式', trigger: ['blur', 'change'] }],
    rules: [{ required: true, message: '请选择生成规则', trigger: ['blur', 'change'] }],
    wx_user_id: [{ required: true, message: '请选择成员', trigger: ['blur', 'change'] }],
    auth_on_sale: [{ required: true, message: '请选择自动上线/下线', trigger: ['blur', 'change'] }],
    on_sale: [{ required: true, message: '请选择上下线', trigger: ['blur', 'change'] }],
    weight: [{ required: true, message: '请选择权重', trigger: ['blur', 'change'] }],
    link_name: [{ required: true, message: '请输入链接名称', trigger: ['blur', 'change'] }],
    time_str: [
      {
        required: true,
        validator: (_, value) => {
          if (String(value).indexOf('1') === -1) {
            return Promise.reject('请选择时段')
          } else {
            return Promise.resolve()
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    off_sale_rule: [
      {
        required: true,
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('请输入下线规则')
          } else {
            return Promise.resolve()
          }
        },
        trigger: ['blur', 'change']
      }
    ]
  }
  const ruleForm = ref()
  const textareaCache = { start: '', end: '' }
  const text_content = ref(null)
  const props = defineProps(['item'])
  const emits = defineEmits(['event'])
  const state = reactive({
    loading: false,
    enterpriseWechatList: [],
    memberList: [] as any,
    form: {
      corpid: undefined,
      gen_method: 1,
      rules: 1,
      wx_user_id: undefined,
      auth_on_sale: 1,
      add_fans_type: undefined,
      on_sale: 1,
      weight: undefined,
      link_name: undefined,
      checkCycle: 1,
      off_sale_rule: undefined,
      time_str: undefined
    } as any
  })
  const dataDisabled = computed(() => {
    if (!state.form.link_name) return false
    return state.form.link_name?.length > 46 || state.form.link_name?.indexOf('{日期}') !== -1
  })
  const nameDisabled = computed(() => {
    if (!state.form.link_name) return false
    return state.form.link_name?.length > 42 || state.form.link_name?.indexOf('{企微成员名称}') !== -1
  })
  const filterOption = (input: string, option: any) => {
    return (option?.label || option?.corp_name || option?.name).toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const authOnSaleChange = () => {
    // 切换时清空时间点
    state.form.time_str = Array(7)
      .fill()
      .map(() => Array(48).fill(0))
  }
  const selectTimeChange = () => {
    ruleForm.value?.validateFields(['time_str'])
  }
  const submitForm = async () => {
    try {
      state.loading = true
      await ruleForm.value?.validate()
      let params = {
        ...state.form
      }
      if (params.wx_user_id?.length) {
        params.wx_user_id = params.wx_user_id.join(',')
      }
      if (params.add_fans_type) {
        params.add_fans_type = 1
      } else {
        params.add_fans_type = 2
      }
      if (props.item?.id) {
        params.id = props.item.id
      }
      if (params.time_str?.length && [2, 3].includes(params.auth_on_sale)) {
        params.time_str = params.time_str.flat().join('')
      } else {
        params.time_str = undefined
      }
      if ([2, 1].includes(params.auth_on_sale)) {
        params.off_sale_rule = undefined
      }
      // if (state.form.link_name?.indexOf('{日期}') !== -1) {
      //   params.link_name = state.form.link_name.replace(/\{日期\}/g, dayjs().format('YYYY-MM-DD'))
      // }
      // if (state.form.link_name?.indexOf('{企微成员名称}') !== -1) {
      //   let current = state.memberList.find((it: any) => state.form?.wx_user_id?.[0] === it.user_id)?.name || ''
      //   params.link_name = params.link_name.replace(/\{企微成员名称\}/g, current)
      // }
      let res: any = await saveGainCustomerLink(params)
      if (res.code === 0) {
        if (state.form.gen_method === 2 && !props.item?.id && Object.keys(res.data)?.length) {
          message.success(`创建成功${res.data?.success_num}个,失败${res.data?.err_num}个`)
        } else {
          message.success(res.msg)
        }

        emits('event', { cmd: 'submit' })
      }
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }

  // 点击按钮添加标签
  const onAddeventSign = debounce((e) => {
    // 根据 e.Value 的值设置 currentTarget
    let currentTarget = `{${e.Name}}`

    if (!textareaCache.start && !textareaCache.end) {
      state.form.link_name = currentTarget
      textareaCache.start = currentTarget
    } else if (textareaCache.start) {
      state.form.link_name = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
    } else if (textareaCache.end) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
    ruleForm.value?.validateFields(['link_name'])
  }, 500)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea: any = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.link_name || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }

  const getEnterpriseWechatList = async () => {
    try {
      let res: any = await getEnterpriseWechatListApi({ page: 1, page_size: 10000 })
      if (res.code === 0) {
        state.enterpriseWechatList = res.data.list || []
      }
    } catch (err) {
      console.log(err)
      state.enterpriseWechatList = []
    }
  }
  getEnterpriseWechatList()
  const enterpriseWechatListChange = (val: number) => {
    console.log('val', val)
    state.memberList = []
    state.form.wx_user_id = undefined
    if (val) {
      getMemeberList(val)
    }
  }
  const getMemeberList = async (corpid: number) => {
    try {
      let res: any = await getMemeberListApi({ corpid: corpid, page: 1, page_size: 10000, status: 1 })
      if (res.code === 0) {
        state.memberList = []
        state.memberList = res.data.wechatWorkUsers || []
      }
    } catch (err) {
      console.log(err)
      state.memberList = []
    }
  }
  // 初始化二维数组为0
  function restoreArray(flatStr: string, chunkSize = 48) {
    const result = []
    for (let i = 0; i < flatStr.length; i += chunkSize) {
      const chunk = flatStr.slice(i, i + chunkSize)
      result.push(chunk.split('').map(Number))
    }
    return result
  }
  onMounted(async () => {
    try {
      if (props.item?.id) {
        let res: any = await getLinkInfo({ id: props.item.id })
        if (res.code === 0) {
          state.form = {
            ...state.form,
            ...res.data
          }
          if (res.data.add_fans_type === 1) {
            state.form.add_fans_type = true
          } else {
            state.form.add_fans_type = false
          }
          if (res.data?.time_str) {
            state.form.time_str = restoreArray(res.data.time_str)
          }
          if (res.data.wx_user_id) {
            state.form.wx_user_id = res.data.wx_user_id.split(',')
          }
          if (res.data?.corp_id) {
            state.form.corpid = res.data.corp_id
            await getMemeberList(res.data.corp_id)
          }

          console.log('state.form', state.form)
        }
      }
    } catch (err) {
      console.log(err)
    }
  })
</script>

<style lang="scss" scoped>
  :deep(.ant-form-item) {
    margin-bottom: 16px;
    .ant-radio-wrapper-checked {
      .span {
        color: var(--primary-color);
      }
    }
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      color: #323233;
    }
    .ant-form-item-extra {
      margin-top: 8px;
      font-size: 12px;
    }
    .color-l {
      color: var(--primary-color);
      cursor: pointer;
    }
    .color-r {
      color: rgba(0, 0, 0, 0.25);
      cursor: pointer;
    }
  }

  .down-rules-count-wrapper {
    height: 28px;

    :deep(.ant-input-number) {
      margin-left: 8px;
      margin-right: 8px;
      .ant-input-number-input {
        height: 28px;
        line-height: 28px;
      }
    }
    :deep(.ant-form-item-explain) {
      margin-left: 120px;
    }
  }
  .period-item {
    :deep(.ant-form-item-explain) {
      margin-left: 36px;
    }
  }
</style>
