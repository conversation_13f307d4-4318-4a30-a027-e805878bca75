<template>
  <div>
    <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
    <TableZebraCrossing class="mt-24px" :data="state.tableConfigOptions" @change="pageChange">
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.dataIndex === 'ad_platform'">
          <div class="flex-y-center">
            <img class="w-15px h-15px mr-4px" :src="adPlatformData[record.ad_platform].img" alt="" />
            <span>{{ adPlatformData[record.ad_platform].name || '--' }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'corp_name'">
          <div class="flex goods_info">
            <div class="goods_info_data">
              <a-tooltip
                popper-class="toolt"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="topLeft"
              >
                <template #title>{{ record.corp_name }}</template>
                <span class="goods_info_data_name">
                  {{ record.corp_name }}
                </span>
              </a-tooltip>
              <span class="number-id">ID：{{ record.corpid }}</span>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'customer_name'">
          <div class="flex goods_info">
            <div class="goods_info_data">
              <a-tooltip
                popper-class="toolt"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="topLeft"
              >
                <template #title>{{ record.customer_name }}</template>
                <span class="goods_info_data_name">
                  {{ record.customer_name }}
                </span>
              </a-tooltip>
              <span class="number-id">ID：{{ record.extend_user_id }}</span>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'wx_user_name'">
          <div class="flex goods_info">
            <div class="goods_info_data">
              <a-tooltip
                popper-class="toolt"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="topLeft"
              >
                <template #title>{{ record.wx_user_name }}</template>
                <span class="goods_info_data_name">
                  {{ record.wx_user_name }}
                </span>
              </a-tooltip>
              <span class="number-id">ID：{{ record.wx_user_id }}</span>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'link_name'">
          <div class="flex goods_info">
            <div class="goods_info_data">
              <a-tooltip
                popper-class="toolt"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="topLeft"
              >
                <template #title>{{ record.link_name }}</template>
                <span class="goods_info_data_name">
                  {{ record.link_name }}
                </span>
              </a-tooltip>
              <span class="number-id">ID：{{ record.wx_link_id }}</span>
            </div>
          </div>
        </template>
        <template v-if="column.key === 'is_chat'">
          <div class="flex-y-center">
            <span class="round" :style="{ background: chatEnumCls(record.is_chat) }"></span>
            <span :style="{ color: chatEnumCls(record.is_chat) }">
              {{ chatEnum(record.is_chat) }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <div class="flex items-center">
            <span class="round" :style="{ background: callbackStatus2Content(record.status)?.color }"></span>
            <div>{{ callbackStatus2Content(record.status)?.label || '--' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'callback_at'">
          <div>{{ record.is_callback === 1 ? record.callback_at : '' }}</div>
        </template>
        <template v-if="column.dataIndex === 'is_welcome'">
          <div>{{ record.is_welcome === 1 ? '是' : '否' }}</div>
        </template>
        <template v-if="column.dataIndex === 'customer_tag'">
          <div>{{ record.customer_tag?.length ? '是' : '否' }}</div>
        </template>
        <template v-if="column.dataIndex === 'corp_img'">
          <a-image style="width: 80px" :src="record.corp_img" />
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'fanList' })
  import { getFansList as getFansListApi } from '../index.api'
  import { onMounted, reactive, ref } from 'vue'
  import dayjs from 'dayjs'
  import { requireImg, callbackStatusParams, callbackStatus2Content, chatEnumCls, chatEnum, mediaType } from '@/utils'
  const schemas = ref([
    {
      type: 'date',
      field: 'customer_add_time',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'ad_platform',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [...mediaType],
        placeholder: '请选择投放媒体'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'is_chat',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [
          {
            label: '客户未发消息',
            value: 2
          },
          {
            label: '客户已发消息',
            value: 1
          },
          {
            label: '客户待通过申请',
            value: 3
          }
          // {
          //   label: '客户发送消息状态未知',
          //   value: 3
          // }
        ],
        placeholder: '请选择回话状态'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },

    {
      field: 'status',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: callbackStatusParams,
        placeholder: '请选择回传状态'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'customer_name',
      type: 'input.text',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请输入客户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = reactive([
    { title: '投放媒体', dataIndex: 'ad_platform', width: 130, key: 'ad_platform' },
    { title: '企微头像', dataIndex: 'corp_img', width: 80, key: 'corp_img' },
    { title: '企微名称', dataIndex: 'corp_name', width: 180, key: 'corp_name' },
    { title: '客户名称', dataIndex: 'customer_name', key: 'customer_name', width: 180 },
    { title: '跟进企微', dataIndex: 'wx_user_name', key: 'wx_user_name', width: 180 },
    { title: '获客链接', dataIndex: 'link_name', key: 'link_name', width: 150 },
    { title: '回话状态', dataIndex: 'is_chat', key: 'is_chat', width: 120 },
    { title: '回传状态', dataIndex: 'status', key: 'status', width: 120 },
    { title: '点击时间', dataIndex: 'created_at', width: 120, key: 'created_at' },
    { title: '回传时间', dataIndex: 'callback_at', width: 120, key: 'callback_at' },
    { title: '是否发送欢迎语', dataIndex: 'is_welcome', key: 'is_welcome', width: 100 },
    { title: '是否有客户标签', dataIndex: 'customer_tag', key: 'customer_tag', width: 100 }
  ])
  const adPlatformData: Record<number, any> = {
    1: {
      name: '广点通',
      img: requireImg('media/gdt.png')
    },
    2: {
      name: '磁力引擎',
      img: requireImg('media/cili.png')
    },
    3: {
      name: '巨量引擎',
      img: requireImg('media/jl.png')
    }
  }
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  })
  function getTimeRanges(type: string) {
    const now = dayjs()
    const dayOfWeek = now.day() // 0（周日）到 6（周六）
    const startOfWeek = dayOfWeek === 0 ? now.subtract(6, 'day') : now.subtract(dayOfWeek - 1, 'day')

    // 本月（1号 00:00:00 至 当前时间）
    const currentMonth = {
      start: now.startOf('month').format('YYYY-MM-DD'),
      end: now.format('YYYY-MM-DD')
    }

    // 本周（周一 00:00:00 至 当前时间）
    const currentWeek = {
      start: startOfWeek.startOf('day').format('YYYY-MM-DD'), // dayjs 默认周日是第一天，+1天调整为周一
      end: now.format('YYYY-MM-DD')
    }

    // 今日（00:00:00 至 23:59:59）
    const today = {
      start: now.startOf('day').format('YYYY-MM-DD'),
      end: now.endOf('day').format('YYYY-MM-DD')
    }

    if (type === 'month') {
      return [currentMonth.start, currentMonth.end]
    } else if (type === 'week') {
      return [currentWeek.start, currentWeek.end]
    } else if (type === 'day') {
      return [today.start, currentWeek.end]
    }
  }
  const state = reactive({
    query: {
      page: 1,
      page_size: 10,
      customer_add_time: getTimeRanges(props.type)
    } as any,
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 2200,
        y: 500
      },
      dataSource: [],
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    }
  })

  const submitForm = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    }
    state.query.page = 1
    getFansList()
  }

  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('sorter', sorter)
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    state.tableConfigOptions.pagination.current = 1
    getFansList()
  }

  const getFansList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let params = {
        ...state.query,
        wx_link_id: props.item.link_id
      }
      if (params.customer_add_time?.length) {
        params.customer_add_time = state.query.customer_add_time[0] + '_' + state.query.customer_add_time[1]
      } else {
        params.customer_add_time = undefined
      }
      const { data } = await getFansListApi(params)
      state.tableConfigOptions.dataSource = data?.list || []
      state.tableConfigOptions.pagination.total = data?.total || 0
      state.tableConfigOptions.pagination.current = data?.page || 1
    } catch (error) {
      console.log('error', error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  onMounted(() => {
    schemas.value.forEach((v) => {
      if (v.field == 'customer_add_time') {
        v.value = getTimeRanges(props.type)
      }
    })
    getFansList()
  })
</script>
<style lang="scss" scoped>
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 18px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
  }
  .welcome {
    background-color: #ffe7ce;
    color: #e87800;
    height: 24px;
    border: 1px solid #fe9d35;
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
  .number-id {
    word-break: break-all;
  }
  .round {
    width: 6px;
    height: 6px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
</style>
