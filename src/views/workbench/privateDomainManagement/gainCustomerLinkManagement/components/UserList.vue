<template>
  <div>
    <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="changeValue" />
    <div class="flex justify-end flex-items-center mt-24px mb-17px">
      <div>
        <a-checkbox v-model:checked="state.checked" @change="filterSearch">异常成员</a-checkbox>
      </div>
    </div>
    <TableZebraCrossing
      :data="state.tableConfigOptions"
      @change="pageChange"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        onChange: onSelectChange,
        getCheckboxProps: (record: any) => ({
          disabled: record.wx_status !== 1 && !state.selectedRowKeys.includes(record.user_id)
        })
      }"
    >
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.dataIndex === 'name'">
          <div class="flex goods_info">
            <div class="goods_info_data flex">
              <a-tooltip
                popper-class="toolt"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="topLeft"
              >
                <template #title>{{ record.name }}</template>
                <span class="goods_info_data_name">
                  {{ record.name }}
                </span>
              </a-tooltip>
              <span v-if="record.wx_status !== 1">
                <a-tooltip>
                  <template #title>{{ '成员异常' }}</template>
                  <ExclamationCircleOutlined class="c-red ml-4px" />
                </a-tooltip>
              </span>
            </div>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
    <div style="text-align: right">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :disabled="pointData.balance <= 0" @click="submitForm()" :loading="state.loading"
        >保存</a-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
  defineOptions({ name: 'gainCustomerLinkManagement' })
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { get_user_list, update_link_user } from '../index.api'
  import { getMemeberList } from '@/views/workbench/privateDomainManagement/gainCustomerLinkManagement/index.api'
  import { onMounted, reactive, ref } from 'vue'
  import { isArray } from 'lodash-es'
  import { message } from 'ant-design-vue'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  const emit = defineEmits(['event'])
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    // 审核不通过时的type
    type: [String, Number]
  })
  const schemas = ref([
    {
      field: 'name',
      type: 'input.text',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请输入成员名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    },
    {
      field: 'user_id',
      type: 'input.text',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请输入成员ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 8
      }
    }
    // {
    //   field: 'tel',
    //   type: 'input.text',
    //   value: undefined,
    //   span: 6,
    //   props: {
    //     placeholder: '请输入成员手机号'
    //   },
    //   layout: {
    //     xs: 24,
    //     sm: 12,
    //     md: 8,
    //     lg: 8,
    //     xl: 8,
    //     xxl: 8
    //   }
    // }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = reactive([
    { title: '成员名称', dataIndex: 'name', width: 140, key: 'name' },
    { title: '成员ID', dataIndex: 'user_id', key: 'user_id', width: 300 },
    { title: '加粉数', dataIndex: 'add_fans_num', key: 'add_fans_num', width: 100 },
    { title: '开口率', dataIndex: 'chat_rate', key: 'chat_rate', width: 100 }
  ])

  const state = reactive({
    checked: false,
    loading: false,
    item: {},
    query: {
      id: props.item.id,
      corpid: props.item.corp_id,
      is_fail: undefined,
      page: 1,
      page_size: 10
    } as any,
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: 'welcome',
      ids: []
    },
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    selectionRowsPlus: [],
    oldList: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'user_id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 400,
        y: 500
      },
      dataSource: [],
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    }
  })

  // 上架状态颜色判断
  const colorType = (val: string | number) => {
    let status = {
      1: '#52C41A',
      2: '#FF4D4F'
    }
    return status[val]
  }
  const filterSearch = async () => {
    try {
      state.query.page = 1
      state.tableConfigOptions.pagination.current = 1
      state.tableConfigOptions.loading = true
      let params = {
        ...state.query
      } as any
      if (state.checked) {
        params.is_fail = 1
      } else {
        delete params.is_fail
        state.query.is_fail = undefined
      }
      const result: any = await getMemeberList(params)
      if (result.code === 0) {
        state.tableConfigOptions.dataSource = result.data.wechatWorkUsers
        state.tableConfigOptions.pagination.total = result.data?.total || 0
        state.tableConfigOptions.pagination.current = result.data?.page || 1
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  // 列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true

      const resp = await getMemeberList(state.query)
      if (resp.data?.wechatWorkUsers?.length) {
        state.tableConfigOptions.dataSource = resp.data?.wechatWorkUsers || []
        if (isArray(state.selectionRowsPlus)) {
          const selectKeys = state.selectionRowsPlus
          const user_id_list = state.tableConfigOptions.dataSource
            .filter((v) => selectKeys.includes(v.user_id))
            .map((v) => v.user_id)
          state.selectedRowKeys = [...user_id_list]
        }
      } else {
        state.tableConfigOptions.dataSource = state.oldList
        state.selectedRowKeys = state.oldList.map((item) => item.user_id)
      }
      state.tableConfigOptions.pagination.total = resp.data?.total || 0
      state.tableConfigOptions.pagination.current = state.query.page || 1
      state.tableConfigOptions.loading = false
    } catch (error) {
      console.error(error)
      state.tableConfigOptions.dataSource = state.oldList
      state.selectedRowKeys = state.oldList.map((item) => item.user_id)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const changeValue = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    } as any
    if (state.checked) {
      state.query.is_fail = 1
    } else {
      state.query.is_fail = undefined
    }
    state.query.page = 1
    getList()
  }

  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const getUserList = async () => {
    const { data } = await get_user_list({ id: props.item.id })
    state.oldList = data
    const old_ids = data?.map((item: any) => item.user_id) || []
    state.selectionRowsPlus = [...old_ids]
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log('selectedRows changed: ', selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
  }

  const onSelect = (record, selected) => {
    console.log('onSelect', record, selected)
    if (selected) {
      state.selectionRowsPlus.push(record.user_id)
    } else {
      const indexToRemove = state.selectionRowsPlus.findIndex((id) => id === record.user_id)
      if (indexToRemove > -1) {
        state.selectionRowsPlus.splice(indexToRemove, 1)
      }
    }
  }

  const onSelectAll = (selected, selectedRows, changeRows) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
    if (selected) {
      const newUserIds = changeRows.map((row) => row.user_id)
      // 使用 Set 来避免重复添加已存在的 user_id
      const existingIds = new Set(state.selectionRowsPlus)
      newUserIds.forEach((id) => {
        if (!existingIds.has(id)) {
          state.selectionRowsPlus.push(id)
        }
      })
    } else {
      const idsToRemove = changeRows.map((row) => row.user_id)
      state.selectionRowsPlus = state.selectionRowsPlus.filter((id) => !idsToRemove.includes(id))
    }
  }
  async function submitForm() {
    try {
      state.loading = true

      if (!state.selectionRowsPlus.length) {
        return message.warning('请选择成员')
      }
      const list = state.selectionRowsPlus.join(',')
      const params = {
        id: props.item.id,
        wx_user_id: list
      }
      await update_link_user(params)
      message.success('保存成功')
      emit('event', { cmd: 'success' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  onMounted(async () => {
    await getUserList()
    await getList()
  })
</script>
<style lang="scss" scoped>
  .round {
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 18px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
  }
  .welcome {
    background-color: #ffe7ce;
    color: #e87800;
    height: 24px;
    border: 1px solid #fe9d35;
  }
  .customer {
    height: 24px;
    border: 1px solid #d9d9d9;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .c-primary {
    color: var(--primary-color);
  }
</style>
