<template>
  <div class="comp_dialog_sold_out">
    <a-form ref="ruleFormRef" :model="form" :labelCol="{ style: { width: '50px' } }">
      <!-- <a-form-item label="标签：" name="link"> -->
      <div class="link-list">
        <div v-for="(item, index) in form.tag_list" :key="index" class="flex-y-center">
          <div class="item-link text_overflow">
            <a-tooltip>
              <template #title>{{ item.name }}</template>
              {{ item.name }}
            </a-tooltip>
          </div>
          <a-button type="link" danger class="c-#FE4D4F" @click="delLink(index)">删除</a-button>
        </div>
      </div>

      <!-- <a-input v-model:value="form.link" class="w-480px" placeholder="请输入标签" showCount :maxlength="20" />
      </a-form-item> -->
      <a-button type="primary" :disabled="form.tag_list.length > 4" @click="addLink">添加标签</a-button>
    </a-form>
    <div style="text-align: right">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="state.loading">保存</a-button>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      @ok="okModal"
      destroyOnClose
    >
      <a-form ref="ruleFormRef" :model="forms" :rules="rules" :labelCol="{ style: { width: '50px' } }">
        <a-form-item label="标签：" name="link">
          <a-input v-model:value="forms.link" placeholder="请输入标签" showCount :maxlength="20" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="tsx" setup name="DialogSoldOut">
  import { reactive, ref } from 'vue'
  import { message } from 'ant-design-vue'
  import { get_tag_list, save_tag } from '../index.api'
  const emit = defineEmits(['event'])
  const state = reactive({
    loading: false,
    dialog: {
      visible: false,
      title: '添加标签',
      width: 500
    }
  })

  const rules = {
    link: [{ required: true, message: '请输入标签', trigger: ['change', 'blur'] }]
  }
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    // 审核不通过时的type
    type: [String, Number]
  })
  const form = reactive({
    id: props.item.id,
    remark: '',
    link: undefined,
    tag_list: [] as Array<{ name: string }>
  })
  const forms = reactive({
    link: ''
  })
  const ruleFormRef = ref()
  const addLink = async () => {
    state.dialog.visible = true
    forms.link = undefined
  }
  const okModal = async () => {
    if (!(await ruleFormRef.value.validate('link'))) return
    form.tag_list.push({
      name: forms.link
    })
    state.dialog.visible = false
  }
  const getTagList = async () => {
    const { data } = await get_tag_list({ id: props.item.id })
    form.tag_list = data || []
  }
  getTagList()
  const delLink = (index: number) => {
    form.tag_list.splice(index, 1)
  }
  async function submitForm() {
    try {
      state.loading = true
      // if (!form.tag_list.length) {
      //   message.error('请添加标签')
      //   return
      // }

      const params = {
        id: form.id,
        tag_list: form.tag_list
      }
      await save_tag(params)
      message.success('保存成功')
      emit('event', { cmd: 'success' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>

<style lang="scss" scoped>
  .comp_dialog_sold_out {
    color: #404040;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-bottom: 20px;
    }
    .tip {
      font-size: 12px;
      color: #85878a;
    }
  }
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
</style>
