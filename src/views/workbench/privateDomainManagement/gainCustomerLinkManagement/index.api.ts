import http from '@/utils/request'

//获客链接列表

export const getWechatWorkLink = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/list`, data)
}
//保存获客链接

export const saveWechatWorkLink = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save`, data)
}
//获取获客链接详情

export const get_link_info = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_link_info`, data)
}
//获取日、周、月粉丝数

export const get_fans_total = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/get_fans_total`, data)
}
//删除获客链接

export const delete_link = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/delete_link`, data)
}
//保存欢迎语

export const save_welcome_txt = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save_welcome_txt`, data)
}
//保存标签

export const save_tag = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save_tag`, data)
}
//获取标签列表

export const get_tag_list = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_tag_list`, data)
}
//保存备注

export const save_remark = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save_remark`, data)
}
// 获取企微列表
export const getEnterpriseWechatList = (data?: any) => {
  return http('get', `/common/work/list`, data)
}
// 获取成员列表
export const getMemeberList = (data?: any) => {
  return http('post', `/admin/wechat_work_user/list`, data)
}
// 获客链接保存
export const saveGainCustomerLink = (data: any) => {
  return http('post', `/admin/wechatWorkLink/save`, data)
}
// 获取获客链接详情
export const getLinkInfo = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_link_info`, data)
}
// 获取关联人员
export const get_user_list = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_user_list`, data)
}

// 获取加粉数据列表
export const getFansList = (data: any) => {
  return http('post', `/admin/adConversion/get_list`, data)
}
// 修改关联人员
export const update_link_user = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/update_link_user`, data)
}
