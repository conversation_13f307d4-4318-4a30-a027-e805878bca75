import { reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { exportCreate } from '@/api/common'
import { useDownloadCenter } from '@/hooks'
import { getWechatWorkLink, getEnterpriseWechatList, delete_link, get_fans_total } from '../index.api'

export default function datas() {
  const { goCenter } = useDownloadCenter()
  const schemas = ref([
    {
      field: 'corpid',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [],
        placeholder: '请选择企微'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'link_name',
      type: 'input.text',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请输入链接名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'link_id',
      type: 'input.text',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请输入链接ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'on_sale',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [
          {
            value: 1,
            label: '上线'
          },
          {
            value: 2,
            label: '下线'
          }
        ],
        placeholder: '请选择关联状态'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },

    {
      type: 'date',
      field: 'created_at',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const formConfig = reactive({
    foldNum: 0
  })
  const columns = reactive([
    { title: '企微名称', dataIndex: 'title', width: 300, key: 'title' },
    { title: '链接名称', dataIndex: 'link_name', width: 208, key: 'link_name' },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   width: 100,
    //   key: 'status',
    //   tips: '当链接中配置的全部成员账号异常时，获客链接状态显示异常；有正常进粉后获客链接状态由异常转为正常'
    // },
    {
      title: '上下线',
      dataIndex: 'on_sale',
      key: 'on_sale',
      width: 120,
      tips: '上线：可通过落地页正常添加。下线：不可通过落地页添加'
    },
    { title: '分组', dataIndex: 'group_list', key: 'group_list', width: 120 },
    { title: '欢迎语', dataIndex: 'welcome', key: 'welcome', width: 120 },
    { title: '客户标签', dataIndex: 'tag', key: 'tag', width: 120 },
    { title: '关联成员', dataIndex: 'wx_user_num', key: 'wx_user_num', width: 120 },
    { title: '本月加粉人数', dataIndex: 'month_num', key: 'month_num', width: 120 },
    { title: '本月开口率', dataIndex: 'month_chat_rate', key: 'month_chat_rate', width: 120 },
    { title: '本周加粉人数', dataIndex: 'week_num', key: 'week_num', width: 120 },
    { title: '本周开口率', dataIndex: 'week_chat_rate', key: 'week_chat_rate', width: 120 },
    { title: '本日加粉人数', dataIndex: 'day_num', key: 'day_num', width: 120 },
    { title: '本日开口率', dataIndex: 'day_chat_rate', key: 'day_chat_rate', width: 120 },
    { title: '备注', dataIndex: 'remark', key: 'remark', sorter: false, width: 160 },
    { title: '创建人', dataIndex: 'create_author', width: 120, key: 'create_author' },
    { title: '创建时间', dataIndex: 'created_at', width: 180, key: 'created_at' },
    { title: '操作', dataIndex: 'handle', fixed: 'right', width: 100, disabled: true, key: 'handle' }
  ])

  const state = reactive({
    item: {},
    query: {
      page: 1,
      page_size: 10
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: 'welcome',
      ids: []
    },
    fansTotal: {
      month_num: 0,
      week_num: 0,
      day_num: 0
    },
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 2100
      },
      dataSource: [],
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    }
  })

  // 上架状态颜色判断
  const colorType = (val: string | number) => {
    let status = {
      1: '#52C41A',
      2: '#FF4D4F'
    }
    return status[val]
  }
  // 审核状态颜色
  const statusTxt = (type: string | number) => {
    return {
      1: '#52C41A',
      2: '#FF4D4F'
    }[type]
  }
  // 商品列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true

      const resp = await getWechatWorkLink(state.query)
      state.tableConfigOptions.dataSource = resp.data?.list || []

      state.tableConfigOptions.pagination.total = resp.data?.total || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1
      state.tableConfigOptions.loading = false

      console.log('state.tableConfigOptions.dataSource', state.tableConfigOptions.dataSource)

      if (state.selectionItem.length) {
        state.selectionItem = []
      }
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const submitForm = (data: any) => {
    state.query = {
      ...state.query,
      ...data.formData
    }
    state.query.created_at =
      !data.formData?.created_at?.length || data.formData?.created_at == null
        ? ''
        : `${data.formData.created_at[0]}_${data.formData.created_at[1]}`
    state.query.page = 1
    getList()
  }
  const getWechatList = async () => {
    try {
      const { data } = await getEnterpriseWechatList({ page: 1, page_size: 10000 })

      schemas.value.forEach((v) => {
        if (v.field === 'corpid') {
          v.props.options = data.list.map((item: any) => ({
            label: item.corp_name || '', // 添加默认值防止undefined
            value: item.corpid || '' // 添加默认值防止undefined
          }))
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const getTotal = async () => {
    try {
      const { data } = await get_fans_total(state.query)
      if (data?.month_num) {
        state.fansTotal.month_num = data.month_num
        state.fansTotal.week_num = data.week_num
        state.fansTotal.day_num = data.day_num
      }
    } catch (error) {
      console.error(error)
    }
  }
  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('sorter', sorter)

    state.query.page = pagination.current
    state.query.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  getList()
  getTotal()
  getWechatList()

  interface DialogConfig {
    visible: boolean
    title: string
    width: number
    type: string
  }
  const dialogConfigs: Record<string, Omit<DialogConfig, 'visible' | 'type'>> = {
    add: { title: '新增获客链接', width: 610 },
    edit: { title: '编辑获客链接', width: 610 },
    welcome: { title: '设置欢迎语', width: 671 },
    user: { title: '关联人员', width: 800 },
    label: { title: '设置客户标签', width: 671 },
    remark: { title: '备注', width: 571 },
    month: { title: '企微加粉记录', width: 1070 },
    week: { title: '企微加粉记录', width: 1070 },
    day: { title: '企微加粉记录', width: 1070 }
  }

  const onShowDialog = (type: string, item: Record<string, any> = {}) => {
    state.item = { ...item }

    const config = dialogConfigs[type] || { title: type, width: 671 } // 默认值
    state.dialog = {
      visible: true,
      type,
      ...config
    }
  }
  const onDel = async (id: any, type: string) => {
    try {
      await delete_link({ id })
      message.success('删除当前链接成功')
      getList()
    } catch (error) {
      console.error('删除当前链接失败')
    }
  }

  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'wx_work_link',
        params: JSON.stringify(state.query)
      })
      goCenter()
    } catch (error) {
      console.log(error)
    }
  }
  const onEvent = (data: { cmd: string }) => {
    if (data.cmd == 'close') {
      state.dialog.visible = false
    } else {
      state.dialog.visible = false
      getList()
    }
    // state.dialog.ids = []
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log('selectedRows changed: ', selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
  }
  const updateUser = async (data: any) => {}

  return {
    schemas,
    state,
    formConfig,
    columns,
    colorType,
    statusTxt,
    pageChange,
    onShowDialog,
    onDel,
    onEvent,
    handleExportCreate,
    getList,
    onSelectChange,
    updateUser,
    submitForm
  }
}
