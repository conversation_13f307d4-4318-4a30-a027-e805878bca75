import http from '@/utils/request'

/**
 * 用户全局菜单
 */
export const setRoleMenuList = (data) => {
  return http('get', `/common/role/role_menu`, data)
}

/**
 * 角色列表
 */
export const setRoleList = (data) => {
  return http('get', `/shop-common/role/list`, data)
}

/**
 * 新权限详情
 */
export const setRoleInfo = (data) => {
  return http('get', `/shop-common/role/role_info`, data)
}

/**
 * 权限添加更新
 */
export const setAddRole = (data) => {
  return http('post', `/shop-common/role/role_add`, data)
}
//新增
export const wechatGroupApi = (data: any) => {
  return http('post', `/admin/wechat_group/store`, data)
}
//更新
export const updateGroupApi = (data: any) => {
  return http('post', `/admin/wechat_group/update/${data?.id}`, data)
}
//详情
export const getGroupApi = (data: any) => {
  return http('get', `/admin/wechat_group/show/${data?.id}`, data)
}
//列表
export const wechatGroupListApi = (data: any) => {
  return http('post', `/admin/wechat_group/list`, data)
}
//删除
export const delGroupApi = (data: any) => {
  return http('get', `/admin/wechat_group/delete/${data.id}`, data)
}
//获客链接
export const WorkLinkApi = (data: any) => {
  return http('post', `/admin/wechatWorkLink/list`, data)
}
//更新微信组获客链接关系
export const linkSetApi = (data: any) => {
  return http('post', `/admin/wechat_group_link/set`, data)
}
//获取已关联的获客链接
export const linkGetApi = (data: any) => {
  return http('get', `/admin/wechat_group_link/get`, data)
}
//保存标签

export const save_tag = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/save_tag`, data)
}
//获取标签列表

export const get_tag_list = (data?: any) => {
  return http('get', `/admin/wechatWorkLink/get_tag_list`, data)
}

//批量更新操作
export const updateLinkInfo = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/update_link_info`, data)
}
//删除获客链接
export const delete_link = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/delete_link`, data)
}
export const batchGeneratedApi = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/batch_generated_link_info`, data)
}
//上传视频/图片 前校验
export const uploadFileApi = (data?: any) => {
  return http('post', `/admin/work/upload_file`, data)
}
//获取客服分组绑定成员
export const wechat_group_link_users = (data: any) => {
  return http('get', `/admin/wechat_group_link/users`, data)
}
// 部门列表
export const department = (data: any) => {
  return http('post', `/admin/wechat_work_user/department`, data)
}
// 获取分享链接
export const wechat_group_share = (data: any) => {
  return http('get', `/admin/wechat_group/share`, data)
}
// 更新指定字段值
export const update_filed_value = (data: any) => {
  return http('post', `/admin/wechat_group/update_filed_value`, data)
}
//更新预加粉值
export const updateFiledApi = (data?: any) => {
  return http('post', `/admin/wechat_group/update_filed_value`, data)
}
//修改获客链接名称
export const updateLinkName = (data?: any) => {
  return http('post', `/admin/wechatWorkLink/update_link_name`, data)
}
// 批量设置获客链接名称
export const batchUpdateLinkName = (data?: any) => {
  return http('post', `/admin/wechat_group/update_link_name`, data)
}
// 获取微信分组的所有标签
export const get_group_tag = (data?: any) => {
  return http('post', `/admin/wechat_work_tag/get_group_tag`, data)
}
// 给微信组连接添加标签
export const add_tag = (data?: any) => {
  return http('post', `/admin/wechat_work_tag/add_tag`, data)
}
// 删除标签
export const delete_group_tag = (data?: any) => {
  return http('post', `/admin/wechat_work_tag/delete_group_tag`, data)
}
// 批量更新获客链接的标签
export const batch_add_wx_tag = (data?: any) => {
  return http('post', `/admin/wechat_work_tag/batch_add_wx_tag`, data)
}
// 获取客服人员
export const wechat_group_list = (data?: any) => {
  return http('post', `/admin/wechat_group/list`, data)
}
//顶部统计字段
export const getTopInfo = (data?: any) => {
  return http('get', `/admin/wechat_group/static_top`, data)
}

//获取10分钟内加粉流速
export const getTenFansFlow = (data?: any) => {
  return http('get', `/common/fans_flow`, data)
}
//获取关联投放链接
export const getAddLink = (data?: any) => {
  return http('post', `/admin/ad_link/list`, data)
}
