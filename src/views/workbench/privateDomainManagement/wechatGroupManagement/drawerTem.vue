<template>
  <div class="drawerTemp-wrapper comp_wrapper w-100%" style="min-height: 100%; padding: 0; padding-bottom: 80px">
    <a-button
      type="primary"
      :disabled="pointData.balance <= 0"
      @click="onShowDialog('group', data.form)"
      class="pos-fixed right-24px top-11px"
      >新增分组</a-button
    >
    <a-tabs class="ml-24px" v-model:activeKey="temData.activeTab" v-if="BIds && AIds">
      <a-tab-pane key="1" tab="通用分组" disabled>
        <ExclamationCircleFilled class="c-#fe9d35" />
        通用分组：该分组将应用仅关联了通用分组类型的投放链接：<span
          class="c-#fe9d35 cursor-pointer"
          @click="onShowDialog('relation_ship_tem', { ids: AIds })"
          >去查看</span
        >
      </a-tab-pane>
      <a-tab-pane key="2" tab="A/B页专用客服组" disabled>
        <ExclamationCircleFilled class="c-#fe9d35" />
        A/B页专用客服组：该分组将应用仅关联了A/B页专用客服组类型的投放链接：
        <span class="c-#fe9d35 cursor-pointer" @click="onShowDialog('relation_ship_tem', { ids: BIds })">去查看</span>
      </a-tab-pane>
    </a-tabs>
    <div class="flex comp_content pb-74px h-80% pt-24px pl-24px pr-24px" :class="isMobile ? 'flex-col' : ''">
      <div class="flex-shrink-0" :class="isMobile ? 'w100%' : 'w-300px'">
        <div class="c-#313233 text-16px font-bold mb-10px line-height-34px flex">
          <span>已选客服分组</span>
        </div>

        <div class="a-wrap" @click="changeWrap('A')" :class="[temData.activeWrap === 'A' && 'active']">
          <div class="c-#333 mb-12px">
            {{ isShowABData ? 'A客服组' : '通用分组' }}({{ temData.selectLeftData.length }})
          </div>
          <div class="overflow-y-auto h-300px">
            <div v-for="(item, index) in temData.selectLeftData" :key="index" class="flex mb-12px">
              <!-- <a-checkbox class="mb-12px" v-model:checked="item.checked" @change="(e) => onSelectChange(e, item)"> -->
              <div>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.name.length > 15">{{ item.name }}</template>
                  <div class="flex flex-wrap">
                    <div class="text_overflow_row1 c-#333 max-w-145px">
                      {{ item.name }}
                    </div>
                    <div class="c-#52C41A">（已上线：{{ item.keNum || 0 }}）</div>
                  </div>
                </a-tooltip>
              </div>
              <a-popconfirm
                title="确认删除此数据吗?"
                ok-text="是"
                cancel-text="否"
                @confirm="onSelectChange(item)"
                @cancel="cancel"
              >
                <DeleteOutlined class="c-#fe9d35 cursor-pointer" />
              </a-popconfirm>
              <!-- </a-checkbox> -->
            </div>
          </div>
        </div>
        <div
          v-if="isShowABData"
          class="a-wrap mt-24px"
          @click="changeWrap('B')"
          :class="[temData.activeWrap === 'B' && 'active']"
        >
          <div class="c-#333 mb-12px">B客服组({{ temData.selectLeftBData.length }})</div>
          <div class="overflow-y-auto h-300px">
            <div v-for="(item, index) in temData.selectLeftBData" :key="index" class="flex mb-12px">
              <div>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.name.length > 15">{{ item.name }}</template>
                  <div class="flex flex-wrap">
                    <div class="text_overflow_row1 c-#333 max-w-145px">
                      {{ item.name }}
                    </div>
                    <div class="c-#52C41A">（已上线：{{ item.keNum || 0 }}）</div>
                  </div>
                </a-tooltip>
              </div>
              <a-popconfirm
                title="确认删除此数据吗?"
                ok-text="是"
                cancel-text="否"
                @confirm="onSelectChange(item)"
                @cancel="cancel"
              >
                <DeleteOutlined class="c-#fe9d35 cursor-pointer" />
              </a-popconfirm>
            </div>
          </div>
        </div>
      </div>

      <DesTablePage
        :style="{ width: isMobile ? '100%' : 'calc(100% - 300px)' }"
        v-if="data.tableConfigOptions.columns.length > 0"
      >
        <template #search>
          <SearchBaseLayout
            ref="searchFormDataRef"
            :data="searchConfig.data"
            @changeValue="searchForm"
            :actions="searchConfig.options"
          />
        </template>
        <template #tableWarp>
          <TableZebraCrossing
            :class="{ 'my-table': data.tableConfigOptions.dataSource.length < 11 }"
            :data="data.tableConfigOptions"
            @change="pageChange"
            :row-selection="rowSelection"
          >
            <template #headerCell="{ scope }">
              <template v-if="headerCell.includes(scope.column.dataIndex)">
                <div>
                  <span>{{ scope.column.title }}</span>
                  <a-tooltip>
                    <template #title>{{ scope.column.text }}</template>
                    <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                  </a-tooltip>
                </div>
              </template>
            </template>
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'name'">
                <div>
                  <div v-if="scope.record.name.length">
                    <a-tooltip placement="topLeft">
                      <template #title>{{ scope.record.name }}</template>
                      <div class="text_overflow">{{ scope.record.name }}</div>
                    </a-tooltip>
                  </div>
                  <div v-else>
                    <div>{{ scope.record.name }}</div>
                  </div>
                  <div class="flex-y-center mt-4px text_overflow_row1" v-if="scope.record.fail_status">
                    <ExclamationCircleOutlined class="c-red font-size-12px" />
                    <div class="c-red ml-4px font-size-12px h-12px line-height-12px">分组内有客服异常，请注意调整</div>
                  </div>
                </div>
              </template>
              <template v-if="scope.column.key === 'corp_name'">
                <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer('linkKefu', scope.record)">
                  {{ scope.record.corp_num || 0 }}
                </a-button>
              </template>

              <template v-if="scope.column.key === 'product_name'">
                <div v-if="scope.record.product_name">
                  <a-tooltip placement="topLeft">
                    <template #title>{{ scope.record.product_name }}</template>
                    <div class="text_overflow">{{ scope.record.product_name }}</div>
                  </a-tooltip>
                </div>
                <div v-else>--</div>
              </template>
              <template v-if="scope.column.key === 'linkNum'">
                <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer('linkKefu', scope.record)">
                  {{ scope.record.linkNum }}
                </a-button>
              </template>
              <template v-if="scope.column.key === 'ad_link_num'">
                <div>{{ scope.record.ad_link_num || '--' }}</div>
              </template>
              <template v-if="scope.column.dataIndex === 'add_fans_num_ten_min'">
                <div class="cursor-pointer" @click="onShowDialog('add_fans_num_ten_min', scope.record)">
                  <span class="c-#52c41a">{{ scope.record.add_fans_num_ten_min || 0 }}/</span>
                  <span class="c-#B6AFAF">10min</span>
                </div>
              </template>
              <template v-if="scope.column.key === 'remark'">
                <div v-if="scope.record.remark.length > 15">
                  <a-tooltip>
                    <template #title>{{ scope.record.remark }}</template>
                    <div class="text_overflow">{{ scope.record.remark }}</div>
                  </a-tooltip>
                </div>
                <div v-else>
                  <div>{{ scope.record.remark || '--' }}</div>
                </div>
              </template>

              <template v-if="scope.column.key === 'status'">
                <a-switch
                  v-model:checked="scope.record.status"
                  :checkedValue="1"
                  :unCheckedValue="2"
                  :disabled="pointData.balance <= 0"
                  @click="
                    (e) => {
                      changeStatus(e, scope.record, rowSelection)
                    }
                  "
                />
              </template>
              <template v-if="scope.column.key === 'pre_fans_num'">
                <div>
                  {{ scope.record.pre_fans_num || '--' }}
                  <EditOutlined
                    class="c-#FE9D35 ml4px"
                    @click="onShowDialog('pre_fans_num', scope.record)"
                  ></EditOutlined>
                </div>
              </template>
              <template v-if="scope.column.key === 'chat_rate'">
                <div>{{ scope.record.chat_rate }}</div>
              </template>

              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <a-button
                      type="link"
                      size="small"
                      class="pa-0!"
                      v-auth="['wechatGroupManEdit']"
                      :disabled="pointData.balance <= 0"
                      @click="onShowDialog('group', scope.record)"
                      >编辑</a-button
                    >
                    <a-button
                      type="link"
                      size="small"
                      v-auth="['wechatGroupManDel']"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="deleteGroup(scope.record)"
                      >删除</a-button
                    >
                  </div>
                </div>
              </template>
            </template>
          </TableZebraCrossing>
        </template>
      </DesTablePage>
      <div class="flex-1" v-else>
        <a-card class="border-rd-16px mt-40px ml-24px">
          <a-empty class="h-340px pt-100px" />
        </a-card>
      </div>
    </div>

    <div class="text-right comp_footer_btn">
      <a-button
        type="primary"
        v-if="BIds && AIds && temData.activeTab == '1'"
        :disabled="temData.selectLeftData.length === 0"
        @click="nextAction"
        >下一步</a-button
      >
      <a-button type="primary" v-if="BIds && AIds && temData.activeTab == '2'" @click="preAction">上一步</a-button>
      <a-button type="primary" :disabled="sureBtnDisabled" @click="sureAction">确定({{ sumNumber }})</a-button>
    </div>

    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :footer="null"
      @cancel="getList"
      :maskClosable="false"
    >
      <addGroup v-if="data.dialog.visible && data.dialog.type == 'group'" :item="data.dialog.item" @event="onEvent" />
      <linkNum v-if="data.dialog.visible && data.dialog.type == 'link'" :data="data.dialog.item" @event="onEvent" />
      <addFansNumTenMin
        v-if="data.dialog.visible && data.dialog.type == 'add_fans_num_ten_min'"
        :data="data.dialog.item"
        @event="onEvent"
      />
      <preFansNum
        v-if="data.dialog.visible && data.dialog.type == 'pre_fans_num'"
        :item="data.dialog.item"
        @event="onEvent"
        @upDateTop="getTop"
      />
      <relationShipTem
        v-if="data.dialog.visible && data.dialog.type == 'relation_ship_tem'"
        :datas="data.dialog.item"
      />
    </a-modal>

    <a-drawer
      v-model:open="data.drawer.visible"
      :title="data.drawer.title"
      :width="data.drawer.width"
      placement="right"
      @close="
        () => {
          data.drawer.visible = false
          getList()
        }
      "
    >
      <linkKefu
        v-if="data.drawer.visible && data.drawer.type == 'linkKefu'"
        :data="data.drawer.item"
        @event="onEventDrawer"
      />
    </a-drawer>
  </div>
</template>

<script setup>
  import { onMounted, reactive, watch, ref, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import datas from './src/data'
  import addGroup from './components/addGroup.vue'
  import linkNum from './components/linkNum.vue'
  import linkKefu from './components/linkKefu.vue'
  import preFansNum from './components/preFansNum.vue'
  import addFansNumTenMin from './components/addFansNumTenMin.vue'
  import relationShipTem from './components/relationShipLink.vue'
  import {
    QuestionCircleFilled,
    ExclamationCircleOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleFilled
  } from '@ant-design/icons-vue'
  import { usePoints } from '@/hooks'
  import { wechat_group_list } from './index.api'
  import { message } from 'ant-design-vue'
  import { getTopInfo } from './index.api'
  import { cloneDeep, union } from 'lodash-es'
  import { useAuth } from '@/hooks/use-auth'
  import { useApp } from '@/hooks'
  const { pointData } = usePoints()
  const route = useRoute()
  const { useInfo, isMobile } = useApp()

  const { filedsNames = [] } = useAuth()
  console.log('filedsNames', filedsNames, useInfo)
  const {
    tabAllCol,
    pageChange,
    searchForm,
    onShowDialog,
    onShowDrawer,
    headerCell,
    searchConfig,
    data,
    statusType,
    getList,
    onEvent,
    onEventDrawer,
    deleteGroup,
    changeStatus,
    searchFormDataRef
  } = datas(true)
  const props = defineProps(['AData', 'BData', 'BIds', 'AIds'])
  const emit = defineEmits(['sure', 'cancel', 'batchSure'])
  console.log('props', props)
  const isShowABData = computed(() => {
    if (props?.BIds && !props?.AIds) {
      return true
    } else if (props?.BIds && temData.activeTab == '2') {
      return true
    }
    if (props.BData.length > 0) {
      return true
    }
    return false
  })
  const height1 = computed(() => window.innerHeight - 305)
  const temData = reactive({
    leftAllData: [],
    selectLeftData: [],
    selectLeftBData: [],
    activeWrap: 'A',
    activeTab: '1',
    tongYongCopyData: [],
    selectACopyData: [],
    selectBCopyData: []
  })
  const sureBtnDisabled = computed(() => {
    console.log('temData.selectLeftData.length', temData.selectLeftData, temData.selectLeftBData)
    if (props.AData.length > 0 && props.BData.length === 0) {
      return temData.selectLeftData.length === 0
    }
    if (props.AIds.length > 0 && props.BIds.length === 0) {
      return temData.selectLeftData.length === 0
    }
    return temData.selectLeftData.length === 0 || temData.selectLeftBData.length === 0
  })
  const nextAction = () => {
    temData.tongYongCopyData = cloneDeep(temData.selectLeftData)
    temData.activeTab = '2'
    temData.activeWrap = 'A'
    rowSelection.value.selectedRowKeys = temData.selectACopyData?.map((item) => {
      return item.id
    })
    temData.selectLeftData = cloneDeep(temData.selectACopyData)
    temData.selectLeftBData = cloneDeep(temData.selectBCopyData)
  }
  const preAction = () => {
    temData.activeTab = '1'
    temData.activeWrap = 'A'
    rowSelection.value.selectedRowKeys = temData.tongYongCopyData?.map((item) => {
      return item.id
    })
    temData.selectACopyData = cloneDeep(temData.selectLeftData)
    temData.selectBCopyData = cloneDeep(temData.selectLeftBData)
    temData.selectLeftData = cloneDeep(temData.tongYongCopyData)
  }
  const sumNumber = computed(() => {
    if (temData.activeTab === '1') {
      return temData.selectACopyData.length + temData.selectBCopyData.length + temData.selectLeftData.length
    }
    return temData.selectLeftData.length + temData.selectLeftBData.length + temData.tongYongCopyData.length
  })
  const topInfo = ref([
    { key: 'user_num', num: 0, tableName: 'name', unit: '个', label: '客服人员' },
    { key: 'pre_fans_num', num: 0, tableName: 'pre_fans_num', unit: '个', label: '预加粉数' },
    { key: 'add_fans_num', num: 0, tableName: 'add_fans_num', unit: '个', label: '加粉数' },
    { key: 'chat_num', num: 0, tableName: 'chat_num', unit: '个', label: '开口数' },
    { key: 'chat_rate', num: 0, tableName: 'chat_rate', unit: '%', label: '开口率' },
    { key: 'add_fans_rate', num: 0, tableName: 'add_fans_rate', unit: '%', label: '进度' }
  ])
  const topLastInfo = ref([])
  const topLoading = ref(false)
  watch(
    () => useInfo.value,
    (newDate) => {
      if (newDate?.user_type === 1) {
        data.tableConfigOptions.columns = tabAllCol
      }
    },
    {
      immediate: true
    }
  )
  const isSuperRole = computed(() => {
    return useInfo.value?.user_type === 1
  })
  const nowPageNum = computed(() => {
    let arr = 0
    const total = data.tableConfigOptions.dataSource.reduce((acc, cur) => {
      return acc + (cur.add_fans_num || 0)
    }, 0)
    return total || 0
  })
  const nowPageCanUseNum = computed(() => {
    let arr = 0
    console.log('data.tableConfigOptions.dataSource', data.tableConfigOptions.dataSource)
    const total = data.tableConfigOptions.dataSource.reduce((acc, cur) => {
      return acc + (cur.can_use_link_num || 0)
    }, 0)
    return total || 0
  })
  const changeWrap = (type) => {
    temData.activeWrap = type
    if (type === 'A') {
      rowSelection.value.selectedRowKeys = temData.selectLeftData?.map((item) => {
        return item.id
      })
    } else {
      rowSelection.value.selectedRowKeys = temData.selectLeftBData?.map((item) => {
        return item.id
      })
    }
  }
  onMounted(() => {
    // getTop()
  })
  const getTop = async () => {
    try {
      topLoading.value = true
      const res = await getTopInfo()

      const objData = res?.data?.data || {}
      if (useInfo.value.user_type !== 1) {
        topInfo.value = topInfo.value.filter((item) => {
          return filedsNames?.includes(item.tableName)
        })
      }

      topInfo.value.forEach((item) => {
        //  console.log('res----', objData)
        for (let key in objData) {
          if (item.key === key) {
            item.num = objData[key]
          }
        }
      })
      topLastInfo.value = topInfo.value
    } finally {
      topLoading.value = false
    }
  }

  const rowSelection = ref({
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys, selectedRows, e) => {
      // const oldSelectedRowKeys = cloneDeep(rowSelection.value.selectedRowKeys)
      // const result = union(oldSelectedRowKeys, selectedRowKeys)
      rowSelection.value.selectedRowKeys = selectedRowKeys
    },
    getCheckboxProps: (record) => ({
      disabled: record.status === 2, // Column configuration not to be checked
      name: record.name
    }),
    selectedRowKeys: []
  })
  watch(
    () => rowSelection.value.selectedRowKeys,
    (val, old) => {
      console.log('a-----,--', val, old)
      if (temData.activeWrap === 'A') {
        temData.selectLeftData = temData.leftAllData
          ?.filter((item) => {
            if (val?.includes(item.id)) {
              return {
                ...item
              }
            }
          })
          .map((item) => {
            return {
              ...item,
              checked: true
            }
          })
      } else {
        temData.selectLeftBData = temData.leftAllData
          ?.filter((item) => {
            if (val?.includes(item.id)) {
              return {
                ...item
              }
            }
          })
          .map((item) => {
            return {
              ...item,
              checked: true
            }
          })
      }
    }
  )

  const onSelectChange = (item) => {
    rowSelection.value.selectedRowKeys = rowSelection.value.selectedRowKeys.filter((key) => {
      return key !== item.id
    })
  }
  const cancelAction = () => {
    emit('cancel')
  }
  const sureAction = () => {
    if (rowSelection.value.selectedRowKeys.length == 0) {
      message.warning('请选择客服分组数据')
      return
    }
    const isBatch = props.AIds || props.BIds

    if (isBatch) {
      let tongYongData = ''
      if (temData.activeTab == '1' && !props.BIds) {
        tongYongData = temData.selectLeftData
      } else {
        tongYongData = temData.tongYongCopyData
      }
      let AData = ''
      let BData = ''
      if ((temData.activeTab == '1' && !props.AIds) || temData.activeTab == '2') {
        AData = temData.selectLeftData
        BData = temData.selectLeftBData
      } else if (temData.activeTab == '1' && props.BIds) {
        AData = temData.selectACopyData
        BData = temData.selectBCopyData
      }

      const params = {}
      if (props.AIds) {
        params.a = {
          ids: props.AIds,
          wx_group_a: tongYongData.map((item) => item.id).join(',')
        }
      }
      if (props.BIds) {
        params.ab = {
          ids: props.BIds,
          wx_group_b: BData.map((item) => item.id).join(','),
          wx_group_a: AData.map((item) => item.id).join(',')
        }
      }
      console.log('params', params)

      emit('batchSure', params)
    } else {
      emit('sure', temData.selectLeftData, temData.selectLeftBData)
    }
  }
  const getLeftData = async () => {
    let params = {
      page: 1,
      page_size: 9999
    }
    const res = await wechat_group_list({ ...params, status: 1, is_link: 1 })
    temData.leftAllData = res.data.WechatGroups?.map((item) => {
      const keNum = item.linkNum - item.fail_num
      return {
        ...item,
        checked: false,
        // name: `${item.name} (已上线：${keNum > 0 ? keNum : 0})`,
        name: item.name,
        keNum
      }
    })
    rowSelection.value.selectedRowKeys = props.AData
    temData.selectLeftData = temData.leftAllData
      ?.filter((item) => {
        if (props.AData.includes(item.id)) {
          return {
            ...item
          }
        }
      })
      .map((item) => {
        return {
          ...item,
          checked: true
        }
      })
    temData.selectLeftBData = temData.leftAllData
      ?.filter((item) => {
        if (props.BData.includes(item.id)) {
          return {
            ...item
          }
        }
      })
      .map((item) => {
        return {
          ...item,
          checked: true
        }
      })
  }
  watch(
    () => props.AData,
    () => {
      getList()
      getLeftData()
    },
    {
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .left-title-tips {
    cursor: pointer;
    font-weight: 400;
    font-size: 12px;
    width: 120px;
    margin-top: 2px;
    color: #656d7d;
  }
  .a-wrap {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 10px;
  }
  .active {
    border: 1px solid #fe9d35;
    background: #fffaf0;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }

  .my-table {
    :deep(.ant-table-cell-fix-right-first) {
      right: 0 !important;
    }
  }
  .drawerTemp-wrapper {
    :deep(.ant-card-head) {
      padding-top: 0 !important;
      min-height: auto;
      height: 0;
    }
    :deep(.ant-card-body) {
      padding-bottom: 0 !important;
    }
  }
</style>
