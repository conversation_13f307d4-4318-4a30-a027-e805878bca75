<template>
  <div class="comp_wrapper">
    <div>
      <SearchBaseLayout
        :data="state.searchConfig.data"
        @changeValue="changeValue"
        ref="followerNumSearchBaseLayoutRef"
        :actions="state.searchConfig.options"
      />
    </div>
    <TableZebraCrossing class="mt-16px" :data="state.tableConfig" @change="changePages">
      <template #headerCell="{ scope: { column } }">
        <div>
          <span>{{ column.title }}</span>
          <a-tooltip>
            <template #title>{{ column.tips }}</template>
            <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'customer_name'">
          <div class="flex">
            <a-image
              :src="scope.record.avatar"
              width="50px"
              height="50px"
              v-if="scope.record.avatar"
              style="min-width: 50px"
            />
            <div class="ml-5px text_overflow_row1">
              <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                <template #title>{{ scope.record.customer_name }}</template>
                <div class="text_overflow">{{ scope.record.customer_name }}</div>
              </a-tooltip>
              <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                <template #title>{{ scope.record.extend_user_id }}</template>
                <div class="text_overflow number-id">ID：{{ scope.record.extend_user_id }}</div>
              </a-tooltip>
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'customer_tag'">
          <div v-if="scope.record.customer_tag?.length > 15">
            <a-tooltip
              placement="top"
              :disabled="!scope.record.customer_tag"
              :overlayInnerStyle="{ width: 'max-content' }"
            >
              <template #title>{{ scope.record.customer_tag }}</template>
              <div class="text_overflow w-full">{{ scope.record.customer_tag || '--' }}</div>
            </a-tooltip>
          </div>
          <span v-else>{{ scope.record.customer_tag || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'is_chat'">
          <div class="flex-y-center">
            <span class="rounds" :style="{ background: chatEnumCls(scope.record.is_chat) }"></span>
            <span :style="{ color: chatEnumCls(scope.record.is_chat) }">
              {{ chatEnum(scope.record.is_chat) }}
            </span>
          </div>
        </template>
        <template v-if="scope.column.key === 'wx_user_id'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.wx_user_name || '--' }}</template>
            <div class="text_overflow">{{ scope.record.wx_user_name || '--' }}</div>
          </a-tooltip>
          <div v-if="scope.record.wx_user_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.wx_user_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.wx_user_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'wx_link_id'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.link_name || '--' }}</template>
            <div class="text_overflow">{{ scope.record.link_name || '--' }}</div>
          </a-tooltip>
          <div v-if="scope.record.link">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.link }}</template>
              <div class="text_overflow number-id">链接：{{ scope.record.link }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'corp_name'">
          <div v-if="scope.record.corp_name.length">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corp_name }}</template>
              <div class="text_overflow">{{ scope.record.corp_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>{{ scope.record.corp_name || '--' }}</div>
          <div v-if="scope.record.corpid">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corpid }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.corpid }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'callback_at'">
          <div>{{ scope.record.is_callback == 1 ? scope.record.callback_at : '--' }}</div>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue'
  import { getFansList } from '@/api/common'
  import datas from '../src/linkKefu'
  import dayjs from 'dayjs'
  import { chatEnum, chatEnumCls } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  const { followerTableConfig: tableConfig, followerSearchConfig: searchConfig } = datas()
  const props = defineProps(['data', 'date', 'group_id'])
  const emits = defineEmits(['event'])
  const followerNumSearchBaseLayoutRef = ref()
  const state = reactive({
    isNormal: false,
    selectedRowKeys: [],
    selectedRows: [],
    tableConfig,
    searchConfig,
    initParams: {
      page: 1,
      page_size: 20,
      customer_add_time: '',
      apply_map: <any>[],
      group_id: props.group_id,
      wx_user_id: ''
    },
    selectionRowsPlus: [],
    oldData: []
  })

  onMounted(async () => {
    state.initParams.wx_user_id = props.data?.wx_user_id
    if (props.date) {
      state.initParams.apply_map = props.date.split('_')
      state.searchConfig.data[0].value = props.date.split('_')
    } else {
      state.initParams.apply_map = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.searchConfig.data[0].value = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    await initData()
  })
  const initData = async () => {
    try {
      state.tableConfig.loading = true
      const result = await getFansList(state.initParams)
      if (result.code === 0) {
        state.tableConfig.dataSource = result.data.list
        state.tableConfig.pagination.total = result.data?.total || 0
        state.tableConfig.pagination.current = result.data?.page || 1
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const changeValue = (data: any) => {
    if (!data.status) {
      state.initParams.page = 1
      state.initParams.page_size = 20
    }
    if (data.formData.add_fans_time?.length) {
      const str = data.formData.add_fans_time.join('_')
      if (str == '_') delete data.formData.add_fans_time
    }
    if (data.formData.callback_time_map?.length) {
      const str = data.formData.callback_time_map.join('_')
      if (str == '_') delete data.formData.callback_time_map
    }
    if (data.formData.apply_map?.length) {
      const str = data.formData.apply_map.join('_')
      if (str == '_') delete data.formData.apply_map
    }
    if (!data.status) {
      searchConfig.data.forEach((item) => {
        if (item.type == 'joint_date') {
          item.field = 'apply_map'
        }
      })
      state.initParams.apply_map = props.date
        ? props.date.split('_')
        : [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      data.formData.apply_map = props.date
        ? props.date.split('_')
        : [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }

    state.initParams = { ...state.initParams, ...data.formData, page: 1 }

    initData()
  }
  const changePages = (data: any) => {
    state.initParams.page = data.current
    state.initParams.page_size = data.pageSize
    state.tableConfig.pagination.pageSize = data.pageSize
    initData()
  }
</script>

<style scoped lang="scss">
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .goods-warp {
    display: flex;
    .goods-warp-img {
      width: 40px;
      height: 40px;
      border-radius: 2px;
      flex: none;
      margin-right: 8px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .goods-warp-content {
      flex: 1;
      padding: 0 10px;
    }
    .text-overflow-row1 {
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 1;
    }
  }
</style>
