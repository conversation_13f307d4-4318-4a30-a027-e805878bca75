<template>
  <div>
    <a-tabs v-model:activeKey="activeKey" @change="handleChange">
      <a-tab-pane key="id" tab="以客服ID查询"></a-tab-pane>
      <a-tab-pane key="name" tab="以客服名称查询"></a-tab-pane>
      <a-tab-pane key="phone" tab="以客服手机号查询" v-if="isPhone"></a-tab-pane>
    </a-tabs>
    <!-- 每次最多可查{{ pageSize }}单， -->
    <div class="mb-8px mt-8px tips">
      输入{{ renderTypeTitle }}批量查询客服，每行一个{{ renderTypeTitle }}，换行输入下一个{{ renderTypeTitle }}
    </div>
    <div class="mb-8px mt-8px tips c-#FF4D4F!" v-if="activeKey === 'phone'">
      注释：请确保手机号的正确性，若出错的次数超出企业规模人数的20%，会导致1天不可调用。
    </div>
    <div
      contenteditable="true"
      @input="handleInput($event)"
      ref="orderInput"
      @blur="editnameSet(index, $event)"
      @paste="handlePaste($event)"
      class="order-input"
    ></div>

    <div class="footer flex justify-between items-center mt-24px">
      <div class="flex items-center" @click="clearText">
        <img class="del_icon" src="@/assets/images/del_icon.png" />
        <div class="desc ml-6px mt-4px">清空</div>
      </div>
      <div class="flex">
        <a-button :mr="20" @click="close">取消</a-button>
        <a-button type="primary" @click="submit(rulesForm)">确认</a-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { nextTick, reactive, ref, watch, computed } from 'vue'
  import { message } from 'ant-design-vue'

  const props = defineProps({
    pageSize: {
      type: Number
    },
    invalidOrderNums: {
      type: Array
    },
    isPhone: {
      type: Boolean,
      default: false
    }
  })
  const renderTypeTitle = computed(() => {
    return (
      {
        id: '客服ID',
        name: '客服名称',
        phone: '客服手机号'
      }[activeKey.value] || '客服名称'
    )
  })
  const content = ref('请输入')
  const orderInput = ref(null)
  const orderNumbers = ref([])
  const orderNumArr = ref([])
  const matchList = ref([])
  const activeKey = ref('id')
  const setStyle = () => {
    if (orderNumArr.value && orderNumArr.value.length > 0) {
      // 获取内容并检测是否包含特定文本
      const content = orderInput.value.textContent
      //   console.log('dddddddddd', content, orderInput.value)
      //   const divContent = orderInput.value.innerHTML
      let ccc = ''
      console.log('orderNumbers.value', orderNumbers.value)

      orderNumbers.value.forEach((item) => {
        ccc += `<div>${item}</div>`
      })
      orderInput.value.innerHTML = `<div>${ccc}</div>`

      let divContent = orderInput.value.innerHTML
      orderNumbers.value.forEach((item) => {
        if (matchList.value.includes(item)) {
          divContent = divContent.replace(`<div>${item}</div>`, `<div style="color: red;">${item}</div>`)
        }
      })

      orderInput.value.innerHTML = divContent
      console.log('orderInput.value.innerHTML', orderInput.value.innerHTML)
    }
  }
  const handlePaste = (event) => {
    // 阻止默认的粘贴行为
    event.preventDefault()

    // 获取纯文本
    const text = event.clipboardData.getData('text')

    // 将纯文本插入到 contenteditable 中
    document.execCommand('insertText', false, text)
  }
  const handleChange = () => {
    init()
  }
  watch(
    () => props.invalidOrderNums,
    () => {
      matchList.value = props.invalidOrderNums
      setStyle()
      console.log('009090909', matchList.value)
    },
    {
      immediate: true,
      deep: true
    }
  )
  const emit = defineEmits(['close', 'submit'])

  const close = () => {
    emit('close')
  }

  const handleInput = () => {
    nextTick(() => {
      const text = orderInput.value.innerText
      console.log(text, 'text----')
      orderNumbers.value = text.split('\n').map((order) => order.trim())
    })
  }
  const editnameSet = (index, target) => {
    orderNumArr.value = orderNumbers.value.filter((item) => item)
    console.log('target', orderNumArr.value)
    // if (orderNumArr.value.length > props.pageSize) {
    //   message.warning(`本次最多可查${props.pageSize}条！`)
    // } else {
    //   if (orderNumArr.value.length == 0) return
    //   // setStyle()
    // }
  }

  const init = () => {
    console.log('*-*-*-**-*-')
    nextTick(() => {
      orderInput.value.innerHTML = ''
      orderInput.value.textContent = ''
      orderNumbers.value = []
      orderNumArr.value = []
    })
  }
  const clearText = () => {
    init()
    message.success('数据已清空')
    console.log('-0-0-0-0-0')
  }
  const submit = (formEl) => {
    emit('submit', orderNumArr.value, activeKey.value)
  }
  defineExpose({
    init
  })
</script>

<style lang="scss" scoped>
  .tips {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .order-input {
    height: 238px;
    overflow-y: auto;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #fe9d35;
    padding: 8px;
    box-sizing: border-box;

    &:focus-visible {
      outline: 2px solid #fe9d35 !important;
    }
  }
  .order-input:empty::before {
    content: '请输入';
    white-space: pre-wrap;
    color: #ccc;
  }
  div[contenteditable='true'] {
    caret-color: red; /* 设置光标颜色为蓝色 */
  }
  .footer {
    cursor: pointer;
    .del_icon {
      width: 15px;
      height: 16px;
    }
    .desc {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #fe0301;
    }
  }
  :deep(.ant-tabs .ant-tabs-tab) {
    padding-top: 0;
  }
</style>
