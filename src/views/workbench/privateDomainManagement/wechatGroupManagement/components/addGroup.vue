<template>
  <div>
    <!-- -->
    <a-form :model="data.form" ref="ruleForm" labelAlign="left" :rules="rules" :labelCol="{ style: 'width:95px;' }">
      <a-form-item label="加粉模式" name="addFanModel" required>
        <a-select
          ref="select"
          :disabled="props.item?.id"
          v-model:value="data.form.addFanModel"
          @focus="focus"
          @change="handleChange"
        >
          <a-select-option :value="1">企微加粉</a-select-option></a-select
        >
      </a-form-item>
      <a-form-item label="分组名称" name="name" required>
        <a-input v-model:value="data.form.name" :maxlength="100" @keydown.space.prevent placeholder="请输入分组名称" />
      </a-form-item>
      <a-form-item
        label="产品库"
        name="product_id"
        :rules="[
          {
            required: true,
            message: '请选择产品库',
            trigger: ['blur', 'change']
          }
        ]"
      >
        <div class="flex items-center">
          <a-select
            show-search
            placeholder="请选择产品库"
            :field-names="{ label: 'name', value: 'id' }"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="data.form.product_id"
            :options="data.productList"
            :filter-option="filterOption"
          >
            <template #dropdownRender="{ menuNode: menu }">
              <v-nodes :vnodes="menu" />
              <a-divider style="margin: 4px 0" />
              <a-space>
                <a-button class="ml8px pa-0!" size="small" type="link" @click="addProduct">新增</a-button>
                <a-button size="small" class="pa-0!" type="link" @click="get_product_list(true)">刷新</a-button>
              </a-space>
            </template>
          </a-select>
        </div>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input v-model:value="data.form.remark" :maxlength="100" placeholder="请输入备注" />
      </a-form-item>
      <a-form-item label="状态">
        <a-switch :checkedValue="1" :unCheckedValue="2" v-model:checked="data.form.status" />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect, defineComponent } from 'vue'
  import { message } from 'ant-design-vue'
  import { wechatGroupApi, updateGroupApi, getGroupApi } from '../index.api'
  import { product_list } from '@/api/common'
  import datas from '../src/data'
  import { useRouter } from '@/hooks/use-router'
  const { routerResolve } = useRouter()
  const { rules } = datas()
  const VNodes = defineComponent({
    props: {
      vnodes: {
        type: Object,
        required: true
      }
    },
    render() {
      return this.vnodes
    }
  })
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      name: '',
      addFanModel: 1,
      status: 1,
      product_id: null
    }
  })

  //获取产品库数据
  const get_product_list = async (refresh) => {
    try {
      let params = {
        page: 1,
        page_size: 9999
      }
      const res = await product_list(params)
      data.productList = res.data.list || []
      if (refresh) {
        message.success('产品库更新成功')
      }
    } catch (error) {
      console.error(error)
    }
  }
  const filterOption = (input, option) => {
    return (option?.label || option?.corp_name || option?.name).toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  //新页面打开产品库
  const addProduct = () => {
    routerResolve({ name: 'ProductManagement', query: { isAdd: true } })
  }
  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.name = ''
    data.form.addFanModel = 1
    data.form.status = 1
    data.form.remark = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ...data.form
      }
      if (props.item?.id) {
        let res = await updateGroupApi(params)
      } else {
        await wechatGroupApi(params)
      }

      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }
  const getDetail = async () => {
    try {
      let res = await getGroupApi({ id: props.item?.id })
      data.form = {
        ...res.data.wechatGroup,
        product_id: res.data.wechatGroup?.product_id || null
      }
      delete data.form?.createdAt
      delete data.form?.updatedAt
      delete data.form?.linkNum
      console.log('1111111111', data.form)
    } catch (error) {}
  }
  watchEffect(() => {
    if (!props.item?.id) {
      clearData()
    } else {
      ruleForm.value?.clearValidate()
    }
    get_product_list()
  })
  if (props.item?.id) {
    getDetail()
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
