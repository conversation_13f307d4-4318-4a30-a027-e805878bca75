<template>
  <div class="comp_wrapper">
    <a-row :gutter="20">
      <a-col :span="isMobile ? 24 : 12">
        <SearchBaseLayout
          :data="leftState.searchConfig.data"
          :actions="leftState.searchConfig.options"
          @changeValue="leftSubmitForm"
        />
        <div class="mt-24px font-size-12px line-height-17px c-#656D7D">
          共选企微：<span class="c-#B46007 mr-8px">{{ leftSelectedCounts }}个</span> 共选成员：<span class="c-#B46007"
            >{{ rightSelectedCounts }}个</span
          >
        </div>

        <TableZebraCrossing class="mt-10px" :data="leftState.tableConfig" @change="(v) => changePages(v, leftState)">
          <!-- <template #headerCell="{ scope: { column } }">
          <template v-if="column.key === 'checkbox'">
            <a-checkbox
              v-model:checked="leftState.checkedAll"
              :indeterminate="leftState.indeterminate"
              @change="(e: any) => checkAll(leftState, e, 'left')"
            >
            </a-checkbox>
          </template>
        </template> -->
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.key === 'checkbox'">
              <a-checkbox
                v-model:checked="record.checked"
                :indeterminate="record.indeterminate"
                :disabled="!record.user_list?.length"
                v-if="record.id"
                @change="(e: any) => subChange(record, e, leftState, 'left')"
                @click.stop
              >
              </a-checkbox>
            </template>
            <template v-if="column.key === 'corp_name'">
              <div class="flex">
                <!-- <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ record.corp_name }}</template>
                  
                </a-tooltip> -->
                <div class="text_overflow">{{ record.corp_name }}</div>
                <div class="select-tip" v-show="leftSingleCounts(record.corpid)">
                  已选<span class="c-#FE9D35 ml-4px mr-4px">{{ leftSingleCounts(record.corpid) }}</span
                  >个成员
                </div>
              </div>

              <!-- <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                <template #title>{{ record.corpid }}</template>
                <div class="flex-y-center">
                  
                </div>
              </a-tooltip> -->
              <div class="text_overflow number-id">ID：{{ record.corpid }}</div>
              <div v-if="[1, 2].includes(record.is_fail)" class="flex-y-center mt-4px">
                <ExclamationCircleOutlined class="c-red font-size-12px mr-4px text_overflow_row1" />
                <div class="c-red font-size-12px h-12px line-height-12px">
                  {{ record.is_fail == 2 ? '成员全部异常' : '成员部分异常' }},请尽快处理
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </a-col>
      <a-col :span="isMobile ? 24 : 12">
        <SearchBaseLayout
          :data="rightState.searchConfig.data"
          :actions="rightState.searchConfig.options"
          @changeValue="rightSubmitForm"
          :btnNames="['batchCheckCustomer']"
          :key="rightState.searchConfig.key"
        />
        <TableZebraCrossing class="mt-16px" :data="rightState.tableConfig" @change="(v) => changePages(v, rightState)">
          <template #headerCell="{ scope: { column } }">
            <template v-if="column.key === 'checkbox'">
              <a-checkbox
                v-model:checked="rightState.checkedAll"
                :indeterminate="rightState.indeterminate"
                :disabled="!rightState.rawDataSource.length"
                @change="(e: any) => checkAll(rightState, e, 'right')"
              >
              </a-checkbox>
            </template>
          </template>
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.key === 'checkbox'">
              <a-checkbox
                :disabled="record.disabled"
                v-model:checked="record.checked"
                v-if="record.id"
                @change="(e: any) => subChange(record, e, rightState, 'right')"
              >
              </a-checkbox>
            </template>
            <template v-if="column.key === 'department_name'">
              <div class="text_overflow c-#313233 max-w-170px">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title> {{ record.department_name || '--' }} </template>
                  <span class="">{{ record.department_name || '--' }}</span>
                </a-tooltip>
              </div>
            </template>
            <template v-if="column.key === 'name'">
              <div class="flex">
                <!-- <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ record.name }}</template>
                  
                </a-tooltip> -->
                <div class="text_overflow">{{ record.name }}</div>
              </div>

              <!-- <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                <template #title>{{ record.user_id }}</template>
                <div class="flex-y-center">
                  
                </div>
              </a-tooltip> -->
              <div class="text_overflow number-id">ID：{{ record.user_id }}</div>

              <div v-if="record.wx_status !== 1" class="flex-y-center mt-4px">
                <ExclamationCircleOutlined class="c-red font-size-12px mr-4px text_overflow_row1" />
                <div class="c-red font-size-12px h-12px line-height-12px">成员异常</div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </a-col>
    </a-row>

    <div :class="[!isMobile ? 'comp_footer_btn' : 'mt-24px mb-24px text-right']">
      <a-button @click="emits('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </div>
    <a-modal
      v-model:open="dialog.visible"
      :maskClosable="false"
      :title="dialog.title"
      :width="dialog.width"
      :footer="null"
      destory-on-close
    >
      <CheckOrder v-if="dialog.type == 'checkOrder'" :isPhone="true" @close="onClose" @submit="onBatchCheck" />
      <SetName v-else :wxUserId="currentSelected.result" :groupId="groupId" @event="event" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed } from 'vue'
  import { workListApi, userListApi } from '../../enterpriseWechatManagement/index.api'
  import { saveGainCustomerLink } from '../../gainCustomerLinkManagement/index.api'
  import { wechat_group_link_users, department } from '../index.api'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import SetName from './setName.vue'
  import CheckOrder from './CheckOrder.vue'
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const currentSelected: any = ref({
    id: undefined,
    corpid: undefined,
    result: []
  })
  const emits = defineEmits(['event'])
  const props = defineProps(['groupId'])
  const dialog = reactive({
    visible: false,
    title: '批量添加客服',
    width: 600,
    type: ''
  })
  const innerHeight = computed(() => window.innerHeight - 272)
  const leftState = reactive({
    bindMemberList: [] as any, // 获取客服分组绑定成员
    // 存储原始数据用于前端分页
    rawDataSource: [],
    checkedAll: false,
    indeterminate: false,
    initParams: {
      corp_id: undefined,
      corp_name: undefined
    },
    selectedRowId: undefined,
    searchConfig: {
      data: [
        {
          type: 'input.text',
          field: 'corp_id',
          value: undefined,
          props: {
            placeholder: '请输入企微ID'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 10
          }
        },
        {
          type: 'input.text',
          field: 'corp_name',
          value: undefined,
          props: {
            placeholder: '请输入企微名称'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 10
          }
        }
      ],
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 8,
          md: 8,
          lg: 8,
          xl: 6,
          xxl: 6
        }
      }
    },
    tableConfig: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      size: 'small',
      defaultExpandedRowKeys: [],
      dataSource: [],
      scroll: { y: 'calc(100vh - 272px - 48px - 48px - 54px)', scrollToFirstRowOnChange: false },
      columns: [
        {
          title: '',
          dataIndex: 'checkbox',
          key: 'checkbox',
          width: 20
        },
        {
          title: '企微名称',
          dataIndex: 'corp_name',
          key: 'corp_name',
          width: 240
        }
      ],
      customRow: (record: any) => {
        return {
          class: record.id === leftState.selectedRowId ? 'highlight-row' : '',
          onClick: () => {
            leftState.selectedRowId = record.id
            rightInitData({ corpid: record.corpid, id: record.id })
            try {
              department({
                corpid: record.corpid
              }).then((res) => {
                rightState.searchConfig.data.forEach((item) => {
                  if (item.field == 'department_ids') {
                    if (res.data?.length) {
                      item.props.options = res.data
                      item.value = undefined
                    } else {
                      item.props.options = []
                      item.value = undefined
                    }
                  }
                })
                rightState.searchConfig.key++
              })
            } catch {}
          }
        }
      },
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    }
  })
  const rightState = reactive({
    // 存储原始数据用于前端分页
    rawDataSource: [],
    checkedAll: false,
    indeterminate: false,
    initParams: {
      user_id: undefined,
      name: undefined
    },
    searchConfig: {
      data: [
        {
          type: 'input.text',
          field: 'name',
          value: undefined,
          props: {
            placeholder: '请输入成员名称'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 8
          }
        },
        {
          type: 'input.text',
          field: 'user_id',
          value: undefined,
          props: {
            placeholder: '请输入成员ID'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 8
          }
        },
        {
          type: 'input.text',
          field: 'phone',
          value: undefined,
          props: {
            placeholder: '请输入手机号'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 8
          }
        },
        {
          type: 'treeSelect',
          field: 'department_ids',
          value: undefined,
          span: 6,
          multiple: true,
          props: {
            options: [],
            placeholder: '请选择所属部门'
          },
          fieldNames: {
            label: 'name',
            value: 'department_id'
          },
          layout: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            xxl: 8
          }
        }
      ],
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 8,
          md: 8,
          lg: 8,
          xl: 12,
          xxl: 6
        }
      },
      key: 0
    },
    tableConfig: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      size: 'small',
      defaultExpandedRowKeys: [],
      dataSource: [],
      scroll: { y: 'calc(100vh - 272px - 48px - 48px)', scrollToFirstRowOnChange: false },
      columns: [
        {
          title: '',
          dataIndex: 'checkbox',
          key: 'checkbox',
          width: 20
        },
        {
          title: '成员名称',
          dataIndex: 'name',
          key: 'name',
          width: 180
        },
        {
          title: '所属部门',
          dataIndex: 'department_name',
          key: 'department_name',
          width: 100
        }
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 20,
        size: 'small',
        showTotal: (total: any) => `共${total}条数据`
      }
    }
  })
  const loading = ref(false)
  // 记录根据左侧点击保存右侧选中的数据
  const newMap = reactive(new Map())
  let leftStateStatus = new Map()
  const leftSubmitForm = (v: any) => {
    leftState.rawDataSource.forEach((it: any) => {
      leftStateStatus.set(it.corpid, {
        checked: it.checked,
        indeterminate: it.indeterminate
      })
    })
    if (v.status) {
      leftState.initParams = {
        ...v.formData
      }
    } else {
      leftState.initParams = {
        corp_id: undefined,
        corp_name: undefined
      }
    }
    currentSelected.value.id = undefined
    currentSelected.value.corpid = undefined
    currentSelected.value.result = []
    rightState.rawDataSource = []
    rightState.tableConfig.dataSource = []
    rightState.checkedAll = false
    rightState.indeterminate = false
    rightState.tableConfig.pagination.total = 0
    leftInitData()
  }
  const rightSubmitForm = (v: any) => {
    console.log(v)
    if (v.type == 'batchCheckCustomer') {
      if (!currentSelected.value.id) {
        return message.warning('请选择企业')
      }
      dialog.type = 'checkOrder'
      dialog.visible = true
      dialog.width = 500
      return
    }
    if (v.status) {
      rightState.initParams = {
        ...v.formData
      }
    } else {
      rightState.initParams = {
        user_id: undefined,
        name: undefined
      }
    }
    rightInitData({
      id: currentSelected.value.id,
      corpid: currentSelected.value.corpid,
      user_names: v.formData?.name,
      user_ids: v.formData?.user_id,
      phones: v.formData?.phone,
      department_ids: v.formData?.department_ids,
      isSearch: v.status
    })
  }
  const changePages = (data: any, state: any) => {
    state.tableConfig.pagination.current = data.current
    state.tableConfig.pagination.pageSize = data.pageSize
    updateTableData(state)
  }
  const leftInitData = async (isInit = false) => {
    try {
      console.log('newMap', newMap)
      leftState.tableConfig.loading = true
      let result = await wechat_group_link_users({ wx_group_id: props.groupId })
      leftState.bindMemberList = result.data?.list || []
      let res = await workListApi({ page: 1, page_size: 100000, scene: 1 })

      let dataSource =
        res.data?.list.map((it: any) => {
          let status = leftStateStatus.get(it.corpid)
          let user_list = it.user_list || []
          let checked = false
          let indeterminate = false
          if (isInit) {
            let _user_list = user_list.filter((k: any) => leftState.bindMemberList.includes(k.user_id)) || []
            checked = Boolean(user_list?.length && _user_list.length && _user_list.length === user_list.length)

            indeterminate = Boolean(user_list?.length && _user_list.length && _user_list.length < user_list.length)
            if (user_list?.length && _user_list.length) {
              newMap.set(
                it.corpid,
                user_list.filter((k: any) => leftState.bindMemberList.includes(k.user_id))
              )
            }
          } else {
            checked = status?.checked
            indeterminate = status?.indeterminate
          }
          return {
            ...it,
            checked,
            indeterminate
          }
        }) || []
      console.log('init', newMap)
      if (leftState.initParams.corp_id) {
        dataSource = dataSource.filter((it: any) => it.corpid === leftState.initParams.corp_id)
      }
      if (leftState.initParams.corp_name) {
        dataSource = dataSource.filter((it: any) => it.corp_name === leftState.initParams.corp_name)
      }

      leftState.rawDataSource = dataSource
      leftState.tableConfig.pagination.current = 1
      if (!dataSource.length) {
        rightState.rawDataSource = []
        rightState.tableConfig.dataSource = []
        rightState.checkedAll = false
        rightState.indeterminate = false
        rightState.tableConfig.pagination.total = 0
      }
      updateTableData(leftState)
    } catch (error) {
      console.log(error)
    } finally {
      leftState.tableConfig.loading = false
    }
  }
  const rightInitData = async (query: any) => {
    try {
      const { id, corpid, department_ids } = query
      rightState.tableConfig.loading = true
      let params = {
        page: 1,
        page_size: query.page_size || 100000,
        corpid,
        department_ids: department_ids?.length ? department_ids.join(',') : '',
        group_id: props.groupId
      }
      if (query.user_names) params.user_names = query.user_names
      if (query.user_ids) params.user_ids = query.user_ids
      if (query.phones) params.phones = query.phones
      let res = await userListApi(params)
      rightState.checkedAll = false
      rightState.indeterminate = false
      rightState.tableConfig.dataSource = []
      rightState.rawDataSource = []

      let dataSource =
        res.data?.wechatWorkUsers?.map((it: any) => {
          return {
            ...it,
            // checked: leftState.bindMemberList?.includes(it.user_id) || false,
            checked:
              newMap
                .get(corpid)
                ?.map((v) => v.user_id)
                ?.includes(it.user_id) || false,
            disabled:
              (it.wx_status !== 1 && !leftState.bindMemberList?.includes(it.user_id)) ||
              (it.wx_status !== 1 &&
                !newMap
                  .get(it.corpid)
                  ?.map((v) => v.user_id)
                  ?.includes(it.user_id))
          }
        }) || []
      let leftCurrent: any = leftState.rawDataSource.find((it: any) => it.corpid === corpid)
      if (!query.isSearch) {
        let t = dataSource.filter((it: any) => !it.disabled)
        leftCurrent.allCanUseData = t.length
      }
      console.log('dataSource', dataSource, query)
      if (dataSource.filter((it: any) => it.checked).length) {
        rightState.checkedAll = dataSource.length === dataSource.filter((it: any) => it.checked).length
        rightState.indeterminate =
          dataSource.length === dataSource.filter((it: any) => it.checked).length ? false : true
        if (!query.isSearch) {
          newMap.set(
            corpid,
            dataSource.filter((it: any) => it.checked)
          )
        }

        if (leftCurrent) {
          let t = dataSource.filter((it: any) => !it.disabled)

          leftCurrent.checked = t.filter((it: any) => it.checked).length === t.length
          leftCurrent.indeterminate =
            !!t.filter((it: any) => it.checked).length && t.filter((it: any) => it.checked).length < t.length
        }
      }

      if (rightState.initParams.user_id) {
        dataSource = dataSource.filter((it: any) => it.user_id === rightState.initParams.user_id)
      }
      if (rightState.initParams.name) {
        dataSource = dataSource.filter((it: any) => it.name === rightState.initParams.name)
      }

      if (newMap.has(corpid)) {
        let leftCurrent: any = leftState.rawDataSource.find((it: any) => it.corpid === corpid)
        dataSource.forEach((v: any) => {
          if (newMap.get(corpid).find((it: any) => it.id === v.id)) {
            v.checked = true
          } else {
            v.checked = false
          }
        })
        if (newMap.get(corpid).length && newMap.get(corpid).length === leftCurrent?.allCanUseData) {
          rightState.checkedAll = true
          rightState.indeterminate = false
        } else if (newMap.get(corpid).length && newMap.get(corpid).length !== leftCurrent?.allCanUseData) {
          rightState.checkedAll = false
          rightState.indeterminate = true
        } else {
          rightState.checkedAll = false
          rightState.indeterminate = false
        }
      }
      // 左侧当前点击
      currentSelected.value.corpid = corpid
      currentSelected.value.id = id

      rightState.rawDataSource = dataSource
      rightState.tableConfig.pagination.current = 1
      updateTableData(rightState)
    } catch (error) {
      console.log(error)
    } finally {
      rightState.tableConfig.loading = false
    }
  }
  // 获取当前页数据
  const getCurrentPageData = (state: any) => {
    const { current, pageSize } = state.tableConfig.pagination
    const start = (current - 1) * pageSize
    const end = start + pageSize
    return state.rawDataSource.slice(start, end)
  }
  // 更新表格显示数据
  const updateTableData = (state: any) => {
    state.tableConfig.dataSource = getCurrentPageData(state)
    state.tableConfig.pagination.total = state.rawDataSource.length
  }
  leftInitData(true)
  const checkAll = (state: any, e: any, type: string) => {
    state.rawDataSource.forEach((v: any) => {
      if (v.id) {
        v.checked = v.disabled ? false : e.target.checked
      }
    })

    if (type === 'right') {
      let leftCurrent = leftState.rawDataSource.find((it: any) => it.corpid === currentSelected.value.corpid)
      if (leftCurrent) {
        leftCurrent.checked = e.target.checked
        leftCurrent.indeterminate = false
      }
      if (e.target.checked) {
        newMap.set(
          currentSelected.value.corpid,
          state.rawDataSource.filter((it: any) => it.checked)
        )
      } else {
        newMap.delete(currentSelected.value.corpid)
      }
      console.log('newMap---checkAll-right', newMap)
    }
    // if (type === 'left') {
    //   if (currentSelected.value.corpid) {
    //     rightState.rawDataSource.forEach((v: any) => {
    //       if (v.id) v.checked = e.target.checked
    //     })
    //     rightState.indeterminate = false
    //     rightState.checkedAll = e.target.checked
    //     newMap.set(
    //       currentSelected.value.corpid,
    //       rightState.rawDataSource.filter((it: any) => it.checked)
    //     )
    //     console.log('newMap---checkAll-left', newMap)
    //   }
    // }
    if (e.target.checked) {
      state.indeterminate = false
    }
  }
  const leftSelectedCounts = computed(() => {
    return [...newMap.values()].filter((arr) => arr.length > 0).length || 0
  })
  const rightSelectedCounts = computed(() => {
    let res = []
    for (let value of newMap.values()) {
      res.push(...value)
    }
    return res.length || 0
  })
  const leftSingleCounts = (corp_id: any) => {
    return newMap.get(corp_id)?.length || 0
  }
  const subChange = async (data: any, e: any, state: any, type: string) => {
    data.checked = e.target.checked
    if (type === 'right') {
      if (!e.target.checked && data.wx_status !== 1 && leftState.bindMemberList?.includes(data.user_id)) {
        data.disabled = true
      }
      allCheckedStatus(state)
      let leftCurrent: any = leftState.rawDataSource.find((it: any) => it.corpid === data.corpid)
      let rightCurrent: any = newMap.get(currentSelected.value.corpid) || []
      if (e.target.checked) {
        rightCurrent.push(data)
      } else {
        rightCurrent = rightCurrent.filter((it: any) => it.id !== data.id)
      }
      newMap.set(currentSelected.value.corpid, rightCurrent)
      if (leftCurrent) {
        // let t = state.rawDataSource.filter((it: any) => !it.disabled)

        leftCurrent.checked = rightCurrent.length === leftCurrent.allCanUseData
        leftCurrent.indeterminate = !!rightCurrent.length && rightCurrent.length < leftCurrent.allCanUseData
      }
    }
    if (type === 'left') {
      // if (currentSelected.value.corpid !== data.corpid) {
      // 右侧触发勾选时
      await rightInitData({
        id: data.id,
        corpid: data.corpid
      })
      // }
      // 左侧选中时，右侧全选状态
      rightState.rawDataSource.forEach((v: any) => {
        if (v.id) {
          // v.checked = leftState.bindMemberList.includes(v.user_id)
          //   ? leftState.bindMemberList.includes(v.user_id)
          //   : e.target.checked
          // v.disabled = leftState.bindMemberList.includes(v.user_id) ? true : false
          v.checked = v.disabled ? false : e.target.checked
        }
      })
      rightState.indeterminate = false
      rightState.checkedAll = e.target.checked

      const newArr = rightState.rawDataSource.filter((v: any) => !v.disabled)
      const length = newArr.filter((v: any) => v.checked).length

      data.indeterminate = !!length && newArr.length && length < newArr.length
      data.checked = !!length && newArr.length && length === newArr.length
      newMap.set(
        currentSelected.value.corpid,
        rightState.rawDataSource.filter((it: any) => it.checked)
      )
      console.log('newMap----subChange-left', newMap)
    }
  }
  function allCheckedStatus(state: any) {
    const newArr = state.rawDataSource.filter((v: any) => !v.disabled)
    const length = newArr.filter((v: any) => v.checked).length
    state.indeterminate = !!length && length < newArr.length
    state.checkedAll = length === newArr.length
  }
  const submitForm = async () => {
    try {
      loading.value = true
      let res = []
      for (let value of newMap.values()) {
        res.push(...value)
      }
      currentSelected.value.result = res.map((it: any) => it.user_id)
      if (currentSelected.value.result.length === 0) {
        message.error('至少选择一名成员')
        return
      }
      console.log('currentSelected.value.result', currentSelected.value.result)
      await saveGainCustomerLink({
        group_id: props.groupId,
        wx_user_id: currentSelected.value.result.join(',')
      })
      emits('event', { cmd: 'submit' })
      loading.value = false
      // dialog.title = '设置名称'
      // dialog.type = 'userName'
      // dialog.visible = true
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  const onBatchCheck = (data: any, type: string) => {
    console.log(data, type)
    if (data.length == 0) {
      message.warning('请输入正确的客服数据')
      return
    }
    // let page_size = 10
    // if (data.length > page_size) {
    //   message.warning(`每次最多可查${page_size}条！`)
    //   return
    // }
    try {
      onClose()
      let params = {
        id: currentSelected.value.id,
        corpid: currentSelected.value.corpid,
        isSearch: true
        // page_size
      }
      let str = (data && data.join(',')) || ''
      if (type == 'id') {
        params.user_ids = str
      } else if (type == 'phone') {
        params.phones = str
      } else {
        params.user_names = str
      }
      console.log(params, 'paramsparamsparamsparams')
      rightInitData(params)
    } catch (error) {
      console.log(error, 'error')
    }
  }

  const onClose = () => {
    dialog.visible = false
    dialog.type = ''
  }

  const event = ({ cmd }: any) => {
    dialog.visible = false
    if (cmd === 'submit') {
      emits('event', { cmd: 'submit' })
    }
    dialog.type = ''
  }
</script>

<style scoped lang="scss">
  .select-tip {
    background: #fff4e9;
    border-radius: 2px;
    border: 1px solid #ffe6cb;
    color: #c57d31;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    padding-left: 4px;
    padding-right: 4px;
    margin-left: 4px;
  }
  :deep(.highlight-row) {
    background: #f7f9fc;
  }
</style>
