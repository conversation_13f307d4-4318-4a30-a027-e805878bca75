<template>
  <div class="comp_dialog_sold_out">
    <a-form :model="form" :labelCol="{ style: { width: '62px' } }">
      <a-form-item label="欢迎语：" name="">
        <a-textarea
          :auto-size="{ minRows: 7 }"
          v-model:value="form.remark"
          :maxlength="200"
          showCount
          @keydown.space.prevent
          placeholder="请输入欢迎语"
        ></a-textarea>
        <div class="">
          <a-button type="link" class="p-0!" @click="addName">插入客户昵称</a-button>
        </div>
      </a-form-item>

      <div class="link-list">
        <div v-for="(item, index) in form.welcome_link" :key="index" class="flex-y-center">
          <div class="item-link text_overflow">
            <a-tooltip>
              <template #title>{{ item.link }}</template>
              {{ item.link }}
            </a-tooltip>
          </div>
          <a-button type="link" danger class="c-#FE4D4F mb-10px" @click="delLink(index)">删除</a-button>
        </div>
      </div>
    </a-form>
    <a-button type="primary" class="mb-10px" :disabled="form.welcome_link.length > 4" @click="addLink"
      >添加链接</a-button
    >
    <a-form-item label="上传图片/视频" name="">
      <div class="flex flex-wrap" v-loading="state.fileLoading">
        <div v-for="(item, index) in forms.imgList" :key="index" class="mr-10px mb-10px content_box">
          <a-image
            width="101px"
            height="101px"
            class="mb8px"
            :src="item.file_url"
            v-if="item.file_type == 'image'"
          ></a-image>
          <FilePreview
            v-if="item.file_type == 'video'"
            :src="getUrlWithTimestamp(item)"
            :isShowVideo="true"
            :imageSrc="item.cover_url"
            style="width: 101px; height: 101px"
            class="overflow-hidden"
          ></FilePreview>
          <img src="@/assets/images/h5/delete.png" class="delete-icon flex-center" @click="onDel(index)" />
        </div>
        <Upload
          v-if="forms.imgList.length < 5"
          modes="image"
          type="promotion"
          :goods-id="form.images"
          accept=".jpg,.png,.mp4"
          :size="10"
          :mixedType="{
            image: {
              size: 10
            },
            video: {
              size: 10,
              use: 'goods'
            }
          }"
          @change="onUpload"
        >
        </Upload>
      </div>
    </a-form-item>
    <!--   -->
    <div style="text-align: right">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="state.loading">保存</a-button>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      @ok="okModal"
      destroyOnClose
    >
      <a-form ref="ruleFormRef" :model="forms" :rules="rules" :labelCol="{ style: { width: '62px' } }">
        <a-form-item label="链接：" name="link">
          <a-input v-model:value="forms.link" @keydown.space.prevent placeholder="请输入链接，以http或https开头" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="tsx" setup name="DialogSoldOut">
  import { reactive, ref, watchEffect } from 'vue'
  import { save_welcome_txt } from '@/api/common'
  import { message } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { uploadFileApi } from '../index.api'
  import { convertToImg } from '@/utils/common'
  const emit = defineEmits(['event'])
  const state = reactive({
    loading: false,
    fileLoading: false,
    dialog: {
      visible: false,
      title: '添加链接',
      width: 500,
      type: 'welcome'
    }
  })

  const rules = {
    remark: [{ required: true, message: '请输入欢迎语', trigger: ['blur', 'change'] }],
    link: [
      { required: true, message: '请输入链接', trigger: ['change', 'blur'] },
      {
        pattern: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
        message: '请输入正确的链接',
        trigger: ['change', 'blur']
      }
    ]
  }
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    // 审核不通过时的type
    type: [String, Number]
  })
  const form = reactive({
    id: props.item.id,
    remark: props.item.welcome_txt,
    link: '',
    images: '',
    welcome_link: props.item.welcome_link
      ? props.item.welcome_link.split(',').map((item) => {
          return {
            link: item
          }
        })
      : ([] as Array<{ link: string }>)
  })
  const forms = reactive({
    link: undefined,
    imgList: []
  })
  const ruleFormRef = ref()
  const addLink = async () => {
    state.dialog.visible = true
    forms.link = undefined
  }
  const onDel = (index: any) => {
    forms.imgList.splice(index, 1)
  }
  const onUpload = debounce(async (v) => {
    if (v.content) {
      try {
        let params = {
          corp_id: props.item.corp_id,
          file_type: v.fileInfo.type,
          file_url: v.content
        }
        state.fileLoading = true
        let res = await uploadFileApi(params)
        if (res.code == 0) {
          forms.imgList.push({
            file_type: v.fileInfo.type,
            file_url: v.content,
            cover_url: v.fileInfo.type == 'video' ? convertToImg(v.content) : ''
          })
          state.fileLoading = false
        }
      } catch (error) {
        console.log(error)
      } finally {
        state.fileLoading = false
      }
    }
  }, 500)
  const okModal = async () => {
    if (!(await ruleFormRef.value.validate('link'))) return
    form.welcome_link.push({
      link: forms.link
    })
    state.dialog.visible = false
  }

  const addName = () => {
    const textToAdd = '{客户昵称}'
    if (form.remark.length + textToAdd.length <= 200) {
      form.remark += textToAdd
    } else {
      // 可以选择在这里给用户一个提示，例如：
      // 或者使用 message 组件提示用户
      message.warning('已达到最大字数限制')
    }
  }
  const delLink = (index: number) => {
    form.welcome_link.splice(index, 1)
  }
  async function submitForm() {
    try {
      state.loading = true
      const params = {
        id: props.item.id,
        welcome_txt: form.remark,
        welcome_link: form.welcome_link.map((item) => item.link).join(','),
        images: forms.imgList.length > 0 ? JSON.stringify({ list: forms.imgList }) : ''
      }
      await save_welcome_txt(params)
      message.success('保存成功')
      emit('event', { cmd: 'success' })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  const getUrlWithTimestamp = (item: any) => {
    if (item.file_type == 'video') {
      return `${item.file_url}?timestamp=${item.timestamp}`
    }
    return item.file_url
  }
  watchEffect(async () => {
    forms.imgList = (props.item.images && JSON.parse(props.item.images)?.list) || []
  })
</script>

<style lang="scss" scoped>
  .comp_dialog_sold_out {
    color: #404040;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-bottom: 20px;
    }
    .tip {
      font-size: 12px;
      color: #85878a;
    }
  }
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
  .content_box {
    position: relative;
    .delete-icon {
      position: absolute;
      right: -5px;
      top: -6px;
      width: 14px;
      height: 14px;
      margin-top: 0;
      cursor: pointer;
    }
  }
</style>
