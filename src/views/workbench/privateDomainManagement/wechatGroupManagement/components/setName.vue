<template>
  <div class="form-scroll-wrapper">
    <a-form
      class="form-scroll-box"
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width: 128px;' }"
      autocomplete="off"
      :colon="true"
      :rules="rules"
    >
      <a-form-item name="link_name" label="获客链接名称">
        <a-input
          id="textarea"
          ref="text_content"
          :maxLength="20"
          placeholder="请输入获客链接名称"
          v-model:value="state.form.link_name"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <template #extra>
          <a-button
            type="link"
            class="p-0! h-auto"
            :disabled="dataDisabled"
            @click.stop="onAddeventSign({ Name: '日期', Value: 'date' })"
            >{日期}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="nameDisabled"
            @click.stop="onAddeventSign({ Name: '账号名称', Value: 'name' })"
            >{账号名称}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="memberNameDisabled"
            @click.stop="onAddeventSign({ Name: '成员名称', Value: 'memberName' })"
            >{成员名称}</a-button
          >
        </template>
      </a-form-item>
      <a-form-item name="tag_list" label="标签">
        <div class="link-list">
          <div v-for="(item, index) in state.form.tag_list" :key="index" class="flex-y-center">
            <div class="item-link text_overflow mb-6px">
              <a-tooltip>
                <template #title>{{ item.name }}</template>
                {{ item.name }}
              </a-tooltip>
            </div>
            <a-button type="link" danger class="c-#FE4D4F" @click="delLink(index)">删除</a-button>
          </div>
        </div>

        <a-button type="primary" class="mt-10px" :disabled="state.form.tag_list.length > 4" @click="addLink"
          >添加标签</a-button
        >
      </a-form-item>
    </a-form>
    <div class="text-right mr-20px">
      <AButton @click="emits('event', { cmd: 'close' })">取消</AButton>
      <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      destroy-on-close
      :footer="null"
    >
      <setNameTag @onEvent="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import type { Rule } from 'ant-design-vue/es/form'
  import { reactive, ref, onMounted, computed, nextTick } from 'vue'
  import setNameTag from './setNameTag.vue'
  import { debounce } from 'lodash-es'
  import { saveGainCustomerLink } from '../../gainCustomerLinkManagement/index.api'
  import { message } from 'ant-design-vue'
  const rules: Record<string, Rule[]> = {
    link_name: [{ required: true, message: '请输入链接名称', trigger: ['blur', 'change'] }]
  }
  const dataDisabled = computed(() => {
    if (!state.form.link_name) return false
    return state.form.link_name?.length > 46 || state.form.link_name?.indexOf('{日期}') !== -1
  })
  const nameDisabled = computed(() => {
    if (!state.form.link_name) return false
    return state.form.link_name?.length > 44 || state.form.link_name?.indexOf('{账号名称}') !== -1
  })
  const memberNameDisabled = computed(() => {
    if (!state.form.link_name) return false
    return state.form.link_name?.length > 44 || state.form.link_name?.indexOf('{成员名称}') !== -1
  })
  const ruleForm = ref()
  const textareaCache = { start: '', end: '' }
  const text_content = ref(null)
  const props = defineProps(['wxUserId', 'groupId'])
  const emits = defineEmits(['event'])
  const state = reactive({
    loading: false,
    dialog: {
      visible: false,
      title: '添加标签',
      width: 500
    },
    form: {
      wxUserId: props.wxUserId,
      link_name: undefined,
      tag_list: []
    } as any,
    tagList: [{ label: undefined, value: undefined }] as { label: string | undefined; value: string | undefined }[]
  })
  const addLink = async () => {
    state.dialog.visible = true
    // forms.link = undefined
  }
  const delLink = (index: number) => {
    state.form.tag_list.splice(index, 1)
  }
  const submitForm = async () => {
    try {
      state.loading = true
      await ruleForm.value?.validate()
      let params = {
        group_id: props.groupId,
        wx_user_id: props.wxUserId.join(','),
        link_name: state.form.link_name,
        tag_list: state.form.tag_list.map((item: any) => {
          return {
            name: item.name
          }
        })
      }
      let res: any = await saveGainCustomerLink(params)
      if (res.code === 0) {
        message.success(res.msg)

        emits('event', { cmd: 'submit' })
      }
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
  const onEvent = ({ cmd, data }: any) => {
    state.dialog.visible = false
    if (cmd === 'submit') {
      state.form.tag_list.push({
        name: data
      })
      ruleForm.value?.validateFields(['tag_list'])
    }
  }
  // 点击按钮添加标签
  const onAddeventSign = debounce((e) => {
    // 根据 e.Value 的值设置 currentTarget
    let currentTarget = `{${e.Name}}`

    if (!textareaCache.start && !textareaCache.end) {
      state.form.link_name = currentTarget
      textareaCache.start = currentTarget
    } else if (textareaCache.start) {
      state.form.link_name = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
    } else if (textareaCache.end) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
    ruleForm.value?.validateFields(['link_name'])
  }, 500)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea: any = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.link_name || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }
</script>

<style lang="scss" scoped>
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
</style>
