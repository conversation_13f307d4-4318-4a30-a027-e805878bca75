<template>
  <div class="mt-20px">
    <a-form :model="state.form" ref="ruleForm" :rules="state.rules" :labelCol="{ style: 'width:105px;' }">
      <a-form-item label="获客链接名称" name="name" required>
        <a-input
          v-model:value="state.form.name"
          @keydown.space.prevent
          placeholder="请输入获客链接名称"
          show-count
          :maxlength="20"
          id="textarea"
          ref="text_content"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <!-- <div class="mt-8px c-#7A869F text-13px line-height-12px flex-y-center">
          <div>{日期}</div>
          <div>{成员名称}</div>
          <div>{账号名称}</div>
        </div> -->
        <template #extra>
          <a-button
            type="link"
            class="p-0! h-auto"
            :disabled="dataDisabled"
            @click.stop="onAddeventSign({ Name: '日期', Value: 'date' })"
            >{日期}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="nameDisabled"
            @click.stop="onAddeventSign({ Name: '账号名称', Value: 'name' })"
            >{账号名称}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="memberNameDisabled"
            @click.stop="onAddeventSign({ Name: '成员名称', Value: 'memberName' })"
            >{成员名称}</a-button
          >
        </template>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="state.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { onMounted, reactive, ref, computed } from 'vue'
  import { message } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { updateLinkName, batchUpdateLinkName } from '../index.api'
  const ruleForm = ref(null)
  const props = defineProps(['item', 'type', 'ids'])
  const emit = defineEmits(['event'])

  const textareaCache = { start: '', end: '' }
  const state = reactive({
    loading: false,
    form: {
      name: props.item?.link_name || ''
    },
    rules: {
      name: [
        {
          required: true,
          message: '请输入获客链接名称',
          trigger: ['blur', 'change'] // 添加 change 触发
        },
        {
          max: 20,
          message: '获客链接名称最多20个字符',
          trigger: ['blur', 'change'] // 添加 change 触发
        }
      ]
    }
  })

  const dataDisabled = computed(() => {
    if (!state.form.name) return false
    return state.form.name?.length + 4 > 20 || state.form.name?.indexOf('{日期}') !== -1
  })
  const nameDisabled = computed(() => {
    if (!state.form.name) return false
    return state.form.name?.length + 6 > 20 || state.form.name?.indexOf('{账号名称}') !== -1
  })
  const memberNameDisabled = computed(() => {
    if (!state.form.name) return false
    return state.form.name?.length + 6 > 20 || state.form.name?.indexOf('{成员名称}') !== -1
  })

  // 点击按钮添加标签
  const onAddeventSign = debounce((e) => {
    // 根据 e.Value 的值设置 currentTarget
    let currentTarget = `{${e.Name}}`

    if (!textareaCache.start && !textareaCache.end) {
      state.form.name = state.form.name + currentTarget
      textareaCache.start = state.form.name || currentTarget
      // console.log(1, textareaCache.start, textareaCache.end)
    } else if (textareaCache.start) {
      state.form.name = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
      // console.log(2, textareaCache.start, textareaCache.end)
    } else if (textareaCache.end) {
      state.form.name = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
    // console.log(3, textareaCache.start, textareaCache.end)
    ruleForm.value?.validateFields(['name'])
  }, 200)

  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.name || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }

  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    state.form.name = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        if (props.type == 2) {
          batchUpdate()
        } else {
          add()
        }
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      state.loading = true
      let params = {
        id: props.item.id,
        name: state.form.name
      }
      console.log(" console.log('走着了； ---', res)", props.item, params)
      await updateLinkName(params)
      emit('event', { cmd: 'edit' })
      state.loading = false
    } catch (error) {
      state.loading = false
      console.error(error)
    }
  }

  const batchUpdate = async () => {
    try {
      state.loading = true
      let params = {
        link_id: props.ids,
        name: state.form.name,
        group_id: props.item.group_id
      }
      await batchUpdateLinkName(params)
      emit('event', { cmd: 'edit' })
      state.loading = false
    } catch (error) {
      state.loading = false
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
