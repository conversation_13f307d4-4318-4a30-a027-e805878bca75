<template>
  <Echarts key="left" id="left_echarts" :data="state.setOption_1" parentHeight="270px" />
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue'
  import { getTenFansFlow } from '../index.api'
  const props = defineProps(['data'])
  const state = reactive({
    setOption_1: {}
  })
  onMounted(() => {
    getData()
  })
  const getData = async () => {
    const res = await getTenFansFlow({ type: 3, wx_group_id: props.data.id })
    initEcharts(res.data.list)
  }
  const initEcharts = (data) => {
    let xAxisData = data.map((item) => item.interval)

    let yAxisData1 = data?.map((item) => item.fan_count)
    state.setOption_1 = {
      legend: {
        top: 6,
        right: 1,
        itemHeight: 6,
        itemWidth: 16,
        lineStyle: {
          width: 1
        },
        textStyle: {
          color: '#242F57',
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
            color: '#FE9D35'
          }
        },
        extraCssText:
          'background:rgba(100,100,100,0.9);border-radius: 8px;padding: 8px;width: 157px;border-color:transparent;',
        formatter: function (params) {
          console.log('params--', params)
          let html = `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">${params[0]?.name}</div>`
          params.forEach((v) => {
            html += `<div style="color: #fff;font-size: 12px;line-height: 17px;margin-bottom:8px;">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background:${v.color}"></span>
                <span style="display: inline-block;width:52px;margin-right:16px"> ${v.seriesName} </span>
                <span>${v.value}</span>
                </div>
                `
          })
          return html
        }
      },
      grid: {
        left: 1,
        right: 10,
        top: '16%',
        bottom: '1%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            }
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            onZero: false,
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: [
        {
          type: 'value',
          minInterval: 5,
          interval: 1,
          // name: '加粉数',
          axisLabel: {
            textStyle: {
              color: '#242F57',
              fontSize: '12px'
            },
            formatter: function (value: any) {
              // 根据数值动态设置 Y 轴单位
              if (value >= 100000000) {
                return value / 100000000 + '亿'
              } else if (value >= 10000000) {
                return value / 10000000 + '千万'
              } else if (value >= 1000000) {
                return value / 1000000 + '百万'
              } else if (value >= 10000) {
                return value / 10000 + '万'
              } else {
                return value
              }
            }
          },
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#f3f3f3'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '加粉数',
          color: '#FE005A',
          type: 'line',
          smooth: true,
          symbolSize: 4,
          label: {
            show: true,
            position: 'top',
            formatter: function (params: any) {
              return params.value == 0 ? '' : params.value
            }
          },
          areaStyle: {
            normal: {
              color: 'rgba(254,0,90,0.1)'
            }
          },
          lineStyle: {
            normal: {
              color: '#FE005A'
            }
          },
          data: yAxisData1
        }
      ]
    }
  }
</script>
