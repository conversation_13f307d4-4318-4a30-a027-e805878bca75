<template>
  <div>
    <!-- -->
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:60px;' }">
      <a-form-item name="weight" required>
        <template #label>
          <div class="flex-align">
            <span>权重</span>
            <a-tooltip>
              <template #title>权重越大，进粉几率越大</template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-select ref="select" v-model:value="data.form.weight">
          <a-select-option :value="item" v-for="(item, index) in data.weightList">{{ item }}</a-select-option></a-select
        >
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { updateLinkInfo } from '../index.api'
  import datas from '../src/data'

  const { rules } = datas()
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item', 'type', 'ids'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      weight: null
    },
    weightList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
  })

  onMounted(() => {})
  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.weight = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        type: 2,
        ids: props.type == 2 ? props.ids : [props.item.id],
        ...data.form,
        group_id: props.item.group_id
      }
      console.log(" console.log('走着了； ---', res)", props.item, params)
      await updateLinkInfo(params)
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }

  watchEffect(() => {
    data.form.weight = props.item.weight
  })
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
