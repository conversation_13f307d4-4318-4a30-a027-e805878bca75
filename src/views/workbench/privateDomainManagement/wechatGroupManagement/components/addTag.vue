<template>
  <div>
    <!-- -->
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:95px;' }">
      <a-form-item label="标签" name="addFanModel" required>
        <!-- <a-input
          :disabled="props.item?.id"
          v-model:value="data.form.name"
          :maxlength="20"
          @keydown.space.prevent
          placeholder="请输入标签"
        />
        <div>{日期}{成员名称}{客服组名称}</div> -->
        <a-textarea
          id="textarea"
          ref="text_content"
          class="flex-1"
          :maxLength="50"
          :rows="3"
          placeholder="请输入标签"
          v-model:value="data.form.name"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <template #extra>
          <a-button
            type="link"
            class="p-0! h-auto"
            :disabled="dataDisabled"
            @click.stop="onAddeventSign({ Name: '日期', Value: 'date' })"
            >{日期}</a-button
          >
          <a-button
            type="link"
            class="p-0! ml-6px h-auto"
            :disabled="nameDisabled"
            @click.stop="onAddeventSign({ Name: '企微成员名称', Value: 'name' })"
            >{企微成员名称}</a-button
          >
        </template>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { wechatGroupApi, updateGroupApi, getGroupApi } from '../index.api'
  import datas from '../src/data'
  import { debounce } from 'lodash-es'
  const { rules } = datas()
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      name: '',
      addFanModel: 1,
      status: 1,
      remark: ''
    }
  })
  // 点击按钮添加标签
  const onAddeventSign = debounce((e) => {
    // 根据 e.Value 的值设置 currentTarget
    let currentTarget = `{${e.Name}}`

    if (!textareaCache.start && !textareaCache.end) {
      state.form.link_name = currentTarget
      textareaCache.start = currentTarget
    } else if (textareaCache.start) {
      state.form.link_name = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
    } else if (textareaCache.end) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
    ruleForm.value?.validateFields(['link_name'])
  }, 200)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.link_name || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }
  onMounted(() => {})
  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.name = ''
    data.form.addFanModel = 1
    data.form.status = 1
    data.form.remark = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ...data.form
      }
      console.log(" console.log('走着了； ---', res)", props.item, params)
      if (props.item?.id) {
        console.log('走着了； ---', '2222222')

        let res = await updateGroupApi(params)
      } else {
        console.log('走着了； ---', '11111')
        await wechatGroupApi(params)
        console.log('走着了； ---', params)
      }

      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }
  const getDetail = async () => {
    try {
      let res = await getGroupApi({ id: props.item?.id })
      data.form = {
        ...res.data.wechatGroup
      }
      delete data.form?.createdAt
      delete data.form?.updatedAt
      delete data.form?.linkNum
      console.log('1111111111', data.form)
    } catch (error) {}
  }
  watchEffect(() => {
    if (!props.item?.id) {
      clearData()
    } else {
      ruleForm.value?.clearValidate()
    }
  })
  if (props.item?.id) {
    getDetail()
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
