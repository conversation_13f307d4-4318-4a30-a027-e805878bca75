<template>
  <div>
    <div>
      <SearchBaseLayout
        :data="state.searchConfig.data"
        @changeValue="changeValue"
        :actions="state.searchConfig.options"
      />
    </div>
    <div class="flex justify-end flex-items-center mt-24px mb-17px">
      <a-checkbox v-model:checked="state.isNormal" @change="normalSearch">异常成员</a-checkbox>
    </div>
    <TableZebraCrossing
      class="mt-16px"
      :data="state.tableConfig"
      :rowSelection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: selectedChange,
        hideSelectAll: false,
        getCheckboxProps,
        onSelect: onSelect,
        onSelectAll: onSelectAll
      }"
      @change="changePages"
    >
      <template #headerCell="{ scope: { column } }">
        <div>
          <span>{{ column.title }}</span>
          <a-tooltip>
            <template #title>{{ column.tips }}</template>
            <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'link_id'">
          <div class="flex">
            <div class="c-#313233 line-height-14px" v-if="scope.record.link_name?.length > 15">
              <a-tooltip>
                <template #title
                  ><div>{{ scope.record.link_name || '-' }}</div></template
                >
                <div class="text-overflow-row1 c-#313233">{{ scope.record.link_name || '-' }}</div>
              </a-tooltip>
            </div>
            <div v-else>
              <span>{{ scope.record.link_name || '--' }}</span>
            </div>
          </div>

          <div class="mt-8px c-#7A869F font-size-12px line-height-12px">
            <span> ID：{{ scope.record.link_id || '--' }}</span>
          </div>
          <div v-if="[1, 2].includes(scope.record.fail_status)" class="flex-y-center mt-4px text_overflow_row1">
            <ExclamationCircleOutlined class="c-red font-size-12px mr-4px" />
            <div class="c-red font-size-12px h-12px line-height-12px">
              {{ fail_status(scope.record.fail_status) }}
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'on_sale'">
          <span class="item-tag" :class="{ online: scope.record?.on_sale == 1, offline: scope.record?.on_sale == 2 }">
            {{ statusType(scope.record?.on_sale)?.text }}
          </span>
        </template>
        <template v-if="scope.column.key === 'group_name'">
          <span>{{
            (scope.record?.group_list && scope.record?.group_list.map((it: any) => it.name).join(',')) || '--'
          }}</span>
        </template>
      </template>
    </TableZebraCrossing>
    <div class="text-end mt-10px" v-if="pointData.balance > 0">
      <AButton :mr="30" @click="close">取消</AButton>
      <AButton type="primary" @click="submit">确定</AButton>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue'
  import { WorkLinkApi, linkSetApi, linkGetApi } from '../index.api'
  import datas from '../src/linkData'
  import { message } from 'ant-design-vue'
  import { QuestionCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { isArray } from 'lodash-es'
  import { usePoints } from '@/hooks'
  const { pointData } = usePoints()
  const { goodsTableConfig: tableConfig, goodsSearchConfig: searchConfig, statusType, fail_status } = datas()
  const props = defineProps(['data'])
  const emits = defineEmits(['event'])
  const state = reactive({
    isNormal: false,
    selectedRowKeys: [],
    selectedRows: [],
    tableConfig,
    searchConfig,
    initParams: {
      page: 1,
      page_size: 20,
      is_fail: 0
    },
    selectionRowsPlus: [],
    oldData: []
  })
  const normalSearch = () => {
    state.initParams.page = 1
    state.initParams.is_fail = state.isNormal ? 1 : 0
    initData()
  }
  const getCheckboxProps = (record: any) => {
    return {
      disabled:
        record.on_sale == 2 ||
        pointData.value.balance <= 0 ||
        (record.fail_status == 2 && !state.selectedRowKeys.includes(record.id))
    }
  }
  const selectedChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRows = selectedRows
    state.selectedRowKeys = selectedRowKeys
  }
  const onSelect = (record: any, selected: any) => {
    if (selected) {
      state.selectionRowsPlus.push(record.id)
    } else {
      const indexToRemove = state.selectionRowsPlus.findIndex((id) => id === record.id)
      if (indexToRemove > -1) {
        state.selectionRowsPlus.splice(indexToRemove, 1)
      }
    }
  }

  const onSelectAll = (selected: any, selectedRows: any, changeRows: any) => {
    if (selected) {
      const newUserIds = changeRows.map((row) => row.id)
      // 使用 Set 来避免重复添加已存在的 user_id
      const existingIds = new Set(state.selectionRowsPlus)
      newUserIds.forEach((id) => {
        if (!existingIds.has(id)) {
          state.selectionRowsPlus.push(id)
        }
      })
    } else {
      const idsToRemove = changeRows.map((row) => row.id)
      state.selectionRowsPlus = state.selectionRowsPlus.filter((id) => !idsToRemove.includes(id))
    }
  }
  const close = () => {
    emits('event', { cmd: 'close' })
  }
  const submit = async () => {
    if (!state.selectionRowsPlus.length) {
      message.warning('请选择获客链接')
      return
    }
    try {
      let params = {
        wx_group_id: props?.data?.id,
        ids: state.selectionRowsPlus
      }
      await linkSetApi(params)
      emits('event', { cmd: 'edit' })
    } catch (error) {}
  }
  onMounted(async () => {
    await getOldData()
    await initData()
  })
  const initData = async () => {
    try {
      state.tableConfig.loading = true
      const result = await WorkLinkApi(state.initParams)
      if (result.code === 0) {
        state.tableConfig.dataSource = result.data.list
        state.tableConfig.pagination.total = result.data?.total || 0
        state.tableConfig.pagination.current = result.data?.page || 1
        if (isArray(state.selectionRowsPlus)) {
          const selectKeys = state.selectionRowsPlus
          const user_id_list = state.tableConfig.dataSource.filter((v) => selectKeys.includes(v.id)).map((v) => v.id)
          state.selectedRowKeys = [...user_id_list]
        }
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const changeValue = (data: any) => {
    if (!data.status) {
      state.initParams.page = 1
      state.initParams.page_size = 20
    }
    state.initParams = { ...state.initParams, ...data.formData, page: 1 }
    initData()
  }
  const changePages = (data: any) => {
    state.initParams.page = data.current
    state.initParams.page_size = data.pageSize
    state.tableConfig.pagination.pageSize = data.pageSize
    initData()
  }
  const getOldData = async () => {
    try {
      const result = await linkGetApi({ wx_group_id: props.data?.id })
      if (result.code === 0) {
        state.selectionRowsPlus = (result.data.list && result.data.list.map((v) => v.work_link_id)) || []
        if (state.selectionRowsPlus.length > 0) {
          state.selectedRowKeys = [...state.selectionRowsPlus]
        } else {
          state.selectedRowKeys = []
        }
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
</script>

<style scoped lang="scss">
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .goods-warp {
    display: flex;
    .goods-warp-img {
      width: 40px;
      height: 40px;
      border-radius: 2px;
      flex: none;
      margin-right: 8px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .goods-warp-content {
      flex: 1;
      padding: 0 10px;
    }
    .text-overflow-row1 {
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 1;
    }
  }
</style>
