<template>
  <div>
    <!-- -->
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:95px;' }">
      <a-form-item label="客户名称" name="user_name" required>
        <!-- <a-select
          :show-search="true"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="data.form.corpid"
          :options="data.enterpriseWechatList"
          :filter-option="filterOption"
          :fieldNames="{ value: 'corpid', label: 'corp_name' }"
          placeholder="请选择企微"
          allowClear
        >
        </a-select> -->
        <a-input v-model:value="data.form.user_name" @keydown.space.prevent placeholder="请输入客户名称" />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { wechatGroupApi, updateGroupApi, getGroupApi, updateLinkInfo } from '../index.api'
  import datas from '../src/data'
  import { workListApi } from '@/api/common'
  const { rules } = datas()
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      user_name: ''
    },
    enterpriseWechatList: []
  })
  const filterOption = (input, option) => {
    return (option?.label || option?.corp_name || option?.name).toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  onMounted(() => {})
  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.user_name = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ids: [props.item.id],
        type: 3,
        ...data.form,
        group_id: props.item.group_id
      }
      console.log(" console.log('走着了； ---', res)", props.item, params)
      await updateLinkInfo(params)
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }

  watchEffect(() => {
    data.form.user_name = props.item.user_name
  })
  const getEnterpriseWechatList = async () => {
    try {
      let res = await workListApi({ page: 1, page_size: 10000 })
      if (res.code === 0) {
        data.enterpriseWechatList = res.data.list || []
      }
    } catch (err) {
      console.log(err)
      state.enterpriseWechatList = []
    }
  }
  //   getEnterpriseWechatList()
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
