<template>
  <div>
    <!-- -->
    <a-form :model="data.form" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:140px;' }">
      <a-form-item name="auth_on_sale" required>
        <template #label>
          <div class="flex-align">
            <span>自动上线/下线</span>
            <a-tooltip>
              <template #title> 可根据时间段及添加企微次数自动变更上下线状态 </template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-radio-group v-model:value="data.form.auth_on_sale" @change="authOnSaleChange">
          <a-radio :value="1"><span class="span">不设置</span></a-radio>
          <a-radio :value="2"><span class="span">按时间段</span></a-radio>
          <a-radio :value="3"><span class="span">按成功添加企微次数</span></a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item name="time_str" class="period-item" v-if="[2, 3].includes(data.form.auth_on_sale)">
        <div class="ml-36px mt--14px">
          <SelectTime
            :key="data.form.auth_on_sale"
            v-model="data.form.time_str"
            @change="selectTimeChange"
          ></SelectTime>
        </div>
      </a-form-item>
      <a-form-item
        name="off_sale_rule"
        class="down-rules-count-wrapper"
        label="下线规则"
        v-if="data.form.auth_on_sale === 3"
      >
        <div class="flex-y-center">
          <span>成功添加企业微信</span>
          <a-input-number
            v-model:value.trim="data.form.off_sale_rule"
            :controls="false"
            :precision="0"
            :min="1"
            placeholder="请输入"
          />
          <span>次后自动下线</span>
        </div>
      </a-form-item>
      <a-form-item name="on_sale" required>
        <template #label>
          <div class="flex-align">
            <span>上下线</span>
            <a-tooltip>
              <template #title> 开启为上线，关闭为下线</template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-switch v-model:checked="data.form.on_sale" :checkedValue="1" :unCheckedValue="2" />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { updateLinkInfo } from '../index.api'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { getLinkInfo } from '@/api/common'
  const rules = {
    auth_on_sale: [{ required: true, message: '请选择自动上线/下线', trigger: ['blur', 'change'] }],
    on_sale: [{ required: true, message: '请选择上下线', trigger: ['blur', 'change'] }],
    time_str: [
      {
        required: true,
        validator: (_, value) => {
          if (String(value).indexOf('1') === -1) {
            return Promise.reject('请选择时段')
          } else {
            return Promise.resolve()
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    off_sale_rule: [
      {
        required: true,
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('请输入下线规则')
          } else {
            return Promise.resolve()
          }
        },
        trigger: ['blur', 'change']
      }
    ]
  }
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item', 'type', 'ids'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      auth_on_sale: 1,
      off_sale_rule: undefined,
      time_str: undefined,
      on_sale: 1
    }
  })
  const authOnSaleChange = () => {
    // 切换时清空时间点
    data.form.time_str = Array(7)
      .fill()
      .map(() => Array(48).fill(0))
  }
  const selectTimeChange = () => {
    ruleForm.value?.validateFields(['time_str'])
  }
  onMounted(() => {})
  const close = () => {
    ruleForm.value.clearValidate()
    if (!props.item?.id) {
      clearData()
    }
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.auth_on_sale = 1
    data.form.off_sale_rule = undefined
    data.form.time_str = undefined
    data.form.on_sale = 1
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ids: props.type == 2 ? props.ids : [props.item.id],
        type: 1, //1：批量处理上线下，2：批量更新权重，3：修改客户名称
        ...data.form,
        group_id: props.item.group_id
      }
      if (params.time_str?.length && [2, 3].includes(params.auth_on_sale)) {
        params.time_str = params.time_str.flat().join('')
      } else {
        params.time_str = undefined
      }
      await updateLinkInfo(params)
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }
  // 初始化二维数组为0
  function restoreArray(flatStr, chunkSize = 48) {
    const result = []
    for (let i = 0; i < flatStr.length; i += chunkSize) {
      const chunk = flatStr.slice(i, i + chunkSize)
      result.push(chunk.split('').map(Number))
    }
    return result
  }
  watchEffect(async () => {
    if (props.item?.id) {
      let res = await getLinkInfo({ id: props.item.id })
      if (res.code === 0) {
        data.form = {
          ...data.form,
          ...res.data
        }
        if (data.form.auth_on_sale == 0) {
          data.form.auth_on_sale = 1
        }
        if (res.data?.time_str) {
          data.form.time_str = restoreArray(res.data.time_str)
        }
        if (res.data.wx_user_id) {
          data.form.wx_user_id = res.data.wx_user_id.split(',')
        }
      }
    }
  })
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
