<template>
  <div>
    <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
    <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange" class="mt16px">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'name'">
          <a-tooltip placement="topLeft">
            <template #title>{{ scope.record.name }}</template>
            <div class="url_overflow">{{ scope.record.name }}</div>
          </a-tooltip>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>

<script setup>
  import datas from '../src/relationShipLink'
  const props = defineProps(['datas'])
  const { pageChange, searchForm, searchConfig, state } = datas(props.datas)
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';

  .url_overflow {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
</style>
