<template>
  <div class="form-scroll-wrapper">
    <a-form
      class="form-scroll-box"
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width: 80px;' }"
      autocomplete="off"
      :colon="true"
    >
      <a-form-item
        name="tag"
        label="标签"
        :rules="[{ required: true, message: '请输入标签', trigger: ['blur', 'change'] }]"
      >
        <a-input
          id="textarea_tag"
          ref="text_content"
          v-model:value.trim="state.form.tag"
          placeholder="请输入标签"
          show-count
          :maxlength="20"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <div>
          <a-button
            type="link"
            class="p-0! h-auto"
            :disabled="dataDisabled"
            @click.stop="onAddeventSign({ Name: '日期', Value: 'date' })"
            >{日期}</a-button
          >
          <a-button
            type="link"
            :disabled="nameDisabled"
            class="p-0! ml-6px h-auto"
            @click.stop="onAddeventSign({ Name: '成员名称', Value: 'name' })"
            >{成员名称}</a-button
          >
          <a-button
            type="link"
            :disabled="groupDisabled"
            class="p-0! ml-6px h-auto"
            @click.stop="onAddeventSign({ Name: '客服组名称', Value: 'memberName' })"
            >{客服组名称}</a-button
          >
        </div>
      </a-form-item>
    </a-form>
    <div class="text-right mr-20px">
      <AButton @click="emits('onEvent', { cmd: 'close' })">取消</AButton>
      <AButton type="primary" @click="handleActions('save')" :loading="state.loading">保存</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import type { Rule } from 'ant-design-vue/es/form'
  import { reactive, ref, onMounted, computed, nextTick } from 'vue'
  import { debounce } from 'lodash-es'

  import {} from '../index.api'
  const emits = defineEmits(['onEvent'])
  const dataDisabled = computed(() => {
    if (!state.form.tag) return false
    return state.form.tag?.length > 16 || state.form.tag?.indexOf('{日期}') !== -1
  })
  const nameDisabled = computed(() => {
    if (!state.form.tag) return false
    return state.form.tag?.length > 14 || state.form.tag?.indexOf('{成员名称}') !== -1
  })

  const groupDisabled = computed(() => {
    if (!state.form.tag) return false
    return state.form.tag?.length > 13 || state.form.tag?.indexOf('{客服组名称}') !== -1
  })
  const ruleForm = ref()
  const textareaCache = { start: '', end: '' }
  const text_content = ref(null)
  const state = reactive({
    loading: false,
    form: {
      tag: undefined
    } as any
  })
  const handleActions = async (type: string) => {
    try {
      if (type === 'save') {
        state.loading = true
        await ruleForm?.value?.validate()
        emits('onEvent', { cmd: 'submit', data: state.form.tag })
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.loading = false
    }
  }

  // 点击按钮添加标签
  const onAddeventSign = debounce((e) => {
    // 根据 e.Value 的值设置 currentTarget
    let currentTarget = `{${e.Name}}`

    if (!textareaCache.start && !textareaCache.end) {
      state.form.tag = currentTarget
      textareaCache.start = currentTarget
    } else if (textareaCache.start) {
      state.form.tag = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
    } else if (textareaCache.end) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
    ruleForm.value?.validateFields(['tag'])
  }, 500)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea_tag: any = document.getElementById('textarea_tag')
    // const cursorPos = textarea_tag.selectionStart

    let _content = state.form?.tag || ''
    const startPos = textarea_tag.selectionStart
    const endPos = textarea_tag.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }
</script>

<style lang="scss" scoped></style>
