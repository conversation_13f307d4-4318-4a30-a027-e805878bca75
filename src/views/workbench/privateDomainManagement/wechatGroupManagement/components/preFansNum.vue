<template>
  <div>
    <a-form :model="data.form" ref="ruleForm" labelAlign="left" :rules="rules" :labelCol="{ style: 'width:95px;' }">
      <!-- 选择类型 -->
      <a-form-item label="加粉模式" name="config_type" required>
        <a-radio-group v-model:value="data.form.config_type" @change="handleSelectionChange">
          <a-radio :value="1">
            <span>每天固定预加粉数量</span>
            <a-tooltip class="ml6px">
              <template #title> 每天固定预加粉数量模式下，只能选择一天</template>
              <QuestionCircleFilled class="c-#C5C6CC ml6px" />
            </a-tooltip>
          </a-radio>
          <a-radio :value="2">
            <span>总预加粉数量</span>
            <a-tooltip>
              <template #title> 总预加粉数量模式下，可以选择多天进行分段选择</template>
              <QuestionCircleFilled class="c-#C5C6CC ml6px" />
            </a-tooltip>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- 日历组件 -->
      <a-form-item label="时间选择">
        <div class="calendar-wrapper">
          <CalendarComponent
            :current-date="data.currentDate"
            :selected-dates="data.selectedDates"
            :historical-data="data.historicalData"
            @date-click="handleDateClick"
            @month-change="handleMonthChange"
            @batch-select="handleBatchSelect"
          />
        </div>
      </a-form-item>

      <!-- 每天固定预加粉数量输入 -->
      <a-form-item
        v-if="data.form.config_type === 1"
        label="每天数量"
        name="fans_count"
        :rules="[
          {
            required: true,
            message: '请输入每天固定预加粉数量',
            trigger: ['blur', 'change']
          }
        ]"
      >
        <a-input-number
          class="w-200px"
          v-model:value="data.form.fans_count"
          :min="1"
          :precision="0"
          placeholder="请输入每天固定预加粉数量"
          @change="handleDailyAmountChange"
        />
      </a-form-item>

      <!-- 总预加粉数量输入 -->
      <a-form-item
        v-if="data.form.config_type === 2"
        label="预加粉总数"
        name="fans_count"
        :rules="[
          {
            required: true,
            message: '请输入预加粉总数',
            trigger: ['blur', 'change']
          }
        ]"
      >
        <a-input-number
          class="w-200px"
          v-model:value="data.form.fans_count"
          :min="1"
          :precision="0"
          placeholder="请输入预加粉总数"
          @change="handleTotalAmountChange"
        />
      </a-form-item>

      <!-- 显示选中的日期和分配的数量 -->
      <a-form-item v-if="data.selectedDates.length > 0" label="分配详情">
        <div class="selected-dates">
          <div class="date-list">
            <div v-for="(dateItem, index) in data.selectedDates" :key="dateItem.key" class="date-item">
              <a-input-number
                :addon-before="`${formatDate(dateItem.date)}`"
                v-model:value="dateItem.amount"
                :min="0"
                :precision="0"
                style="width: 100%; margin: 0 8px"
                :placeholder="`请输入${formatDate(dateItem.date)}的加粉数量`"
                @change="onAmountInputChange"
                @blur="validateAmountInput"
                :status="dateItem.amount === null || dateItem.amount === undefined ? 'error' : ''"
              />
              <a-button type="text" size="small" @click="removeDate(index)" class="delete-btn">
                <DeleteOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">提交</a-button>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, computed, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import { CloseOutlined, DeleteOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
  import { updateFiledApi } from '../index.api'
  import CalendarComponent from '@/components/ui/common/CalendarComponent/index.vue'
  const props = defineProps(['item', 'visible'])
  const emit = defineEmits(['event', 'upDateTop', 'close'])

  const ruleForm = ref(null)

  // 表单验证规则
  const rules = {
    config_type: [
      {
        required: true,
        message: '请选择加粉模式',
        trigger: ['blur', 'change']
      }
    ]
  }

  const data = reactive({
    loading: false,
    currentDate: new Date(),
    form: {
      config_type: 1, // 1-固定数量、2-总预加粉
      fans_count: null
    },
    selectedDates: [],
    historicalData: {} // 存储历史数据
  })

  // 格式化日期
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 处理日期点击
  const handleDateClick = (date) => {
    if (date.isPast) {
      // 历史日期，显示历史数据
      if (date.hasHistoricalData) {
        showHistoricalData(date)
      }
      return
    }

    if (data.form.config_type === 1) {
      handleDailyDateClick(date)
    } else {
      handleTotalDateClick(date)
    }
  }

  // 处理月份变化
  const handleMonthChange = (newDate) => {
    data.currentDate = newDate
  }

  // 处理批量选择
  const handleBatchSelect = (batchData) => {
    if (data.form.config_type === 1) {
      // 每天固定预加粉数量模式下，只选择第一个日期
      if (batchData.dates.length > 0) {
        const firstDate = batchData.dates[0]
        const dateObj = {
          date: firstDate,
          amount: data.form.fans_count || 0,
          key: formatDate(firstDate)
        }
        data.selectedDates = [dateObj]
      }
    } else {
      // 总预加粉数量模式下，添加所有日期
      batchData.dates.forEach((date) => {
        const existingIndex = data.selectedDates.findIndex((d) => d.date.getTime() === date.getTime())

        if (existingIndex === -1) {
          data.selectedDates.push({
            date: date,
            amount: 0,
            key: formatDate(date)
          })
        }
      })

      // 按时间正序排序
      data.selectedDates.sort((a, b) => a.date - b.date)

      // 重新计算分配
      calculateTotalDistribution()
    }
  }

  // 处理每天固定数量的日期点击
  const handleDailyDateClick = (date) => {
    const existingIndex = data.selectedDates.findIndex((d) => d.date.getTime() === date.date.getTime())

    if (existingIndex >= 0) {
      // 移除已选择的日期
      data.selectedDates.splice(existingIndex, 1)
    } else {
      // 每天固定预加粉数量模式下只能选择一天，先清空之前的选择
      data.selectedDates = []
      // 添加新日期
      data.selectedDates.push({
        date: date.date,
        amount: data.form.fans_count || 0,
        key: date.key
      })
    }
  }
  // const handleDailyDateClick = (date) => {
  //   const startDate = date.date
  //   const { fans_count = 0 } = data.form

  //   // 判断是否已经选中这段七天
  //   const isSelected = data.selectedDates.some((d) => d.date.getTime() === startDate.getTime())

  //   if (isSelected) {
  //     // 取消选择（移除这7天）
  //     const startTime = startDate.getTime()
  //     data.selectedDates = data.selectedDates.filter((d) => {
  //       const timeDiff = d.date.getTime() - startTime
  //       return timeDiff < 0 || timeDiff >= 7 * 24 * 60 * 60 * 1000
  //     })
  //   } else {
  //     // 只保留当前点击起始日期后的七天数据
  //     data.selectedDates = []

  //     for (let i = 0; i < 7; i++) {
  //       const newDate = new Date(startDate)
  //       newDate.setDate(startDate.getDate() + i)

  //       data.selectedDates.push({
  //         date: newDate,
  //         amount: fans_count,
  //         key: `${date.key}-${i}` // 可根据需要生成唯一 key
  //       })
  //     }
  //   }
  // }

  // 处理总数量模式的日期点击
  const handleTotalDateClick = (date) => {
    const existingIndex = data.selectedDates.findIndex((d) => d.date.getTime() === date.date.getTime())

    if (existingIndex >= 0) {
      // 移除已选择的日期
      data.selectedDates.splice(existingIndex, 1)
    } else {
      // 添加新日期，设置默认值为0而不是null
      data.selectedDates.push({
        date: date.date,
        amount: 0,
        key: date.key
      })
      // 按时间正序排序
      data.selectedDates.sort((a, b) => a.date - b.date)
    }

    // 重新计算分配
    calculateTotalDistribution()
  }

  // 计算总数量分配
  const calculateTotalDistribution = () => {
    if (!data.form.fans_count || data.selectedDates.length === 0) return

    const totalDays = data.selectedDates.length
    const baseAmount = Math.floor(data.form.fans_count / totalDays)
    const remainder = data.form.fans_count % totalDays

    data.selectedDates.forEach((dateItem, index) => {
      if (index < remainder) {
        dateItem.amount = baseAmount + 1
      } else {
        dateItem.amount = baseAmount
      }
    })
  }

  // 处理选择类型变化
  const handleSelectionChange = () => {
    data.selectedDates = []
    data.form.fans_count = null
  }

  // 处理每天数量变化
  const handleDailyAmountChange = () => {
    data.selectedDates.forEach((dateItem) => {
      dateItem.amount = data.form.fans_count
    })
  }

  // 处理总数量变化
  const handleTotalAmountChange = () => {
    calculateTotalDistribution()
  }

  // 移除日期
  const removeDate = (index) => {
    data.selectedDates.splice(index, 1)
    if (data.form.config_type === 2) {
      calculateTotalDistribution()
    }
  }

  // 显示历史数据
  const showHistoricalData = (date) => {
    const historicalData = data.historicalData[formatDate(date)]
    message.info(`历史数据: ${formatDate(date)} - ${historicalData.amount}`)
  }

  // 关闭弹窗
  const close = () => {
    emit('event', { cmd: 'close' })
  }

  // 提交表单
  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        submit()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  // 提交数据
  const submit = async () => {
    if (data.selectedDates.length === 0) {
      message.warning('请选择至少一个日期')
      return
    }

    if (data.form.config_type === 1 && !data.form.fans_count) {
      message.warning('请输入每天固定预加粉数量')
      return
    }

    if (data.form.config_type === 2 && !data.form.fans_count) {
      message.warning('请输入预加粉总数')
      return
    }

    if (data.form.config_type === 2) {
      // 校验分配数量是否为空
      const emptyIndexes = validateAllAmounts()
      if (emptyIndexes.length > 0) {
        message.warning('请填写所有日期的加粉数量')
        // 滚动到第一个空值位置
        scrollToEmptyInput(emptyIndexes[0])
        return
      }

      const sum = data.selectedDates.reduce((acc, cur) => acc + Number(cur.amount || 0), 0)
      if (sum !== Number(data.form.fans_count)) {
        message.warning('各日期加粉数之和必须等于总预加粉数，请调整后再提交！')
        return
      }
    }

    try {
      data.loading = true

      // 构建提交数据
      let submitData = {}

      if (data.form.config_type === 1) {
        // 每天固定预加粉数量模式
        submitData = {
          config_type: 1, // 1-固定数量
          fans_count: data.form.fans_count,
          data_list: {}
        }

        // 为选中的日期设置相同的数量
        data.selectedDates.forEach((item) => {
          const dateKey = formatDate(item.date).replace(/-/g, '') // 转换为YYYYMMDD格式
          submitData.data_list[dateKey] = data.form.fans_count
        })
      } else {
        // 总预加粉数量模式
        submitData = {
          config_type: 2, // 2-总预加粉
          fans_count: data.form.fans_count,
          data_list: {}
        }

        // 为每个选中的日期设置对应的数量
        data.selectedDates.forEach((item) => {
          const dateKey = formatDate(item.date).replace(/-/g, '') // 转换为YYYYMMDD格式
          submitData.data_list[dateKey] = item.amount
        })
      }

      // 调用API
      await updateFiledApi({
        field: 'pre_fans_schedule',
        value: JSON.stringify(submitData),
        id: props.item.id
      })

      message.success('预加粉计划设置成功')
      emit('event', { cmd: 'edit' })
      close()
    } catch (error) {
      console.error(error)
      message.error('设置失败，请重试')
    } finally {
      data.loading = false
    }
  }

  // 滚动到空值输入框
  const scrollToEmptyInput = (index) => {
    setTimeout(() => {
      const dateList = document.querySelector('.date-list')
      if (dateList) {
        const dateItems = dateList.querySelectorAll('.date-item')
        if (dateItems[index]) {
          dateItems[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
          // 聚焦到输入框
          const input = dateItems[index].querySelector('input')
          if (input) {
            input.focus()
          }
        }
      }
    }, 100)
  }

  // 输入时不自动分配
  const onAmountInputChange = () => {
    // 不自动分配，手动输入即可
  }

  // 校验单个输入框
  const validateAmountInput = () => {
    // 清除所有错误状态
    data.selectedDates.forEach((item) => {
      if (item.amount === null || item.amount === undefined) {
        item.amount = 0
      }
    })
  }

  // 校验所有分配数量
  const validateAllAmounts = () => {
    const emptyIndexes = []
    data.selectedDates.forEach((item, index) => {
      if (item.amount === null || item.amount === undefined) {
        emptyIndexes.push(index)
      }
    })
    return emptyIndexes
  }

  // 初始化
  onMounted(() => {
    // 设置当前日期为今天
    data.currentDate = new Date()

    // 如果有历史数据，加载历史数据
    if (props.item.pre_fans_schedule) {
      try {
        const scheduleData = JSON.parse(props.item.pre_fans_schedule)

        // 根据config_type设置选择类型
        if (scheduleData.config_type === 1) {
          data.form.config_type = 1
          data.form.fans_count = scheduleData.fans_count
        } else if (scheduleData.config_type === 2) {
          data.form.config_type = 2
          data.form.fans_count = scheduleData.fans_count
        }

        // 解析data_list
        if (scheduleData.data_list) {
          const today = new Date()
          today.setHours(0, 0, 0, 0)

          Object.entries(scheduleData.data_list).forEach(([dateKey, amount]) => {
            // 将YYYYMMDD格式转换为Date对象
            const year = parseInt(dateKey.substring(0, 4))
            const month = parseInt(dateKey.substring(4, 6)) - 1 // 月份从0开始
            const day = parseInt(dateKey.substring(6, 8))
            const date = new Date(year, month, day)
            const dateString = formatDate(date)

            if (date < today) {
              // 历史数据
              data.historicalData[dateString] = {
                amount: amount
              }
            } else {
              // 未来数据
              data.selectedDates.push({
                date,
                amount: amount,
                key: dateString
              })
            }
          })
        }
      } catch (error) {
        console.error('解析历史数据失败:', error)
      }
    }
  })
</script>

<style lang="scss" scoped>
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .close-btn {
      border: none;
      padding: 4px;
    }
  }

  .calendar-wrapper {
    width: 448px;
    border-radius: 4px;
    border: 1px dashed #d8d8d8;
  }

  .selection-tips {
    margin-top: 12px;

    .tip-text {
      margin: 0;
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
      gap: 4px;

      .anticon {
        color: #1890ff;
      }
    }
  }

  .selected-dates {
    .date-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 8px;

      .date-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        // border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .amount {
          font-weight: bold;
          color: #1890ff;
        }

        .delete-btn {
          color: #ff4d4f;

          &:hover {
            color: #ff7875;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
  }
</style>
