<template>
  <div class="comp_wrapper">
    <a-button type="primary" @click="addTag" class="pos-fixed right-24px top-11px">新增</a-button>
    <SearchBaseLayout :data="searchConfig.data" @changeValue="changeValue">
      <template #btns>
        <div class="flex flex-justify-end flex-items-center mr-10px ml-8px">
          <a-button type="primary" @click="syncGroupTag(true, true)" class="">同步企微标签</a-button>
        </div>
      </template>
    </SearchBaseLayout>
    <div class="TableZebraCrossing mt-16px">
      <TableZebraCrossing
        :data="tableConfig"
        :rowSelection="{
          selectedRowKeys: tableConfig.selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            tableConfig.selectedRowKeys = selectedRowKeys
            tableConfig.selectedRows = selectedRows
          }
        }"
        @change="changePages"
      >
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'action'">
            <div class="flex items-center">
              <a-popconfirm
                :disabled="tableConfig.selectedRowKeys.indexOf(scope.record.id) > -1"
                title="确定要删除该条标签吗？"
                placement="topRight"
                @confirm="delItem(scope.record)"
              >
                <a-button
                  :disabled="tableConfig.selectedRowKeys.indexOf(scope.record.id) > -1"
                  class="h-auto pa-0"
                  type="link"
                  >删除</a-button
                >
              </a-popconfirm>
            </div>
          </template>
        </template>
      </TableZebraCrossing>
    </div>
    <div class="footer mt-20px comp_footer_btn" style="text-align: right">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="data.loading">确定</a-button>
    </div>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      destroy-on-close
      :footer="null"
    >
      <setNameTag @onEvent="onEvent" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue'
  import { get_group_tag, add_tag, delete_group_tag, batch_add_wx_tag, get_tag_list } from '../index.api'
  import { message } from 'ant-design-vue'
  import setNameTag from './setNameTag.vue'
  const ruleForm = ref(null)
  const props = defineProps(['item', 'ids', 'names', 'corpids'])
  const emit = defineEmits(['event'])
  const searchConfig = {
    data: [
      {
        type: 'input.text',
        field: 'tag_name',
        value: undefined,
        props: {
          placeholder: '请输入客户标签'
        },
        layout: {}
      }
    ],
    options: {}
  }
  const tableConfig = reactive({
    columns: [
      {
        title: '客户标签',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100
      }
    ],
    dataSource: [],
    selectedRowKeys: [],
    selectedRows: [],
    rowKey: 'id',
    loading: false,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      page: 1,
      current: 1,
      page_size: 100,
      pageSize: 100,
      tag_name: '',
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  })
  const data = reactive({
    loading: false,
    form: {
      name: '',
      addFanModel: 1,
      status: 1,
      remark: ''
    },
    tagForm: {
      link: ''
    },
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    }
  })
  const onEvent = ({ cmd, data: r }: any) => {
    data.dialog.visible = false
    if (cmd === 'submit') {
      okModal(r)
    }
  }
  const addTag = () => {
    data.dialog.visible = true
    data.dialog.title = '新增客户标签'
    data.dialog.width = '550px'
    data.tagForm.link = ''
  }
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  const submitForm = async () => {
    try {
      const params = {
        group_id: props.item.group_id,
        link_ids: props.ids,
        tag_ids: tableConfig.selectedRows.map((item: any) => item.id),
        tag_name: tableConfig.selectedRows.map((item: any) => item.name)
      }
      await batch_add_wx_tag(params)
      message.success('保存成功')
      emit('event', { cmd: 'edit' })
    } catch (error) {}
  }
  const okModal = async (r: any) => {
    console.log('props.item.group_id', props.item.group_id, r)

    try {
      const params = {
        group_id: props.item.group_id,
        tag_name: r,
        link_ids: props.ids
      }
      await add_tag(params)
      data.dialog.visible = false
      message.success('保存成功')
      getGroupTag(false)
    } catch (error) {
      console.log('=====', error)
    }
  }
  const delItem = async (item: any) => {
    try {
      const params = {
        group_id: props.item.group_id,
        tag_id: item.id,
        corp_ids: [...new Set(props.corpids)]
      }
      await delete_group_tag(params)
      message.success('删除成功')
      getGroupTag(false)
    } catch (error) {
      console.log('=====', error)
    }
  }
  const getGroupTag = async (echo: boolean) => {
    try {
      if (tableConfig.loading) return
      tableConfig.loading = true
      const res = await get_group_tag({
        group_id: props.item.group_id,
        corpids: [...new Set(props.corpids)],
        page: tableConfig.pagination.page,
        page_size: tableConfig.pagination.page_size,
        tag_name: tableConfig.pagination.tag_name
      })
      tableConfig.pagination.total = res.data.total
      tableConfig.dataSource = res.data.list || []
      tableConfig.loading = false
      if (!echo) return
      props.names.forEach((selectedItem: any) => {
        const matchedItem = tableConfig.dataSource.find((item: any) => item.name === selectedItem)
        if (matchedItem.id && tableConfig.selectedRowKeys.indexOf(matchedItem.id) === -1) {
          tableConfig.selectedRows.push(matchedItem)
          tableConfig.selectedRowKeys.push(matchedItem.id)
        }
      })
    } catch (error) {
      tableConfig.loading = false
    }
  }
  const syncGroupTag = async () => {
    try {
      await get_group_tag({
        group_id: props.item.group_id,
        corpids: [...new Set(props.corpids)],
        page: tableConfig.pagination.page,
        page_size: tableConfig.pagination.page_size,
        tag_name: tableConfig.pagination.tag_name,
        scene: 1
      })
      message.success('标签同步成功')
      getGroupTag(true)
    } catch (error) {
      message.error('标签同步失败')
    }
  }
  const changePages = (data: any) => {
    tableConfig.pagination.page = data.current
    tableConfig.pagination.current = data.current
    tableConfig.pagination.page_size = data.pageSize
    tableConfig.pagination.pageSize = data.pageSize
    getGroupTag(true)
  }
  const changeValue = (data: any) => {
    tableConfig.pagination.tag_name = data.formData.tag_name
    if (!data.status) {
      tableConfig.pagination.page = 1
      tableConfig.pagination.page_size = 100
      tableConfig.pagination.pageSize = 100
      tableConfig.pagination.current = 1
      tableConfig.selectedRowKeys = []
      tableConfig.selectedRows = []
      getGroupTag(false)
      return
    }
    getGroupTag(true)
  }
  getGroupTag(true)
</script>

<style scoped lang="scss">
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
  }
  .TableZebraCrossing {
    max-height: calc(100vh - 240px);
    overflow-y: auto;
  }
</style>
