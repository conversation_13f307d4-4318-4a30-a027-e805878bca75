<template>
  <div class="comp_wrapper">
    <a-button type="primary" @click="onShowDrawer('addMember')" class="pos-fixed right-24px top-11px">新增</a-button>
    <div>
      <SearchBaseLayout
        :data="state.searchConfig.data"
        @changeValue="changeValue"
        :actions="state.searchConfig.options"
        :btnNames="['batchSearchCustomer']"
        :batchSetData="state.batchSetData"
      >
        <template #btns>
          <a-button
            @click="screenShotHandler"
            :disabled="screenShotData.loading"
            v-auth="['linkKefu_screenShot']"
            class="flex-y-center"
          >
            <SvgIcon icon="camera" class="mr-6px"></SvgIcon>
            <span>一键截图</span>
          </a-button>
        </template>
      </SearchBaseLayout>
    </div>

    <!-- <div class="flex mt-24px mb-17px batch_content">
      <div class="batch_btn">
        <a-button type="primary" class="mr-10px" v-if="state.selectedRowKeys.length > 0">批量操作</a-button>
        <div class="dot_list">
          <div class="dot_item" @click="onShowDialog('updateLine', {}, 2)">上下线</div>
          <div class="dot_item" @click="onShowDialog('batchUpdateAcqLinkName', {}, 2)">修改获客链接名称</div>
          <div class="dot_item" @click="onShowDrawer('batchCustomerTag', { group_id: data?.id })">修改标签</div>
          <div class="dot_item" @click="onShowDialog('weight', {}, 2)">编辑权重</div>
          <div class="dot_item" @click="batchGetLinkInfo">生成获客链接</div>
          <div class="dot_item" @click="batchDelete">移除</div>
        </div>
      </div>
    </div> -->
    <TableZebraCrossing
      class="mt-16px"
      :data="state.tableConfig"
      :rowSelection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: selectedChange,
        hideSelectAll: false,
        onSelect: onSelect,
        onSelectAll: onSelectAll
      }"
      @change="changePages"
    >
      <template #headerCell="{ scope: { column } }">
        <div>
          <span>{{ column.title }}</span>
          <a-tooltip>
            <template #title>{{ column.tips }}</template>
            <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'corp_id'">
          <div class="flex">
            <div class="c-#313233 line-height-14px" v-if="scope.record.corp_name?.length">
              <a-tooltip>
                <template #title
                  ><div>{{ scope.record.corp_name }}</div></template
                >
                <div class="text_overflow_row1 c-#313233">{{ scope.record.corp_name }}</div>
              </a-tooltip>
            </div>
            <div v-else>
              <span>{{ scope.record.corp_name || '--' }}</span>
            </div>
          </div>

          <div class="mt-8px c-#7A869F font-size-12px line-height-12px">
            <div v-if="scope.record.corp_id">
              <a-tooltip>
                <template #title
                  ><div class="text_overflow">{{ scope.record.corp_id }}</div></template
                >
                <div class="text_overflow">ID：{{ scope.record.corp_id }}</div>
              </a-tooltip>
            </div>
            <span v-else> ID：--</span>
          </div>
        </template>
        <template v-if="scope.column.key === 'link_id'">
          <div class="flex">
            <a-tooltip>
              <template #title>
                <div>{{ scope.record.link_name || '-' }}</div>
              </template>
              <div class="text_overflow c-#313233 inline-block">{{ scope.record.link_name || '-' }}</div>
            </a-tooltip>
            <a-tooltip v-if="!scope.record.link_id">
              <template #title>获客链接异常，不可进行操作</template>
              <EditOutlined class="ml4px" :class="[scope.record.link_id ? 'edit_icon_default' : 'edit_icon_disable']" />
            </a-tooltip>
            <EditOutlined
              v-else
              class="c-#FE9D35 ml4px"
              @click="onShowDialog('updateAcqLinkName', scope.record)"
            ></EditOutlined>
          </div>
          <a-tooltip v-if="scope.record.link">
            <template #title>{{ scope.record.link }}</template>
            <div class="flex-y-center">
              <span class="goods_info_data_name"> 链接：{{ scope.record.link }} </span>
              <CopyOutlined class="c-primary" @click="copy(scope.record.link)" />
            </div>
          </a-tooltip>
          <div class="flex-y-center" v-else>
            <span class="goods_info_data_name"> 链接： --</span>
          </div>

          <!-- <a-tooltip>
            <template #title
              ><div>{{ scope.record.link_id || '-' }}</div></template
            >
            <div class="text_overflow mt-8px c-#7A869F font-size-12px" style="word-break: break-all">
              ID：{{ scope.record.link_id || '--' }}
            </div>
          </a-tooltip> -->
        </template>
        <template v-if="scope.column.key === 'user_name'">
          <div class="flex">
            <div class="c-#313233 line-height-14px" v-if="scope.record.user_name?.length > 15">
              <a-tooltip>
                <template #title
                  ><div>{{ scope.record.user_name || '-' }}</div></template
                >
                <div class="text_overflow c-#313233">{{ scope.record.user_name || '-' }}</div>
              </a-tooltip>
            </div>
            <div v-else>
              <span>{{ scope.record.user_name || '--' }}</span>
              <!-- 这个版本先去掉 -->
              <!-- <EditOutlined class="c-#FE9D35 ml4px" @click="onShowDialog('userName', scope.record)"></EditOutlined> -->
            </div>
          </div>
          <a-tooltip>
            <template #title
              ><div>{{ scope.record.wx_user_id || '-' }}</div></template
            >
            <div class="text_overflow mt-6px c-#7A869F font-size-12px" style="word-break: break-all">
              ID：{{ scope.record.wx_user_id || '--' }}
            </div>
          </a-tooltip>
          <div class="flex-y-center mt-4px text_overflow_row1" v-if="[1].includes(scope.record.fail_status)">
            <ExclamationCircleOutlined class="c-red font-size-12px" />
            <div class="c-red ml-4px font-size-12px h-12px line-height-12px">成员异常</div>
          </div>
        </template>
        <template v-if="scope.column.key === 'status'">
          <div class="flex-y-center text_overflow_row1">
            <div
              :class="kfLinkExceptionEnum[scope.record.status].className"
              v-if="kfLinkExceptionEnum[scope.record.status]"
            >
              {{ kfLinkExceptionEnum[scope.record.status].text }}
            </div>
            <a-tooltip>
              <template #title
                ><div>{{ scope.record.fail_reason || '-' }}</div></template
              >
              <ExclamationCircleOutlined
                v-if="[2, 4].includes(scope.record.status) && scope.record.fail_reason"
                class="c-red font-size-12px mr-4px"
              />
            </a-tooltip>
          </div>
        </template>
        <template v-if="scope.column.key === 'tag_list'">
          <div class="flex">
            <a-tooltip>
              <template #title
                ><div>{{ scope.record.tags || '-' }}</div></template
              >
              <div class="text_overflow c-#313233">{{ scope.record.tags || '--' }}</div>
            </a-tooltip>
            <a-tooltip v-if="scope.record?.status == 2 && !scope.record?.link_id">
              <template #title>获客链接异常，不可进行操作</template>
              <EditOutlined
                class="ml4px"
                :class="[
                  scope.record?.status == 2 && !scope.record?.link_id ? 'edit_icon_disable' : 'edit_icon_default'
                ]"
              />
            </a-tooltip>
            <EditOutlined
              v-else
              class="c-#FE9D35 ml4px"
              @click="onShowDrawer('setCustomerTag', scope.record)"
            ></EditOutlined>
          </div>
        </template>
        <template v-if="scope.column.key === 'auth_on_sale'">
          <span class="text_overflow">{{ auth_on_sale_text(scope.record.auth_on_sale) || '--' }}</span>
          <a-tooltip v-if="!scope.record?.link_id">
            <template #title>获客链接异常，不可进行操作</template>
            <EditOutlined class="ml4px" :class="[!scope.record?.link_id ? 'edit_icon_disable' : 'edit_icon_default']" />
          </a-tooltip>
          <EditOutlined v-else class="c-#FE9D35 ml4px" @click="onShowDialog('updateLine', scope.record)"></EditOutlined>
        </template>

        <template v-if="scope.column.key === 'on_sale'">
          <!-- <span class="item-tag" :class="{ online: scope.record?.on_sale == 1, offline: scope.record?.on_sale == 2 }">
            {{ statusType(scope.record?.on_sale)?.text }}
          </span> -->
          <a-switch
            v-model:checked="scope.record.on_sale"
            :checkedValue="1"
            :unCheckedValue="2"
            @change="handleSwitch($event, scope.record)"
          />
        </template>
        <template v-if="scope.column.key === 'add_fans_num'">
          <a-button class="h-auto pa-0" type="link" @click="onShowDrawer('follower', scope.record)">{{
            scope.record.add_fans_num
          }}</a-button>
        </template>
        <template v-if="scope.column.key === 'welcome_txt'">
          <div class="flex-y-center">
            <span class="text_overflow inline-block" style="max-width: calc(100% - 16px)">{{
              scope.record.welcome_txt || '--'
            }}</span>
            <a-tooltip v-if="!scope.record?.link_id">
              <template #title>获客链接异常，不可进行操作</template>
              <EditOutlined
                class="ml4px"
                :class="[!scope.record?.link_id ? 'edit_icon_disable' : 'edit_icon_default']"
              />
            </a-tooltip>
            <EditOutlined
              v-else
              class="c-#FE9D35 ml4px"
              @click="onShowDialog('setWelcome', scope.record)"
            ></EditOutlined>
          </div>
        </template>
        <template v-if="scope.column.key === 'weight'">
          <span>{{ scope.record.weight || '--' }}</span>
          <a-tooltip v-if="!scope.record?.link_id">
            <template #title>获客链接异常，不可进行操作</template>
            <EditOutlined class="ml4px" :class="[!scope.record?.link_id ? 'edit_icon_disable' : 'edit_icon_default']" />
          </a-tooltip>
          <EditOutlined v-else class="c-#FE9D35 ml4px" @click="onShowDialog('weight', scope.record)"></EditOutlined>
        </template>
        <template v-if="scope.column.key === 'action'">
          <div class="flex items-center">
            <a-popconfirm title="确认要删除选中的客服成员吗？" placement="topRight" @confirm="delItem(scope.record)">
              <a-button class="h-auto pa-0" type="link">移除</a-button>
            </a-popconfirm>
            <template v-if="[2, 3].includes(scope.record.status) || !scope.record.link_id">
              <a-button class="h-auto pa-0" type="link" @click="getLinkInfo(scope.record)">生成获客链接</a-button>
            </template>
          </div>
        </template>
      </template>
      <template #summary>
        <a-table-summary fixed>
          <a-table-summary-row>
            <template v-for="(item, index) in state.sumColumns">
              <template v-if="['corp_id'].includes(item.dataIndex)">
                <a-table-summary-cell :index="index"
                  >总计：{{ state.tableConfig.pagination.total }}</a-table-summary-cell
                >
              </template>
              <template v-else-if="item.dataIndex === 'add_fans_num'">
                <a-table-summary-cell :index="index">
                  {{
                    state.tableConfig.dataSource &&
                    state.tableConfig.dataSource?.reduce((total, item: any) => total + item.add_fans_num, 0)
                  }}
                </a-table-summary-cell>
              </template>
              <template v-else>
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="state.dialog.visible"
      :maskClosable="false"
      :title="state.dialog.title"
      :width="state.dialog.width"
      destroyOnClose
      :footer="null"
    >
      <CheckOrder
        v-if="state.dialog.visible && state.dialog.type == 'checkID'"
        :invalidOrderNums="state.invalid_order_nums"
        :pageSize="state.initParams.page_size"
        @close="onClose"
        @submit="onSubmit"
        ref="checkOrder"
      />
      <updateLine
        v-if="state.dialog.visible && state.dialog.type == 'updateLine'"
        :item="state.dialog.item"
        :type="state.batchType"
        :ids="state.selectedRowKeys"
        @event="onEvent"
      />
      <editName
        v-if="state.dialog.visible && state.dialog.type == 'userName'"
        :item="state.dialog.item"
        @event="onEvent"
      />
      <setWelcome
        v-if="state.dialog.visible && state.dialog.type == 'setWelcome'"
        :item="state.dialog.item"
        @event="onEvent"
      />
      <weight
        :type="state.batchType"
        :ids="state.selectedRowKeys"
        v-if="state.dialog.visible && state.dialog.type == 'weight'"
        :item="state.dialog.item"
        @event="onEvent"
      />
      <updateAcqLinkName
        :type="state.batchType"
        :ids="state.selectedRowKeys"
        v-if="state.dialog.visible && ['batchUpdateAcqLinkName', 'updateAcqLinkName'].includes(state.dialog.type)"
        :item="state.dialog.item"
        @event="onEvent"
      />
    </a-modal>

    <a-drawer
      v-model:open="state.drawer.visible"
      :title="state.drawer.title"
      :width="state.drawer.width"
      placement="right"
      @close="state.drawer.visible = false"
    >
      <addMember
        v-if="state.drawer.visible && state.drawer.type == 'addMember'"
        :data="state.drawer.item"
        :groupId="props.data.id"
        @event="onEvent"
      />
      <followerNum
        v-if="state.drawer.visible && state.drawer.type == 'follower'"
        :data="state.drawer.item"
        :date="state.drawer.date"
        :group_id="data.id"
        @event="onEvent"
      />
      <batchCustomerTag
        v-if="state.drawer.visible && state.drawer.type == 'batchCustomerTag'"
        :item="state.drawer.item"
        :ids="state.selectedRowKeys"
        :corpids="state.corpids"
        :names="state.names"
        @event="onEvent"
      />
      <batchCustomerTag
        v-if="state.drawer.visible && state.drawer.type == 'setCustomerTag'"
        :item="state.drawer.item"
        :ids="[state.drawer.item.id]"
        :corpids="[state.drawer.item.corp_id]"
        :names="state.drawer.item?.tags?.split(',').filter((item: string) => item)"
        @event="onEvent"
      />
    </a-drawer>
    <screenShot
      v-if="screenShotData.isShow"
      :params="screenShotData.params"
      :dataSource="screenShotData.dataSource"
      @onEvent="screenShotEvent"
    />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, createVNode, nextTick, computed } from 'vue'
  import { WorkLinkApi, linkSetApi, delete_link, updateLinkInfo, batchGeneratedApi } from '../index.api'
  import dayjs from 'dayjs'
  import { workListApi } from '@/api/common'
  import datas from '../src/linkKefu'
  import { message, Modal } from 'ant-design-vue'
  import {
    QuestionCircleFilled,
    ExclamationCircleOutlined,
    EditOutlined,
    CopyOutlined,
    DownOutlined,
    UpOutlined,
    CameraOutlined
  } from '@ant-design/icons-vue'
  import { cloneDeep } from 'lodash-es'
  import followerNum from './followerNum.vue'
  import addMember from './addMember.vue'
  import setCustomerTag from './setCustomerTag.vue'
  import batchCustomerTag from './batchCustomerTag.vue'
  import CheckOrder from './CheckOrder.vue'
  import updateLine from './updateLine.vue'
  import editName from './editName.vue'
  import setWelcome from './setWelcome.vue'
  import weight from './addWeight.vue'
  import updateAcqLinkName from './updateAcqLinkName.vue'
  import screenShot from './screenShot.vue'
  import { copy, kfLinkExceptionEnum, ScreenshotTool } from '@/utils'
  import { useApp } from '@/hooks'
  const { isMobile } = useApp()
  const {
    goodsTableConfig: tableConfig,
    goodsSearchConfig: searchConfig,
    statusType,
    text_status,
    auth_on_sale_text,
    columns
  } = datas()
  const checkOrder = ref()
  const props = defineProps(['data', 'is_fail'])
  const emits = defineEmits(['event'])
  const batchSetCallback = (item: any) => {
    if (item.type === 'updateLine') {
      onShowDialog('updateLine', {}, 2)
    } else if (item.type === 'batchUpdateAcqLinkName') {
      onShowDialog('batchUpdateAcqLinkName', {}, 2)
    } else if (item.type === 'weight') {
      onShowDialog('weight', {}, 2)
    } else if (item.type === 'batchCustomerTag') {
      onShowDrawer('batchCustomerTag', { group_id: props.data?.id })
    } else if (item.type === 'batchGetLinkInfo') {
      batchGetLinkInfo()
    } else if (item.type === 'batchDelete') {
      batchDelete()
    }
  }
  const state = reactive({
    batchType: '',
    isNormal: false,
    selectedRowKeys: [],
    selectedRows: [],
    corpids: [],
    names: [],
    tableConfig,
    searchConfig,
    initParams: {
      page: 1,
      page_size: 20,
      is_fail: 0,
      user_id: '',
      wx_group_id: '',
      user_names: '',
      created_at: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')].join('_')
    },
    selectionRowsPlus: [],
    oldData: [],
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    },
    drawer: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: '',
      date: undefined
    },
    invalid_order_nums: [],
    sumColumns: cloneDeep([{ checked: true, dataIndex: 'index' }, ...columns]),
    dropOpen: false,

    batchSetData: {
      isShow: true,
      list: [
        {
          text: '上下线',
          type: 'updateLine'
        },
        {
          text: '修改获客链接名称',
          type: 'batchUpdateAcqLinkName'
        },
        {
          text: '编辑权重',
          type: 'weight'
        },
        {
          text: '修改标签',
          type: 'batchCustomerTag'
        },
        {
          text: '生成获客链接',
          type: 'batchGetLinkInfo'
        },
        {
          text: '移除',
          type: 'batchDelete'
        }
      ],
      callback: batchSetCallback,
      isSelected: false
    }
  })
  const screenShotData = ref({
    isShow: false,
    loading: false,
    params: {},
    dataSource: []
  })
  const sleep = (ms: number) => {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
  const screenShotHandler = async () => {
    message.loading('正在复制中')
    screenShotData.value.loading = true

    // screenShotData.value.params = state.initParams
    const params = {
      ...state.initParams,
      page: 1,
      page_size: 100000
    }
    const result = await WorkLinkApi(params)

    // 渲染到一个隐藏 DOM 里（v-show）
    screenShotData.value.dataSource = result.data.list
    screenShotData.value.isShow = true

    await nextTick()
    await sleep(200) // 让 DOM 稳定（html2canvas 需要）

    const tool = new ScreenshotTool('screenShotId')
    await tool.captureScreenshot()

    message.destroy()
    message.success('截屏成功，已复制到剪切板')

    screenShotData.value.loading = false
    screenShotData.value.isShow = false
  }
  const screenShotEvent = () => {
    try {
      screenShotData.value.isShow = false
      screenShotData.value.loading = false
      nextTick(() => {
        message.success('截屏成功，已复制到剪切板')
      })
    } finally {
      screenShotData.value.loading = false
    }
  }
  const getWechatList = async () => {
    try {
      const { data } = await workListApi({ page: 1, page_size: 1000, group_id: props.data?.id })
      state.searchConfig.data.forEach((v) => {
        if (v.field === 'corpid') {
          v.props.options =
            data.list?.map((item: any) => ({
              label: item.corp_name || '',
              value: item.corpid || ''
            })) || []
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getWechatList()
  const onClose = () => {
    state.initParams.user_id = ''
    state.dialog.visible = false
  }
  const getLinkInfo = async (item: any) => {
    try {
      await batchGeneratedApi({ ids: [item.id], group_id: props.data?.id })
      message.success('操作成功')
      initData()
    } catch (error) {}
  }
  const onSubmit = async (data: any, type: string) => {
    if (data.length == 0) {
      message.warning('请输入正确的客服数据')
      return
    }
    // if (data.length > state.initParams.page_size) {
    //   message.warning(`每次最多可查${state.initParams.page_size}条！`)
    //   return
    // }
    try {
      onClose()
      if (type == 'id') {
        state.initParams.user_id = (data && data.join(',')) || ''
      } else {
        state.initParams.user_names = (data && data.join(',')) || ''
      }

      initData()
      checkOrder.value.init()
    } catch (error) {}
  }
  //删除
  const delItem = async (item: any) => {
    try {
      await delete_link({ ids: [item.id], group_id: props.data?.id })
      message.success('操作成功')
      initData()
    } catch (error) {}
  }
  const batchGetLinkInfo = async () => {
    try {
      await batchGeneratedApi({ ids: state.selectedRowKeys, group_id: props.data?.id })
      message.success('操作成功')
      initData()
    } catch (error) {}
  }

  const batchDelete = () => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '确认要删除选中的客服成员吗？'),
        async onOk() {
          try {
            await delete_link({ ids: state.selectedRowKeys, group_id: props.data?.id })
            message.success('操作成功')
            initData()
          } catch (error) {
          } finally {
            Modal.destroyAll()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  const selectedChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRows = selectedRows
    state.selectedRowKeys = selectedRowKeys
    state.names = []
    state.corpids = []
    state.selectedRows.forEach((item: any) => {
      state.names = state.names.concat(item?.tags?.split(',').filter((item: string) => item))
      state.corpids.push(item.corp_id)
    })
    console.log(state.drawer.item)
    state.names = Array.from(new Set(state.names))
    state.batchSetData.isSelected = selectedRows.length > 0
  }
  const onSelect = (record: any, selected: any) => {
    if (selected) {
      state.selectionRowsPlus.push(record.id)
    } else {
      const indexToRemove = state.selectionRowsPlus.findIndex((id) => id === record.id)
      if (indexToRemove > -1) {
        state.selectionRowsPlus.splice(indexToRemove, 1)
      }
    }
  }

  const onSelectAll = (selected: any, selectedRows: any, changeRows: any) => {
    if (selected) {
      const newUserIds = changeRows.map((row) => row.id)
      // 使用 Set 来避免重复添加已存在的 user_id
      const existingIds = new Set(state.selectionRowsPlus)
      newUserIds.forEach((id) => {
        if (!existingIds.has(id)) {
          state.selectionRowsPlus.push(id)
        }
      })
    } else {
      const idsToRemove = changeRows.map((row) => row.id)
      state.selectionRowsPlus = state.selectionRowsPlus.filter((id) => !idsToRemove.includes(id))
    }
  }
  onMounted(async () => {
    state.initParams.wx_group_id = props.data.id
    if (props.is_fail) {
      state.initParams.status = props.is_fail
      state.searchConfig.data.forEach((v) => {
        if (v.field === 'status') {
          v.value = props.is_fail
        }
      })
    }
    await initData()
  })
  const initData = async () => {
    try {
      state.tableConfig.loading = true
      const result = await WorkLinkApi(state.initParams)
      if (result.code === 0) {
        state.tableConfig.dataSource = result.data.list?.map((item: any) => {
          return {
            ...item,
            fail_reason: item.fail_status === 1 ? '成员异常' : item.fail_reason || '',
            status: item.fail_status === 1 ? 4 : item.status // 将 fail_status 1 映射为状态 4
          }
        })
        state.tableConfig.pagination.total = result.data?.total || 0
        state.tableConfig.pagination.current = result.data?.page || 1
        state.selectedRowKeys = []
        getWechatList()
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const handleSwitch = async (val: number, item: { id: number }) => {
    item.on_sale = val == 1 ? 2 : 1
    try {
      let params = {
        type: 11,
        on_sale: val,
        group_id: props.data?.id,
        ids: [item.id]
      }
      await updateLinkInfo(params)
      message.success('操作成功')
      initData()
    } catch (error) {}
  }
  const changeValue = (data: any) => {
    if (data.type == 'batchSearchCustomer') {
      onShowDialog('checkID')
      return
    }

    console.log('changeValue---', data)
    state.initParams = { ...state.initParams, ...data.formData, page: 1 }
    state.initParams.created_at = data.formData.created_at
      ? data.formData.created_at[0] + '_' + data.formData.created_at[1]
      : undefined
    if (!data.status) {
      state.initParams.page = 1
      state.initParams.page_size = 20
      state.initParams.user_names = ''
      data.formData.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.initParams.created_at = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')].join('_')
    }
    initData()
  }
  const changePages = (data: any) => {
    state.initParams.page = data.current
    state.initParams.page_size = data.pageSize
    state.tableConfig.pagination.pageSize = data.pageSize
    initData()
  }

  const onShowDialog = (type: any, item?: any, val?: any) => {
    if (preJudgment(type)) return
    state.batchType = val ? val : 1
    if (!val && item && !item.link_id) {
      message.warning('获客链接异常，不可进行操作')
      return
    }
    state.dialog.item = {
      group_id: props.data?.id,
      ...item
    }
    state.dialog.visible = true
    state.dialog.type = type
    state.dialog.width = '500px'
    switch (type) {
      case 'follower':
        state.dialog.width = '1000px'
        state.dialog.title = '加粉数'
        break
      case 'checkID':
        state.dialog.title = `批量查询客服`
        break
      case 'updateLine':
        state.dialog.width = '700px'
        state.dialog.title = `更新客服上下线`
        break
      case 'userName':
        state.dialog.width = '550px'
        state.dialog.title = `编辑客服名称`
        break
      case 'setWelcome':
        state.dialog.width = '700px'
        state.dialog.title = `设置欢迎语`
        break
      case 'weight':
        state.dialog.width = '500px'
        state.dialog.title = '编辑权重'
        break
      case 'updateAcqLinkName':
        state.dialog.title = '设置获客链接名称'
        break
      case 'batchUpdateAcqLinkName':
        state.dialog.title = '批量设置获客链接名称'
        break
    }
  }
  const preJudgment = (type: any) => {
    let isWarning = false
    if (['batchCustomerTag', 'updateLine', 'batchUpdateAcqLinkName', 'weight'].includes(type)) {
      state.selectedRows.forEach((item) => {
        if (!item.link_id) {
          isWarning = true
        }
      })
    }
    isWarning && message.warning('获客链接异常，不可进行操作')
    return isWarning
  }
  const onShowDrawer = (type: any, item: any) => {
    state.dropOpen = false
    if (preJudgment(type)) return
    if ((type == 'setCustomerTag' && item && !item.link_id) || (item?.status == 2 && !item?.link_id)) {
      message.warning('获客链接异常，不可进行操作')
      return
    }
    state.drawer.item = item
    state.drawer.visible = true
    state.drawer.type = type
    state.drawer.width = isMobile.value ? '100%' : '500px'
    switch (type) {
      case 'addMember':
        state.drawer.width = isMobile.value ? '100%' : '90%'
        state.drawer.title = `新增成员`
        break
      case 'follower':
        state.drawer.width = isMobile.value ? '100%' : '90%'
        state.drawer.title = `加粉数`
        state.drawer.date = state.initParams.created_at
        break
      case 'batchCustomerTag':
        state.drawer.width = isMobile.value ? '100%' : '51%'
        state.drawer.title = `批量设置客户标签`
        break
      case 'setCustomerTag':
        state.drawer.item.group_id = props.data.id
        state.drawer.width = isMobile.value ? '100%' : '51%'
        state.drawer.title = `设置客户标签`
        break
    }
  }

  // 关闭弹框
  const onEvent = (val?: any) => {
    state.dialog.visible = false
    state.drawer.visible = false
    if (val.cmd == 'close') {
      getWechatList()
    } else {
      initData()
    }
  }
</script>

<style scoped lang="scss">
  .batch_btn {
    z-index: 999;
    position: relative;
    .dot_list {
      width: 132px;
      display: none;
      margin-top: 10px;
      background: #fff;
      position: absolute;
      top: 24px;
      left: 0;
      z-index: 99;
      font-size: 13px;
      padding: 10px;
      border-radius: 4px;
      box-shadow:
        0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.2s;
      .dot_item {
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(5, 5, 5, 0.06);
        margin-bottom: 10px;
        color: #141414;
        cursor: pointer;
      }
    }
    .dot_list .dot_item:last-child {
      border: none;
      padding: 0;
      margin-bottom: 0;
    }
  }
  .batch_btn:hover .dot_list,
  .dot_list:hover {
    display: block; /* hover 时显示 .dot_list */
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  .goods-warp {
    display: flex;
    .goods-warp-img {
      width: 40px;
      height: 40px;
      border-radius: 2px;
      flex: none;
      margin-right: 8px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .goods-warp-content {
      flex: 1;
      padding: 0 10px;
    }
    .text-overflow-row1 {
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 1;
    }
  }
  .light {
    display: none;
  }
  .ant-btn-default:not(:disabled):hover {
    .light {
      display: block;
    }
    .default {
      display: none;
    }
  }
  .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: #fffaf4;
    color: var(--primary-color);
  }
  .edit_icon_disable {
    cursor: not-allowed;
    color: #c9c9c9;
  }
  .edit_icon_default {
    color: #fe9d35;
  }
</style>
