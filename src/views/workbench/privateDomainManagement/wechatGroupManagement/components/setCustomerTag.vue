<template>
  <div>
    <div class="flex flex-justify-end flex-items-center mt-16px mb-16px">
      <a-button type="primary" @click="addTag">新增</a-button>
    </div>
    <a-form :model="data.form" ref="ruleForm" :labelCol="{ style: 'width:70px;' }">
      <a-form-item label="客户标签" v-if="data.tagList.length > 0">
        <div class="flex mb-10px" v-for="(item, index) in data.tagList" :key="index">
          <div class="item-link text_overflow">
            <a-tooltip>
              <template #title>{{ item.name }}</template>
              {{ item.name }}
            </a-tooltip>
          </div>
          <a-button type="link" danger class="c-#FE4D4F" @click="delLink(index)">删除</a-button>
        </div>
      </a-form-item>
    </a-form>
    <div class="footer" style="text-align: right">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="data.loading">确定</a-button>
    </div>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      destroy-on-close
      :footer="null"
    >
      <setNameTag @onEvent="onEvent" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue'
  import { get_tag_list, save_tag } from '../index.api'
  import { message } from 'ant-design-vue'
  import setNameTag from './setNameTag.vue'
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      name: '',
      addFanModel: 1,
      status: 1,
      remark: ''
    },
    tagForm: {
      link: ''
    },
    tagList: [],
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    }
  })
  const onEvent = ({ cmd, data: r }: any) => {
    data.dialog.visible = false
    if (cmd === 'submit') {
      okModal(r)
    }
  }
  const addTag = () => {
    data.dialog.visible = true
    data.dialog.title = '新增客户标签'
    data.dialog.width = '550px'
    data.tagForm.link = ''
  }
  const delLink = (index: number) => {
    data.tagList.splice(index, 1)
  }
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  const submitForm = async () => {
    try {
      const params = {
        group_id: props.item.group_id,
        id: props.item.id,
        tag_list: data.tagList
      }
      await save_tag(params)
      message.success('保存成功')
      emit('event', { cmd: 'edit' })
    } catch (error) {}
  }
  const okModal = async (r: any) => {
    console.log('props.item.group_id', props.item.group_id, r)

    try {
      if (r) {
        data.tagList.push({
          name: r
        })
      } else {
        return
      }
      const params = {
        group_id: props.item.group_id,
        id: props.item.id,
        tag_list: data.tagList
      }
      await save_tag(params)
      data.dialog.visible = false
      message.success('保存成功')
      getTagList()
    } catch (error) {
      console.log('=====', error)
    }
  }
  const getTagList = async () => {
    const res = await get_tag_list({ id: props.item.id })
    data.tagList = res.data || []
  }
  getTagList()
</script>

<style scoped lang="scss">
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
  }
</style>
