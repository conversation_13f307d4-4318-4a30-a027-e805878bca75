<template>
  <div id="screenShotId" class="pos-fixed z--10 w-2200px bg-#fff">
    <!-- <myWaterMark :items="watermarkItems" :width="400" :height="240" :rotate="-15"> -->
    <TableZebraCrossing :data="state.screenshotTableConfig">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'corp_id'">
          <div class="c-#313233">{{ scope.record.corp_name }}</div>

          <div class="mt-6px c-#7A869F font-size-12px w-270px" style="word-break: break-all">
            ID：{{ scope.record.corp_id }}
          </div>
        </template>
        <template v-if="scope.column.key === 'link_id'">
          <div>{{ scope.record.link_name || '-' }}</div>
          <div style="word-break: break-all">链接： {{ scope.record.link || '-' }}</div>
        </template>
        <template v-if="scope.column.key === 'user_name'">
          <div>{{ scope.record.user_name || '--' }}</div>
          <div class="mt-6px c-#7A869F font-size-12px w-270px" style="word-break: break-all">
            ID：{{ scope.record.wx_user_id || '--' }}
          </div>
          <div class="flex-y-center mt-4px text_overflow_row1" v-if="[1].includes(scope.record.fail_status)">
            <ExclamationCircleOutlined class="c-red font-size-12px text_overflow_row1" />
            <div class="c-red ml-4px font-size-12px h-12px line-height-12px">成员异常</div>
          </div>
        </template>
        <template v-if="scope.column.key === 'status'">
          <div class="flex-y-center">
            <div
              :class="kfLinkExceptionEnum[scope.record.status].className"
              v-if="kfLinkExceptionEnum[scope.record.status]"
            >
              {{ kfLinkExceptionEnum[scope.record.status].text }}
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'tag_list'">
          <div class="c-#313233" style="word-break: break-all">{{ scope.record.tags || '--' }}</div>
        </template>
        <template v-if="scope.column.key === 'auth_on_sale'">
          <span class="text_overflow">{{ auth_on_sale_text(scope.record.auth_on_sale) || '--' }}</span>
        </template>

        <template v-if="scope.column.key === 'on_sale'">
          <a-switch class="w-43px" :unCheckedValue="2" :checkedValue="1" :checked="scope.record?.on_sale" />
        </template>
        <template v-if="scope.column.key === 'add_fans_num'">
          <div>{{ scope.record.add_fans_num }}</div>
        </template>
        <template v-if="scope.column.key === 'welcome_txt'">
          <div class="inline-block">{{ scope.record.welcome_txt || '--' }}</div>
        </template>
        <template v-if="scope.column.key === 'weight'">
          <span>{{ scope.record.weight || '--' }}</span>
        </template>
      </template>
      <template #summary>
        <a-table-summary fixed>
          <a-table-summary-row>
            <template v-for="(item, index) in state.screenSumColumns">
              <template v-if="['corp_id'].includes(item.dataIndex)">
                <a-table-summary-cell :index="index"
                  >总计：{{ state.screenshotTableConfig.dataSource?.length || 0 }}</a-table-summary-cell
                >
              </template>
              <template v-else-if="item.dataIndex === 'add_fans_num'">
                <a-table-summary-cell :index="index">
                  {{
                    state.screenshotTableConfig.dataSource &&
                    state.screenshotTableConfig.dataSource?.reduce((total, item: any) => total + item.add_fans_num, 0)
                  }}
                </a-table-summary-cell>
              </template>
              <template v-else>
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </TableZebraCrossing>
    <!-- </myWaterMark> -->
  </div>
</template>
<script setup lang="ts">
  import { WorkLinkApi } from '../index.api'
  import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
  import { cloneDeep } from 'lodash-es'
  import datas from '../src/linkKefu'
  import { useApp } from '@/hooks'
  import myWaterMark from '@/layout/template1/components/waterMark.vue'
  import waterLogo from '@/assets/images/water_logo2.png'
  import { ScreenshotTool, kfLinkExceptionEnum } from '@/utils'
  import { message } from 'ant-design-vue'
  const { screenshotTableConfig, screenColumns, auth_on_sale_text } = datas()
  const { useInfo } = useApp()
  const props = defineProps(['params', 'dataSource'])
  const emits = defineEmits(['onEvent'])
  const watermarkItems = [
    {
      type: 'text',
      content: useInfo.value?.realname + ' ' + (useInfo.value?.phone || '').substr(-4),
      font: 'bold 19px Microsoft YaHei',
      color: 'rgba(0, 0, 0, 0.07)',
      x: 100,
      y: 30
    },
    {
      type: 'image',
      src: waterLogo, // 替换为实际图片URL
      x: 30,
      y: 30,
      width: 105,
      height: 24,
      opacity: 1
    }
  ]
  const state = reactive({
    screenshotTableConfig,
    screenColumns,
    screenSumColumns: cloneDeep([...screenColumns]),
    screen: null as any
  })
  const sleep = (ms: number) => {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
  onMounted(async () => {
    try {
      state.screenshotTableConfig.dataSource = props.dataSource
      // let params = {
      //   ...props.params,
      //   page: 1,
      //   page_size: 100000
      // }
      // const result: any = await WorkLinkApi(params)
      // if (result.code === 0) {
      //   state.screenshotTableConfig.dataSource = result.data.list?.map((item: any) => {
      //     return {
      //       ...item,
      //       fail_reason: item.fail_status === 1 ? '成员异常' : item.fail_reason || '',
      //       status: item.fail_status === 1 ? 4 : item.status // 将 fail_status 1 映射为状态 4
      //     }
      //   })
      //   nextTick(async () => {
      //     state.screen = new ScreenshotTool('screenShotId')
      //     await state.screen.captureScreenshot()
      //     await sleep(1000)
      //     emits('onEvent')
      //   })
      // }
    } catch (err) {
      console.log(err)
    }
  })
  onUnmounted(() => {
    if (state.screen) {
      state.screen = null
    }
  })
</script>
<style lang="scss" scoped>
  :deep(.ant-switch:not(.ant-switch-checked)) {
    background-color: rgba(0, 0, 0, 0.25) !important;
  }
</style>
