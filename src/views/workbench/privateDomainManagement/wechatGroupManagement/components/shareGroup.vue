<template>
  <div>
    <a-form :model="item" ref="ruleForm" :rules="rules" :labelCol="{ style: 'width:150px;' }">
      <a-form-item label="分享链接" name="url_code">
        <a-input
          disabled
          v-model:value="completeUrlCode"
          @keydown.space.prevent
          placeholder="请输入分享链接"
        />
      </a-form-item>
      <a-form-item label="密码" name="verify_code">
        <div class="flex flex-items-center">
          <a-input
            v-model:value="item.verify_code"
            :maxlength="6"
            @keydown.space.prevent
            placeholder="请生成密码"
          />
          <a-button type="link" size="small" class="pa-0! ml-10px" @click="regenerate">重新生成密码</a-button>
        </div>
        <div class="font-size-12px c-#999999 mt-6px">点击【重新生成密码】之后，原提取密码将失效;</div>
      </a-form-item>
      <a-form-item label="是否支持编辑上下线">
        <a-switch :loading="loading" :checkedValue="1" :unCheckedValue="2" v-model:checked="item.change_onsale" @change="handleChange" />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm">复制全部信息</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watch, computed } from 'vue'
  import { wechat_group_share, update_filed_value } from '../index.api'
  import { message } from 'ant-design-vue'
  import datas from '../src/data'
  const { data } = datas()
  const rules = ref({
    url_code: [
      { required: true, message: '请生成分享链接', trigger: 'blur' },
    ],
    verify_code: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9]+$/, message: '只能包含数字或大小写字母', trigger: 'blur' },
      { min: 6, max: 6, message: '密码需要是6位', trigger: 'blur' },
    ]
  })
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event', 'close'])
  const close = () => {
    emit('close')
  }
  const submitForm = () => {
    ruleForm.value.validate().then(async () => {
      let text = `他人向你分享了「${props.item.name}」，点击链接即可访问。链接：${completeUrlCode.value} 密码：${props.item.verify_code}`
      try {
        await update_filed_value({ 
          id: props.item.id,
          field: 'verify_code',
          value: props.item.verify_code
        })
        await navigator.clipboard.writeText(text);
        message.success('复制成功');
      } catch (error) {
        message.error('复制失败');
      }
    })
  }
  const regenerate = async () => {
    try {
      const res = await wechat_group_share({ id: props.item.id })
      message.success('生成密码成功')
      props.item.verify_code = res.data.verify_code
      props.item.url_code = res.data.url_code
    } catch {}
  }
  const loading = ref(false)
  const handleChange = async (val) => {
    loading.value = true
    try {
      const res = await update_filed_value({ id: props.item.id, field: 'change_onsale', value: val })
      props.item.change_onsale = val
    } catch {}
    loading.value = false
  }
  const completeUrlCode = computed(() => {
    return `${window.location.origin}/#/ShareLinkOpen?url_code=${props.item.url_code}`
  })
  watch(() => props.item.id, async (newVal, oldVal) => {
    if (!props.item.url_code || !props.item.verify_code) {
      await regenerate()
    }
  }, { immediate: true })
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
