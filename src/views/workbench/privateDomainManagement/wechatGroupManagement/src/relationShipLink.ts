import { reactive } from 'vue'
import { getAddLink } from '../index.api'
export default function datas(record: any) {
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入投放链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 8
        }
      },
      {
        type: 'input.text',
        field: 'ids',
        value: undefined,
        props: {
          placeholder: '请输入投放链接ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 8
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 'max-content'
    },
    dataSource: [],
    columns: [
      {
        title: '投放链接名称',
        dataIndex: 'name',
        key: 'name',
        slot: true,
        width: 180
      },
      {
        title: '投放链接ID',
        dataIndex: 'id',
        key: 'id',
        slot: true,
        width: 200
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      current: 1,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const state = reactive({
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    }
  })

  // 获取列表
  const getList = async (ids?: any) => {
    console.log('getList', record)
    try {
      state.tableConfigOptions.loading = true
      let res = await getAddLink({ ...state.params, ids: ids || record?.ids })
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
      if (res.data?.list.length > 0) {
        state.tableConfigOptions.scroll.y = 350
      } else {
        delete state.tableConfigOptions.scroll.y
      }
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  getList()
  const pageChange = (pagination) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    state.params = {
      ...state.params,
      ...v.formData
    }
    state.params.page = 1
    getList(v.formData?.ids)
  }
  return {
    pageChange,
    searchForm,
    searchConfig,
    state
  }
}
