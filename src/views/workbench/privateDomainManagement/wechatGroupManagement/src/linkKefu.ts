import dayjs from 'dayjs'
export default function datas() {
  const statusType = (val: string | number) => {
    let status = {
      1: {
        text: '上线'
      },
      2: {
        text: '下线'
      }
    }
    return (status as any)[val]
  }
  //检测状态
  const text_status = (val: string | number) => {
    let status = {
      1: '正常',
      2: '异常'
    }
    return (status as any)[val]
  }
  const auth_on_sale_text = (val: string | number) => {
    let status = {
      1: '不设置',
      2: '按时间段',
      3: '按成功添加企业微信数'
    }
    return (status as any)[val]
  }
  const columns = [
    {
      title: '所属企微/ID',
      dataIndex: 'corp_id',
      key: 'corp_id',
      width: 240
    },
    {
      title: '客服名称/ID',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 200
    },
    {
      title: '标签',
      key: 'tag_list',
      dataIndex: 'tag_list',
      width: 120
    },
    {
      title: '获客链接',
      key: 'link_id',
      dataIndex: 'link_id',
      width: 260
    },
    {
      title: '获客链接异常监测',
      key: 'status',
      dataIndex: 'status',
      width: 140
    },
    {
      title: '上下线',
      key: 'on_sale',
      dataIndex: 'on_sale',
      width: 100
    },
    {
      title: '加粉数',
      key: 'add_fans_num',
      dataIndex: 'add_fans_num',
      width: 100
    },
    {
      title: '加粉权重',
      key: 'weight',
      dataIndex: 'weight',
      width: 100
    },
    {
      title: '自动上下线规则',
      key: 'auth_on_sale',
      dataIndex: 'auth_on_sale',
      width: 180
    },
    {
      title: '欢迎语',
      key: 'welcome_txt',
      dataIndex: 'welcome_txt',
      width: 120
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 150,
      fixed: 'right'
    }
  ]
  const screenColumns = [
    {
      title: '所属企微/ID',
      dataIndex: 'corp_id',
      key: 'corp_id',
      width: 270
    },
    {
      title: '客服名称/ID',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 270
    },
    {
      title: '标签',
      key: 'tag_list',
      dataIndex: 'tag_list',
      width: 300
    },
    {
      title: '获客链接',
      key: 'link_id',
      dataIndex: 'link_id',
      width: 420
    },
    {
      title: '获客链接异常监测',
      key: 'status',
      dataIndex: 'status',
      width: 150
    },
    {
      title: '上下线',
      key: 'on_sale',
      dataIndex: 'on_sale',
      width: 100
    },
    {
      title: '加粉数',
      key: 'add_fans_num',
      dataIndex: 'add_fans_num',
      width: 100
    },
    {
      title: '加粉权重',
      key: 'weight',
      dataIndex: 'weight',
      width: 100
    },
    {
      title: '自动上下线规则',
      key: 'auth_on_sale',
      dataIndex: 'auth_on_sale',
      width: 160
    },
    {
      title: '欢迎语',
      key: 'welcome_txt',
      dataIndex: 'welcome_txt'
      // width: 300
    }
  ]
  // 选择弹窗里的信息
  const goodsTableConfig = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    defaultExpandedRowKeys: [],
    dataSource: [],
    rowSelection: {},
    scroll: { scrollToFirstRowOnChange: false, x: 1000 },
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 20,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  }
  // 截图
  const screenshotTableConfig = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    dataSource: [],
    scroll: { scrollToFirstRowOnChange: false, x: 2200 },
    columns: screenColumns,
    pagination: false
  }
  const goodsSearchConfig = {
    data: [
      {
        field: 'corpid',
        type: 'select',
        value: undefined,
        span: 6,
        props: {
          options: [],
          placeholder: '请选择企微'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'on_sale',
        props: {
          placeholder: '请选择状态',
          options: [
            { value: 1, label: '上线' },
            { value: 2, label: '下线' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'status',
        props: {
          placeholder: '请选择获客链接状态',
          options: [
            { value: 1, label: '正常' },
            { value: 2, label: '异常' },
            { value: 3, label: '链接异常' },
            { value: 4, label: '成员异常' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'is_user_active',
        props: {
          placeholder: '请选择互通账号状态',
          options: [
            { value: 1, label: '已激活' },
            { value: 2, label: '未激活' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'user_name',
        value: undefined,
        props: {
          placeholder: '请输入客服名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'user_id',
        value: undefined,
        props: {
          placeholder: '请输入客服ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'link_name',
        value: undefined,
        props: {
          placeholder: '请输入获客链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'tag',
        value: undefined,
        props: {
          placeholder: '请输入标签'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'created_at',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 8,
      //   md: 8,
      //   lg: 8,
      //   xl: 6,
      //   xxl: 6
      // }
    }
  }
  const followerTableConfig = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    dataSource: [],
    scroll: { y: '76vh', scrollToFirstRowOnChange: false },
    columns: [
      {
        title: '客户名称',
        dataIndex: 'customer_name',
        key: 'customer_name',
        width: 180
      },
      {
        title: '标签',
        dataIndex: 'customer_tag',
        key: 'customer_tag',
        width: 110
      },
      {
        title: '回话状态',
        key: 'is_chat',
        dataIndex: 'is_chat',
        width: 130
      },
      {
        title: '跟进企微',
        key: 'wx_user_id',
        dataIndex: 'wx_user_id',
        width: 120
      },
      {
        title: '获客链接',
        key: 'wx_link_id',
        dataIndex: 'wx_link_id',
        width: 120
      },
      {
        title: '企微名称',
        key: 'corp_name',
        dataIndex: 'corp_name',
        width: 120
      },
      {
        title: '申请添加时间',
        dataIndex: 'apply_time',
        key: 'apply_time',
        width: 140
      },
      {
        title: '点击时间',
        key: 'created_at',
        dataIndex: 'created_at',
        width: 140
      },
      {
        title: '回传时间',
        key: 'callback_at',
        dataIndex: 'callback_at',
        width: 140
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 20,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  }
  const followerSearchConfig = {
    data: [
      {
        type: 'joint_date',
        field: 'apply_map',
        value: undefined,
        props: {
          options: [
            {
              value: 'apply_map',
              label: '申请时间'
            },
            {
              value: 'add_fans_time',
              label: '加粉时间'
            },
            {
              value: 'callback_time_map',
              label: '回传时间'
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 8
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 8,
        md: 8,
        lg: 8,
        xl: 6,
        xxl: 4
      }
    }
  }
  return {
    statusType,
    text_status,
    goodsSearchConfig,
    goodsTableConfig,
    followerTableConfig,
    followerSearchConfig,
    auth_on_sale_text,
    columns,
    screenshotTableConfig,
    screenColumns
  }
}
