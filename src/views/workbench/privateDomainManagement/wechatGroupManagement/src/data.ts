import { reactive, createVNode, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { wechatGroupListApi, delGroupApi, updateGroupApi, setRoleInfo, wechat_group_share } from '../index.api'
import { useAuth } from '@/hooks/use-auth'
import { getShowTitle } from '@/utils'

import dayjs from 'dayjs'
import { useApp } from '@/hooks'
export default function datas(type?: boolean) {
  const { isMobile } = useApp()
  const searchFormDataRef = ref()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入分组名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'ids',
        value: undefined,
        props: {
          placeholder: '请输入分组ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'status',
        props: {
          placeholder: '请选择状态',
          options: [
            { value: 1, label: '启用' },
            { value: 2, label: '关闭' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'is_fail',
        props: {
          placeholder: '请选择异常状态',
          options: [
            { value: 1, label: '正常' },
            { value: 2, label: '异常' },
            { value: 3, label: '链接异常' },
            { value: 4, label: '成员异常' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'updatedAt',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        range: 'NearlyThirty',
        props: {
          placeholder: ['加粉开始时间', '加粉结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: type ? 10 : 8,
          lg: type ? 10 : 8,
          xl: type ? 6 : 4,
          xxl: type ? 6 : 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })
  const searchLink = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'order_source',
        props: {
          placeholder: '请选择状态',
          options: [
            { value: 1, label: '自然流量' },
            { value: 2, label: '广告流量' },
            { value: 3, label: '回流流量' },
            { value: 9, label: '异常流量' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })
  const headerCell = ['status', 'add_fans_rate', 'add_fans_num_ten_min']
  const tabAllCol = [
    {
      title: '客服分组',
      dataIndex: 'name',
      key: 'name',
      width: '240px'
    },
    {
      title: '关联企微',
      dataIndex: 'corp_name',
      key: 'corp_name',
      width: '150px'
    },
    {
      title: '关联客服',
      dataIndex: 'linkNum',
      key: 'linkNum',
      width: '100px'
    },
    {
      title: '产品库',
      dataIndex: 'product_name',
      key: 'product_name',
      width: '150px'
    },

    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      text: '状态是关闭的客服分组新建投放链接时不能选择。已创建投放链接的客服分组状态关闭后依旧能通过落地页正常跳转，跟能否跳转无关',
      width: '100px'
    },
    {
      title: '关联投放链接数量',
      dataIndex: 'ad_link_num',
      key: 'ad_link_num',
      width: '135px'
    },
    {
      title: '加粉数',
      dataIndex: 'add_fans_num',
      key: 'add_fans_num',
      width: '100px'
    },
    {
      title: '开口数',
      dataIndex: 'chat_num',
      key: 'chat_num',
      width: '100px'
    },
    {
      title: '开口率',
      dataIndex: 'chat_rate',
      key: 'chat_rate',
      width: '100px'
    },

    {
      title: '流速',
      dataIndex: 'add_fans_num_ten_min',
      key: 'add_fans_num_ten_min',
      text: '流速：当前时间往前推10分钟的加粉数据',
      width: '120px'
    },
    {
      title: '预加粉',
      dataIndex: 'pre_fans_num',
      key: 'pre_fans_num',
      width: '100px'
    },
    {
      title: '进度',
      dataIndex: 'add_fans_rate',
      key: 'add_fans_rate',
      text: '已完成的数量/预加粉数',
      width: '100px'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 180
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: '180px'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '180px'
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '140px',
      fixed: 'right'
    }
  ]
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1800,
      y: type ? window.innerHeight - 450 : 'auto'
    },
    dataSource: [],
    columns: tabAllCol,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }
  const linkForm = reactive({
    url_code: '',
    verify_code: '',
    change_onsale: 2
  })

  const { filedsNames } = useAuth()

  if (filedsNames?.length) {
    tableConfigOptions.columns = tableConfigOptions.columns.filter((item: any) => {
      if (filedsNames?.includes(item?.key)) {
        return item
      }
    })
  } else {
    tableConfigOptions.columns = []
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20,
      begin_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD')
    },
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    },
    drawer: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: '',
      is_fail: undefined
    },
    form: {
      model: '',
      groupName: '',
      remark: '',
      status: ''
    }
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#E63030',
        text: '关闭'
      },
      1: {
        color: '#60A13B',
        text: '启用'
      }
    }
    return (status as any)[val]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await wechatGroupListApi(data.params)
      data.tableConfigOptions.dataSource = res.data?.WechatGroups || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination: any) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v: any) => {
    data.params = {
      ...data.params,
      ...v.formData,
      begin_time: (v.formData?.updatedAt && v.formData?.updatedAt[0]) || undefined,
      end_time: (v.formData?.updatedAt && v.formData?.updatedAt[1]) || undefined
    }
    delete data.params.updatedAt
    if (!v.status) {
      data.params.begin_time = dayjs().format('YYYY-MM-DD')
      data.params.end_time = dayjs().format('YYYY-MM-DD')
      searchFormDataRef.value.formData.updatedAt = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type: any, item: any) => {
    data.dialog.item = item
    data.dialog.visible = true
    data.dialog.type = type
    data.dialog.width = '500px'
    switch (type) {
      case 'link':
        data.dialog.width = '980px'
        data.dialog.title = '获客链接数'
        break
      case 'group':
        if (!data.dialog.item?.id) {
          data.dialog.title = '新增分组'
        } else {
          data.dialog.title = '编辑分组'
        }
        break
      case 'linkKefu':
        data.dialog.width = '980px'
        data.dialog.title = `客服组【${item.name}】关联客服`
        break
      case 'add_fans_num_ten_min':
        data.dialog.width = '980px'
        data.dialog.title = `加粉数`
        break
      case 'pre_fans_num':
        data.dialog.title = '预加粉'
        data.dialog.width = '600px'
        break
      case 'relation_ship_tem':
        data.dialog.width = '900px'
        data.dialog.title = '关联投放链接'
        break
    }
  }

  const onShowDrawer = (type: any, item: any) => {
    data.drawer.item = item
    data.drawer.visible = true
    data.drawer.type = type
    data.drawer.width = isMobile.value ? '100%' : '500px'
    switch (type) {
      case 'linkKefu':
        data.drawer.width = isMobile.value ? '100%' : '90%'
        let _title = getShowTitle(item.name)
        data.drawer.title = `客服组【${item.name}】关联客服`
        data.drawer.show_title = `客服组【${_title}】关联客服`
        data.drawer.is_fail = data.params.is_fail

        console.log(data.drawer.show_title, '_title')
        break
      case 'pre_fans_num':
        data.dialog.title = '提示'
        break
    }
  }

  // 关闭弹框
  const onEvent = (val: any) => {
    if (val.cmd == 'close') {
      data.dialog.visible = false
    } else {
      data.dialog.visible = false
      console.log('edi,--==33=t')
      getList()
    }
  }

  const onEventDrawer = (val: any) => {
    if (val.cmd == 'close') {
      data.drawer.visible = false
    } else {
      data.drawer.visible = false
      console.log('edi,--==33=t')
      getList()
    }
  }

  //表单规则
  const rules = { name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }] }
  //删除分组
  const deleteGroup = async (row: any) => {
    try {
      if (!row.linkNum) {
        Modal.confirm({
          title: '提示',
          content: createVNode('div', {}, '是否删除该分组？'),
          async onOk() {
            try {
              await delGroupApi({ id: row.id })
              getList()
            } catch (error) {
            } finally {
              Modal.destroyAll()
            }
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      } else {
        message.warning('当前分组有关联的获客链接，请先删除获客链接再进行删除客服分组')
      }
    } catch (error) {}
  }
  // 分享
  const shareBtn = async (row: any) => {
    data.dialog.visible = true
    data.dialog.type = 'share'
    data.dialog.width = '700px'
    data.dialog.title = '分享分组'
    data.dialog.item = row
  }
  // 重新获取
  const getLink = async () => {
    try {
      let res = await wechat_group_share({ id: linkForm.id })
      linkForm.url_code = res.data.url_code
      linkForm.verify_code = res.data.verify_code
    } catch (error) {}
  }
  //修改状态
  const changeStatus = async (val: any, item: any, rowSelection: any) => {
    item.status = val == 1 ? 2 : 1
    try {
      let params = {
        id: item.id,
        name: item.name,
        remark: item.remark,
        addFanModel: item.addFanModel,
        status: val,
        product_id: item?.product_id
      }
      await updateGroupApi(params)

      message.success('操作成功')
      getList()
      formatCheckData(val, item, rowSelection)
    } catch (error) {}
  }
  const formatCheckData = (e: any, data: any, rowSelection: any) => {
    if (e === 2) {
      rowSelection.value.selectedRowKeys = rowSelection.value.selectedRowKeys.filter((key) => {
        return key !== data.id
      })
    }
  }
  return {
    tableConfigOptions,
    tabAllCol,
    pageChange,
    searchForm,
    onShowDialog,
    onShowDrawer,
    deleteGroup,
    searchConfig,
    data,
    statusType,
    getList,
    onEvent,
    onEventDrawer,
    headerCell,
    searchLink,
    rules,
    changeStatus,
    shareBtn,
    linkForm,
    searchFormDataRef
  }
}
