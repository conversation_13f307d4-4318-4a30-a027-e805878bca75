export default function datas() {
  const statusType = (val: string | number) => {
    let status = {
      1: {
        text: '上线'
      },
      2: {
        text: '下线'
      }
    }
    return (status as any)[val]
  }
  //异常状态
  const fail_status = (val: string | number) => {
    let status = {
      1: '存在异常， 请尽快处理',
      2: '全部异常， 请尽快处理'
    }
    return (status as any)[val]
  }
  // 选择弹窗里的信息
  const goodsTableConfig = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    defaultExpandedRowKeys: [],
    dataSource: [],
    rowSelection: {},
    scroll: { y: 400, scrollToFirstRowOnChange: false },
    columns: [
      {
        title: '获客链接名称/ID',
        dataIndex: 'link_id',
        key: 'link_id',
        width: 300
      },
      {
        title: '上下线',
        key: 'on_sale',
        dataIndex: 'on_sale',
        width: 100,
        tips: '上线：可通过落地页正常添加。下线：不可通过落地页添加'
      },
      {
        title: '当前分组',
        key: 'group_name',
        dataIndex: 'group_name',
        width: 180
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 20,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  }

  const goodsSearchConfig = {
    data: [
      {
        type: 'input.text',
        field: 'link_name',
        value: undefined,
        props: {
          placeholder: '请输入链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'link_id',
        value: undefined,
        props: {
          placeholder: '请输入链接ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'on_sale',
        props: {
          placeholder: '请选择状态',
          options: [
            { value: 1, label: '上线' },
            { value: 2, label: '下线' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 8,
        md: 8,
        lg: 8,
        xl: 4,
        xxl: 4
      }
    }
  }

  return { statusType, fail_status, goodsSearchConfig, goodsTableConfig }
}
