<template>
  <div>
    <!-- <div class="statistics-box mb-16px">
      <div class="flex-y-center justify-between">
        <div class="text-16px fw-600 lh-22px c-#313232">客服分组</div>
        <a-button
          type="primary"
          v-auth="['wechatGroupManAdd']"
          :disabled="pointData.balance <= 0"
          @click="onShowDialog('group', data.form)"
          >新增分组</a-button
        >
      </div>
      <a-spin :spinning="topLoading">
        <div v-if="filedsNames?.length || isSuperRole" class="list flex-y-center">
          <div class="item" v-for="(item, index) in topLastInfo" :key="index">
            <div class="desc">{{ item.label }}</div>
            <div class="value">
              <span>{{ item.num }}</span>
              <span class="tag">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </a-spin>
    </div> -->
    <DesTablePage class="shipping_templates" v-if="data.tableConfigOptions.columns.length > 0">
      <template #title>
        <div>客服分组</div>
      </template>
      <template #extra>
        <a-button
          type="primary"
          v-auth="['wechatGroupManAdd']"
          :disabled="pointData.balance <= 0"
          @click="onShowDialog('group', data.form)"
          >新增分组</a-button
        >
      </template>
      <template #search>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="searchConfig.data"
          @changeValue="searchForm"
          :actions="searchConfig.options"
        />
      </template>
      <template #tableWarp>
        <div
          class="flex space-between flex-items-center mb-12px"
          :class="{ 'justify-end': !filedsNames?.includes('add_fans_num') && !isSuperRole }"
        >
          <div v-if="filedsNames?.includes('add_fans_num') || isSuperRole">
            当前页共加粉 <span class="c-#B46007">{{ nowPageNum }}</span> 个；当前页在线客服共<span class="c-#B46007">{{
              nowPageCanUseNum
            }}</span>
            个
          </div>
        </div>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #headerCell="{ scope }">
              <template v-if="headerCell.includes(scope.column.dataIndex)">
                <div>
                  <span>{{ scope.column.title }}</span>
                  <a-tooltip>
                    <template #title>{{ scope.column.text }}</template>
                    <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                  </a-tooltip>
                </div>
              </template>
            </template>
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'name'">
                <div class="flex">
                  <a-tooltip>
                    <template #title v-if="scope.record.name.length > 15">{{ scope.record.name }}</template>
                    <div class="text_overflow">{{ scope.record.name }}</div>
                  </a-tooltip>
                  <EditOutlined
                    v-auth="['wechatGroupManEdit']"
                    :disabled="pointData.balance <= 0"
                    class="c-#FE9D35 ml4px"
                    @click="onShowDialog('group', scope.record)"
                  ></EditOutlined>
                </div>
                <div class="flex-y-center mt-4px text_overflow_row1" v-if="scope.record.fail_status">
                  <ExclamationCircleOutlined class="c-red font-size-12px" />
                  <div class="c-red ml-4px font-size-12px h-12px line-height-12px">分组内有客服异常，请注意调整</div>
                </div>
              </template>
              <template v-if="scope.column.key === 'corp_name'">
                <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer('linkKefu', scope.record)">
                  {{ scope.record.corp_num || 0 }}
                </a-button>
              </template>

              <template v-if="scope.column.key === 'product_name'">
                <div>
                  <div v-if="scope.record">
                    <a-tooltip>
                      <template #title>{{ scope.record.product_name }}</template>
                      <div class="text_overflow">{{ scope.record.product_name }}</div>
                    </a-tooltip>
                  </div>
                  <div v-else>
                    <div>{{ scope.record.product_name || '--' }}</div>
                  </div>
                </div>
              </template>

              <template v-if="scope.column.key === 'linkNum'">
                <a-button type="link" size="small" class="pa-0!" @click="onShowDrawer('linkKefu', scope.record)">
                  {{ scope.record.linkNum }}
                </a-button>
              </template>
              <template v-if="scope.column.dataIndex === 'add_fans_num_ten_min'">
                <div class="cursor-pointer" @click="onShowDialog('add_fans_num_ten_min', scope.record)">
                  <span class="c-#52c41a">{{ scope.record.add_fans_num_ten_min || 0 }}/</span>
                  <span class="c-#B6AFAF">10min</span>
                </div>
              </template>
              <template v-if="scope.column.key === 'ad_link_num'">
                <div>{{ scope.record.ad_link_num || '--' }}</div>
              </template>
              <template v-if="scope.column.key === 'remark'">
                <div v-if="scope.record.remark?.length > 15">
                  <a-tooltip>
                    <template #title>{{ scope.record.remark }}</template>
                    <div class="text_overflow">{{ scope.record.remark }}</div>
                  </a-tooltip>
                </div>
                <div v-else>
                  <div>{{ scope.record.remark || '--' }}</div>
                </div>
              </template>

              <template v-if="scope.column.key === 'status'">
                <a-switch
                  v-model:checked="scope.record.status"
                  :checkedValue="1"
                  :unCheckedValue="2"
                  :disabled="pointData.balance <= 0"
                  @click="changeStatus($event, scope.record)"
                />
              </template>
              <template v-if="scope.column.key === 'pre_fans_num'">
                <div>
                  {{ scope.record.pre_fans_num || '--' }}
                  <EditOutlined
                    class="c-#FE9D35 ml4px"
                    @click="onShowDialog('pre_fans_num', scope.record)"
                  ></EditOutlined>
                </div>
              </template>

              <template v-if="scope.column.key === 'chat_rate'">
                <div>{{ scope.record.chat_rate }}</div>
              </template>
              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <a-button
                      type="link"
                      size="small"
                      class="pa-0!"
                      v-auth="['wechatGroupManEdit']"
                      :disabled="pointData.balance <= 0"
                      @click="onShowDialog('group', scope.record)"
                      >编辑</a-button
                    >
                    <a-button
                      type="link"
                      size="small"
                      v-auth="['wechatGroupManDel']"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="deleteGroup(scope.record)"
                      >删除</a-button
                    >
                    <a-button
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="pointData.balance <= 0"
                      @click="shareBtn(scope.record)"
                      >分享</a-button
                    >
                  </div>
                </div>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>
    <a-card class="border-rd-16px" v-else>
      <a-empty class="h-400px pt-100px" />
    </a-card>

    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :footer="null"
      @cancel="getList"
      :maskClosable="false"
    >
      <addGroup v-if="data.dialog.visible && data.dialog.type == 'group'" :item="data.dialog.item" @event="onEvent" />
      <shareGroup
        v-if="data.dialog.visible && data.dialog.type == 'share'"
        :item="data.dialog.item"
        @event="onEvent"
        @close="data.dialog.visible = false"
      />
      <linkNum v-if="data.dialog.visible && data.dialog.type == 'link'" :data="data.dialog.item" @event="onEvent" />
      <addFansNumTenMin
        v-if="data.dialog.visible && data.dialog.type == 'add_fans_num_ten_min'"
        :data="data.dialog.item"
        @event="onEvent"
      />
      <preFansNum
        v-if="data.dialog.visible && data.dialog.type == 'pre_fans_num'"
        :item="data.dialog.item"
        @event="onEvent"
      />
    </a-modal>
    <a-drawer
      v-model:open="data.drawer.visible"
      :width="data.drawer.width"
      placement="right"
      @close="
        () => {
          data.drawer.visible = false
          getList()
        }
      "
    >
      <template #title>
        <a-tooltip placement="bottomLeft">
          <template #title>{{ data.drawer.title }}</template>
          <div>{{ data.drawer.show_title }}</div>
        </a-tooltip>
      </template>
      <linkKefu
        v-if="data.drawer.visible && data.drawer.type == 'linkKefu'"
        :data="data.drawer.item"
        :is_fail="data.drawer.is_fail"
        @event="onEventDrawer"
      />
    </a-drawer>
  </div>
</template>

<script setup>
  import { onMounted, ref, computed, watch } from 'vue'
  import { useRoute } from 'vue-router'
  import datas from './src/data'
  import addGroup from './components/addGroup.vue'
  import shareGroup from './components/shareGroup.vue'
  import linkNum from './components/linkNum.vue'
  import linkKefu from './components/linkKefu.vue'
  import preFansNum from './components/preFansNum.vue'
  import addFansNumTenMin from './components/addFansNumTenMin.vue'
  import { QuestionCircleFilled, ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons-vue'
  import { usePoints } from '@/hooks'
  import { getTopInfo } from './index.api'
  import { useAuth } from '@/hooks/use-auth'
  import { useApp } from '@/hooks'
  import { set } from 'lodash-es'
  const { useInfo } = useApp()
  const { filedsNames = [] } = useAuth()
  console.log('filedsNames', filedsNames, useInfo)
  const { pointData } = usePoints()
  const route = useRoute()
  const {
    tabAllCol,
    pageChange,
    searchForm,
    onShowDialog,
    onShowDrawer,
    headerCell,
    searchConfig,
    data,
    statusType,
    getList,
    onEvent,
    onEventDrawer,
    deleteGroup,
    changeStatus,
    shareBtn,
    searchFormDataRef
  } = datas()
  getList()
  const topInfo = ref([
    { key: 'user_num', num: 0, tableName: 'name', unit: '个', label: '客服人员' },
    { key: 'pre_fans_num', num: 0, tableName: 'pre_fans_num', unit: '个', label: '预加粉数' },
    { key: 'add_fans_num', num: 0, tableName: 'add_fans_num', unit: '个', label: '加粉数' },
    { key: 'chat_num', num: 0, tableName: 'chat_num', unit: '个', label: '开口数' },
    { key: 'chat_rate', num: 0, tableName: 'chat_rate', unit: '%', label: '开口率' },
    { key: 'add_fans_rate', num: 0, tableName: 'add_fans_rate', unit: '%', label: '进度' }
  ])
  const topLastInfo = ref([])
  const topLoading = ref(false)
  watch(
    () => useInfo.value,
    (newDate) => {
      if (newDate.user_type === 1) {
        data.tableConfigOptions.columns = tabAllCol
      }
    },
    {
      immediate: true
    }
  )
  const isSuperRole = computed(() => {
    return useInfo.value.user_type === 1
  })
  const nowPageNum = computed(() => {
    let arr = 0
    const total = data.tableConfigOptions.dataSource.reduce((acc, cur) => {
      return acc + (cur.add_fans_num || 0)
    }, 0)
    return total || 0
  })
  const nowPageCanUseNum = computed(() => {
    let arr = 0
    console.log('data.tableConfigOptions.dataSource', data.tableConfigOptions.dataSource)
    const total = data.tableConfigOptions.dataSource.reduce((acc, cur) => {
      return acc + (cur.can_use_link_num || 0)
    }, 0)
    return total || 0
  })
  onMounted(() => {
    if (route.query.isAdd) {
      onShowDialog('group', data.form)
    }
    // getTop()
  })
  const getTop = async () => {
    try {
      topLoading.value = true
      const res = await getTopInfo()

      const objData = res?.data?.data || {}
      if (useInfo.value.user_type !== 1) {
        topInfo.value = topInfo.value.filter((item) => {
          return filedsNames?.includes(item.tableName)
        })
      }

      topInfo.value.forEach((item) => {
        //  console.log('res----', objData)
        for (let key in objData) {
          if (item.key === key) {
            item.num = objData[key]
          }
        }
      })
      topLastInfo.value = topInfo.value
    } finally {
      topLoading.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .statistics-box {
    padding: 16px 24px;
    background: #ffffff;
    border-radius: 16px;
    .list {
      padding: 20px 10% 0 16px;
      margin-top: 9px;
      background: #f7f9fc;
      border-radius: 8px;
      flex-wrap: wrap;
      .item {
        width: 15%;
        min-width: 200px;
        margin-bottom: 20px;
        .desc {
          margin-bottom: 6px;
          font-weight: 500;
          font-size: 14px;
          color: #6d6d6d;
          line-height: 20px;
        }
        .value {
          font-weight: 500;
          font-size: 28px;
          color: #101010;
          line-height: 40px;
        }
        .tag {
          margin-left: 8px;
          font-weight: 400;
          font-size: 12px;
          color: #aeaeae;
          line-height: 17px;
        }
      }
    }
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  :deep(.ant-table-cell-fix-right-first) {
    right: 0 !important;
  }

  // :deep(.shipping_templates) {
  //   .ant-card-head {
  //     display: none;
  //   }
  // }
</style>
