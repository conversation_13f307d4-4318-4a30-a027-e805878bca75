<template>
  <div>
    <a-form :model="state.form" ref="ruleForm" :labelCol="{ style: 'width:110px;' }">
      <a-form-item label="异常标签" name="tag_id" :rules="[{ required: true, message: '请选择异常标签' }]">
        <a-select
          v-model:value="state.form.tag_id"
          :options="resTagList"
          placeholder="请选择异常标签"
          @change="handleTagIdChange"
        />
      </a-form-item>
      <a-form-item
        label="异常原因"
        name="result"
        :rules="[{ required: state.form.tag_id == 4, message: '请输入异常原因' }]"
      >
        <a-textarea
          v-model:value="state.form.result"
          :auto-size="{ minRows: 2, maxRows: 3 }"
          :maxlength="100"
          show-count
          placeholder="请输入异常原因"
        />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="state.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { reactive, ref, nextTick } from 'vue'
  import { message } from 'ant-design-vue'
  import { resTagList } from '@/utils'
  import { res_user_set_tag } from './index.api'
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const state = reactive({
    loading: false,
    form: {
      tag_id: undefined,
      result: ''
    }
  })
  const handleTagIdChange = (value) => {
    if (value !== 4) {
      // 清除验证状态
      nextTick(() => {
        ruleForm.value.validateFields(['result'])
      })
    }
  }
  const close = () => {
    ruleForm.value.clearValidate()
    emit('event', { cmd: 'close' })
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }
  const add = async () => {
    try {
      state.loading = true
      let params = {
        ...state.form,
        uid: props.item?.res_uid
      }
      await res_user_set_tag(params)
      message.success('操作成功')
      emit('event', { cmd: 'markAnomalies' })
      state.loading = false
    } catch (error) {
      state.loading = false
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
