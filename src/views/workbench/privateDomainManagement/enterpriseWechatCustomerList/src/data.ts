import { onMounted, reactive, createVNode } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getListApi, manualCallbackApi, updateTag } from '../index.api'
import { workListApi, exportCreate } from '@/api/common'
import { usePoints, useDownloadCenter } from '@/hooks'
import { callbackStatusParams, delete_statusList, mediaTypeSearch } from '@/utils'
import { ad_ip_add } from '@/views/workbench/nameList/blacklist/index.api'
import dayjs from 'dayjs'
export default function datas(type?: string, parentItem?: any) {
  const { goCenter } = useDownloadCenter()
  const { pointData } = usePoints()
  const searchConfig = reactive({
    data: [
      {
        field: 'corpid',
        type: 'select',
        value: undefined,
        span: 6,
        props: {
          options: [],
          placeholder: '请选择企微'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'link_name',
        value: undefined,
        props: {
          placeholder: '请输入获客链接名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'customer_name',
        value: undefined,
        props: {
          placeholder: '请输入客户名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'extend_user_id',
        value: undefined,
        props: {
          placeholder: '请输入客户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'wx_user_name',
        value: undefined,
        props: {
          placeholder: '请输入跟进客服名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'wx_user_id',
        value: undefined,
        props: {
          placeholder: '请输入跟进客服ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_user_id',
        value: type === 'report' ? parentItem.account_id + '' : undefined,
        props: {
          placeholder: '请输入账户ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_ad_group_id',
        value: undefined,
        props: {
          placeholder: '请输入计划ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        field: 'ad_platform',
        type: 'select',
        value: '',
        props: {
          options: mediaTypeSearch,
          placeholder: '请选择广告平台'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'tr_material_id',
        value: undefined,
        props: {
          placeholder: '请输入素材ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'select',
        field: 'is_chat',
        value: undefined,
        props: {
          placeholder: '请选择回话状态',
          options: [
            {
              value: 1,
              label: '客户已发送消息'
            },
            {
              value: 2,
              label: '客户未发送消息'
            },
            {
              value: 3,
              label: '客户待通过申请'
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'select',
        field: 'status',
        value: undefined,
        props: {
          placeholder: '请选择回传状态',
          options: callbackStatusParams
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'select',
        field: 'chat_num',
        value: undefined,
        props: {
          showArrow: true,
          placeholder: '请选择开口次数',
          mode: 'multiple',
          options: [
            {
              label: '1',
              value: 1
            },
            {
              label: '3',
              value: 3
            },
            {
              label: '5',
              value: 5
            },
            {
              label: '10',
              value: 10
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'select',
        field: 'del_type',
        value: undefined,
        props: {
          placeholder: '请选择删除状态',
          options: delete_statusList
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'customer_tag',
        value: undefined,
        props: {
          placeholder: '请输入客户标签'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      // {
      //   type: 'input.text',
      //   field: 'ad_link_name',
      //   value: undefined,
      //   props: {
      //     placeholder: '请输入投放链接名称'
      //   },
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 4
      //   }
      // },
      {
        type: 'input.text',
        field: 'ad_h5_name',
        value: undefined,
        props: {
          placeholder: '请输入落地页名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'input.text',
        field: 'ip',
        value: undefined,
        props: {
          placeholder: '请输入IP'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: type ? 6 : 4
        }
      },
      {
        type: 'joint_date',
        field: 'apply_map',
        value: type ? undefined : [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        props: {
          options: [
            {
              value: 'apply_map',
              label: '申请时间'
            },
            {
              value: 'created_at',
              label: '加粉时间'
            },
            {
              value: 'callback_time_map',
              label: '回传时间'
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 8
        }
      }
    ],
    options: {
      foldNum: 0
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 2280
    },
    dataSource: [],
    columns: [
      {
        title: '客户名称',
        dataIndex: 'customer_name',
        key: 'customer_name',
        width: 180,
        minWidth: 150,
        fixed: 'left'
      },
      {
        title: '性别',
        dataIndex: 'gender',
        key: 'gender',
        width: 80
      },
      {
        title: '客户标签',
        dataIndex: 'customer_tag',
        key: 'customer_tag',
        width: 170
      },
      {
        title: '客户标记',
        dataIndex: 'customer_marker',
        key: 'customer_marker',
        width: 150
      },
      {
        title: '回话状态',
        dataIndex: 'is_chat',
        key: 'is_chat',
        width: 150
      },
      {
        title: '开口次数',
        dataIndex: 'chat_num',
        key: 'chat_num',
        width: 100
      },
      // {
      //   title: '流量版位',
      //   dataIndex: 'tr_site',
      //   key: 'tr_site',
      //   width: 150
      // },
      {
        title: '落地页',
        dataIndex: 'ad_h5_name',
        key: 'ad_h5_name',
        width: 180
      },
      {
        title: '流量版位',
        dataIndex: 'tr_sub_site',
        key: 'tr_sub_site',
        width: 150
      },
      {
        title: '回传状态',
        dataIndex: 'is_callback',
        key: 'is_callback',
        width: 120
      },
      {
        title: '跟进企微',
        dataIndex: 'wx_user_id', //wx_user_name
        key: 'wx_user_id',
        width: 150
      },
      {
        title: '获客链接',
        dataIndex: 'wx_link_id', //link_name
        key: 'wx_link_id',
        width: 150
      },
      {
        title: '企微名称',
        dataIndex: 'corp_name', //corpid
        key: 'corp_name',
        width: 200
      },
      {
        title: '账户信息',
        dataIndex: 'tr_user_id', //ad_account_name
        key: 'tr_user_id',
        width: 200
      },
      {
        title: '计划信息',
        dataIndex: 'ad_name', //ad_name
        key: 'ad_name',
        width: 200
      },
      {
        title: '素材信息',
        dataIndex: 'tr_material_id', //ad_name
        key: 'tr_material_id',
        width: 200
      },
      {
        title: '删除状态',
        dataIndex: 'del_type',
        key: 'del_type',
        width: 150
      },
      // {
      //   title: '广告平台',
      //   dataIndex: 'ad_platform',
      //   key: 'ad_platform',
      //   width: 150
      // },
      {
        title: 'IP',
        dataIndex: 'ip',
        key: 'ip',
        width: 210
      },
      {
        title: '地区',
        dataIndex: 'city',
        key: 'city',
        width: 100
      },
      {
        title: '访问时长',
        dataIndex: 'visit_second',
        key: 'visit_second',
        width: 140
      },
      {
        title: '答题时长',
        dataIndex: 'answer_second',
        key: 'answer_second',
        width: 140
      },
      {
        title: '申请添加时间',
        dataIndex: 'apply_time',
        key: 'apply_time',
        width: 180
      },
      {
        title: '添加成功时间',
        dataIndex: 'add_fans_time',
        key: 'add_fans_time',
        width: 180
      },
      {
        title: '点击时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '回传时间',
        dataIndex: 'callback_at',
        key: 'callback_at',
        width: 180
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20,
      apply_map: type ? undefined : [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    },
    dialog: {
      visible: false,
      title: '',
      width: null as any,
      type: '',
      item: null
    },
    selectedRowKeys: [],
    selectedRows: []
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#999999',
        text: '未回传'
      },
      1: {
        color: '#60A13B',
        text: '已回传'
      }
    }
    return (status as any)[val]
  }
  const ad_platform_type = (status: any) => {
    let text = {
      1: '广点通',
      2: '磁力引擎',
      3: '巨量引擎'
    }
    return (text as any)[status]
  }
  //  转化状态
  const status_text = (status: any) => {
    //
    let text = {
      1: '未转化',
      2: '转化成功',
      3: '转化失败'
    }
    return (text as any)[status]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getListApi(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data?.total || 0
      data.tableConfigOptions.pagination.current = data.params?.page || 0
      if (type) {
        data.tableConfigOptions.scroll.y = 240
      } else {
        delete data.tableConfigOptions.scroll.y
      }
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  const exportsData = reactive({
    visible: false,
    type: 'wx_customer_export',
    params: {}
  })
  const onEvent = ({ cmd }: { cmd: string }) => {
    data.dialog.visible = false
    data.dialog.title = ''
    data.dialog.width = 0
    data.dialog.type = null
    data.dialog.item = null
    if (cmd === 'markAnomalies') {
      getList()
      return
    }
    exportsData.visible = false
  }
  //导出
  // const handleExportCreate = async () => {
  //   try {
  //     let params = {
  //       ...data.params
  //     }
  //     await exportCreate({
  //       type: 'wx_customer_export',
  //       params: JSON.stringify(params)
  //     })
  //     goCenter()
  //   } catch (error) {
  //     console.log(error)
  //   }
  // }
  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData,
      add_fans_time: (v.formData.created_at && v.formData.created_at) || undefined,
      callback_time_map: v.formData.callback_time_map || undefined,
      apply_map: v.formData.apply_map || undefined
    }
    delete data.params.created_at

    if (data.params.add_fans_time?.length) {
      const str = data.params.add_fans_time.join('_')
      if (str == '_') delete data.params.add_fans_time
    }
    if (data.params.callback_time_map?.length) {
      const str = data.params.callback_time_map.join('_')
      if (str == '_') delete data.params.callback_time_map
    }
    if (data.params.apply_map?.length) {
      const str = data.params.apply_map.join('_')
      if (str == '_') delete data.params.apply_map
    }
    data.params.page = 1
    if (!v.status) {
      searchConfig.data.forEach((item) => {
        if (item.type == 'joint_date') {
          item.field = 'apply_map'
          data.params.apply_map = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
          v.formData.apply_map = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
          data.params.callback_time_map = undefined
          data.params.add_fans_time = undefined
        }
      })
    }

    if (v.type === 'special_export') {
      // handleExportCreate()
      exportsData.visible = true
      exportsData.params = data.params
      return false
    } else {
      getList()
    }
  }
  const selectedChange = (selectedRowKeys: any, selectedRows: any) => {
    data.selectedRows = selectedRows
    data.selectedRowKeys = selectedRowKeys
  }
  const getCheckboxProps = (record: any) => {
    return {
      disabled: pointData.value.balance <= 0 || record.is_callback == 1
    }
  }
  const manualCallback = async (ad_conversion_ids: any) => {
    try {
      let params = {
        ad_conversion_ids: ad_conversion_ids
      }
      await manualCallbackApi(params)
      message.success('操作成功')
      getList()
      data.selectedRowKeys = []
    } catch (error) {}
  }
  // 同步用户标签
  const onUpdateTag = async () => {
    try {
      let ids = data.tableConfigOptions.dataSource.map((item) => item.id)
      await updateTag({ ids })
      message.success('本页标签同步成功')
      getList()
      data.selectedRowKeys = []
    } catch (error) {
      console.log(error, 'error')
      message.error('本页标签同步失败')
    }
  }
  const onShowDialog = (type, item) => {
    data.info = item
    switch (type) {
      case 'batch':
        if (data.selectedRowKeys.length == 0) {
          message.warning('请选择回传数据')
          return
        }
        manualCallback(data.selectedRowKeys)
        break
      case 'single':
        manualCallback([item.id])
        break
      case 'mark':
        data.dialog.visible = true
        data.dialog.title = '标记异常'
        data.dialog.width = 600
        data.dialog.type = type
        data.dialog.item = item
        break
    }
  }
  const getWechatList = async () => {
    try {
      const { data } = await workListApi({ page: 1, page_size: 1000 })
      searchConfig.data.forEach((v) => {
        if (v.field === 'corpid') {
          v.props.options = data.list.map((item: any) => ({
            label: item.corp_name || '',
            value: item.corpid || ''
          }))
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getWechatList()
  const IpClick = (item: any) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否要将该IP拉入黑名单？'),
        async onOk() {
          await ad_ip_add({
            type: 1,
            ip: item.ip
          })
          message.success('拉黑成功')
          getList()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  onMounted(() => {
    if (type === 'report') {
      data.params.tr_user_id = parentItem.account_id + ''
    }
    getList()
  })
  return {
    pageChange,
    searchForm,
    onShowDialog,
    searchConfig,
    data,
    statusType,
    getList,
    selectedChange,
    getCheckboxProps,
    ad_platform_type,
    status_text, //
    exportsData,
    onEvent,
    onUpdateTag,
    IpClick
  }
}
