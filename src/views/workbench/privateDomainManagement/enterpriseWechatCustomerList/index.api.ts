import http from '@/utils/request'
//获取企微加粉数据列表
export const getListApi = (data: any) => {
  return http('post', `/admin/adConversion/get_list`, data)
}
//回传
export const manualCallbackApi = (data: any) => {
  return http('post', `/admin/ad_conversion/manual_callback`, data)
}

//批量更新用户标签
export const updateTag = (data: any) => {
  return http('post', `/admin/fans/update_tag`, data)
}
//设置用户标签
export const res_user_set_tag = (data: any) => {
  return http('post', `/common/res_user/set_tag`, data)
}
