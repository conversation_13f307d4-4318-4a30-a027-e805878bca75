<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title v-if="type !== 'report'">
        <div>客户明细</div>
      </template>
      <template #search>
        <SearchBaseLayout
          :data="searchConfig.data"
          @changeValue="searchForm"
          :actions="searchConfig.options"
          :btnNames="[isAuth(['enterpriseWechatCustomerExport']) ? 'special_export' : '']"
        >
          <template #btns>
            <div class="btn_group ml-8px">
              <a-button type="primary" @click="onShowDialog('batch')" :disabled="pointData.balance <= 0"
                >批量手动回传</a-button
              >
              <a-button
                v-auth="['UpdateTag']"
                type="primary"
                style="margin-left: 16px"
                @click="onUpdateTag"
                :disabled="pointData.balance <= 0 || data.tableConfigOptions.dataSource.length <= 0"
                >同步企微标签</a-button
              >
            </div>
          </template>
        </SearchBaseLayout>
      </template>
      <template #tableWarp>
        <TableZebraCrossing
          :data="data.tableConfigOptions"
          :rowSelection="{
            selectedRowKeys: data.selectedRowKeys,
            onChange: selectedChange,
            getCheckboxProps
          }"
          @change="pageChange"
        >
          <template #headerCell="{ scope }">
            <template v-if="scope.column.key === 'gender'">
              <div>
                <span>性别</span>
                <a-tooltip>
                  <template #title>信息来源于企业微信</template>
                  <QuestionCircleFilled class="ml-3px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'customer_name'">
              <template v-if="scope.record.customer_name || scope.record.extend_user_id">
                <div class="flex">
                  <a-image
                    :src="scope.record.avatar"
                    width="50px"
                    height="50px"
                    v-if="scope.record.avatar"
                    style="min-width: 50px"
                  />
                  <div class="ml-5px text_overflow_row1">
                    <div class="flex">
                      <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                        <template #title>{{ scope.record.customer_name || '--' }}</template>
                        <div class="text_overflow_row1">{{ scope.record.customer_name || '--' }}</div>
                      </a-tooltip>
                      <span v-if="[1].includes(scope.record.is_water)" class="risk_labels ml8px"> 风险客户</span>
                    </div>

                    <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                      <template #title>{{ scope.record.extend_user_id }}</template>
                      <div class="text_overflow_row1 number-id mt6px">
                        ID：{{ scope.record.extend_user_id || '--' }}
                      </div>
                    </a-tooltip>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="flex">
                  <span>--</span>
                  <span v-if="[1].includes(scope.record.is_water)" class="risk_labels ml8px"> 风险客户</span>
                </div>
              </template>
            </template>
            <template v-if="scope.column.key === 'customer_tag'">
              <a-tooltip
                placement="top"
                :disabled="!scope.record.customer_tag"
                :overlayInnerStyle="{ width: 'max-content' }"
              >
                <template #title v-if="scope.record.customer_tag?.length > 15">{{
                  scope.record.customer_tag
                }}</template>
                <div class="text_overflow_row1 w-full">{{ scope.record.customer_tag || '--' }}</div>
              </a-tooltip>
              <div class="mark_anomalies" v-if="scope.record.res_tag_num">
                <a-tooltip placement="top">
                  <template #title
                    >{{ scope.record.res_tag }}
                    <span v-if="scope.record.res_tag_result">-{{ scope.record.res_tag_result }}</span></template
                  >
                  <ExclamationCircleOutlined
                    v-if="scope.record.res_tag_result || scope.record.res_tag"
                    class="font-size-12px mr4px"
                  />
                </a-tooltip>
                <span class="text_overflow_row1">已被多人标记为异常用户</span>
              </div>
            </template>
            <template v-if="scope.column.key === 'customer_marker'">
              <div v-if="scope.record.customer_marker?.length">
                <a-tooltip
                  placement="top"
                  :disabled="!scope.record.customer_marker"
                  :overlayInnerStyle="{ width: 'max-content' }"
                  v-if="scope.record.customer_marker"
                >
                  <template #title>{{ scope.record.customer_marker }}</template>
                  <div class="text_overflow w-full">{{ scope.record.customer_marker }}</div>
                </a-tooltip>
              </div>
              <span v-else>{{ '--' }}</span>
            </template>

            <template v-if="scope.column.key === 'is_chat'">
              <div class="flex-y-center">
                <span class="round" :style="{ background: chatEnumCls(scope.record.is_chat) }"></span>
                <span :style="{ color: chatEnumCls(scope.record.is_chat) }">
                  {{ chatEnum(scope.record.is_chat) }}
                </span>
              </div>
            </template>
            <template v-if="scope.column.key === 'is_callback'">
              <div class="flex items-center">
                <span class="round" :style="{ background: callbackStatus2Content(scope.record.status)?.color }"></span>
                <div :style="{ color: callbackStatus2Content(scope.record.status)?.color }">
                  {{ callbackStatus2Content(scope.record.status)?.label || '--' }}
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'ad_platform'">
              <div class="flex platform_content items-center">
                <img v-if="scope.record.ad_platform == 1" :src="requireImg('media/gdt.png')" alt="" />
                <img v-if="scope.record.ad_platform == 2" :src="requireImg('media/cili.png')" alt="" /><img
                  v-if="scope.record.ad_platform == 3"
                  :src="requireImg('media/jl.png')"
                  alt=""
                />{{ ad_platform_type(scope.record.ad_platform) }}
              </div>
            </template>
            <template v-if="scope.column.key === 'tr_site'">
              <span>{{ scope.record.tr_site || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'tr_sub_site'">
              <div v-if="scope.record.tr_sub_site">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.tr_sub_site }}</template>
                  <div class="text_overflow">{{ scope.record.tr_sub_site }}</div>
                </a-tooltip>
              </div>
              <span v-else>{{ '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'apply_time'">
              <span>{{ scope.record.apply_time || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'add_fans_time'">
              <span>{{ scope.record.add_fans_time || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'visit_second'">
              <span>{{ secondsToTime(scope.record.visit_second) }}</span>
            </template>
            <template v-if="scope.column.key === 'answer_second'">
              <span v-if="!scope.record.customer_marker && !scope.record.answer_second">--</span>
              <span v-else>{{ secondsToTime(scope.record.answer_second) }}</span>
            </template>
            <template v-if="scope.column.key === 'gender'">
              <span>{{ ['未知', '男', '女'][scope.record.gender] || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'wx_user_id'">
              <div v-if="scope.record.wx_user_name.length">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.wx_user_name }}</template>
                  <div class="text_overflow">{{ scope.record.wx_user_name }}</div>
                </a-tooltip>
              </div>
              <div v-else>{{ scope.record.wx_user_name || '--' }}</div>
              <div v-if="scope.record.wx_user_id">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.wx_user_id }}</template>
                  <div class="text_overflow number-id">ID：{{ scope.record.wx_user_id }}</div>
                </a-tooltip>
              </div>
              <div v-else>--</div>
            </template>
            <template v-if="scope.column.key === 'wx_link_id'">
              <div v-if="scope.record.link_name.length">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.link_name }}</template>
                  <div class="text_overflow">{{ scope.record.link_name }}</div>
                </a-tooltip>
              </div>
              <div v-else>
                {{ scope.record.link_name || '--' }}
              </div>
              <div v-if="scope.record.link">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.link }}</template>
                  <div class="flex">
                    <div class="text_overflow number-id">链接：{{ scope.record.link }}</div>
                    <CopyOutlined class="c-primary" @click="copy(scope.record.link)" />
                  </div>
                </a-tooltip>
              </div>
              <div v-else>--</div>
            </template>
            <template v-if="scope.column.key === 'corp_name'">
              <div v-if="scope.record.corp_name.length">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.corp_name }}</template>
                  <div class="text_overflow">{{ scope.record.corp_name }}</div>
                </a-tooltip>
              </div>
              <div v-else>{{ scope.record.corp_name || '--' }}</div>
              <div v-if="scope.record.corpid">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.corpid }}</template>
                  <div class="text_overflow number-id">ID：{{ scope.record.corpid }}</div>
                </a-tooltip>
              </div>
              <div v-else>--</div>
            </template>
            <!-- 账户信息 -->
            <template v-if="scope.column.key === 'tr_user_id'">
              <div class="relative">
                <img
                  class="absolute left-0px top-2px w-15px h-15px"
                  v-if="scope.record.ad_platform == 1"
                  :src="requireImg('media/gdt.png')"
                  alt=""
                />
                <img
                  class="absolute left-0px top-2px w-15px h-15px"
                  v-if="scope.record.ad_platform == 2"
                  :src="requireImg('media/cili.png')"
                  alt=""
                />
                <img
                  class="absolute left-0px top-2px w-15px h-15px"
                  v-if="scope.record.ad_platform == 3"
                  :src="requireImg('media/jl.png')"
                  alt=""
                />
                <div class="ml-20px" v-if="scope.record.ad_account_name.length">
                  <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                    <template #title>{{ scope.record.ad_account_name }}</template>
                    <div class="text_overflow">{{ scope.record.ad_account_name }}</div>
                  </a-tooltip>
                </div>
                <div v-else>{{ scope.record.ad_account_name || '--' }}</div>
              </div>
              <div v-if="scope.record.tr_user_id">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.tr_user_id }}</template>
                  <div class="text_overflow number-id">ID：{{ scope.record.tr_user_id }}</div>
                </a-tooltip>
              </div>
              <div v-else class="text_overflow number-id">ID：{{ '--' }}</div>
            </template>

            <!-- 计划信息 -->
            <template v-if="scope.column.key === 'ad_name'">
              <div v-if="scope.record.ad_group_name.length > 12">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.ad_group_name }}</template>
                  <div class="text_overflow">{{ scope.record.ad_group_name }}</div>
                </a-tooltip>
              </div>
              <div v-else>{{ scope.record.ad_group_name || '--' }}</div>
              <div v-if="scope.record.tr_ad_id">
                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.tr_ad_id }}</template>
                  <div class="text_overflow number-id">ID：{{ scope.record.tr_ad_id }}</div>
                </a-tooltip>
              </div>
              <div v-else class="text_overflow number-id">ID：{{ '--' }}</div>
            </template>
            <template v-if="scope.column.key === 'tr_material_id'">
              <span> {{ scope.record.tr_material_id || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'del_type'">
              <div v-if="!scope.record.extend_user_id">--</div>
              <div v-else class="flex items-center">
                <span class="round" :style="{ background: delStatusContent(scope.record.del_type)?.color }"></span>
                <div :style="{ color: delStatusContent(scope.record.del_type)?.color }">
                  {{ delStatusContent(scope.record.del_type)?.label || '--' }}
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'callback_at'">
              <span v-if="scope.record.is_callback == 1"> {{ scope.record.callback_at }}</span>
              <span v-else>--</span>
            </template>
            <template v-if="scope.column.key === 'ip'">
              <div class="c-#B6AFAF flex-y-center" v-if="scope.record.is_blacklist">
                <span>{{ scope.record.ip }}</span>
                <span class="tag-wrapper font-size-12px! line-height-16px!">IP黑名单</span>
              </div>
              <div v-else>{{ scope.record.ip }}</div>
            </template>
            <template v-if="scope.column.key === 'ad_h5_name'">
              <a-tooltip placement="topLeft">
                <template #title v-if="scope.record.ad_h5_name?.length > 15">{{ scope.record.ad_h5_name }}</template>
                <div class="text_overflow">{{ scope.record.ad_h5_name || '--' }}</div>
              </a-tooltip>
            </template>

            <template v-if="scope.column.key === 'action'">
              <a-button
                v-auth="['enterpriseWechatCustomerList_callback']"
                type="link"
                size="small"
                class="pa-0!"
                :disabled="scope.record.is_callback == 1 || pointData.balance <= 0"
                @click="onShowDialog('single', scope.record)"
                >手动回传</a-button
              >
              <template v-if="!scope.record.res_tag && !['0', ''].includes(scope.record.res_uid)">
                <a-button
                  v-auth="['enterpriseWechatMarkAnomalies']"
                  type="link"
                  size="small"
                  class="pa-0!"
                  :disabled="pointData.balance <= 0"
                  @click="onShowDialog('mark', scope.record)"
                  >异常标记</a-button
                >
              </template>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <specialExport :exportsData="exportsData" @onEvent="onEvent" />
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :footer="null"
      :centered="true"
      destroyOnClose
    >
      <MarkAnomalies v-if="data.dialog.type === 'mark'" :item="data.dialog.item" @event="onEvent" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import datas from './src/data'
  import { usePoints, useAuth } from '@/hooks'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  const { isAuth } = useAuth()
  import {
    requireImg,
    callbackStatus2Content,
    delStatusContent,
    copy,
    chatEnum,
    chatEnumCls,
    secondsToTime
  } from '@/utils'
  import { CopyOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
  import MarkAnomalies from './MarkAnomalies.vue'
  const { pointData } = usePoints()
  const props = defineProps(['type', 'item'])

  const {
    pageChange,
    searchForm,
    onShowDialog,
    searchConfig,
    data,
    selectedChange,
    getCheckboxProps,
    ad_platform_type,
    exportsData,
    onEvent,
    onUpdateTag
  } = datas(props.type, props.item)
</script>

<style lang="scss" scoped>
  .platform_content {
    img {
      width: 15px;
      height: 15px;
      margin-right: 5px;
      margin-top: 2px;
    }
  }
  .page_main_page {
    // min-height: calc(100vh - 146px - 15px * 2);
    // background-color: #fff;
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 6px;
    height: 6px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  // .btn_group {
  //   margin-top: 24px;
  //   margin-bottom: 16px;
  // }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  .risk_labels {
    padding: 2px 4px;
    background: #fff1f0;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    color: #ff4d4f;
  }
  .mark_anomalies {
    display: flex;
    align-items: center;
    color: #fe4d4f;
    font-size: 12px;
    white-space: nowrap;
  }
  .tag-wrapper {
    margin-left: 4px;
    flex: none;
    display: inline-block;
    padding: 2px 8px;
    font-size: 14px;
    color: #2b2b2b;
    border: 1px solid #d9d9d9;
    background: #f1f1f1;
    border-radius: 4px;
  }
</style>
