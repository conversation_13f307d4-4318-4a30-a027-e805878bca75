<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div class="flex justify-between flex-items-center">
          <div>企微管理</div>
        </div>
      </template>
      <template #extra>
        <a-button type="primary" v-auth="['shareWechatEnterprise']" @click="onShowDialog('share')">共享</a-button>
        <a-button type="primary" v-auth="['addAuth']" @click="getUrl" :disabled="pointData.balance <= 0"
          >新增授权</a-button
        >
      </template>
      <template #search>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="searchConfig.data"
          @changeValue="searchForm"
          :actions="searchConfig.options"
        />
      </template>
      <template #tableWarp>
        <div class="page_main_table">
          <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'corp_name'">
                <div class="flex">
                  <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                    <template #title>{{ scope.record.corp_name }}</template>
                    <div class="text_overflow">{{ scope.record.corp_name }}</div>
                  </a-tooltip>
                  <div v-if="[1, 2].includes(scope.record.is_fail)">
                    <QuestionCircleFilled class="ml-3px font-size-12px c-#FF4D4F" />{{
                      scope.record.is_fail == 2 ? '成员全部异常' : '成员部分异常'
                    }},请尽快处理
                  </div>
                </div>

                <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
                  <template #title>{{ scope.record.corpid }}</template>
                  <div class="flex-y-center">
                    <div class="text_overflow number-id">ID：{{ scope.record.corpid }}</div>
                    <CopyOutlined class="c-primary" @click="copy(scope.record.corpid)" />
                  </div>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'corp_square_logo_url'">
                <a-image class="corp_logo" :src="scope.record.corp_square_logo_url" alt=""></a-image>
              </template>
              <template v-if="scope.column.key === 'work_quota'">
                <div>
                  <div>
                    累积使用量： <span class="c-#FE9D35">{{ scope.record.work_quota.total }}</span>
                  </div>
                  <div>
                    剩余使用量： <span class="c-#52C41A">{{ scope.record.work_quota.balance }}</span>
                  </div>

                  <div class="flex warn_text max-w-132px" v-if="scope.record.is_not_full_balance">
                    <!-- 使用量不足提示 -->
                    <div
                      class="c-#FF4D4F font-size-12px text_overflow_row1"
                      style="background-color: rgb(255, 241, 240); padding: 0 4px; border-radius: 2px; padding-top: 1px"
                    >
                      获客助手余量不足
                    </div>

                    <a-tooltip>
                      <template #title>
                        该企业在企业微信的获客助手使用量已不足，请您及时关注剩余使用量并及时充值～
                      </template>
                      <QuestionCircleFilled class="ml-3px font-size-12px c-#FF4D4F" />
                    </a-tooltip>
                  </div>

                  <div v-if="scope.record.work_quota.quota_list">
                    <div>剩余使用量明细：</div>
                    <div v-for="(item, index) in scope.record.work_quota.quota_list" :key="index">
                      其中：<span class="c-#BC44D0">{{ item.balance }}个 </span> 到期时间：
                      <span>{{ item.expire_date }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="scope.column.key === 'status'">
                <div
                  class="flex_align_center"
                  style="min-width: 100px"
                >
                  <div
                    :class="qaAuthStatusEnum[`${scope.record.status}`].className"
                    v-if="qaAuthStatusEnum[`${scope.record.status}`]"
                  >
                    {{ qaAuthStatusEnum[`${scope.record.status}`].text }}
                  </div>
                  <div v-else>--</div>
                </div>
              </template>
              <template v-if="scope.column.key === 'permission_status'">
                <div class="flex_align_center" :class="{ 'cursor-pointer': [1,4].includes(scope.record.permission_status) && scope.record.status!=2 }" style="min-width: 100px" @click="statusClick(scope.record)">
                  <span class="round" :style="{ background: data.permission_status?.[scope.record.permission_status]?.color }"></span>
                  <span :style="{ color: data.permission_status?.[scope.record.permission_status]?.color }"
                    :class="{ 'underline': [1,4].includes(scope.record.permission_status) && scope.record.status!=2 }"
                  >
                    {{ data.permission_status?.[scope.record.permission_status]?.text }}
                    <span v-if="[1,4].includes(scope.record.permission_status) && scope.record.status!=2" class="">查看进度</span>
                  </span>
                </div>
              </template>
              <template v-if="scope.column.key === 'subject_type'">
                <span>
                  {{ subjectType(scope.record.subject_type).text }}
                </span>
              </template>
              <template v-if="scope.column.key === 'admin_name'">
                <span>
                  {{ scope.record.admin_name || '--' }}
                </span>
              </template>
              <template v-if="scope.column.key === 'action'">
                <div class="handle_btns">
                  <div class="flex_align_center">
                    <a-button
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="scope.record.status!=1 || scope.record.permission_status != 2 || pointData.balance <= 0"
                      @click="onShowDraw('look', scope.record)"
                      >查看成员</a-button
                    >
                    <a-button
                      type="link"
                      size="small"
                      class="pa-0!"
                      :disabled="scope.record.status!=1 || scope.record.permission_status != 2 || pointData.balance <= 0"
                      :loading="scope.record.loading"
                      @click="asyncUser(scope.record)"
                      >同步成员</a-button
                    >
                  </div>
                </div>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :maskClosable="false"
      centered
      :footer="null"
      @cancel="onEvent"
    >
      <!-- <lookMember v-if="data.dialog.visible && data.dialog.type == 'look'" :item="data.dialog.item" @event="onEvent" /> -->
      <authorizationStatus v-if="data.dialog.visible && data.dialog.type == 'Qr'" :data="data" @event="onEvent" @codeOk="codeOk" @confirmCode="confirmCode" />
      <shareWechatEnterprise v-if="data.dialog.visible && data.dialog.type == 'share'" @event="onEvent" />
    </a-modal>
    <a-modal
      v-model:open="data.successOpen"
      title=""
      :width="340"
      :maskClosable="false"
      :closable="false"
      centered
      :footer="null"
    >
      <div class="flex-center flex-col">
        <img class="w40px h40px mt-10px" :src="successImg" alt="" srcset="">
        <div class="mt-15px fw-500 font-size-18px">配置成功，可以进行操作</div>
        <a-button class="mt-15px mb-10px w-88px h-32px font-size-14px" @click="successClick">好的</a-button>
      </div>
    </a-modal>
    <a-drawer
      v-model:open="data.drawer.visible"
      :title="data.drawer.title"
      :width="data.drawer.width"
      placement="right"
      @close="
        () => {
          data.drawer.visible = false
          getList()
        }
      "
    >
      <lookMember v-if="data.drawer.visible && data.drawer.type == 'look'" :item="data.drawer.row" @event="onEvent" />
    </a-drawer>
  </div>
</template>

<script setup>
  import { ReloadOutlined, QuestionCircleFilled, CopyOutlined } from '@ant-design/icons-vue'
  import datas from './src/data'
  import lookMember from './components/lookMember.vue'
  import shareWechatEnterprise from './components/shareWechatEnterprise.vue'
  import authorizationStatus from './components/authorizationStatus.vue'
  import successImg from './image/success.png'
  import { qaAuthStatusEnum } from '@/utils'
  import { usePoints } from '@/hooks'
  import { copy } from '@/utils'
  import { useRoute } from 'vue-router'
  import { watch } from 'vue'
  const route = useRoute()
  const { pointData } = usePoints()
  const {
    getUrl,
    pageChange,
    searchForm,
    onShowDialog,
    onShowDraw,
    searchConfig,
    data,
    statusType,
    subjectType,
    getList,
    onEvent,
    asyncUser,
    searchFormDataRef,
    statusClick,
    statusView,
    codeOk,
    confirmCode,
    successClick
  } = datas()
  watch(
    () => route.query?.corp_id,
    (newValue) => {
      if (newValue) {
        setTimeout(() => {
          searchFormDataRef.value.formData.corp_id = newValue
          searchConfig.data.forEach((v) => {
            if (v.field == 'corp_id') {
              v.value = newValue
            }
          })
          data.params.corp_id = newValue
          getList()
        }, 500)
      } else {
        getList()
      }
    },
    {
      deep: true,
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  .warn_text {
    //样式
    background-color: rgb(255, 241, 240);
    padding: 1px 4px 0 4px;
    border-radius: 2px;
  }
  :deep(.ant-image) {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    object-fit: contain;
    img {
      width: 50px;
      height: 50px;
      border-radius: 4px;
      object-fit: contain;
    }
  }
  .page_main_page {
    padding: 8px 0;
    border-radius: 6px;
  }
  .round {
    width: 6px;
    height: 6px;
    background: #999999;
    border-radius: 50%;
    margin-right: 4px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
</style>
