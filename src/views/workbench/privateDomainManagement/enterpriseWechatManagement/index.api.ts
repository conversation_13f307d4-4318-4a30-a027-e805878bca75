import http from '@/utils/request'

/**
 * 角色列表
 */
export const setRoleList = (data) => {
  return http('get', `/shop-common/role/list`, data)
}

/**
 * 权限添加更新
 */
export const setAddRole = (data) => {
  return http('post', `/shop-common/role/role_add`, data)
}
// 部门列表 
export const department =(data: any) => {
  return http('post', `/admin/wechat_work_user/department`, data)
}
//列表
export const workListApi = (data: any) => {
  return http('get', `/common/work/list`, data)
}
//获取授权URl
export const authUrlApi = (data?: any) => {
  return http('get', `/admin/work/auth_url`, data)
}
//是否授权成功
export const checkAuthApi = (data?: any) => {
  return http('get', `/admin/work/check_auth`, data)
}
//获取客服统计
export const quotaApi = (data?: any) => {
  return http('get', `/common/work/quota`, data)
}
//成员列表
export const userListApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/list`, data)
}
//同步成员
export const syncUsersApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/sync_users`, data)
}
//修改企业成员名称
export const setNameApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/set_name`, data)
}
//激活单个员工账号
export const activeOneUserApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/active_one_user`, data)
}
//批量激活员工账号
export const activeMoreUserApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/active_more_user`, data)
}
//转移继承账号
export const transferAccountApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/transfer_account`, data)
}
//设置用户启用状态
export const setStatusApi = (data?: any) => {
  return http('post', `/admin/wechat_work_user/set_status`, data)
}

//分配企业-共享
export const set_allot = (data?: any) => {
  return http('post', `/admin/work/set_allot`, data)
}
// 校验企业微信权限
export const work_check = (data?: any) => {
  return http('get', `/common/work/work_check`, data)
}
// 获取登录二维码
export const get_login_qrcode = (data?: any) => {
  return http('post', `/admin/work/get_login_qrcode`, data)
}
// 获取企微信息
export const get_corp_info = (data?: any) => {
  return http('get', `/admin/work/get_corp_info`, data)
}
// 检查服务商/企微的登录状态
export const check_wx_login_status = (data?: any) => {
  return http('post', `/admin/work/check_wx_login_status`, data)
}
// 自动化配置
export const auto_set_config = (data?: any) => {
  return http('post', `/admin/work/auto_set_config`, data)
}
// 验证码验证接口
export const get_cookie_by_code = (data?: any) => {
  return http('post', `/admin/work/get_cookie_by_code`, data)
}
// 获取验证码
export const send_captcha = (data?: any) => {
  return http('post', `/admin/work/send_captcha`, data)
}
