<template>
  <div>
    <a-form
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width: 80px;' }"
      autocomplete="off"
      :colon="true"
      :rules="rules"
    >
      <a-form-item name="corp_id" label="企微ID">
        <a-input :maxLength="100" show-count placeholder="请输入企微ID" v-model:value="state.form.corp_id" />
      </a-form-item>
    </a-form>
    <div class="text-right">
      <AButton @click="emits('event', { cmd: 'close' })">取消</AButton>
      <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import type { Rule } from 'ant-design-vue/es/form'
  import { reactive, ref } from 'vue'
  import { set_allot } from '../index.api'
  import { message } from 'ant-design-vue'
  const rules: Record<string, Rule[]> = {
    corp_id: [{ required: true, message: '请输入企微ID', trigger: ['blur', 'change'] }]
  }

  const ruleForm = ref()
  const emits = defineEmits(['event'])
  const state = reactive({
    loading: false,
    form: {
      corp_id: undefined
    } as any
  })

  const submitForm = async () => {
    try {
      state.loading = true
      await ruleForm.value?.validate()
      let res: any = await set_allot(state.form)
      if (res.code === 0) {
        message.success(res.msg)

        emits('event', { cmd: 'submit' })
      }
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
</script>

<style lang="scss" scoped>
  .item-link {
    width: 100%;
    background-color: #f2f3f7;
    color: #313233;
    padding: 10px 8px;
    border-radius: 4px;
  }
  .link-list {
    max-height: 255px;
    overflow-y: auto;
  }
</style>
