<template>
  <div class="comp_wrapper">
    <SearchBaseLayout :data="state.searchConfig.data" @changeValue="changeValue" :actions="state.searchConfig.options">
      <template #btns
        ><a-button type="primary" @click="onShowDialog('activeAccount', null)" class="ml-8px"
          >批量激活账户</a-button
        ></template
      >
    </SearchBaseLayout>

    <div class="flex flex-justify-end flex-items-center mt-16px">
      <!-- <div>
        <a-checkbox v-model:checked="state.checked" @change="filterSearch">仅查看互通账号成员</a-checkbox>
        <a-checkbox v-model:checked="state.isNormal" @change="normalSearch">异常成员</a-checkbox>
      </div> -->
    </div>
    <TableZebraCrossing
      :data="state.tableConfig"
      :rowSelection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: selectedChange,
        getCheckboxProps
      }"
      @change="changePages"
    >
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'name'">
          <div class="text_overflow c-#313233">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title> {{ scope.record.name || '--' }} </template>
              <span class="">{{ scope.record.name || '--' }}</span>
            </a-tooltip>
          </div>
          <div class="text_overflow mt-2px number-id font-size-12px line-height-12px">
            ID：
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title> {{ scope.record.user_id || '--' }} </template>
              <span class="">{{ scope.record.user_id || '--' }}</span>
            </a-tooltip>
          </div>
          <div v-if="scope.record.wx_status !== 1" class="flex-y-center mt-4px">
            <ExclamationCircleOutlined class="c-red font-size-12px mr-4px" />
            <div class="c-red font-size-12px h-12px line-height-12px">成员异常</div>
          </div>
        </template>
        <template v-if="scope.column.key === 'department_name'">
          <div class="text_overflow c-#313233 max-w-170px">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title> {{ scope.record.department_name || '--' }} </template>
              <span class="">{{ scope.record.department_name || '--' }}</span>
            </a-tooltip>
          </div>
        </template>
        <template v-if="scope.column.key === 'alias_name'">
          <div class="text_overflow c-#313233">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title> {{ scope.record.alias_name || '--' }} </template>
              <span class="">{{ scope.record.alias_name || '--' }}</span>
            </a-tooltip>
          </div>
        </template>
        <template v-if="scope.column.key === 'start_at'">
          <span v-if="scope.record.is_active == 1">{{ scope.record.start_at }} - {{ scope.record.end_at }}</span>
          <span v-else>--</span>
        </template>
        <template v-if="scope.column.key === 'status'">
          <div class="flex_align_center" style="min-width: 60px">
            <a-switch
              v-model:checked="scope.record.status"
              :checkedValue="1"
              :unCheckedValue="2"
              @click="change($event, scope.record)"
            />
          </div>
        </template>
        <template v-if="scope.column.key === 'action'">
          <a-button
            type="link"
            class="pa-0! h-24px"
            style="margin-right: 10px"
            @click="onShowDialog('editName', scope.record)"
            >编辑</a-button
          >
          <a-button
            type="link"
            class="pa-0! h-24px"
            style="margin-right: 10px"
            @click="onShowDialog('transferAccount', scope.record)"
            v-if="scope.record.is_active == 1"
            >转移互通账号</a-button
          >
          <a-button
            v-else
            type="link"
            class="pa-0! h-24px"
            :disabled="scope.record.wx_status !== 1"
            style="margin-right: 10px"
            @click="onShowDialog('activeAccount', scope.record)"
          >
            激活账户</a-button
          >
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="data.dialog.visible"
      :maskClosable="false"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :footer="null"
    >
      <activeAccount
        v-if="data.dialog.visible && data.dialog.type == 'activeAccount'"
        :item="data.dialog.item"
        :user_ids="currSelectedRows"
        :type="state.type"
        @event="onEvent"
      />
      <editName
        v-if="data.dialog.visible && data.dialog.type == 'editName'"
        :item="data.dialog.item"
        @event="onEvent"
      />
      <transferAccount
        v-if="data.dialog.visible && data.dialog.type == 'transferAccount'"
        :item="data.dialog.item"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive } from 'vue'
  import activeAccount from './activeAccount.vue'
  import editName from './editName.vue'
  import transferAccount from './transferAccount.vue'
  import datas from '../src/linkData'
  import { message } from 'ant-design-vue'
  import { userListApi, setStatusApi } from '../index.api'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  const props = defineProps(['item'])
  const { goodsTableConfig: tableConfig, goodsSearchConfig: searchConfig, fail_status } = datas(props.item)
  const emits = defineEmits(['selectedRows', 'event'])
  const data = reactive({
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    }
  })
  const state = reactive({
    checked: false,
    isNormal: false, //是否正常
    selectedRowKeys: [],
    selectedRows: [],
    tableConfig,
    searchConfig,
    initParams: {
      page: 1,
      page_size: 20
    },
    type: 1
  })
  //已激活的不可激活
  const getCheckboxProps = (record: any) => {
    return {
      disabled: record.is_active == 1 || record.wx_status !== 1
    }
  }
  const filterSearch = async () => {
    try {
      state.initParams.page = 1
      state.tableConfig.pagination.current = 1
      state.tableConfig.loading = true
      let params = {
        ...state.initParams,
        corpid: props.item.corpid
      }
      if (state.checked) {
        params.is_active = 1
      } else {
        delete params.is_active
        delete state.initParams.is_active
      }
      if (params?.department_ids?.length) {
        params.department_ids = params.department_ids.join(',')
      } else {
        params.department_ids = ''
      }
      const result = await userListApi(params)
      if (result.code === 0) {
        state.tableConfig.dataSource = result.data.wechatWorkUsers
        state.tableConfig.pagination.total = result.data?.total || 0
        state.tableConfig.pagination.current = result.data?.page || 1
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
  //正常
  const normalSearch = () => {
    state.initParams.page = 1
    state.tableConfig.pagination.current = 1
    if (state.checked) {
      state.initParams.is_active = 1
    } else {
      delete state.initParams.is_active
    }
    initData()
  }
  const change = async (val: any, item: any) => {
    item.status = val == 1 ? 2 : 1
    try {
      let params = {
        id: item.id,
        corpid: item.corpid,
        status: val
      }
      await setStatusApi(params)
      message.success(`${val == 1 ? '启用' : '禁用'}成功`)
      initData()
    } catch (error) {}
  }
  const onShowDialog = (type: any, item?: any) => {
    data.dialog.item = item
    data.dialog.type = type
    data.dialog.width = '500px'
    switch (type) {
      case 'look':
        data.dialog.visible = true
        data.dialog.width = '800px'
        data.dialog.title = '查看成员'
        break
      case 'editName':
        data.dialog.visible = true
        data.dialog.title = '编辑'
        break
      case 'transferAccount':
        data.dialog.visible = true
        data.dialog.title = '转移互通账号'
        break
      case 'activeAccount':
        if (!item) {
          state.type = 1
          data.dialog.item = {
            corpid: props.item?.corpid
          }
          if (!state.selectedRowKeys.length) {
            message.warning('请选择激活账户')
            data.dialog.visible = false
            return
          }
        } else {
          state.type = 2
          // state.selectedRows = []
          // state.selectedRows.push(data.dialog.item)
        }
        data.dialog.visible = true
        data.dialog.title = '激活账户'
    }
  }
  const currSelectedRows = computed(() => {
    if (state.type === 1) {
      return state.selectedRows
    } else if (state.type === 2) {
      return [data.dialog.item]
    }
  })
  // 关闭弹框
  const onEvent = (val: any) => {
    if (val.cmd == 'close') {
      data.dialog.visible = false
    } else {
      data.dialog.visible = false
      initData()
    }
  }

  const selectedChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRows = selectedRows.filter((it: any) => it.user_id)
    state.selectedRowKeys = selectedRowKeys
  }

  onMounted(() => {
    initData()
  })
  const initData = async () => {
    try {
      state.tableConfig.loading = true
      let params = {
        ...state.initParams,
        corpid: props.item.corpid
        // is_fail: state.isNormal ? 1 : 0
      }
      if (params?.department_ids?.length) {
        params.department_ids = params.department_ids.join(',')
      } else {
        params.department_ids = ''
      }
      const result = await userListApi(params)
      if (result.code === 0) {
        state.tableConfig.dataSource = result.data.wechatWorkUsers
        state.tableConfig.pagination.total = result.data?.total || 0
        state.tableConfig.pagination.current = result.data?.page || 1
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const changeValue = (data: any) => {
    let is_active = undefined
    let is_fail = undefined
    if (data.formData?.is_active?.length > 0) {
      if (data.formData.is_active.length == 1) {
        is_active = data.formData.is_active[0] == 1 ? 1 : undefined
        is_fail = data.formData.is_active[0] == 2 ? 1 : undefined
      }
      if (data.formData.is_active.length == 2) {
        is_active = 1
        is_fail = 1
      }
    } else {
      is_active = undefined
      is_fail = undefined
    }
    state.initParams = { ...state.initParams, ...data.formData, page: 1, is_active: is_active, is_fail: is_fail }
    initData()
  }
  const changePages = (data: any) => {
    state.initParams.page = data.current
    state.initParams.page_size = data.pageSize
    state.tableConfig.pagination.pageSize = data.pageSize
    initData()
  }
</script>

<style scoped lang="scss">
  :deep(.ant-btn-link) {
    padding: 0px !important;
  }
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .goods-warp {
    display: flex;
    .goods-warp-img {
      width: 40px;
      height: 40px;
      border-radius: 2px;
      flex: none;
      margin-right: 8px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .goods-warp-content {
      flex: 1;
      padding: 0 10px;
    }
    .text-overflow-row1 {
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 1;
    }
  }
</style>
