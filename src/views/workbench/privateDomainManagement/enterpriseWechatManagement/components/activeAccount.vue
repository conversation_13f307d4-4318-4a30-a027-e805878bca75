<template>
  <div>
    <a-form :model="data.form" ref="ruleForm" :labelCol="{ style: 'width:70px;' }">
      <div>
        <div class="name_title">成员名称</div>
        <div class="name_list mt-8px p-8px mb-18px">{{ data.name }}</div>
      </div>
      <!-- <a-form-item label="订单ID" name="order_id" required>
        <a-input v-model:value="data.form.order_id" @keydown.space.prevent placeholder="请输入企微订单ID" />
      </a-form-item> -->
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { activeOneUserApi, activeMoreUserApi } from '../index.api'
  const ruleForm = ref(null)
  const props = defineProps(['item', 'user_ids', 'type'])
  const emit = defineEmits(['event'])
  const rules = {
    order_id: [{ required: true, message: '请输入企微订单ID', trigger: ['blur'] }]
  }
  const data = reactive({
    loading: false,
    name: '',
    form: {
      order_id: ''
    }
  })

  onMounted(() => {})

  const close = () => {
    ruleForm.value.clearValidate()
    clearData()
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.order_id = ''
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ...data.form,
        user_ids: props.user_ids.map((it) => it.user_id),
        corpid: props.item?.corpid
      }

      console.log(params, 'params')
      if (props.type == 1) {
        await activeMoreUserApi(params)
      } else {
        await activeOneUserApi(params)
      }
      message.success('操作成功')
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }

  watchEffect(() => {
    if (props.user_ids) {
      data.name = props.user_ids.map((it) => it.name).join(',')
      console.log('props.user_idsprops.user_ids', data.name)
    }
  })
</script>
<style lang="scss" scoped>
  .name_title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #656d7d;
  }
  .name_list {
    background: #f7f9fc;
    border-radius: 6px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #313233;
  }

  :deep(.el-cascader) {
    width: 100%;
  }
  :deep(.el-checkbox) {
    margin-right: 0;
  }
  .footer {
    text-align: end;
  }
  .check {
    margin-left: 10px;
    color: #404040;
  }
  .mar {
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-top: -13px;
    }
  }
</style>
