<template>
  <div>
    <a-form :model="data.form" ref="ruleForm" :labelCol="{ style: 'width:80px;' }">
      <a-form-item label="接收账号" name="takeover_userid" required>
        <a-select
          ref="select"
          v-model:value="data.form.takeover_userid"
          placeholder="请选择接收互通账号成员"
          :options="data.list"
          :show-search="true"
          allowClear
          @popupScroll="onScroll"
          :filterOption="onFiterOption"
        ></a-select>
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import { message } from 'ant-design-vue'
  import { userListApi, transferAccountApi } from '../index.api'
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])
  const data = reactive({
    loading: false,
    list: [],
    resList: [],
    form: {
      takeover_userid: null
    },
    param: {
      page: 1,
      page_size: 20
    }
  })

  onMounted(() => {
    data.param.page = 1
    initList()
  })
  // select搜索
  const onFiterOption = (value, option) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }
  //下拉刷新
  const onScroll = (event) => {
    const target = event.target
    if (!target) return
    const bottom = target.scrollHeight === target.scrollTop + target.clientHeight
    if (bottom) {
      if (data.resList.length > 0) {
        data.param.page++
        setTimeout(() => {
          initList()
        }, 300)
      }
    }
  }
  const initList = async () => {
    let params = {
      page: data.param.page,
      page_size: 20,
      is_active: 2,
      corpid: props.item?.corpid
    }
    const result = await userListApi(params)
    let list = (result.data.wechatWorkUsers || []).map((item) => {
      {
        return {
          label: item.name,
          value: item.user_id
        }
      }
    })
    data.resList = list
    data.list.push(...list)
  }

  const close = () => {
    clearData()
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.takeover_userid = null
  }

  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const add = async () => {
    try {
      data.loading = true
      let params = {
        ...data.form,
        corpid: props.item?.corpid,
        handover_userid: props.item?.user_id
      }
      let res = await transferAccountApi(params)
      message.success('转移后原成员的互通账号无有效期，转移到新的成员上并回显剩下的有效期')
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .footer {
    text-align: end;
  }
</style>
