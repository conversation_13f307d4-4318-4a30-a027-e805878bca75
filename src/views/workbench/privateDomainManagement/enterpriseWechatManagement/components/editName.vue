<template>
  <div>
    <a-form :model="data.form" ref="ruleForm" :labelCol="{ style: 'width:110px;' }">
      <a-form-item label="内部成员名称" name="name" required>
        <a-input
          v-model:value="data.form.name"
          :maxlength="20"
          @keydown.space.prevent
          placeholder="请输入内部成员名称"
        />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button :mr="20" @click="close">取消</a-button>
      <a-button type="primary" @click="submitForm(ruleForm)" :loading="data.loading">确定</a-button>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'
  import { message } from 'ant-design-vue'
  import { setNameApi } from '../index.api'
  import { kMaxLength } from 'buffer'
  const cascader = ref(null)
  const ruleForm = ref(null)
  const props = defineProps(['item'])
  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    areaList: [],
    form: {
      name: ''
    }
  })

  onMounted(() => {
    //   initList()
  })
  const initList = async () => {
    const result = await get_area_list()
    data.areaList = result.data
  }

  const close = () => {
    ruleForm.value.clearValidate()
    clearData()
    emit('event', { cmd: 'close' })
  }
  const clearData = () => {
    data.form.name = ''
  }
  const submitForm = (formEl) => {
    formEl
      .validate()
      .then(() => {
        add()
      })
      .catch((error) => {
        console.error(error)
      })
  }
  const add = async () => {
    try {
      data.loading = true
      let params = {
        ...data.form,
        id: props.item?.id,
        corpid: props.item?.corpid
      }

      console.log(params, 'params')

      let res = await setNameApi(params)
      message.success('操作成功')
      emit('event', { cmd: 'edit' })
      data.loading = false
    } catch (error) {
      data.loading = false
      console.error(error)
    }
  }

  watchEffect(() => {
    if (props.item?.id) {
      data.form.name = props.item?.alias_name
    }
  })
</script>
<style lang="scss" scoped>
  .name_title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #656d7d;
  }
  .name_list {
    background: #f7f9fc;
    border-radius: 6px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #313233;
  }

  :deep(.el-cascader) {
    width: 100%;
  }
  :deep(.el-checkbox) {
    margin-right: 0;
  }
  .footer {
    text-align: end;
  }
  .check {
    margin-left: 10px;
    color: #404040;
  }
  .mar {
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-top: -13px;
    }
  }
</style>
