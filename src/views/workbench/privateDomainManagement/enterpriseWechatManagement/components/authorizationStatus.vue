<template>
  <div>
    <div class="step-box flex flex-justify-between">
      <div class="flex flex-center" v-for="(item, index) in data.steps" :key="index">
        <div class="text-center min-w-72px">
          <div
            :class="[
              'step-num pt-10px text-10px line-height-10px',
              data.stepsCurrent >= index ? 'step_default' : 'step_finish'
            ]"
          >
            <span class="w-8px inline-block">{{ index + 1 }}</span>
          </div>
          <div :class="['text-12px', data.stepsCurrent >= index ? 'c-#FE9D35' : 'c-#313233']">{{ item.title }}</div>
        </div>
        <div v-if="index != 2" class="ml-34px">
          <img v-if="data.stepsCurrent > index" src="@/assets/images/points/select_r.png" alt="" class="w-132px h-8px" />
          <img v-else src="@/assets/images/points/right.png" alt="" class="w-132px h-8px" />
        </div>
      </div>
    </div>
    <div class="flex flex-col flex-items-center border border-#FFEBD7 mt-14px rounded-8px boxGg"
      :class="[data.stepsCurrent==2 && data.setupType==2 ? 'h-376px' : 'h-430px', isMobile ? 'w-100%' : 'w-656px']"
    >
      <div class="flex mt-56px flex-col flex-items-center" v-if="data.stepsCurrent == 0">
        <div class="c-#FE4D4F font-size-12px fw-500">
          <span>请企微管理员扫二维码添加应用</span>
          <a-tooltip color="#fff" :overlayInnerStyle="{
            width: isMobile ? '215px' : '430px',
            padding: '10px 15px',
          }">
            <template #title>
              <div class="c-#000 mb-8px font-size-15px">示例图片</div>
              <a-image v-if="isMobile" :src="example" :height="145" :width="200" />
              <a-image v-else :src="example" :height="290" :width="400" />
            </template>
            <span class="ml-4px c-#1B80FF cursor-pointer">查看示例</span>
          </a-tooltip>
        </div>
        <div class="w-200px h-200px block pa-16px mt-40px bg-#fff border border-dashed border-#DADADA border-1px rounded-8px overflow-hidden" >
          <img class="" :src="data.QrSrc" alt="" />
        </div>
      </div>
      <div v-if="data.stepsCurrent == 1" class="flex flex-col flex-items-center">
        <img class="w-150px h-120px block mt-110px" :src="loadingImg" alt="">
        <div class="font-size-14px c-#9C9C9C mt-20px">请联系商务配置应用，本流程预计5-10分钟完成</div>
      </div>
      <div v-if="data.stepsCurrent == 2" class="mt-16px flex-col flex-items-center" >
        <div class="flex">
          <div class="relative border border-#ececec rounded-4px w130px h30px flex-center mx-8px bg-#fefdfc cursor-pointer overflow-hidden" :class="{active: data.setupType==1}"
            @click="data.setupType=1"
          >
            <span>自动化配置</span>
            <a-tag color="success" class="w-32px h-16px font-size-12px line-height-16px pa-0px flex-center ml-4px mr-0px">推荐</a-tag>
            <div v-if="data.setupType == 1">
              <div class="triangle absolute top-0 right-0"></div>
              <CheckOutlined class="absolute z-10 top-1px right-1px c-#fff font-size-9px" />
            </div>
          </div>
          <div class="relative border border-#ececec rounded-4px w130px h30px flex-center mx-8px bg-#fefdfc cursor-pointer overflow-hidden" :class="{active: data.setupType==2}"
            @click="data.setupType=2"
          >
            <span>手动配置</span>
            <div v-if="data.setupType == 2">
              <div class="triangle absolute top-0 right-0"></div>
              <CheckOutlined class="absolute z-10 top-1px right-1px c-#fff font-size-9px" />
            </div>
          </div>
        </div>
        <div class="flex-center flex-col w-100%" v-if="data.setupType == 1">
          <div class="c-#FE4D4F font-size-12px mt-10px mb-26px fw-500">注释：扫码登录企微后台-全自动化配置，无需手动配置</div>
          <div class="w-200px h-200px flex-center bg-#fff border border-dashed border-#DADADA border-1px rounded-8px overflow-hidden" >
            <div class="w-146px h-146px">
              <img v-if="data.loginQrcode.qr_code" class="w-146px h-146px block" :src="data.loginQrcode.qr_code" alt="">
            </div>
          </div>
        </div>
        <div class="flex-center flex-col w-100%" v-if="data.setupType == 2">
          <div class="c-#FE4D4F font-size-12px mt-10px mb-2px fw-500">
            <span>注释：请根据以下示例图进行配置，详细说明查看</span>
            <span class="ml-4px c-#1B80FF cursor-pointer" @click="openUrl('https://doc.weixin.qq.com/doc/w3_AdAAJwaeAKsCNTapoUYXoRD2Fg5Wv?')" >操作指南</span>
          </div>
          <div class="flex flex-justify-center mb-7px">
            <div class="flex flex-center" v-for="(item, index) in 3" :key="index">
              <div class="text-center mb--6px">
                <div :class="[
                  'pt-2px text-10px line-height-10px',
                  data.setupIndex >= index ? 'num_default' : 'num_finish'
                ]" > <span class="w-6px inline-block">{{ index + 1 }}</span> </div>
              </div>
              <div v-if="index != 2" class="mx-11px">
                <img v-if="data.setupIndex > index" src="@/assets/images/points/select_r.png" alt="" class="w-62px" />
                <img v-else src="@/assets/images/points/right.png" alt="" class="w-62px" />
              </div>
            </div>
          </div>
          <a-image v-if="data.setupIndex==0" :src="setup2" :height="247" :width="342" />
          <a-image v-if="data.setupIndex==1" :src="setup1" :height="247" :width="228" />
          <a-image v-if="data.setupIndex==2" :src="setup3New" :height="247" :width="521" />
          <!-- <div class="flex flex-center" v-if="data.setupIndex==2">
            <a-image :src="setup3" :height="247" :width="190" />
            <a-image :src="setup32" :height="120" :width="345" />
          </div> -->
        </div>
      </div>
    </div>
    <div class="flex flex-justify-end mt-20px" v-if="data.stepsCurrent==2 && data.setupType==2">
      <a-button class="w90px" v-show="data.setupIndex>0" @click="data.setupIndex>0 && data.setupIndex--">上一步</a-button>
      <a-button class="w90px" v-if="data.setupIndex<2" type="primary" @click="data.setupIndex<2 && data.setupIndex++">下一步</a-button>
      <a-button class="w90px" v-else type="primary" @click="complete">完成授权</a-button>
    </div>
    <a-modal v-model:open="tipsOpen" title="" centered :footer="null" width="420px">
      <div class="font-size-18px fw-700 c-#000 flex flex-items-center">
        <InfoCircleFilled class="mr-8px c-#faac15 font-size-20px" />
        <span>提示</span>
      </div>
      <div class="mt-16px mb-8px">系统检测到当前企微账号暂未授权相关功能权限，详细说明查看操作文档</div>
      <div class="c-#1B80FF underline cursor-pointer mb-30px" @click="openUrl('https://doc.weixin.qq.com/doc/w3_AdAAJwaeAKsCNTapoUYXoRD2Fg5Wv?')">https://doc.weixin.qq.com/doc/w3_AdAAJwaeAKsCNTapoUYXoRD2Fg5Wv?</div>
    </a-modal>
    <a-modal v-model:open="data.codeOpen" title="" centered width="420px" @ok="emit('codeOk', {})">
      <div class="font-size-18px fw-700 c-#000 flex flex-items-center">
        <InfoCircleFilled class="mr-8px c-#faac15 font-size-20px" />
        <span>提示</span>
      </div>
      <div class="flex flex-items-center">
        <span>短信验证码</span>
        <a-input
          v-model:value="data.code"
          class="flex-1 ma-8px"
          placeholder="请输入短信验证码"
          :maxLength="6"
        ></a-input>
        <a-button class="w-100px" type="primary" :disabled="data.codeTime>0" @click="emit('confirmCode', {})">
          {{ data.codeTime>0 ? `${data.codeTime}s` : '获取验证码' }}
        </a-button>
      </div>
    </a-modal>
  </div>
</template>
<script setup>
  import { ref, onUnmounted } from 'vue'
  import { work_check } from '../index.api'
  import { Modal } from 'ant-design-vue';
  import { CheckOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
  import {useApp } from '@/hooks'
  const { isMobile } = useApp()
  const props = defineProps(['data'])
  import example from '../image/example.png'
  import setup1 from '../image/setup1.png'
  import setup2 from '../image/setup2.png'
  import setup3 from '../image/setup3.png'
  import setup32 from '../image/setup3-2.png'
  import setup3New from '../image/setup3-new.png'
  import loadingImg from '@/assets/images/loading.gif'
  const emit = defineEmits(['event', 'codeOk', 'confirmCode'])
  const openUrl = (url) => {
    window.open(url, '_blank')
  }
  const tipsOpen = ref(false)
  const timeout = ref(0)
  const complete = async (tips=true) => {
    try {
      clearTimeout(timeout.value)
      const res = await work_check({ corp_id: props.data.corp_id })
      if (!res.data.auth) {
        tips && (tipsOpen.value = true)
        timeout.value = setTimeout(() => {
          complete(false)
        }, 3000);
      } else {
        emit('event', {cmd: 'manual'})
      }
    } catch {}
  }
  onUnmounted(() => {
    clearTimeout(timeout.value)
  })
</script>
<style lang="scss" scoped>
  .boxGg {
    background: linear-gradient( 180deg, #FFFDFB 0%, #FFFFFF 100%);
  }
  .step-box {
    border-radius: 2px;
    padding: 0px 0;
    .step-num {
      width: 30px;
      height: 30px;
      margin: 0 auto 7px;
      border-radius: 50%;
      color: #fff;
    }
    .step_default {
      width: 30px;
      height: 30px;
      background: url('@/assets/images/points/step_default.png') no-repeat;
      background-size: 100% 100%;
    }
    .step_finish {
      width: 30px;
      height: 30px;
      background: url('@/assets/images/points/step_finish.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .active {
    border: 1px solid #FE9D35;
    background: #ffecd7;
    color: #FE9D35;
  }
  .triangle {
    width: 0;
    height: 0;
    border-left: 18px solid transparent;
    border-right: 0px solid transparent;
    border-top: 18px solid #FE9D35; /* 颜色可自定义 */
  }
  .num_default {
    width: 16px;
    height: 16px;
    border: 1px solid #FE9D35;
    border-radius: 50%;
    color: #FE9D35;
  }
  .num_finish {
    width: 16px;
    height: 16px;
    border: 1px solid #FFECD9;
    border-radius: 50%;
    color: #333;
  }
  ::v-deep .ant-image-img {
    width: 100%!important;
  }
</style>