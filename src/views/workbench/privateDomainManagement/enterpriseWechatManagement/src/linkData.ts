import { reactive } from 'vue'
import { department } from '../index.api'

export default function datas(item: any) {
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#FF4D4F',
        text: '禁用'
      },
      1: {
        color: '#52C41A',
        text: ' 启用'
      }
    }
    return (status as any)[val]
  }
  // 选择弹窗里的信息
  const goodsTableConfig = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    defaultExpandedRowKeys: [],
    dataSource: [],
    rowSelection: {},
    scroll: { scrollToFirstRowOnChange: false },
    columns: [
      {
        title: '企微成员名称/ID',
        dataIndex: 'name',
        key: 'name',
        width: 120
      },
      {
        title: '所属部门',
        dataIndex: 'department_name',
        key: 'department_name',
        width: 120
      },
      {
        title: '内部成员名称',
        dataIndex: 'alias_name',
        key: 'alias_name',
        width: 120
      },
      {
        title: '成员状态',
        key: 'status',
        dataIndex: 'status',
        width: 60
      },
      {
        title: '互通账号有效期',
        key: 'start_at',
        dataIndex: 'start_at',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        dataIndex: 'action',
        width: 115
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 20,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  }

  const goodsSearchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入企微成员名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'user_id',
        value: undefined,
        props: {
          placeholder: '请输入企微成员ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'treeSelect',
        field: 'department_ids',
        value: undefined,
        span: 6,
        multiple: true,
        props: {
          options: [],
          placeholder: '请选择所属部门'
        },
        fieldNames: {
          label: 'name',
          value: 'department_id'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        field: 'is_active',
        type: 'select',
        value: undefined,
        props: {
          options: [
            { label: '互通成员', value: 1 },
            { label: '异常成员', value: 2 }
          ],
          mode: 'multiple',
          showArrow: true,
          placeholder: '请选择成员类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 8,
      //   md: 8,
      //   lg: 8,
      //   xl: 8,
      //   xxl: 5
      // }
    }
  })
  try {
    department({
      corpid: item.corpid
    }).then((res) => {
      if (res.data?.length) {
        goodsSearchConfig.data.forEach((item) => {
          if (item.field == 'department_ids') {
            item.props.options = res.data
          }
        })
      }
    })
  } catch {}
  //异常状态
  const fail_status = (val: string | number) => {
    let status = {
      2: '已禁用',
      4: '未激活',
      5: '已退出企业',
      6: '已删除'
    }
    return (status as any)[val]
  }
  return { statusType, fail_status, goodsSearchConfig, goodsTableConfig }
}
