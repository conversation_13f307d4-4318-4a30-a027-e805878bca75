import { reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { workList<PERSON><PERSON>, authUrl<PERSON>pi, checkAuth<PERSON>pi, syncUsers<PERSON><PERSON>, get_corp_info, get_login_qrcode, check_wx_login_status, auto_set_config, get_cookie_by_code, send_captcha } from '../index.api'
import { debounce } from 'lodash-es'
import dayjs from 'dayjs'
import { useApp } from '@/hooks'
const { isMobile } = useApp()
export default function datas() {
  const searchFormDataRef = ref()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'corp_id',
        value: undefined,
        props: {
          placeholder: '请输入企微ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入企微名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        field: 'status',
        type: 'select',
        value: undefined,
        props: {
          options: [
            { label: '授权成功',  value: 1 },
            { label: '取消授权', value: 2 },
          ],
          placeholder: '请选择授权状态'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'select',
        field: 'permission_status',
        value: undefined,
        props: {
          options: [
            { label: '待配置', value: 1 },
            { label: '配置中', value: 4 },
            { label: '配置成功', value: 2 },
            { label: '配置失败', value: 3 },
          ],
          placeholder: '请选择配置状态'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'updated_time',
        value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        props: {
          placeholder: ['加粉开始日期', '加粉结束日期']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1000
    },
    dataSource: [],
    columns: [
      {
        title: '企微头像',
        dataIndex: 'corp_square_logo_url',
        key: 'corp_square_logo_url',
        width: 87
      },
      {
        title: '企微名称',
        dataIndex: 'corp_name',
        key: 'corp_name',
        width: 200,
        slot: true
      },
      {
        title: '获客助手使用概况',
        dataIndex: 'work_quota',
        key: 'work_quota',
        width: 180
      },
      {
        title: '用户规模',
        dataIndex: 'corp_user_max',
        key: 'corp_user_max',
        width: 100
      },
      {
        title: '加粉数',
        dataIndex: 'add_fans_num',
        key: 'add_fans_num',
        width: 100
      },
      {
        title: '开口率',
        dataIndex: 'chat_rate',
        key: 'chat_rate',
        width: 100
      },
      {
        title: '所属行业',
        dataIndex: 'corp_industry',
        key: 'corp_industry',
        width: 120
      },
      {
        title: '企业类型',
        dataIndex: 'subject_type',
        key: 'subject_type',
        width: 100
      },
      {
        title: '授权状态',
        dataIndex: 'status',
        key: 'status',
        width: 110
      },
      {
        title: '配置状态',
        dataIndex: 'permission_status',
        key: 'permission_status',
        width: 150
      },
      {
        title: '创建人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        width: 120
      },
      {
        title: '授权时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 160
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 160,
        fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    QrSrc: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    stepsCurrent: 0,
    corp_id: '',
    steps: [
      { title: '扫码添加应用', },
      { title: '应用发布', },
      { title: '权限配置' },
    ],
    setupType: 1, // 1自动化 2手动
    setupIndex: 0,
    prevDisabled: false,
    params: {
      page: 1,
      page_size: 20,
      fans_start_time: dayjs().format('YYYY-MM-DD 00:00:00'),
      fans_end_time: dayjs().format('YYYY-MM-DD 23:59:59')
    },
    dialog: {
      visible: false,
      title: '',
      width: '',
      item: {},
      type: ''
    },
    successOpen: false,
    drawer: {
      visible: false,
      title: '查看成员',
      width: isMobile.value ? '100%' : '80%',
      type: '',
      landTitleA: '',
      landTitleB: '',
      AData: [],
      BData: [],
      row: {}
    },
    loginQrcode: {},
    login_status: '',
    tl_key: '',
    codeOpen: false,
    code: '',
    codeTime: 60,
    codeTimer: 0,
    permission_status: {
      1: {text: '待配置', color: '#b7aeb0'},
      4: {text: '配置中', color: '#fe9d35'},
      2: {text: '配置成功', color: '#50c41a'},
      3: {text: '配置失败', color: '#FF4D4F'},
    }
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#FF4D4F',
        text: '取消授权'
      },
      1: {
        color: '#52C41A',
        text: '授权成功'
      }
    }
    return (status as any)[val]
  }
  //企业类型
  const subjectType = (val: string | number) => {
    let status = {
      1: {
        text: '企业'
      },
      2: {
        text: '政府以及事业单位'
      },
      3: {
        text: '其他组织'
      },
      4: {
        text: '团队号'
      }
    }
    return (status as any)[val]
  }

  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await workListApi(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    if (v.formData.updated_time?.length && v.formData.updated_time[0] && v.formData.updated_time[1]) {
      data.params.fans_start_time = dayjs(v.formData.updated_time[0]).format('YYYY-MM-DD 00:00:00')
      data.params.fans_end_time = dayjs(v.formData.updated_time[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      data.params.fans_start_time = undefined
      data.params.fans_end_time = undefined
    }
    delete data.params.updated_time
    if (!v.status) {
      data.params.fans_start_time = dayjs().format('YYYY-MM-DD 00:00:00')
      data.params.fans_end_time = dayjs().format('YYYY-MM-DD 23:59:59')
      searchFormDataRef.value.formData.updated_time = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type: any, item: any) => {
    data.dialog.item = item
    data.dialog.visible = true
    data.dialog.type = type
    data.dialog.width = '500px'
    switch (type) {
      case 'look':
        data.dialog.width = '1100px'
        data.dialog.title = '查看成员'
        break
      case 'Qr':
        data.dialog.width = '704px'
        data.dialog.title = '企微授权配置步骤'
        data.stepsCurrent = 0
        data.prevDisabled = false
        data.corp_id = ''
        break
      case 'share':
        data.dialog.width = '600px'
        data.dialog.title = '共享企微'
        break
    }
  }
  const onShowDraw = (type: any, item: any) => {
    data.drawer.row = item
    data.drawer.visible = true
    data.drawer.type = type
    switch (type) {
      case 'look':
        data.drawer.width = isMobile.value ? '100%' : '80%'
        data.drawer.title = '查看成员'
        break
    }
  }
  // 关闭弹框
  const onEvent = (val?: any) => {
    if (val.cmd == 'close') {
      data.dialog.visible = false
    } else if (val.cmd == 'manual') {
      data.successOpen = true
      getList()
    } else {
      data.dialog.visible = false
      getList()
    }
    clearInterval(timer.value)
    clearInterval(data.codeTimer)
  }
  let timer: any = ref(null)
  //获取二维码
  const getUrl = debounce(async () => {
    try {
      let res = await authUrlApi()
      data.QrSrc = res.data.Qr
      if (data.QrSrc) {
        onShowDialog('Qr', data.QrSrc)
        timer.value = setInterval(() => {
          checkAuth()
        }, 5000)
      }
    } catch (error) {}
  }, 500)
  //检查是否授权
  const checkAuth = async () => {
    try {
      let res = await checkAuthApi()
      if (res.data?.auth) {
        data.corp_id = res.data.corpid
        data.stepsCurrent = 1
        clearInterval(timer.value)
        getList()
        timer.value = setInterval(() => {
          getCorpInfo()
        }, 5000)
      }
    } catch (error) {}
  }
  // 获取企微信息
  const getCorpInfo = async () => {
    try {
      let res = await get_corp_info({ corpid: data.corp_id })
      if (res.data.app_status == 4) {
        clearInterval(timer.value)
        data.stepsCurrent = 2
        getLoginQrcode()
      }
    } catch (error) {}
  }
  // 获取登录二维码
  const getLoginQrcode = async () => {
    try {
      let res = await get_login_qrcode({ type: 1 })
      data.loginQrcode = res.data
      data.login_status = ''
      checkLoginStatus()
    } catch (error) {}
  }
  // 检查服务商/企微的登录状态
  const codeOk = async () => {
    try {
      if (data.code.length != 6) return message.error('请输入正确的验证码')
      const res = await get_cookie_by_code({
        corpid: data.corp_id,
        code: data.code,
        tl_key: data.tl_key,
        type: 1
      })
      if (res.data.status == 1) {
        await auto_set_config({ corpid: data.corp_id })
        data.codeOpen = false
        data.successOpen = true
        getList()
      }
    } catch {
    }
  }
  const confirmCode = async () => {
    try {
      const res = await send_captcha({
        corpid: data.corp_id,
        tl_key: data.tl_key,
      })
      data.codeOpen = true
      data.codeTime = 60
      data.codeTimer = setInterval(() => {
        data.codeTime--
        if (data.codeTime <= 0) {
          clearInterval(data.codeTimer)
        }
      }, 1000)
    } catch {}
  }
  const checkLoginStatus = async () => {
    try {
      if (!data.dialog.visible || data.dialog.type!='Qr') return
      let res = await check_wx_login_status({ 
        type: 1,
        corpid: data.corp_id,
        qr_code_key: data.loginQrcode.qr_code_key,
        login_status: data.login_status
      })
      if (res.code != 0) return
      if (res.data.login_status == 'QRCODE_SCAN_FAIL') {
        data.dialog.visible = false
        message.error('扫码登录失败')
        data.login_status = ''
        return
      }
      data.login_status = res.data.login_status
      if (res.data.status == 1) {
        if (res.data.tl_key) {
          data.tl_key = res.data.tl_key
          data.codeOpen = true
          data.codeTime = 60
          data.codeTimer = setInterval(() => {
            data.codeTime--
            if (data.codeTime <= 0) {
              clearInterval(data.codeTimer)
            }
          }, 1000)
        } else {
          await auto_set_config({ corpid: data.corp_id })
          data.successOpen = true
        }
        getList()
      } else {
        setTimeout(() => {
          checkLoginStatus()
        }, 1000);
      }
    } catch (error) {
      if (error && typeof error === 'object' && 'code' in error && error.code != 0) {
        data.dialog.visible = false
        getList()
      } else {
        setTimeout(() => {
          checkLoginStatus()
        }, 1000);
      }
    }
  }
  // 成功后点击好的
  const successClick = () => {
    data.successOpen = false
    data.dialog.visible = false
  }
  //同步成员
  const asyncUser = async (row: any) => {
    try {
      row.loading = true
      await syncUsersApi({ corpid: row.corpid })
      message.success('同步成功')
      row.loading = false
    } catch (error) {
      row.loading = false
    }
  }
  const statusClick = async (row: any) => {
    if ([1,4].includes(row.permission_status) && row.status!=2) {
      data.dialog.visible = true
      data.dialog.type = 'Qr'
      data.dialog.width = '704px'
      data.dialog.title = '企微授权配置步骤'
      data.prevDisabled = true
      data.corp_id = row.corpid
      data.stepsCurrent = 1
      getCorpInfo()
      timer.value = setInterval(() => {
        getCorpInfo()
      }, 5000)
    }
  }
  const statusView = async (row: any) => {
    try {
      let res = await get_corp_info({ corpid: row.corpid })
      row.app_status = res.data.app_status
      row.permission_status = res.data.permission_status
      row.status = res.data.status
      row.work_status = res.data.work_status
    } catch { }
  }
  return {
    pageChange,
    searchForm,
    onShowDialog,
    onShowDraw,
    searchConfig,
    data,
    statusType,
    subjectType,
    getList,
    onEvent,
    getUrl,
    asyncUser,
    searchFormDataRef,
    statusClick,
    statusView,
    codeOk,
    confirmCode,
    successClick
  }
}
