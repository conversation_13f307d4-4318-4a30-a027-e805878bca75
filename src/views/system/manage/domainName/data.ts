import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getDomainList, domainDelete } from './index.api'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'domain',
        value: undefined,
        props: {
          placeholder: '请输入域名'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })
  const state = reactive({
    modalConfigData: {
      open: false,
      title: '新增',
      type: 'add',
      data: {}
    }
  })
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '域名',
        dataIndex: 'domain',
        key: 'domain',
        width: 200,
        slot: true
      },
      {
        title: '公钥',
        dataIndex: 'public_key',
        key: 'public_key',
        width: 100,
        slot: true
      },
      {
        title: '私钥',
        dataIndex: 'private_key',
        key: 'private_key',
        width: 100,
        slot: true
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        slot: true
      },
      {
        title: '可使用账号',
        dataIndex: 'user_num',
        key: 'user_num',
        width: 100,
        slot: true
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
        slot: true
      },
      {
        title: '创建人',
        dataIndex: 'user_name',
        key: 'user_name',
        width: 120,
        slot: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await getDomainList(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    data.params.page = 1
    getList()
  }
  const delItem = async (row) => {
    try {
      await domainDelete({ id: row.id })
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  const rules = {
    domain: [
      {
        required: true,
        message: '请输入域名',
        trigger: ['blur']
      }
    ],
    media_type: [
      {
        required: true,
        message: '请选择媒体类型',
        trigger: ['change']
      }
    ],
    media_type_arr: [
      {
        required: true,
        message: '请选择媒体类型',
        trigger: ['change']
      }
    ],
    // public_key: [
    //   {
    //     required: true,
    //     message: '请输入公钥',
    //     trigger: ['blur']
    //   }
    // ],
    // private_key: [
    //   {
    //     required: true,
    //     message: '请输入私钥',
    //     trigger: ['blur']
    //   }
    // ]
  }

  return {
    rules,
    state,
    pageChange,
    searchForm,
    delItem,
    searchConfig,
    data,
    getList
  }
}
