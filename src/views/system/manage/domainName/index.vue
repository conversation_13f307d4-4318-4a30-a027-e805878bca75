<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>域名管理</div>
      </template>
      <template #extra>
        <a-button v-auth="['domainNameAdd']" type="primary" @click="onShowDialog('add')">新增</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'user_num'">
              <a-button class="p-0" size="small" type="link" @click="setAccountList(scope.record)">{{
                scope.record.user_num
              }}</a-button>
            </template>
            <template v-if="scope.column.key === 'public_key'">
              <div>{{ scope.record.public_key ? '已配置' : '未配置' }}</div>
            </template>
            <template v-if="scope.column.key === 'private_key'">
              <div>{{ scope.record.private_key ? '已配置' : '未配置' }}</div>
            </template>
            <template v-if="scope.column.key === 'status'">
              <a-popconfirm
                title="停用后，则域名在提取链接时无法选择"
                ok-text="停用"
                cancel-text="取消"
                :disabled="scope.record.company_status != 1 ? true : scope.record.status == 2"
                @confirm="
                  () => {
                    scope.record.status = 2
                    changeStatus(scope.record)
                  }
                "
              >
                <a-tooltip>
                  <template #title v-if="scope.record.company_status != 1">
                    <div>域名配置中，暂不支持启用</div>
                  </template>
                  <a-switch
                    v-model:checked="scope.record.status"
                    :checkedValue="1"
                    :unCheckedValue="2"
                    :disabled="scope.record.company_status != 1"
                    @change="
                      () => {
                        if (scope.record.status == 2) {
                          return (scope.record.status = 1)
                        }
                        changeStatus(scope.record)
                      }
                    "
                  />
                </a-tooltip>
              </a-popconfirm>
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="handle_btns">
                <div class="flex_align_center">
                  <span
                    v-auth="['domainNameEdit']"
                    style="margin-right: 10px"
                    @click="onShowDialog('edit', scope.record)"
                    >编辑</span
                  >
                  <a-popconfirm
                    title="删除后将无法选择该域名创建投放链接"
                    placement="topRight"
                    @confirm="delItem(scope.record)"
                  >
                    <span v-auth="['domainNameDelete']">删除</span>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
      @cancel="modalCancel"
      centered
      width="800px"
    >
      <Editor
        v-if="['edit', 'add'].includes(state.modalConfigData.type)"
        v-model:data="state.modalConfigData.data"
        ref="editorRef"
        :config="state.modalConfigData"
        @success="successSave"
      />
      <AccountList
        v-if="['accountList'].includes(state.modalConfigData.type)"
        v-model:data="state.modalConfigData.data"
        ref="accountListRef"
        :config="state.modalConfigData"
        @success="successSave"
      />
      <template #footer>
        <a-button @click="modalCancel">取消</a-button>
        <a-button
          v-if="['edit', 'add'].includes(state.modalConfigData.type)"
          type="primary"
          @click="editorRef.handleOk()"
          >确定</a-button
        >
        <a-button
          v-if="['accountList'].includes(state.modalConfigData.type)"
          type="primary"
          @click="accountListRef.handleOk()"
          >确定</a-button
        >
      </template>
    </a-modal>
  </div>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue'
  import Editor from './components/Editor.vue'
  import AccountList from './components/AccountList.vue'
  import { updateStatus } from './index.api'
  import datas from './data'
  const { state, pageChange, searchForm, delItem, searchConfig, data, getList } = datas()
  const editorRef = ref(null)
  const accountListRef = ref(null)
  // 添加修改
  const onShowDialog = (type, item) => {
    state.modalConfigData.type = type
    state.modalConfigData.open = true
    if (type === 'add') {
      state.modalConfigData.title = '新增域名'
      state.modalConfigData.data = {
        status: 2,
        media_type: 'all'
      }
    } else if (type === 'edit') {
      state.modalConfigData.title = '编辑域名'
      state.modalConfigData.data = JSON.parse(JSON.stringify(item))
      if (state.modalConfigData.data.media_type != 'all') {
        state.modalConfigData.data.media_type_arr = state.modalConfigData.data.media_type.split(',')
        state.modalConfigData.data.media_type = 'specify'
      }
      state.modalConfigData.data.domain = state.modalConfigData.data.domain.split('https://')[1]
    }
  }
  // 行内编辑状态
  const changeStatus = async (row) => {
    try {
      await updateStatus({
        id: row.id,
        status: row.status
      })
    } catch {}
    getList()
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    state.modalConfigData.data = {}
  }
  const successSave = () => {
    getList()
    modalCancel()
  }
  // 可使用账号
  const setAccountList = (data) => {
    state.modalConfigData.title = '可使用账号'
    state.modalConfigData.open = true
    state.modalConfigData.type = 'accountList'
    state.modalConfigData.data = data
    nextTick(() => {
      accountListRef.value.open()
    })
  }
  getList()
</script>

<style lang="scss" scoped>
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
</style>
