<template>
  <a-form
    ref="formRef"
    name="organizationForm"
    :model="data"
    :rules="rules"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
    :label-col="{ style: { width: '80px' } }"
  > 
    <a-input class="w-300px mb-12px" v-model:value="searchValue" placeholder="搜索名称" />
    <div>
      <a-tree
        ref="treeRef"
        checkable
        :tree-data="treeData"
        v-model:checkedKeys="checkedKeys"
        v-model:expandedKeys="expandedKeys"
        :field-names="{ key: 'id' }"
        :height="500"
        @check="handleCheck"
      >
        <template #title="{ name, id }">
          <a-tooltip>
            <template #title>{{ name }}</template>
            <div class="text_overflow flex-1 w-200px">
              <div v-if="name.indexOf(searchValue)>-1 && searchValue" class="c-#262829 border border-#ffE8CF pa-4px bg-#fff6eb rounded-3px inline-block font-size-14px line-height-14px">
                {{ name.substring(0, name.indexOf(searchValue)) }}
                <span class="c-#ff931e">{{ searchValue }}</span>
                {{ name.substring(name.indexOf(searchValue) + searchValue.length) }}
              </div>
              <span v-else class="font-size-14px">{{ name }}</span>
            </div>
          </a-tooltip>
        </template>
      </a-tree>
    </div>
  </a-form>
</template>

<script setup lang="ts">
  import { setShareUserIds, searchUser, domainTree } from '../index.api'
  const props = defineProps(['data', 'role', 'shop', 'config', 'departmentList'])
  import { message } from 'ant-design-vue'
  import { ref, watch, nextTick } from 'vue'
  import datas from '../data'
  const formRef = ref()
  const { rules } = datas()
  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  const emit = defineEmits(['success'])
  const handleOk = async () => {
    try {
      const res = await setShareUserIds({ user_ids: filteredCheckedKeys.value, id: props.data.id })
      if (res.code === 0) {
        message.success('保存成功')
        emit('success')
      }
    } catch {}
  }
  const account = ref('')
  const treeData = ref([])
  const checkedKeys = ref<any[]>([])
  const expandedKeys = ref<any[]>([])
  const filteredCheckedKeys = ref<any[]>([])
  const searchValue = ref('')
  function collectCheckedIds(data:any) {
    const checkedIds:any[] = [];
    function traverse(nodes:any) {
      if (!nodes) return;
      nodes.forEach((node:any) => {
        if (node.type === 2 && node.checked) {
          checkedIds.push(node.id);
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    }
    traverse(data);
    return checkedIds;
  }
  // 监听变化，过滤掉 type=1 的节点
  const handleCheck = (_:any, node:any) => {
    filteredCheckedKeys.value = node.checkedNodes.filter((item:any) => item.type === 2).map((item:any) => item.id);
  }
  const open = async () => {
    account.value = ''
    const res = await domainTree({ company_id: props.data.company_id, id: props.data.id })
    treeData.value = res.data || []
    checkedKeys.value = collectCheckedIds(res.data || [])
    filteredCheckedKeys.value = collectCheckedIds(res.data || [])
    expandedKeys.value = treeData.value.map(item => item.id);
    searchValue.value = ''
  }
  const treeRef = ref()
  watch(searchValue, (value) => {
    if (value) {
      function findMatchingKeys(nodes: any[], searchValue: string): any[] {
        const matchingKeys: any[] = [];
        nodes.forEach((node) => {
          if (node.name && node.name.includes(searchValue)) {
            matchingKeys.push(node.id);
          }
          if (node.children) {
            const childMatchingKeys = findMatchingKeys(node.children, searchValue);
            if (childMatchingKeys.length > 0) {
              matchingKeys.push(node.id);
              matchingKeys.push(...childMatchingKeys);
            }
          }
        });
        return matchingKeys;
      }
      expandedKeys.value = findMatchingKeys(treeData.value, value);
      nextTick(() => {
        if (expandedKeys.value.length > 1) {
          treeRef.value.scrollTo({
            key: expandedKeys.value[1],
            align: 'top'
          });
        }
      });
    } else {
      expandedKeys.value = treeData.value.map(item => item.id);
    }
  })
  defineExpose({
    handleOk,
    validateFields,
    open
  })
</script>

<style scoped lang="scss"></style>
