<template>
  <a-form
    ref="formRef"
    name="organizationForm"
    :model="data"
    :rules="rules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
  >
    <a-form-item label="域名" name="domain">
      <a-input
        v-model:value="data.domain"
        addon-before="https://"
        :disabled="!!data.id"
        placeholder="请输入域名"
        @input="data.domain = data.domain.replace(/[\u4e00-\u9fa5]/g, '')"
      />
    </a-form-item>
    <a-form-item label="公钥" name="public_key">
      <a-textarea
        class="max-h-200px"
        v-model:value="data.public_key"
        :rows="5"
        placeholder="-----BEGIN CERTIFICATE-----xxxxxxxx-----END CERTIFICATE----------BEGIN CERTIFICATE-----xxxxxxxx-----END CERTIFICATE-----"
        @input="data.public_key = data.public_key.replace(/[\u4e00-\u9fa5]/g, '')"
      />
    </a-form-item>
    <a-form-item label="私钥" name="private_key">
      <a-textarea
        class="max-h-200px"
        v-model:value="data.private_key"
        :rows="5"
        placeholder="-----BEGIN RSA PRIVATE KEY-----xxxxxxxx -----END RSA PRIVATE KEY-----"
        @input="data.private_key = data.private_key.replace(/[\u4e00-\u9fa5]/g, '')"
      />
    </a-form-item>
    <a-form-item label="媒体类型" name="media_type">
      <a-radio-group v-model:value="data.media_type" name="radioGroup" class="mt-6px">
        <a-radio value="all">全部媒体类型</a-radio>
        <a-radio value="specify">指定媒体类型</a-radio>
      </a-radio-group>
      <a-form-item name="media_type_arr" v-if="data.media_type == 'specify'">
        <a-checkbox-group v-model:value="data.media_type_arr" style="width: 100%" class="mt-6px">
          <a-checkbox value="3">巨量引擎</a-checkbox>
          <a-checkbox value="1">广点通</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form-item>
    <a-form-item label="启用" name="status">
      <a-tooltip>
        <template #title v-if="config.type == 'add' || data.company_status == 2">
          <div>域名配置中，暂不支持启用</div>
        </template>
        <a-switch
          :disabled="config.type == 'add' || data.company_status == 2"
          v-model:checked="data.status"
          :checkedValue="1"
          :unCheckedValue="2"
        />
      </a-tooltip>
      <span class="ml-12px c-#666666">停用后，则域名在提取链接时无法选择</span>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
  import { setDomainSave } from '../index.api'
  import { message } from 'ant-design-vue'
  const props = defineProps(['data', 'role', 'shop', 'config', 'departmentList'])
  import { ref } from 'vue'
  import datas from '../data'
  const formRef = ref()
  const { rules } = datas()

  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  const emit = defineEmits(['success'])
  const handleOk = async () => {
    validateFields()
      .then(async () => {
        try {
          const res: any = await setDomainSave({
            id: props.data.id,
            company_id: props.data.company_id,
            domain: props.data.domain.startsWith('https://') ? props.data.domain : 'https://' + props.data.domain,
            status: props.data.status,
            public_key: props.data.public_key,
            private_key: props.data.private_key,
            user_ids: props.data.users ? props.data.users.map((item) => item.user_id) : [],
            media_type: props.data.media_type=='specify' ? props.data.media_type_arr.join(',') : props.data.media_type,
          })
          if (res.code === 0) {
            message.success('保存成功')
            emit('success')
          }
        } catch (error) {
          console.log(error)
        }
      })
      .catch((_) => {})
  }
  defineExpose({
    handleOk,
    validateFields
  })
</script>

<style scoped lang="scss"></style>
