import http from '@/utils/request'

/**
 * 域名列表
 */
export const getDomainList = (data:any) => {
  return http('get', `/admin/domain/list`, data)
}

/**
 * 域名保存
 */
export const setDomainSave = (data:any) => {
  return http('post', `/admin/domain/save`, data)
}

/**
 * 域名删除
 */
export const domainDelete = (data:any) => {
  return http('post', `/admin/domain/delete`, data)
}

/**
 * 设置可见账号
 */
export const setShareUserIds = (data:any) => {
  return http('post', `/admin/domain/set_share_user_ids`, data)
}

/**
 * 搜索可见账号
 */
export const searchUser = (data:any) => {
  return http('post', `/admin/domain/search_user`, data)
}

/**
 * 更新域名状态
 */
export const updateStatus = (data:any) => {
  return http('post', `/admin/domain/update_status`, data)
}

/**
 * 可使用账号树形列表
 */
export const domainTree = (data:any) => {
  return http('post', `/admin/domain/tree`, data)
}
