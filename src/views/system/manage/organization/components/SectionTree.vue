<template>
  <div class="tree-warp">
    <div v-auth="['addOrganization']" class="add-warp" @click="addItem"><PlusOutlined /><span>新增部门</span></div>
    <div class="tree_content common_scroll">
      <a-tree
        v-if="state.data.length"
        v-model:expandedKeys="state.expandedKeys"
        :tree-data="state.data"
        :blockNode="true"
        v-model:selectedKeys="state.selectedKeys"
        @select.self="selectItem"
        :field-names="{
          title: 'name',
          key: 'id',
          children: 'children'
        }"
      >
        <template #title="{ name, key, data }">
          <div class="w-full">
            <a-input
              ref="inputRef"
              v-if="data.input_status === 1"
              v-model:value.trim="state.formData.text"
              :maxlength="50"
              @change="(e) => changeValue(e, data)"
              @pressEnter.stop="blurValue(e, data)"
              @blur.stop="blurValue(e, data)"
              placeholder="请输入部门名称"
            />
            <a-input
              ref="inputRef"
              :maxlength="50"
              v-if="state.itemData?.id === data.id && ['edit'].includes(state.type)"
              v-model:value.trim="state.formData.text"
              @change="(e) => changeValue(e, data)"
              @pressEnter.stop="blurValue(e, data)"
              @blur.stop="blurValue(e, data)"
            />
            <template v-else>
              <div class="w-full flex flex-content-between item-warp-text">
                <div class="title-item-warp">
                  <a-tooltip>
                    <template #title>{{ name }}</template>
                    {{ name }}
                  </a-tooltip>
                </div>
                <div class="icon-list" v-if="data.level != 1">
                  <a-space>
                    <FormOutlined v-auth="['editOrganization']" @click.stop="handleActions('edit', data)" />
                    <DeleteOutlined v-auth="['delOrganization']" @click.stop="handleActions('del', data)" />
                  </a-space>
                </div>
              </div>
            </template>
          </div>
        </template>
        <template #switcherIcon="{ switcherCls }"><down-outlined :class="switcherCls" /></template>
      </a-tree>
    </div>
    <a-modal
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
      @cancel="modalCancel"
      centered
      @ok="handleOk"
    >
      <div v-if="['del'].includes(state.type)">确定删除此部门吗?</div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { DownOutlined, SmileOutlined, FormOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { nextTick, onMounted, reactive, ref, watch } from 'vue'
  import { post_department_save, post_department_delete, get_department_info } from '../index.api'
  const props = defineProps(['add', 'departmentList'])
  const inputRef = ref(null)
  const state = reactive({
    selectedKeys: [],
    expandedKeys: [],
    data: [],
    itemData: null,
    type: null,
    modalConfigData: {
      open: false,
      title: '提示'
    },
    formData: {
      text: '',
      data: null
    }
  })
  const emits = defineEmits(['selectStatus', 'refreshLoad'])
  const selectItem = (selectedKeys, data) => {
    if (data.selected) {
      if (selectedKeys.filter((v) => v)?.length) {
        state.itemData = data.selectedNodes[0]
        emits('selectStatus', selectedKeys, data.selectedNodes)
      }
    }
  }
  onMounted(() => {})

  const setInit = async () => {
    // const result = await get_department_info()
    // if (result.code === 0) {
    //   state.data = result.data
    //   state.selectedKeys = [state.data[0].id]
    //   state.itemData = state.data[0]
    //   state.expandedKeys = setExpandedKeys(state.data)

    // }
    // 避免同一接口多次渲染
    emits('refreshLoad')
  }
  const setExpandedKeys = (data) => {
    let itemobj = []
    function _cycel(data) {
      data.forEach((item) => {
        if (item.children) {
          itemobj.push(item.id)
        }
        if (item.children && item.children.length) {
          _cycel(item.children)
        }
      })
    }
    _cycel(data)
    return itemobj
  }

  const addItem = async () => {
    state.type = 'add'
    console.log(state.itemData, '2222')

    if (state.itemData.level === 3) return message.warning('最多添加三级')
    if (state.itemData.children) {
      state.itemData.children.push({
        children: [],
        id: null,
        input_status: 1,
        level: state.itemData.level,
        name: undefined,
        pid: state.itemData.id
      })
    } else {
      let arr = []
      arr.push({
        children: [],
        id: null,
        input_status: 1,
        level: state.itemData.level,
        name: undefined,
        pid: state.itemData.id
      })
      state.itemData.children = arr
    }

    state.expandedKeys = setExpandedKeys(state.data)
    await nextTick(() => {
      inputRef.value?.focus()
    })
    // setTimeout(() => {

    // }, 300)
  }

  const focueInput = () => {
    inputRef.value.focus()
  }

  const handleActions = (type, data) => {
    state.itemData = data
    state.selectedKeys.push(data.id)
    state.type = type
    if (['edit'].includes(type)) {
      state.formData.text = data.name
      state.formData.data = data
      nextTick(() => {
        inputRef.value.focus()
      })
    }
    if (['del'].includes(type)) {
      state.modalConfigData.open = true
    }
  }
  const changeValue = (e, data) => {
    state.formData.data = data
  }

  const blurValue = async (e, data) => {
    let obj = null
    if (['edit'].includes(state.type)) {
      obj = {
        id: state.formData.data.id,
        name: state.formData.text,
        pid: state.formData.data.pid
      }
    }
    if (['add'].includes(state.type)) {
      if (!state.formData.text) {
        state.itemData.children.pop()
        return
      }
      // state.itemData.children[state.itemData.children.length - 1].level =
      //   state.itemData.children[state.itemData.children.length - 1].level + 1

      obj = {
        name: state.formData.text,
        pid: data.pid
      }
    }
    if (!obj) {
      return
    }
    const isflag = isRepeat(obj.name, state.data)
    if (isflag) {
      if (['edit'].includes(state.type)) {
        if (isflag.id !== obj.id) {
          message.warning('组织架构名称已经存在')
          inputRef.value.focus()
          return
        }
      } else {
        message.warning('组织架构名称已经存在')
        inputRef.value.focus()
        return
      }
    }
    const result = await post_department_save(obj)
    if (result.code === 0) {
      message.success('操作成功')
      setInit()
      modalCancel()
    }
  }
  const handleOk = async () => {
    const result = await post_department_delete({ id: state.itemData.id })
    if (result.code === 0) {
      const items = isRepeat(state.itemData.pid, state.data, 'id')
      state.expandedKeys = state.expandedKeys.filter((item) => item !== items.id)
      message.success('操作成功')
      setInit()
      modalCancel()
    }
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    state.modalConfigData.title = ''
    state.type = null
    state.formData.text = ''
    state.formData.data = ''
  }

  const isRepeat = (key, data, name: 'name') => {
    let itemobj = null
    function _cycel(key, data) {
      data.forEach((item) => {
        if (key === item[name]) {
          itemobj = item
        } else if (item.children && item.children.length) {
          _cycel(key, item.children)
        }
      })
    }
    _cycel(key, data)
    return itemobj
  }
  watch(
    () => props.departmentList,
    (val, oldVal) => {
      if (val != oldVal) {
        console.log('======', val, oldVal)
        state.data = props.departmentList
        state.selectedKeys = [state.data[0].id]
        state.itemData = state.data[0]
        state.expandedKeys = setExpandedKeys(state.data)
      }
    },
    {
      deep: true,
      immediate: true
    }
  )
</script>

<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  .tree_content {
    height: calc(100vh - 270px);
    overflow-x: hidden;
  }
  .add-warp {
    padding-bottom: 10px;
    cursor: pointer;
    span {
      padding-right: 10px;
      color: var(--primary-color);
    }
  }
  .item-warp-text {
    flex: 1;
  }
  .icon-list {
    display: none;
  }
  .item-warp-text:hover {
    .icon-list {
      display: block;
    }
  }

  .title-item-warp {
    flex: 1;
    padding-right: 10px;
    box-sizing: border-box;
    word-break: break-all;
    @include text_overflow(1);
  }
  :deep(.ant-tree .ant-tree-switcher) {
    display: flex;
    align-items: center;
  }
</style>
