<template>
  <a-form
    ref="formRef"
    name="organizationForm"
    :model="data"
    :rules="rules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
  >
    <a-form-item label="邮箱" name="email">
      <div class="flex flex-center">
        <a-input v-model:value="data.email" placeholder="请输入邮箱地址" disabled />
        <div v-if="state.receive" class="ml-10px flex">
          <a-button type="link" class="p-r-0px" @click="verifyEmail" :disabled="state.isSend">{{
            state.btnTxt
          }}</a-button>
          <a-tooltip class="ml6px" v-if="state.receive">
            <template #title> 点击发送至您邮箱的链接并确认内容，即为验证通过！ </template>
            <QuestionCircleFilled />
          </a-tooltip>
        </div>

        <span v-else class="item-label">待同意接收</span>
      </div>
      <span v-if="state.receive" class="c-#86888b font-size-12px mt-4px"
        >如您使用邮箱接收验证码，请务必填写正确的邮箱并发起验证！</span
      >
      <span v-else class="c-#86888b font-size-12px mt-4px">邮箱验证已发送至您的邮箱，请前往查收并同意接收！</span>
    </a-form-item>
    <a-button v-if="!state.receive" class="font-size-14px" type="link" @click="verifyEmail">重新发起验证</a-button>
  </a-form>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import datas from '../data'
  const { rules } = datas()
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { send_email } from '../index.api'
  const formRef = ref()
  const props = defineProps(['data', 'config'])
  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  const state = reactive({
    btnTxt: '发起验证',
    isSend: false,
    receive: !props.data.email_verify_code
  })

  const onPwdRule = () => {
    return [
      { required: true, message: '请输入密码', trigger: ['change', 'blur'] },
      { pattern: checkReg.pwd, message: '设置6至16位英文/数字/符号组合，至少包含2种字符' },
      { validator: (rule, value, callback) => verifyPwd(rule, value, callback), trigger: ['change', 'blur'] }
    ]
  }
  const verifyEmail = async () => {
    const params = {
      user_id: props.config.data.id,
      account: props.data.account,
      email: props.data.email
    }
    await send_email(params)
    state.isSend = true
    state.btnTxt = '已发送'
    message.success('验证已发送至您的邮箱，请前往同意接收！')
  }
  defineExpose({
    validateFields
  })
</script>

<style scoped lang="scss">
  .item-label {
    background: rgba(211, 13, 13, 0.14);
    border-radius: 2px;
    font-weight: 400;
    font-size: 12px;
    color: #d30d0d;
    width: 92px;
    border: 1px solid #d30d0d;
    text-align: center;
    margin-left: 20px;
  }
</style>
