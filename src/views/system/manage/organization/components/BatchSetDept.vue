<template>
  <a-form
    ref="batchSetRef"
    name="BatchSetDeptForm"
    :model="data"
    :rules="deptRules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
    class="pt-20px"
  >
    <a-form-item label="所属部门" name="department_id">
      <a-tree-select
        v-model:value="data.department_id"
        show-search
        style="width: 100%"
        :dropdown-style="{ maxHeight: '400px', 'overflow-y': 'auto' }"
        placeholder="请选择所属部门"
        allow-clear
        :tree-default-expand-all="true"
        :tree-data="departmentList"
        :field-names="{
          children: 'children',
          label: 'name',
          value: 'id'
        }"
        tree-node-filter-prop="name"
      >
      </a-tree-select>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import datas from '../data'
  const { deptRules } = datas()
  const batchSetRef = ref()
  defineProps(['data', 'departmentList'])
  const validateFields = async () => {
    return await batchSetRef.value.validateFields()
  }

  defineExpose({
    validateFields
  })
</script>

<style scoped lang="scss"></style>
