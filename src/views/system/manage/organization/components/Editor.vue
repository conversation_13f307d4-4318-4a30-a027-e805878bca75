<template>
  <a-form
    ref="formRef"
    name="organizationForm"
    :model="data"
    :rules="rules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
  >
    <a-form-item label="员工姓名" name="realname">
      <a-input v-model:value="data.realname" placeholder="请输入员工姓名" />
    </a-form-item>
    <a-form-item label="登录账号" name="account">
      <a-input
        :disabled="!['add'].includes(config.type)"
        v-model:value="data.account"
        placeholder="请输入6-16位的英文数字组合"
      />
    </a-form-item>
    <a-form-item v-if="['add'].includes(config.type)" label="登录密码" name="password">
      <a-input v-model:value="data.password" placeholder="密码6-16位，同时包含大小写英文、数字的组合" />
    </a-form-item>
    <a-form-item label="手机号" name="phone">
      <a-input v-model:value="data.phone" maxlength="11" placeholder="请输入手机号" />
    </a-form-item>
    <a-form-item label="邮箱" name="email">
      <a-input v-model:value="data.email" placeholder="请输入邮箱地址" :disabled="state.isSended" />
    </a-form-item>
    <a-form-item label="所属部门" name="department_id">
      <a-tree-select
        v-model:value="data.department_id"
        show-search
        style="width: 100%"
        :dropdown-style="{ maxHeight: '400px', 'overflow-y': 'auto' }"
        placeholder="请选择所属部门"
        :disabled="config.data?.role_info.name == '超级管理员'"
        allow-clear
        :tree-default-expand-all="true"
        :tree-data="departmentList"
        :field-names="{
          children: 'children',
          label: 'name',
          value: 'id'
        }"
        tree-node-filter-prop="name"
      >
      </a-tree-select>
    </a-form-item>
    <a-form-item label="用户角色" name="role_id" :rules="[{ required: true, message: '请选择用户角色' }]">
      <a-select
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        v-model:value="data.role_id"
        :disabled="config.data?.role_info.name == '超级管理员'"
        allowClear
        placeholder="请选择用户角色"
      >
        <a-select-option :value="item.id" v-for="item in role">{{ item.name }}</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="员工状态" name="status" :rules="[{ required: true, message: 'Please input your password!' }]">
      <a-radio-group v-model:value="data.status" :disabled="config.data?.role_info.name == '超级管理员'">
        <a-radio :value="1">启用</a-radio>
        <a-radio :value="2">禁用</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="单点登录" name="open_single_point" :rules="[{ required: true, message: '请选择单点登录' }]">
      <a-radio-group v-model:value="data.open_single_point" :disabled="config.data?.role_info.name == '超级管理员'">
        <a-radio :value="1">启用</a-radio>
        <a-radio :value="0">禁用</a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
  const props = defineProps(['data', 'role', 'shop', 'config', 'departmentList'])
  import { ref } from 'vue'
  import datas from '../data'
  const formRef = ref()
  const { rules, state, verifyTxt, verifyEmail } = datas(props.data, props.config, formRef)

  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  defineExpose({
    validateFields,
    getCode: () => state.code
  })
</script>

<style scoped lang="scss"></style>
