<template>
  <a-row :gutter="[20, 20]">
    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="6" :xxl="6">
      <a-card :bordered="false">
        <SectionTree
          ref="sectionTreeRef"
          v-if="state.depData.length"
          :data="state.depData"
          :departmentList="state.depData"
          @selectStatus="selectStatus"
          @refreshLoad="initDepData"
        />
      </a-card>
    </a-col>
    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="18" :xxl="18">
      <a-card :headStyle="{ border: 'none' }" class="common_page_warp">
        <template #title>
          <div>员工列表</div>
        </template>
        <template #extra>
          <a-button v-auth="['addOrganizationStaff']" type="primary" @click="handleActions('add', null)">
            新增员工
          </a-button>
        </template>
        <a-space class="w-full" direction="vertical" :size="16">
          <SearchBaseLayout
            :data="state.searchConfigData.data"
            @changeValue="changeValue"
            :actions="state.searchConfigData.options"
          >
            <template #btns
              ><a-button
                :disabled="state.selectedRowKeys.length === 0"
                v-auth="['batchSetDepartment']"
                type="primary"
                class="ml-8px"
                @click="handleActions('batchSet', null)"
              >
                批量设置所属部门
              </a-button></template
            >
          </SearchBaseLayout>

          <TableZebraCrossing
            :data="state.tableConfigOptions"
            @change="pageChange"
            :row-selection="{
              selectedRowKeys: state.selectedRowKeys,
              onChange: onSelectChange,
              getCheckboxProps: (record) => {
                return {
                  disabled: record?.role_info.name == '超级管理员'
                }
              }
            }"
          >
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'user_name'">
                <div>{{ scope.record.userinfo.realname }}</div>
              </template>
              <template v-if="scope.column.key === 'account'">
                <div>{{ scope.record.userinfo.account }}</div>
              </template>
              <template v-if="scope.column.key === 'role'">
                <div>{{ scope.record.role_info.name }}</div>
              </template>
              <template v-if="scope.column.key === 'phone'">
                <div>{{ scope.record.userinfo.phone }}</div>
              </template>
              <template v-if="scope.column.key === 'email'">
                <a-tooltip>
                  <template #title>{{ scope.record.userinfo.email }}</template>
                  <span class="text_overflow_row">{{ scope.record.userinfo.email }}</span>
                </a-tooltip>
              </template>
              <template v-if="scope.column.key === 'department_name'">
                <div>{{ scope.record.userinfo.department_name }}</div>
              </template>
              <template v-if="scope.column.key === 'status'">
                <a-space>
                  <div :class="roleStatusEnum[scope.record.userinfo.status]?.className">
                    {{ roleStatusEnum[scope.record.userinfo.status]?.text || '--' }}
                  </div>
                </a-space>
              </template>
              <template v-if="scope.column.key === 'creator'">
                <div>{{ scope.record.userinfo.creator }}</div>
              </template>
              <template v-if="scope.column.key === 'created_at'">
                <div>{{ dayjs(scope.record.userinfo.created_at * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
              </template>
              <template v-if="scope.column.key === 'action'">
                <a-popconfirm
                  :title="`是否${scope.record.userinfo.status === 1 ? '禁用' : '启用'}此员工？`"
                  placement="topRight"
                  @confirm="forbidden(scope.record)"
                >
                  <a-button
                    type="link"
                    size="small"
                    v-auth="['noauthOrganizationStaff']"
                    v-show="scope.record.role_info.name != '超级管理员'"
                    class="pa-0!"
                  >
                    {{ scope.record.userinfo.status === 1 ? '禁用' : '启用' }}</a-button
                  >
                </a-popconfirm>

                <a-button
                  v-auth="['editOrganizationStaff']"
                  type="link"
                  class="pa-0!"
                  :class="[scope.record.role_info.name == '超级管理员' && 'ml-0!']"
                  size="small"
                  @click="handleActions('edit', scope.record)"
                  >编辑</a-button
                >
                <a-popconfirm title="是否确定删除?" placement="topRight" @confirm="del('delete', scope.record)">
                  <a-button
                    v-auth="['delOrganizationStaff']"
                    v-show="scope.record.role_info.name != '超级管理员'"
                    type="link"
                    size="small"
                    class="pa-0!"
                    >删除</a-button
                  >
                </a-popconfirm>
              </template>
            </template>
          </TableZebraCrossing>
        </a-space>
        <a-modal
          v-model:open="state.modalConfigData.open"
          :title="state.modalConfigData.title"
          @cancel="modalCancel"
          centered
          @ok="handleOk"
        >
          <template #footer>
            <a-button v-if="!['email'].includes(state.modalConfigData.type)" @click="modalCancel">取消</a-button>
            <a-button v-if="!['email'].includes(state.modalConfigData.type)" type="primary" @click="handleOk"
              >确定</a-button
            >
          </template>

          <div v-if="['delete'].includes(state.modalConfigData.type)">是否删除此员工？</div>
          <Editor
            v-if="['edit', 'add'].includes(state.modalConfigData.type)"
            v-model:data="state.formData"
            :role="state.roleOptions"
            :shop="state.shopOptions"
            :departmentList="state.depData"
            ref="formRef"
            :config="state.modalConfigData"
          />
          <BatchSetDept
            v-if="state.modalConfigData.type === 'batchSet'"
            v-model:data="state.deptFormData"
            :departmentList="state.depData"
            ref="batchSetDeptFormRef"
          ></BatchSetDept>
          <Email
            v-if="['email'].includes(state.modalConfigData.type)"
            v-model:data="state.formData"
            :config="state.modalConfigData"
            ref="formRef"
          />
        </a-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs'
  import { roleStatusEnum } from '@/utils'
  import SectionTree from './components/SectionTree.vue'
  import Editor from './components/Editor.vue'
  import BatchSetDept from './components/BatchSetDept.vue'
  import Email from './components/Email.vue'
  import datas from './data'
  const {
    state,
    statusType,
    initDepData,
    selectStatus,
    pageChange,
    forbidden,
    onSelectChange,
    handleActions,
    del,
    handleOk,
    modalCancel,
    changeValue,
    batchSetDeptFormRef,
    formRef,
    requireImg,
    sectionTreeRef
  } = datas()
  initDepData()
</script>
<style scoped lang="scss">
  .round {
    display: block;
    width: 6px;
    height: 6px;
    background: #404040;
    border-radius: 50%;
  }
  .text_overflow_row {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
