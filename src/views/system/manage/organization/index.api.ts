import http from '@/utils/request'

export const get_department_info = (data) => {
  return http('get', `/common/department/info`, data)
}

export const get_user_list = (data) => {
  return http('get', `/common/user/list`, data)
}

export const get_role_list = (data) => {
  return http('get', `/common/role/list`, data)
}
export const get_shop_list = (data) => {
  return http('get', `/admin/shop/list`, { page: 1, page_size: 10000, ...data })
}

export const post_user_save = (data) => {
  return http('post', `/common/user/save`, data)
}

export const post_user_delete = (data) => {
  return http('post', `/common/user/delete`, data)
}

export const post_department_save = (data) => {
  return http('post', `/common/department/save`, data)
}
export const post_department_delete = (data) => {
  return http('post', `/common/department/delete`, data)
}
/**
 * 批量设置所属部门
 * @param data
 * @returns
 */
export const batch_set_user_department = (data) => {
  return http('post', `/common/user/edit_departmentId`, data)
}

export const verify_email = (data) => {
  return http('get', `/admin/user/email/verify`, data)
}

export const send_email = (data) => {
  return http('post', `/admin/user/email/send`, data)
}
