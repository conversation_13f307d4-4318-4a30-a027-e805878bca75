import { checkReg, validateMobile } from '@/utils'

import {
  get_department_info,
  get_user_list,
  get_role_list,
  post_user_save,
  post_user_delete,
  send_email,
  batch_set_user_department
} from './index.api'
import { reactive, ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useApp } from '@/hooks'
import { cloneDeep } from 'lodash-es'
import { requireImg } from '@/utils'
export default function datas(editData, editConfig, editFormRef) {
  // 搜索组件 配置数据
  const searchConfigData = {
    data: [
      {
        type: 'input.text',
        field: 'realname',
        value: undefined,
        props: {
          placeholder: '请输入员工姓名'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0
      // layout: {
      //   xs: 24,
      //   sm: 12,
      //   md: 8,
      //   lg: 8,
      //   xl: 8,
      //   xxl: 6
      // }
    }
  }

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1000
    },
    dataSource: [],
    columns: [
      {
        title: '姓名',
        dataIndex: 'user_name',
        key: 'user_name',
        width: 100
      },
      {
        title: '账号',
        dataIndex: 'account',
        key: 'account',
        width: 150
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        width: 100
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        key: 'phone',
        width: 120
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: 200
      },
      {
        title: '部门',
        dataIndex: 'department_name',
        key: 'department_name',
        width: 180
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        width: 100
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        width: 160
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  }

  const statusType = (type) => {
    let status = {
      2: {
        color: '#E63030',
        text: '禁用'
      },
      1: {
        color: '#60A13B',
        text: '启用'
      }
    }
    return status[type]
  }
  const modalConfigData = {
    open: false,
    title: '',
    type: '',
    data: ''
  }
  const modalConfigTitle = {
    status: '提示',
    edit: '编辑员工账号',
    delete: '提示',
    add: '新增员工账号',
    email: '邮箱',
    batchSet: '批量设置所属部门'
  }

  const formData = {
    realname: undefined,
    account: undefined,
    password: undefined,
    phone: undefined,
    email: undefined,
    role_id: undefined,
    status: 1,
    department_id: undefined,
    open_single_point: 1
  }
  const deptFormData = {
    department_id: undefined
  }
  const validPhone = (rule: any, value: string) => {
    if (!validateMobile(value)) {
      return Promise.reject('请输入正确的联系人手机号')
    } else {
      return Promise.resolve()
    }
  }

  const rules = {
    realname: [{ required: true, message: '请输入员工姓名' }],
    account: [
      { required: true, message: '请输入账号', trigger: ['change', 'blur'] },
      { pattern: checkReg.account, message: '账号支持输入6-16位英文和数字组合', trigger: ['change', 'blur'] }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: ['change', 'blur'] },
      { pattern: checkReg.pwd, message: '密码6-16位，同时包含大小写英文、数字的组合', trigger: ['change', 'blur'] }
    ],
    phone: [{ required: true, validator: validPhone, trigger: ['change', 'blur'] }],
    email: [
      { required: true, message: '请输入邮箱', trigger: ['blur', 'change'] },
      {
        pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
        message: '请输入正确的邮箱',
        trigger: ['blur', 'change']
      }
    ],
    department_id: [{ required: true, message: '请选择所属部门', trigger: ['change'] }]
  }

  const deptRules = {
    department_id: [{ required: true, message: '请选择所属部门', trigger: ['change'] }]
  }

  const formRef = ref()
  const batchSetDeptFormRef = ref(null)
  const sectionTreeRef = ref(null)
  const { useInfo } = useApp()
  const state = reactive({
    depData: [],
    searchConfigData,
    tableConfigOptions,
    initParams: {
      department_id: undefined,
      page: 1,
      page_size: 20,
      company_id: useInfo.value?.company_id,
      realname: undefined
    },
    modalConfigData,
    formData,
    deptFormData,
    roleOptions: [],
    shopOptions: [],
    isSended: false,
    email: editData?.email,
    code: '',
    selectedRowKeys: []
  })

  const verifyTxt = computed(() => {
    if (editData.role_id) {
      return !state.isSended ? (editData.email_verified ? '重新发起验证' : '发起验证') : '已发送'
    } else {
      return state.isSended ? '已发送' : '发起验证'
    }
  })

  const verifyEmail = async () => {
    await editFormRef.value.validateFields(['email'])
    const params = {
      user_id: editConfig?.data?.id || 0,
      account: editData.account,
      email: editData.email
    }
    const { data } = await send_email(params)
    state.code = data.code
    state.isSended = true
  }

  const initDepData = async () => {
    const result: any = await get_department_info()
    if (result.code === 0) {
      state.depData = result.data
      state.initParams.department_id = state.depData[0]?.id
      initTable()
    }
  }
  const initTable = async () => {
    state.tableConfigOptions.loading = true
    let obj = cloneDeep(state.initParams)
    // 当第一个部门id等于第一个值的时候 就去除
    // if (obj.department_id == state.depData[0].id) {
    //   delete obj.department_id
    // }

    const result = await get_user_list(obj)
    if (result.code === 0) {
      state.tableConfigOptions.dataSource = (result?.data?.list || [])?.map((it) => {
        return {
          ...it,
          ...{ id: it.userinfo?.id }
        }
      })
      state.tableConfigOptions.pagination.current = result.data?.page || 1
      state.tableConfigOptions.pagination.total = result.data?.total
    }
    let y = window?.innerHeight ? window?.innerHeight - 426 : 500
    if (state.tableConfigOptions.dataSource.length > 0) {
      state.tableConfigOptions.scroll.y = y
    } else {
      delete state.tableConfigOptions.scroll.y
    }
    state.tableConfigOptions.loading = false
    state.selectedRowKeys = []
  }

  const selectStatus = (key, data) => {
    state.initParams.department_id = data[0].id
    state.initParams.page = 1
    state.initParams.page_size = 20

    initTable()
  }

  const pageChange = (pagination) => {
    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    initTable()
  }
  const forbidden = async (row) => {
    try {
      await post_user_save({
        id: row.userinfo?.id || undefined,
        status: row.userinfo?.status === 1 ? 2 : 1
      })
      await initTable()
      await modalCancel()
      message.success('操作成功')
    } catch (error) {
      console.error(error)
    }
  }
  const onSelectChange = (selectedRowKeys: any) => {
    state.selectedRowKeys = selectedRowKeys
  }
  const handleActions = async (type, data) => {
    if (['add', 'edit'].includes(type)) {
      try {
        let res: any = await get_role_list({
          page: 1,
          page_size: 100,
          status: 1
        })
        if (res.code === 0 && res.data?.list?.length > 0) {
          state.roleOptions = res.data.list
        }
        if (['edit'].includes(type)) {
          state.formData.realname = data.userinfo.realname
          state.formData.account = data.userinfo.account
          state.formData.phone = data.userinfo.phone
          state.formData.email = data.userinfo.email
          state.formData.role_id = data.role_info.id
          state.formData.password = data.password
          state.formData.status = data.userinfo.status
          state.formData.department_id = data.userinfo?.department_id
          state.formData.open_single_point = data.userinfo?.open_single_point
          state.formData.email_verified = data.userinfo.email_verified
          state.formData.email_verify_code = data.userinfo.email_verify_code
        } else {
          // 新增员工时，默认携带当前的部门id
          state.formData.department_id = state.initParams.department_id
        }
      } catch (e) {
        console.log(e, 'error')
      }
    }
    if (['email'].includes(type)) {
      state.formData.email = data.userinfo.email
      state.formData.account = data.userinfo.account
      state.formData.email_verify_code = data.userinfo.email_verify_code
    }

    state.modalConfigData.open = true
    state.modalConfigData.type = type
    state.modalConfigData.title = modalConfigTitle[type]
    state.modalConfigData.data = data
  }
  const del = async (type, data) => {
    await post_user_delete({
      id: data?.userinfo?.id || undefined
    })
    await initTable()
    message.success('删除成功')
  }
  const handleOk = async () => {
    let result = null
    console.log(state.modalConfigData.type, 'state.modalConfigData.type')
    if (['delete'].includes(state.modalConfigData.type)) {
      result = await post_user_delete({
        id: state.modalConfigData.data?.userinfo?.id || undefined
      })
    }
    if (['add', 'edit'].includes(state.modalConfigData.type)) {
      const code = formRef.value.getCode()
      const values = await formRef.value.validateFields()
      console.log(values, 'values')

      if (values) {
        result = await post_user_save({
          id: state.modalConfigData.data?.userinfo?.id || undefined,
          email_verify_code: code,
          ...values
        })
      }
    }
    // 批量设置所属部门
    if (state.modalConfigData.type === 'batchSet') {
      const values = await batchSetDeptFormRef.value?.validateFields()

      result = await batch_set_user_department({
        user_ids: state.selectedRowKeys?.join(',') || undefined,
        ...values
      })
    }
    if (result?.code === 0) {
      message.success(result.msg)
      initTable()
    }
    modalCancel()
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    if (['add', 'edit', 'email'].includes(state.modalConfigData.type)) {
      state.formData.realname = undefined
      state.formData.account = undefined
      state.formData.phone = undefined
      state.formData.password = undefined
      state.formData.email = undefined
      state.formData.role_id = undefined
      state.formData.status = 1
      state.formData.department_id = undefined
    }
    // 批量设置所属部门
    if (state.modalConfigData.type === 'batchSet') {
      state.deptFormData.department_id = undefined
    }
    if (state.modalConfigData.type === 'email') {
      initTable()
    }

    setTimeout(() => {
      state.modalConfigData.data = ''
      state.modalConfigData.type = ''
      state.modalConfigData.title = ''
    }, 300)
  }
  const changeValue = (data) => {
    state.initParams = { ...state.initParams, ...data.formData }
    state.initParams.page = 1
    initTable()
  }
  return {
    state,
    statusType,
    modalConfigTitle,
    rules,
    deptRules,
    initDepData,
    selectStatus,
    pageChange,
    forbidden,
    onSelectChange,
    handleActions,
    del,
    handleOk,
    modalCancel,
    changeValue,
    batchSetDeptFormRef,
    formRef,
    requireImg,
    verifyTxt,
    verifyEmail,
    sectionTreeRef
  }
}
