<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>角色管理</div>
      </template>
      <template #extra>
        <a-button v-auth="['addRole']" type="primary" @click="onShowDialog('add')">新增角色</a-button>
      </template>
      <template #search>
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="data.tableConfigOptions" @change="pageChange">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'name'">
              <a-tooltip>
                <template #title>{{ scope.record.name }}</template>
                <div class="text_overflow">{{ scope.record.name }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'department_name'">
              <div>{{ scope.record.department_name }}</div>
            </template>
            <template v-if="scope.column.key === 'desc'">
              <a-tooltip>
                <template #title>{{ scope.record.desc }}</template>
                <div class="text_overflow_row2">{{ scope.record.desc }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'status'">
              <div class="flex_align_center" style="min-width: 100px">
                <div :class="roleStatusEnum[scope.record.status]?.className">
                  {{ roleStatusEnum[scope.record.status]?.text || '--' }}
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'created_at_format'">
              <div style="min-width: 170px">{{ scope.record.created_at_format }}</div>
            </template>
            <template v-if="scope.column.key === 'creator'">
              <div style="min-width: 100px">{{ scope.record.creator }}</div>
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="handle_btns">
                <div class="flex_align_center">
                  <a-button
                    v-if="scope.record.is_system"
                    :disabled="scope.record.is_system"
                    type="link"
                    v-auth="['authRole']"
                    style="margin-right: 10px"
                  >
                    {{ scope.record.status === 1 ? '禁用' : '启用' }}
                  </a-button>
                  <a-popconfirm
                    v-else
                    :title="`请确认是否${scope.record.status === 1 ? '禁用' : '启用'}角色`"
                    placement="topRight"
                    @confirm="enable(scope.record)"
                  >
                    <a-button
                      :disabled="scope.record.is_system"
                      type="link"
                      v-auth="['authRole']"
                      style="margin-right: 10px"
                    >
                      {{ scope.record.status === 1 ? '禁用' : '启用' }}
                    </a-button>
                  </a-popconfirm>
                  <a-button
                    type="link"
                    v-auth="['editRole']"
                    style="margin-right: 10px"
                    @click="onShowDialog('edit', scope.record)"
                    >编辑</a-button
                  >
                  <a-button
                    v-if="scope.record.is_system"
                    type="link"
                    v-auth="['deleteRole']"
                    :disabled="scope.record.is_system"
                    >删除</a-button
                  >
                  <a-popconfirm
                    v-else
                    title="是否确认删除当前角色？"
                    placement="topRight"
                    @confirm="delItem(scope.record)"
                  >
                    <a-button type="link" v-auth="['deleteRole']" :disabled="scope.record.is_system">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
  </div>
</template>

<script setup>
  import { roleStatusEnum } from '@/utils'
  import datas from './data'
  const { pageChange, searchForm, onShowDialog, delItem, enable, searchConfig, data, statusType, getList } = datas()
  getList()
</script>

<style lang="scss" scoped>
  .round {
    width: 8px;
    height: 8px;
    background: #999999;
    border-radius: 50%;
    margin-right: 5px;
  }
  .btn_group {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .handle_btns {
    user-select: none;
    span {
      margin-right: 10px;
    }
    span:nth-last-of-type(1) {
      margin-right: 0;
    }
    .icons {
      margin-bottom: 24px;
      &_item {
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
</style>
