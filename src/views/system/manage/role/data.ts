import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { setRoleList, setAddRole } from './index.api'
export default function datas() {
  // 注册路由实例
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入角色名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '角色名称',
        dataIndex: 'name',
        key: 'name',
        width: 120,
        slot: true
      },
      {
        title: '角色归属',
        dataIndex: 'department_name',
        key: 'department_name',
        width: 120,
        slot: true
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
        width: 120,
        slot: true
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        slot: true,
        width: 80
      },
      {
        title: '创建时间',
        dataIndex: 'created_at_format',
        key: 'created_at_format',
        width: 150,
        slot: true
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        width: 120,
        slot: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 180,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 20,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const data = reactive({
    active: 1,
    info: null,
    defaultTime: '',
    loading: false,
    tableData: [],
    total: 0,
    tableConfigOptions,
    params: {
      page: 1,
      page_size: 20
    },
    dialog: {
      visible: false,
      titie: '',
      width: null,
      type: ''
    }
  })
  const statusType = (val: string | number) => {
    let status = {
      2: {
        color: '#E63030',
        text: '禁用'
      },
      1: {
        color: '#60A13B',
        text: '启用'
      }
    }
    return (status as any)[val]
  }
  // 获取列表
  const getList = async () => {
    try {
      data.tableConfigOptions.loading = true
      let res = await setRoleList(data.params)
      data.tableConfigOptions.dataSource = res.data?.list || []
      data.tableConfigOptions.pagination.total = res.data.total || 0
      data.tableConfigOptions.pagination.current = data.params.page || 0
    } catch (error) {
      console.log(error)
    } finally {
      data.tableConfigOptions.loading = false
    }
  }

  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }

  const searchForm = (v) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    data.params.page = 1
    getList()
  }
  const onShowDialog = (type, item) => {
    data.info = item
    switch (type) {
      case 'add':
        router.push({ name: 'SystemRoleEdit' })
        break
      case 'edit':
        router.push({ name: 'SystemRoleEdit', query: { id: item.id } })
        break
    }
  }
  const delItem = async (row) => {
    try {
      await setAddRole({ id: row.id, is_delete: 1 })
      message.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
    }
  }

  // 启用/禁用
  const enable = async (row) => {
    try {
      let resp = await setAddRole({ id: row.id, status: row.status === 1 ? 2 : 1 })
      message.success(resp.msg)
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  return {
    pageChange,
    searchForm,
    onShowDialog,
    delItem,
    enable,
    searchConfig,
    data,
    statusType,
    getList
  }
}
