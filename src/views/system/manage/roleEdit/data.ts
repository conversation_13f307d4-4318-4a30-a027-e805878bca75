import { computed, reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { randomWord } from '@/utils'
import { setRoleInfo, setAddRole, setRoleMenuList, get_department_info, setRolePermit } from './index.api'
import usedata from '@/views/workbench/privateDomainManagement/wechatGroupManagement/src/data'
const { tabAllCol } = usedata()
export default function datas() {
  const router = useRouter()
  const route = useRoute()
  const columns = ref([
    {
      title: '一级菜单',
      dataIndex: 'menu',
      key: 'menu',
      width: 140,
      customCell: (record: any, rowIndex: Number, column: any) => {
        return {
          rowSpan: record.menuRowSpan
        }
      }
    },
    {
      title: '二级菜单',
      dataIndex: 'twoSubMenu',
      key: 'twoSubMenu',
      width: 140,
      customCell: (record: any) => {
        return {
          rowSpan: record.twoSubMenuRowSpan
        }
      }
    },
    {
      title: '三级菜单',
      dataIndex: 'threeSubMenu',
      key: 'threeSubMenu',
      width: 170
      // customCell: (record, rowIndex, column) => {
      //   // console.log(onRowSpan(dataSource, 'operator', column, rowIndex), record, rowIndex, column)
      //   return {
      //     rowSpan: record.rowSpan
      //   }
      // }
    },
    {
      title: '功能权限',
      dataIndex: 'btn',
      key: 'btn'
      // customCell: (record, rowIndex, column) => {
      //   // console.log(onRowSpan(dataSource, 'operator', column, rowIndex), record, rowIndex, column)
      //   return {
      //     rowSpan: record.rowSpan
      //   }
      // }
    },
    {
      title: '字段权限',
      dataIndex: 'fileds',
      key: 'fileds'
    },
    {
      title: '数据权限',
      dataIndex: 'roles',
      key: 'roles',
      width: 240
      // customCell: (record, rowIndex, column) => {
      //   // console.log(onRowSpan(dataSource, 'operator', column, rowIndex), record, rowIndex, column)
      //   return {
      //     rowSpan: record.rowSpan
      //   }
      // }
    }
  ])
  const ruleData = reactive({
    name: [{ required: true, message: '请输入角色名称', trigger: ['change', 'blur'] }],
    department_id: [{ required: true, message: '请选择角色归属', trigger: ['change', 'blur'] }]
  })

  const ruleForm = ref(null)

  const state = reactive({
    queryId: route?.query?.id || null,
    departmentList: [],
    tableData: [],
    options: [],
    checkedAll: false,
    indeterminate: false,
    loading: false,
    submitLoading: false,
    addForm: {
      name: null,
      department_id: null,
      desc: null,
      id: null
    }
  })
  function groupData(data: any) {
    let obj: any = {
      menu: null,
      twoSubMenu: null
    }

    const mergekeys: String[] = Object.keys(obj)

    mergekeys.forEach((key: any) => {
      data.forEach((item: any, index: any) => {
        if (obj[key] !== item[key].id) {
          obj[key] = item[key].id
          let rowSpan = 0
          for (let i = 0; i < data.length; i++) {
            if (i >= index) {
              const currentItem = data[i]
              if (obj[key] === currentItem[key].id) {
                rowSpan += 1
              } else {
                break
              }
            }
          }
          item[`${key}RowSpan`] = rowSpan
        } else {
          item[`${key}RowSpan`] = 0
        }
      })
    })
    return data
  }

  // function groupData(data: any) {
  //   let currentMenu = ''
  //   return data.map((item: any, index: any) => {
  //     if (currentMenu !== item.menu) {
  //       currentMenu = item.menu
  //       let rowSpan = 0
  //       for (let i = 0; i < data.length; i++) {
  //         if (i >= index) {
  //           const currentItem = data[i]
  //           if (currentMenu === currentItem.menu) {
  //             rowSpan += 1
  //           } else {
  //             break
  //           }
  //         }
  //       }
  //       item.rowSpan = rowSpan
  //     } else {
  //       item.rowSpan = 0
  //     }

  //     return item
  //   })
  // }
  function updateCheckedStatus(data) {
    if (Array.isArray(data)) {
      data.forEach((item) => {
        updateCheckedStatus(item.children)

        if (item.children.length) {
          const hasChecked = item.children.some((child) => child.checked)
          const allChecked = item.children.every((child) => child.checked)

          // const hasEveryFileds = tableConfigOptions.columns.length === item.fields?.length
          item.checked = hasChecked && allChecked
          // item.indeterminate = item.checked ? false : true
        }
      })
    }
    return data
  }
  function formatData(node: any) {
    if (node.slug === 'customer_service_group') {
      const allFiledsBtn = tabAllCol.map((v) => {
        return {
          title: v.title,
          id: randomWord(true, 0, 8),
          key: v.key,
          node_type: 3,
          my_type: 'my-filed-btn',
          pid: node.id,
          checked: node?.fields?.includes(v.key) ? true : false,
          children: [],
          status: 0,
          level: 3,
          dataPermission: 1,
          slug: ''
        }
      })

      console.log('node.slug', node)
      node.children = node.children.concat(allFiledsBtn)
    }

    if (node.children) {
      node.children.forEach((child) => {
        formatData(child)
      })
    }
    if (Array.isArray(node)) {
      node.forEach((item) => {
        formatData(item)
      })
    }
  }

  const getRoleInfo = async () => {
    try {
      state.loading = true
      const resp = await setRoleInfo({ id: state.queryId })
      const ids = resp.data.role_info.department_path ? resp.data.role_info.department_path.split(',') : []
      state.addForm = {
        id: resp.data.role_info.id,
        name: resp.data.role_info.name || null,
        department_id: ids.map((v) => parseInt(v)) || null,
        desc: resp.data.role_info.desc || null
      }
      formatData(resp.data.menus)
      const result = updateCheckedStatus(resp.data.menus || [])
      state.tableData = parseTreeToRow(result, 'edit')
      groupData(state.tableData)
      allCheckedStatus()
      const roleAllSet = new Set() // 用 Set 来保存不重复的角色
      const roleAll = []
      // console.log('state.tableData', state.tableData)
      // state.tableData.forEach((v) => {
      //   if (!roleAllSet.has(v.menu.id)) {
      //     roleAllSet.add(v.menu.id)
      //     roleAll.push(v)
      //   }
      // })
      // roleAll.forEach((v) => {
      //   btnChange(v)
      // })
    } catch (error) {
      console.log(error)
    } finally {
      state.loading = false
    }
  }

  // 获取角色归属
  const getDepartmentInfo = async () => {
    try {
      const resp = await get_department_info()
      state.departmentList = resp.data || []
    } catch (error) {
      console.log(error)
    }
  }

  // 获取路由权限列表
  const getRoleList = async () => {
    try {
      state.loading = true
      let resp = await setRoleMenuList()
      formatData(resp.data)
      state.tableData = parseTreeToRow(resp.data)
      groupData(state.tableData)
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  // const fields = sanSubMenuData.allKFFileds.filter((son) => son.checked).map((son) => son.key)

  function dealTreeTo(data) {
    const roleSet = new Set() // 用 Set 来保存不重复的角色
    const role_ids = []

    const roleAllSet = new Set() // 用 Set 来保存不重复的角色
    const roleAll = []

    function traverse(node: any) {
      if ((node.checked || node.indeterminate) && !roleSet.has(node.id)) {
        roleSet.add(node.id)
        role_ids.push({
          id: node.id,
          pid: node.pid,
          title: node.title,
          field: node.key,
          my_type: node?.my_type,
          slug: node.slug,
          data_permission: node.dataPermission
        })
      }

      if (!roleAllSet.has(node.id)) {
        roleAllSet.add(node.id)
        roleAll.push(node)
      }

      if (node.children) {
        node.children.forEach((child) => {
          traverse(child)
        })
      }
    }

    data.forEach((item) => {
      traverse(item.menu)
    })

    return {
      role_ids,
      roleAll
    }
  }

  // 保存
  const submitForm = (formEl) => {
    // console.log(state.tableData)
    formEl.validate().then(() => {
      edit()
    })
  }
  function formatLastRoleIds(data: any) {
    const cleanData = data.filter((item) => !item.my_type)
    const filedsKeys = data.filter((item) => item.my_type).map((item) => item.field)

    cleanData.forEach((item) => {
      if (item.slug === 'customer_service_group') {
        item.fields = filedsKeys
      }
    })
    return cleanData
  }
  const edit = async () => {
    try {
      let role_ids = dealTreeTo(state.tableData).role_ids
      role_ids = formatLastRoleIds(role_ids)
      const d_id = state.addForm.department_id
      const params = {
        id: state.addForm.id || null,
        department_id: Array.isArray(d_id) ? d_id[d_id.length - 1] : 0,
        name: state.addForm.name,
        desc: state.addForm.desc,
        role_ids
      }
      // console.log('保存--', role_ids, params)

      // console.log('role_ids', role_ids)
      const resp = await setAddRole(params)
      message.success('操作成功')
      router.back()
    } catch (error) {
      console.log(error)
    }
  }

  // 查询权限
  const getRolePermit = async (row, id) => {
    try {
      let resp = await setRolePermit({ id })
      row.isAuth = resp.data?.data_permission || 1
    } catch (error) {
      console.error(error)
    }
  }

  const indeterminateStatus = computed(() => {
    return (prop, value) => {
      // console.log(prop, value, '是什么')
      let status = {
        all: false,
        menu: false,
        twoSubMenu: false,
        sanSubMenu: false
      }
      let hasFileds = false
      // if (prop == 'all') {
      //   const newArr = flatArray(filterTreeTo(state.tableData))
      //   const length = newArr.filter((v) => v.checked).length
      //   status[prop] = length == newArr.length || !length ? false : true
      //   setTimeout(() => {
      //     // 异步修改全选标识
      //     if (newArr.length == length && !state.checkedAll) state.checkedAll = true
      //     if (!length) state.checkedAll = false
      //   }, 0)
      // } else
      if (prop == 'menu') {
        const newArr = flatArray(value[prop].children)
        const length = newArr.filter((v) => v.checked).length
        status[prop] = length == newArr.length || !length ? false : true
        value[prop].indeterminate = status[prop]
      } else if (prop == 'twoSubMenu') {
        const newArr = flatArray(value[prop].children)
        const length = newArr.filter((v) => v.checked).length
        status[prop] = length == newArr.length || !length ? false : true
        value[prop].indeterminate = status[prop]
      } else if (prop === 'sanSubMenu') {
        // console.log('value[prop]?.allKFFileds--', value[prop]?.allKFFileds)
        const length = value[prop].children.filter((v) => v.checked).length
        status[prop] = length == value[prop].children.length || !length ? false : true
        // if (value[prop]?.allKFFileds?.length) {
        //   const length2 = value[prop].allKFFileds.filter((v) => v.checked).length
        //   hasFileds = length2 == value[prop]?.allKFFileds?.length || !length2 ? false : true
        // }
        // console.log('hasFileds--', status[prop], hasFileds)
        value[prop].indeterminate = status[prop]
      }
      return status[prop] || hasFileds
    }
  })

  // 数据权限改变
  function dataPermissionChange(row, e) {
    // console.log(row, e)
    if (row.sanSubMenu.id) {
      row.sanSubMenu.dataPermission = e.target.value
      getRolePermit(row, row.sanSubMenu.id)
      return
    }
    if (row.twoSubMenu.id) {
      row.twoSubMenu.dataPermission = e.target.value
      getRolePermit(row, row.twoSubMenu.id)
      return
    }
  }

  // 全部选中
  function checkAll(e) {
    state.tableData.forEach((v) => {
      if (v.menu.id) v.menu.checked = e.target.checked
      if (v.twoSubMenu.id) v.twoSubMenu.checked = e.target.checked
      if (v.sanSubMenu.id) v.sanSubMenu.checked = e.target.checked
      if (v.btns.length) {
        v.btns.forEach((item) => (item.checked = e.target.checked))
      }
    })
    if (e.target.checked) {
      state.indeterminate = false
    }
  }

  // 权限状态
  function allCheckedStatus() {
    const newArr = dealTreeTo(state.tableData).roleAll
    const length = newArr.filter((v) => v.checked).length
    state.indeterminate = !!length && length < newArr.length
    state.checkedAll = length === newArr.length
  }

  // 勾选主菜单
  function menuChange(data, e) {
    // console.log(data, e, '勾选主菜单')
    data.menu.checked = e.target.checked
    if (e.target.type) {
      state.tableData.forEach((v) => {
        if (v.menu.title === data.menu.title) {
          if (v.twoSubMenu.id) v.twoSubMenu.checked = e.target.checked
          if (v.sanSubMenu.id) v.sanSubMenu.checked = e.target.checked
          if (v.btns.length) {
            v.btns.forEach((item) => (item.checked = e.target.checked))
          }
        }
      })
    }
    allCheckedStatus()
  }

  // 勾选子菜单
  function twoSubMenuChange(data, e) {
    console.log('---2----data', data, e)
    data.twoSubMenu.checked = e.target.checked
    if (e.target.type) {
      let children = data.twoSubMenu.children || []
      children.forEach((v) => {
        v.checked = e.target.checked
        if (v.children?.length) {
          v.children.forEach((item) => (item.checked = e.target.checked))
        }
      })
    }
    const flag1 = (data.menu.children || []).every((v) => v.checked)
    // const flag2 = (data.menu.children || []).every((v) => !v.checked)
    if (flag1) menuChange(data, { target: { checked: true } })
    else menuChange(data, { target: { checked: false } })
  }

  // 勾选三级子菜单
  function sanSubMenuChange(data, e) {
    console.log('----3---data', data, e)
    data.sanSubMenu.checked = e.target.checked

    if (e.target.type) {
      let children = data.btns || []
      children.forEach((v) => {
        v.checked = e.target.checked
      })
    }
    const flag1 = (data.twoSubMenu.children || []).every((v) => v.checked)
    if (flag1) twoSubMenuChange(data, { target: { checked: true } })
    else twoSubMenuChange(data, { target: { checked: false } })
  }

  // btn按钮权限
  function btnChange(data, item, e) {
    console.log(data, item, e, 'btn按钮权限')
    if (item?.id) item.checked = e.target.checked
    const flag1 = data.btns.every((v) => v.checked)
    // const flag2 = data.sanSubMenu?.allKFFileds.every((v) => v.checked)
    // const flag2 = data.btns.every((v) => !v.checked)
    if (flag1) sanSubMenuChange(data, { target: { checked: true } })
    else sanSubMenuChange(data, { target: { checked: false } })
  }
  // 字段按钮权限
  function filedsChange(data, item, e) {
    if (item?.id) item.checked = e.target.checked
    const flag1 = data.sanSubMenu?.allKFFileds.every((v) => v.checked)
    if (flag1) sanSubMenuChange(data, { target: { checked: true } })
    else sanSubMenuChange(data, { target: { checked: false } })
  }

  function flatArray(arr) {
    let res = []
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].children instanceof Array && arr[i].children.length) {
        if (!arr[i].children.length) res.concat([arr[i]])
        res = res.concat(flatArray(arr[i].children))
      } else {
        res.push(arr[i])
      }
    }
    return res
  }

  // 树结构数据做扁平化处理
  function parseTreeToRow(node, type) {
    let arr = []
    node.forEach((item) => {
      item.level = 1
      if (item.children.length) {
        item.children.forEach((item2) => {
          item2.level = 2
          if (item2.children?.length) {
            const btns = item2.children.filter((v) => v.node_type == 3)
            item2.children.forEach((item3) => {
              item3.level = 3
              const sanSubMenu = item3.node_type == 3 ? {} : item3

              if (item3.children?.length) {
                item3.children.forEach((item4) => (item4.level = 4))
                arr.push({
                  menu: item,
                  twoSubMenu: item2,
                  sanSubMenu,
                  btns: item3.children,
                  data_permission: item3.dataPermission,
                  isAuth: type == 'edit' ? 1 : item3.dataPermission
                })
              } else {
                // console.log(item2, item3, item3.title)
                arr.push({
                  menu: item,
                  twoSubMenu: item2,
                  sanSubMenu,
                  btns,
                  data_permission: item3.dataPermission,
                  isAuth: type == 'edit' ? 1 : item3.dataPermission
                })
              }
            })
          } else {
            arr.push({
              menu: item,
              twoSubMenu: item2,
              sanSubMenu: {},
              btns: [],
              data_permission: item2.dataPermission,
              isAuth: type == 'edit' ? 1 : item2.dataPermission
            })
          }
        })
      } else {
        arr.push({
          menu: item,
          twoSubMenu: {},
          sanSubMenu: {},
          btns: [],
          data_permission: item.dataPermission,
          isAuth: type == 'edit' ? 1 : item.dataPermission
        })
      }
    })
    return arr
  }
  const init = () => {
    if (state.queryId) getRoleInfo()
    getDepartmentInfo()
    if (!state.queryId) getRoleList()
  }
  return {
    columns,
    ruleData,
    ruleForm,
    state,
    submitForm,
    indeterminateStatus,
    dataPermissionChange,
    checkAll,
    menuChange,
    twoSubMenuChange,
    sanSubMenuChange,
    btnChange,
    filedsChange,
    router,
    init
  }
}
