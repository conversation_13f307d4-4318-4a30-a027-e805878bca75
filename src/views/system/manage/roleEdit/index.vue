<template>
  <div>
    <BreadcrumbCom
      :data="[{ title: '角色管理', pathname: 'Role' }, { title: state.queryId ? '编辑角色' : '新增角色' }]"
    />
    <CardBaseLayout>
      <template #content>
        <div class="model_block" style="margin-bottom: 20px">
          <a-form
            class="form_box"
            :model="state.addForm"
            ref="ruleForm"
            :rules="ruleData"
            :labelCol="{ style: 'width: 82px;' }"
          >
            <div class="header-title">角色基本信息</div>
            <a-row>
              <a-col :xs="20" :sm="20" :md="16" :lg="12" :xl="12">
                <a-form-item label="角色名称：" name="name">
                  <a-input v-model:value="state.addForm.name" :maxlength="50" placeholder="请输入角色名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :xs="20" :sm="20" :md="16" :lg="12" :xl="12">
                <a-form-item label="角色归属：" name="department_id">
                  <a-cascader
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    v-model:value="state.addForm.department_id"
                    :options="state.departmentList"
                    changeOnSelect
                    placeholder="请选择角色归属"
                    :field-names="{
                      children: 'children',
                      label: 'name',
                      value: 'id'
                    }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :xs="20" :sm="20" :md="16" :lg="12" :xl="12">
                <a-form-item label="角色描述：" name="desc">
                  <a-textarea
                    v-model:value="state.addForm.desc"
                    :rows="6"
                    show-word-limit
                    :maxlength="200"
                    placeholder="请简单描述角色"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <div class="header-title">
              <div>权限设置</div>
              <a-checkbox v-model:checked="state.checkedAll" :indeterminate="state.indeterminate" @change="checkAll">
                全选
              </a-checkbox>
            </div>
            <div class="table_style">
              <a-table
                sticky
                ref="tkTableRef"
                :data-source="state.tableData"
                bordered
                emptyText="空空如也 没有内容"
                :pagination="false"
                style="width: 100%"
                size="small"
                :scroll="{ x: 1000 }"
                :columns="columns"
              >
                <template #bodyCell="{ record, column }">
                  <template v-if="column.dataIndex === 'menu'">
                    <!-- false checked全部为true或者全部为false， true至少有一个checked为true且不全部为true -->
                    <a-checkbox
                      v-model:checked="record.menu.checked"
                      :indeterminate="indeterminateStatus('menu', record)"
                      @change="(e) => menuChange(record, e)"
                    >
                      {{ record.menu.title }}
                    </a-checkbox>
                  </template>
                  <template v-if="column.dataIndex === 'twoSubMenu'">
                    <a-checkbox
                      v-model:checked="record.twoSubMenu.checked"
                      :indeterminate="indeterminateStatus('twoSubMenu', record)"
                      v-if="record.twoSubMenu.id"
                      @change="(e) => twoSubMenuChange(record, e)"
                    >
                      {{ record.twoSubMenu.title }}
                    </a-checkbox>
                  </template>
                  <template v-if="column.dataIndex === 'threeSubMenu'">
                    <a-checkbox
                      v-model:checked="record.sanSubMenu.checked"
                      :indeterminate="indeterminateStatus('sanSubMenu', record)"
                      v-if="record.sanSubMenu.id"
                      @change="(e) => sanSubMenuChange(record, e)"
                    >
                      {{ record.sanSubMenu.title }}
                    </a-checkbox>
                  </template>
                  <template v-if="column.dataIndex === 'btn'">
                    <a-checkbox
                      v-for="it in record.btns?.filter((it) => !it.my_type)"
                      :key="it"
                      v-model:checked="it.checked"
                      @change="(e) => btnChange(record, it, e)"
                    >
                      {{ it.title }}
                    </a-checkbox>
                  </template>
                  <template v-if="column.dataIndex === 'fileds'">
                    <div v-if="record.sanSubMenu.slug === 'customer_service_group'">
                      <!-- <a-checkbox
                        v-for="it in record.sanSubMenu.allKFFileds"
                        :key="it"
                        v-model:checked="it.checked"
                        @change="(e) => filedsChange(record, it, e)"
                      >
                        {{ it.title }}
                      </a-checkbox> -->
                      <a-checkbox
                        v-for="it in record.btns?.filter((it) => it.my_type)"
                        :key="it"
                        v-model:checked="it.checked"
                        @change="(e) => btnChange(record, it, e)"
                      >
                        {{ it.title }}
                      </a-checkbox>
                    </div>
                  </template>
                  <template v-if="column.dataIndex === 'roles'">
                    <a-radio-group
                      v-model:value="record.data_permission"
                      @change="dataPermissionChange(record, $event)"
                    >
                      <a-radio :value="1" :disabled="![1].includes(record.isAuth)">全部</a-radio>
                      <a-radio :value="2" :disabled="![1, 2].includes(record.isAuth)">部门</a-radio>
                      <a-radio :value="3">个人</a-radio>
                    </a-radio-group>
                  </template>
                </template>
              </a-table>
            </div>
          </a-form>
          <div class="buttom">
            <a-button v-auth="['saveRole']" type="primary" :loading="state.submitLoading" @click="submitForm(ruleForm)">
              保存
            </a-button>
            <a-button @click="router.back()">取消</a-button>
          </div>
        </div>
      </template>
    </CardBaseLayout>
  </div>
</template>

<script setup>
  import datas from './data'
  const {
    columns,
    ruleData,
    ruleForm,
    state,
    submitForm,
    indeterminateStatus,
    dataPermissionChange,
    checkAll,
    menuChange,
    twoSubMenuChange,
    sanSubMenuChange,
    btnChange,
    filedsChange,
    router,
    init
  } = datas()
  init()
  const renderTitle = (data) => {
    console.log('title----', data)
  }
</script>

<style lang="scss" scoped>
  .table_style {
    :deep(.ant-table-column-sorter) {
      color: #cccccc;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #cccccc;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      // overflow: hidden;
      border-color: #e8e9ec !important;
      .ant-table-header.ant-table-sticky-holder {
        top: -16px !important;
      }
      .ant-table-summary.ant-table-sticky-holder {
        bottom: -24px !important;
      }
      .ant-table-sticky-scroll {
        display: none;
      }
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: top;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .header-title {
    font-size: 18px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #080f1e;
    margin-bottom: 20px;
  }
  .buttom {
    padding-left: 24px;
    padding-top: 30px;
  }
  .hyd-wrapper {
    :deep(.ant-card-head-wrapper .ant-card-head-title) {
      margin-left: 0 !important;
      &::before {
        display: none !important;
      }
    }
  }
</style>
