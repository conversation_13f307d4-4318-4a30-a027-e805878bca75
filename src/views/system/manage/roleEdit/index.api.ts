import http from '@/utils/request'

/**
 * 用户全局菜单
 */
export const setRoleMenuList = (data) => {
  return http('get', `/common/role/role_menu`, data)
}

/**
 * 角色列表
 */
export const setRoleList = (data) => {
  return http('get', `/common/role/list`, data)
}

/**
 * 新权限详情
 */
export const setRoleInfo = (data) => {
  return http('get', `/common/role/role_info`, data)
}

/**
 * 权限添加更新
 */
export const setAddRole = (data) => {
  return http('post', `/common/role/role_add`, data)
}

/**
 * 查询权限
 */
export const setRolePermit = (data) => {
  return http('get', `/common/role/role_permit`, data)
}

export const get_department_info = (data) => {
  return http('get', `/common/department/info`, data)
}
