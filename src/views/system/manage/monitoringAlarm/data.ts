import { reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { queryList, deleteItem } from './index.api'
export default function datas() {
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'name',
        value: undefined,
        props: {
          placeholder: '请输入名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1100
    },
    dataSource: [],
    columns: [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 160
      },
      {
        title: '监控内容',
        dataIndex: 'type',
        key: 'type',
        width: 120
      },
      {
        title: '触发条件',
        dataIndex: 'conf',
        key: 'conf',
        width: 200
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 120,
        fixed: 'right'
      }
    ],
    pagination: false
  }
  const typeList = [
    {
      label: '成员异常',
      content: '企微内&%&个成员异常',
      value: 3
    },
    {
      label: '获客链接异常',
      content: '客服组内&%&个获客链接异常',
      value: 4
    },
    {
      label: '爆量预警',
      content: '$%$分钟内加粉数大于等于&%&个',
      value: 1
    },
    {
      label: '客服组异常',
      content: '客服组内可正常访问获客链接少于&%&个',
      value: 5
    },
    {
      label: '计划超出成本限制',
      content: '计划超出产品库成本限制',
      value: 2
    },
    {
      label: '计划定向超出监测定向范围',
      content: '计划名称定向与链接定向不一致',
      value: 6
    }
  ]
  const state = reactive({
    selectedType: [],
    tableConfigOptions,
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      data: {}
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      let res = await queryList()
      state.tableConfigOptions.dataSource = res.data?.list || []
      state.selectedType = res.data?.list.map((it: any) => it.type)
    } catch (error) {
      console.log(error)
    } finally {
      state.tableConfigOptions.loading = false
    }
  }
  const handlerAction = async (type: string, item?: any) => {
    try {
      if (type === 'delete') {
        await deleteItem({ id: item.id })
        message.success('删除成功')
        getList()
      } else {
        state.dialog.title = type === 'add' ? '新增监控告警' : '编辑监控告警'
        state.dialog.width = 600
        state.dialog.visible = true
        state.dialog.type = type
        state.dialog.data = type === 'edit' ? item : undefined
      }
    } catch (err) {
      console.log(err)
    }
  }
  const onEvent = ({ cmd, val }: any) => {
    state.dialog.visible = false
    if (cmd === 'submit') {
      getList()
    }
  }
  return {
    handlerAction,
    searchConfig,
    state,
    onEvent,
    typeList,
    getList
  }
}
