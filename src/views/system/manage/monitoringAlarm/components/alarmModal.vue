<template>
  <div>
    <a-form
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: { width: '80px' } }"
      :colon="true"
      :rules="rules"
      labelAlign="left"
    >
      <a-form-item label="名称" name="name">
        <a-input v-model:value.trim="state.form.name" :maxLength="30" :disabled="item?.id" placeholder="请输入名称" />
      </a-form-item>
      <a-form-item label="监控内容" name="type">
        <a-select
          :disabled="item?.id"
          placeholder="请选择监控内容"
          :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          v-model:value="state.form.type"
          :options="state.typeList"
          @change="contetnChange"
        ></a-select>
      </a-form-item>

      <a-form-item label="触发条件" required v-if="state.form.type" class="mb-0">
        <template v-if="state.form.type === 3">
          <div class="flex-y-center">
            <span class="mt--18px mr-4px">企微内</span>
            <a-form-item name="value">
              <a-input-number
                class="w-100px"
                :controls="false"
                min="1"
                :precision="0"
                v-model:value.trim="state.form.value"
                placeholder="请输入个数"
              />
            </a-form-item>
            <span class="mt--18px ml-4px">个成员异常</span>
          </div>
        </template>
        <template v-if="state.form.type === 4">
          <div class="flex-y-center">
            <span class="mt--18px mr-4px">客服组内</span>
            <a-form-item name="value">
              <a-input-number
                class="w-100px"
                :controls="false"
                min="1"
                :precision="0"
                v-model:value.trim="state.form.value"
                placeholder="请输入个数"
              />
            </a-form-item>
            <span class="mt--18px ml-4px">个获客链接异常</span>
          </div>
        </template>
        <template v-if="state.form.type === 1">
          <div class="flex-y-center">
            <a-form-item name="time">
              <a-input-number
                class="w-100px"
                :controls="false"
                min="1"
                :precision="0"
                v-model:value.trim="state.form.time"
                placeholder="请输入分钟"
              />
            </a-form-item>
            <span class="mt--18px mr-4px ml-4px">分钟内加粉数大于等于</span>
            <a-form-item name="value">
              <a-input-number
                class="w-100px"
                :controls="false"
                min="1"
                :precision="0"
                v-model:value.trim="state.form.value"
                placeholder="请输入个数"
              />
            </a-form-item>
            <span class="mt--18px ml-4px">个</span>
          </div>
        </template>
        <template v-if="state.form.type === 5">
          <div class="flex-y-center">
            <span class="mt--18px mr-4px">客服组内可正常访问获客链接少于</span>
            <a-form-item name="value">
              <a-input-number
                class="w-100px"
                :controls="false"
                min="1"
                :precision="0"
                v-model:value.trim="state.form.value"
                placeholder="请输入个数"
              />
            </a-form-item>
            <span class="mt--18px ml-4px">个</span>
          </div>
        </template>
        <template v-if="state.form.type === 2">
          <div class="flex-y-center">
            <span>计划超出产品库成本限制</span>
          </div>
        </template>
        <template v-if="state.form.type === 6">
          <div class="flex-y-center">
            <span>计划名称定向与链接定向不一致</span>
          </div>
        </template>
      </a-form-item>
      <div class="c-#656D7D font-size-12px" :class="[!isMobile && 'ml-80px']">
        需关注
        <a-tooltip color="#fff" placement="bottom">
          <template #title>
            <a-image :src="state.qr.code" style="width: 105px"></a-image>
          </template>
          <span class="c-primary cursor-pointer">{{ state.qr.name || '--' }}</span>
        </a-tooltip>
        公众号才能接收通知
      </div>
    </a-form>
    <div class="mt-24px text-right">
      <a-button @click="emit('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submitForm(ruleForm)">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { reactive, ref, nextTick, onMounted } from 'vue'
  import { addItem, getQrcode } from '../index.api'
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  import datas from '../data'
  const { isMobile } = useApp()
  const { typeList } = datas()
  const props = defineProps(['item', 'selectedType'])
  const emit = defineEmits(['onEvent'])
  const ruleForm = ref()
  interface Form {
    name?: string | undefined
    type?: number | undefined
    value?: number | undefined
    time?: number | undefined
  }
  const state = reactive({
    qr: {
      code: undefined,
      name: undefined
    },
    typeList: [] as any,
    loading: false,
    form: {
      name: undefined,
      type: undefined,
      value: undefined,
      time: undefined
    } as Form
  })
  const rules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: ['change', 'blur'] }],
    type: [{ required: true, message: '请选择监控内容', trigger: ['change', 'blur'] }],
    value: [{ required: true, message: '请输入个数', trigger: ['change', 'blur'] }],
    time: [{ required: true, message: '请输入分钟', trigger: ['change', 'blur'] }]
  })

  const initData = async () => {
    try {
      if (props.item?.id) {
        let { name, type, conf } = props.item
        state.form = {
          name,
          type
        }
        if (conf) {
          let { value, time } = JSON.parse(conf)
          state.form.value = value
          state.form.time = time
        }
        state.typeList = typeList
      } else {
        state.typeList = typeList.map((it: any) => {
          return {
            ...it,
            disabled: props.selectedType.includes(it.value)
          }
        })
      }
      let res: any = await getQrcode()
      state.qr.code = res?.data?.url
      state.qr.name = res?.data?.app_name
    } catch (error) {
      console.error(error)
    }
  }

  onMounted(() => {
    initData()
  })
  const contetnChange = () => {
    state.form.value = undefined
    state.form.time = undefined
  }
  const submitForm = async (formEl: any) => {
    try {
      state.loading = true
      await formEl.validate()
      let params = {
        name: state.form.name,
        type: state.form.type
      } as any
      if (props.item?.id) {
        params.id = props.item.id
      }
      if (state.form.type === 1) {
        params.conf = JSON.stringify({ value: state.form.value, time: state.form.time })
      } else {
        params.conf = JSON.stringify({ value: state.form.value })
      }
      await addItem(params)
      emit('onEvent', { cmd: 'submit', status: true })
      message.success('保存成功')
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .qrcode-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    :deep(.ant-qrcode) {
      padding: 4px;
    }
  }
</style>
