<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>监控告警</div>
      </template>
      <template #extra>
        <a-button v-auth="['addMonitoringAlarm']" type="primary" @click="handlerAction('add')">新增监控告警</a-button>
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="state.tableConfigOptions">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'name'">
              <a-tooltip>
                <template #title v-if="scope.record.name.length > 15">{{ scope.record.name }}</template>
                <div class="text_overflow_row1">{{ scope.record.name }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'type'">
              <span>{{ typeList.find((it) => scope.record.type === it.value)?.label || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'conf'">
              <span>{{
                typeList
                  .find((it) => scope.record.type == it.value)
                  ?.content.replace('$%$', JSON.parse(scope.record.conf)?.time)
                  .replace('&%&', JSON.parse(scope.record.conf)?.value)
              }}</span>
            </template>
            <template v-if="scope.column.key === 'action'">
              <a-button
                type="link"
                class="p-0!"
                size="small"
                v-auth="['editMonitoringAlarm']"
                style="margin-right: 10px"
                @click="handlerAction('edit', scope.record)"
                >编辑</a-button
              >
              <a-popconfirm
                title="是否确认删除当前信息？"
                placement="topRight"
                @confirm="handlerAction('delete', scope.record)"
              >
                <a-button type="link" class="p-0!" size="small" v-auth="['deleteMonitoringAlarm']">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :width="state.dialog.width"
      :title="state.dialog.title"
      :footer="null"
      destroyOnClose
      :centered="true"
    >
      <alarmModal
        v-if="['add', 'edit'].includes(state.dialog.type)"
        :item="state.dialog.data"
        :selectedType="state.selectedType"
        @onEvent="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup>
  import datas from './data'
  import alarmModal from './components/alarmModal.vue'
  const { handlerAction, searchConfig, state, onEvent, typeList, getList } = datas()
  getList()
</script>

<style lang="scss" scoped>
  :deep(.common_page_warp > .ant-card-body) {
    padding-top: 0px;
  }
</style>
