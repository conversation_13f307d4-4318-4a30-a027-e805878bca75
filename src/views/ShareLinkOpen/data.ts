import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { checkVerifyCode, getWechatLinkShareList, getFansList, updateLinkInfo } from './index.api'
export default function datas() {
  const router = useRouter()
  const searchConfig = reactive({
    data: [
      {
        type: 'input.text',
        field: 'user_name',
        value: undefined,
        props: {
          placeholder: '请输入客服名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'input.text',
        field: 'user_id',
        value: undefined,
        props: {
          placeholder: '请输入客服ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      },
      {
        type: 'date',
        field: 'created_at',
        value: [getCurrentDate(), getCurrentDate()],
        range: 'NearlyThirty',
        props: {
          placeholder: ['开始时间', '结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 4,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })
  const jfSearchConfig = reactive({
    data: [
      {
        type: 'date',
        field: 'created_at',
        value: undefined,
        range: 'NearlyThirty',
        props: {
          placeholder: ['开始时间', '结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 12,
          lg: 12,
          xl: 6,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  })
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    dataSource: [],
    scroll: {
      x: 1800
    },
    columns: [
      {
        title: '所属企微/ID',
        dataIndex: 'corp_id',
        key: 'corp_id',
        width: 240
      },
      {
        title: '客服名称/ID',
        dataIndex: 'user_name',
        key: 'user_name',
        width: 200
      },
      {
        title: '标签',
        key: 'tag_list',
        dataIndex: 'tag_list',
        width: 120
      },
      {
        title: '获客链接',
        key: 'link_id',
        dataIndex: 'link_id',
        width: 260
      },
      {
        title: '获客链接异常监测',
        key: 'status',
        dataIndex: 'status',
        width: 140
      },
      {
        title: '上下线',
        key: 'on_sale',
        dataIndex: 'on_sale',
        width: 100
      },
      {
        title: '进粉数',
        key: 'add_fans_num',
        dataIndex: 'add_fans_num',
        width: 100
      },
      {
        title: '自动上下线规则',
        key: 'auth_on_sale',
        dataIndex: 'auth_on_sale',
        width: 180
      },
      {
        title: '进粉权重',
        key: 'weight',
        dataIndex: 'weight',
        width: 100
      },
      {
        title: '欢迎语',
        dataIndex: 'welcome_txt',
        key: 'welcome_txt',
        width: 120
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      page: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`,
      onChange: (page: number, pageSize: number) => {
        listPagination(page, pageSize)
      }
    }
  }
  // 获取当前日期并格式化为 YYYY-MM-DD 格式
  function getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const initParams = ref({
    user_name: '',
    nameID: '',
    created_at: [getCurrentDate(), getCurrentDate()]
  })
  const jsListParams = ref({
    created_at: [getCurrentDate(), getCurrentDate()]
  })
  //检测状态
  const text_status = (val: string | number) => {
    let status = {
      1: '正常',
      2: '异常'
    }
    return (status as any)[val]
  }
  const statusType = (val: string | number) => {
    let status = {
      1: {
        text: '上线'
      },
      2: {
        text: '下线'
      }
    }
    return (status as any)[val]
  }
  const auth_on_sale_text = (val: string | number) => {
    let status = {
      1: '不设置',
      2: '按时间段',
      3: '按成功添加企业微信数'
    }
    return (status as any)[val]
  }
  const jfOptions = reactive({
    bordered: false,
    loading: false,
    rowKey: 'id',
    size: 'small',
    dataSource: [],
    scroll: { y: '76vh', scrollToFirstRowOnChange: false },
    columns: [
      {
        title: '客户名称',
        dataIndex: 'customer_name',
        key: 'customer_name',
        width: 200
      },
      {
        title: '标签',
        dataIndex: 'customer_tag',
        key: 'customer_tag',
        width: 120
      },
      {
        title: '回话状态',
        key: 'is_chat',
        dataIndex: 'is_chat',
        width: 120
      },
      {
        title: '跟进企微',
        key: 'wx_user_id',
        dataIndex: 'wx_user_id',
        width: 120
      },
      {
        title: '获客链接',
        key: 'wx_link_id',
        dataIndex: 'wx_link_id',
        width: 120
      },
      {
        title: '企微名称',
        key: 'corp_name',
        dataIndex: 'corp_name',
        width: 120
      },
      {
        title: '点击时间',
        key: 'created_at',
        dataIndex: 'created_at',
        width: 140
      },
      {
        title: '回传时间',
        key: 'callback_at',
        dataIndex: 'callback_at',
        width: 140
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`,
      onChange: (page: number, pageSize: number) => {
        jsListPagination(page, pageSize)
      }
    }
  })
  const data = reactive({
    passwordForm: {
      url_code: '',
      verify_code: '',
    },
    passwordFormOpen: true,
    tableConfigOptions,
    jsOPen: false,
    jsItem: {},
    jfOptions,
    updateLineOpen: false,
    updateLineItem: {},
    change_onsale: 2, // 1允许 2禁止
  })
  const passwordRules = {
    verify_code: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 6, message: '密码长度必须为6位', trigger: 'blur' },
    ]
  }
  // 初始化数据
  const initData = async () => {
    try {
      data.tableConfigOptions.loading = true
      const res = await getWechatLinkShareList({
        url_code: data.passwordForm.url_code,
        verify_code: data.passwordForm.verify_code,
        page: data.tableConfigOptions.pagination.page,
        page_size: data.tableConfigOptions.pagination.pageSize,
        ...initParams.value,
        created_at: initParams.value.created_at ? `${initParams.value.created_at[0]}_${initParams.value.created_at[1]}` : '',
      })
      res.data.list = res.data.list || []
      data.tableConfigOptions.dataSource = res.data.list?.map((item: any) => {
        return {
          ...item,
          fail_reason: item.fail_status === 1 ? '成员异常' : item.fail_reason || '',
          status: item.fail_status === 1 ? 4 : item.status // 将 fail_status 1 映射为状态 4
        }
      })
      data.tableConfigOptions.pagination.total = res.data.total || 10
      data.tableConfigOptions.pagination.current = res.data.page || 1
      data.tableConfigOptions.loading = false
      console.log(res);
    } catch (error) {
      data.tableConfigOptions.loading = false
      if (error.code === 10003) {
        window.location.reload();
      }
    }
  }
  const searchForm = (e:any) => {
    data.tableConfigOptions.pagination.page = 1
    if (!e.status) {
      e.formData.created_at = [getCurrentDate(), getCurrentDate()]
      initParams.value.created_at = [getCurrentDate(), getCurrentDate()]
    }
    initParams.value = e.formData
    initData()
  }
  const jfSearchForm = (e:any) => {
    data.jfOptions.pagination.page = 1
    if (!e.status) {
      e.formData.created_at = [getCurrentDate(), getCurrentDate()]
      jsListParams.value.created_at = [getCurrentDate(), getCurrentDate()]
    }
    jsListParams.value = e.formData
    getFansListFun()
  }
  const listPagination = (page: number, pageSize: number) => {
    data.tableConfigOptions.pagination.page = page
    data.tableConfigOptions.pagination.pageSize = pageSize
    initData()
  }
  const getFansListFun = async () => {
    try {
      data.jfOptions.loading = true
      const res = await getFansList({
        url_code: data.passwordForm.url_code,
        verify_code: data.passwordForm.verify_code,
        page: data.jfOptions.pagination.page,
        page_size: data.jfOptions.pagination.pageSize,
        wx_link_id: data.jsItem.link_id,
        customer_add_time: jsListParams.value.created_at ? `${jsListParams.value.created_at[0]}_${jsListParams.value.created_at[1]}` : '',
      })
      data.jfOptions.dataSource = res.data.list || []
      data.jfOptions.pagination.total = res.data.total || 10
      data.jfOptions.pagination.current = res.data.page || 1
      data.jfOptions.loading = false
    } catch (error) {
      data.jfOptions.loading = false
      if (error.code === 10003) { 
        window.location.reload();
      }
    }
  }
  const jsListPagination = (page: number, pageSize: number) => {
    data.jfOptions.pagination.page = page
    data.jfOptions.pagination.pageSize = pageSize
    getFansListFun()
  }
  const onShowDialog = (record) => {
    data.jsItem = record
    data.jsOPen = true
    if (initParams.value.created_at) {
      jfSearchConfig.data[0].value = initParams.value.created_at
      jsListParams.value.created_at = initParams.value.created_at
    } else {
      jfSearchConfig.data[0].value = [getCurrentDate(), getCurrentDate()]
      jsListParams.value.created_at = [getCurrentDate(), getCurrentDate()]
    }
    getFansListFun()
  }
  // 密码确认框
  const passwordRef = ref<any>(null)
  const hashParts = window.location.href.split('?');
  const params = new URLSearchParams(hashParts[1]);
  data.passwordForm.url_code = params.get('url_code') as string
  const handleOk = () => {
    try {
      if (hashParts.length > 1) {
        passwordRef.value.validate().then(async () => {
          const res = await checkVerifyCode(data.passwordForm)
          data.change_onsale = res.data.change_onsale
          data.passwordFormOpen = false
          initData()
        }).catch(() => {
        });
      } else {
        message.error('链接错误');
      }
    } catch {}
  }
  const updateLineEdit = (record) => {
    data.updateLineItem = JSON.parse(JSON.stringify(record))
    data.updateLineOpen = true
  }
  const updateLineOk = async () => {
    try {
      await updateLinkInfo({
        url_code: data.passwordForm.url_code,
        verify_code: data.passwordForm.verify_code,
        on_sale: data.updateLineItem.on_sale,
        ids: [data.updateLineItem.id]
      })
      data.updateLineOpen = false
      initData()
    } catch(error) {
      if (error.code === 10003) {
        window.location.reload();
      }
    }
  }
  return {
    data,
    handleOk,
    passwordRules,
    passwordRef,
    searchConfig,
    jfSearchConfig,
    text_status,
    statusType,
    auth_on_sale_text,
    searchForm,
    jfSearchForm,
    onShowDialog,
    updateLineEdit,
    updateLineOk
  }
}