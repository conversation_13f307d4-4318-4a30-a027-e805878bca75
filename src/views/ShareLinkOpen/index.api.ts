// 导出请求封装方法
import http from '@/utils/request'

// 验证密码
export const checkVerifyCode = (data: any) => {
  return http('post', `/common/wechat_group_share/check_verify_code`, data, {doNotToken: true})
}
// 获取获客链接列表
export const getWechatLinkShareList = (data: any) => {
  return http('post', `/common/wechat_link_share/list`, data, {doNotToken: true})
}
// 获取加粉列表
export const getFansList = (data: any) => {
  return http('post', `/common/adConversion_share/get_list`, data, {doNotToken: true})
}
// 更新获客链接上下线
export const updateLinkInfo = (data: any) => {
  return http('post', `/common/wechat_link_share/update_link_info`, data, {doNotToken: true})
}
