<template>
  <div class="share_link_page">
    <a-modal
      v-model:open="data.passwordFormOpen"
      title="请输入密码"
      centered
      @ok="handleOk"
      width="500px"
      :closable="false"
      :maskClosable="false"
      :keyboard="false"
      :cancel-button-props="{ style: { display: 'none' } }"
    >
      <a-form class="mt-20px" :model="data.passwordForm" ref="passwordRef" :rules="passwordRules">
        <a-form-item label="密码" name="verify_code">
          <div class="flex flex-items-center">
            <a-input
              v-model:value="data.passwordForm.verify_code"
              :maxlength="6"
              @keydown.space.prevent
              placeholder="请输入密码"
            />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
    <div class="pa-24px" v-show="!data.passwordFormOpen">
      <div class="bg-#fff rounded-16px pt-19px px-24px pb-16px">
        <SearchBaseLayout :data="searchConfig.data" @changeValue="searchForm" :actions="searchConfig.options" />
        <div class="page_main_table pt-24px">
          <TableZebraCrossing :data="data.tableConfigOptions">
            <template #headerCell="{ scope: { column } }">
              <div>
                <span>{{ column.title }}</span>
                <a-tooltip>
                  <template #title>{{ column.tips }}</template>
                  <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
                </a-tooltip>
              </div>
            </template>
            <template #bodyCell="{ scope }">
              <template v-if="scope.column.key === 'corp_id'">
                <div class="flex">
                  <div class="c-#313233 line-height-14px" v-if="scope.record.corp_name?.length > 15">
                    <a-tooltip>
                      <template #title
                        ><div>{{ scope.record.corp_name }}</div></template
                      >
                      <div class="text_overflow c-#313233">{{ scope.record.corp_name }}</div>
                    </a-tooltip>
                  </div>
                  <div v-else>
                    <span>{{ scope.record.corp_name || '--' }}</span>
                  </div>
                </div>

                <div class="mt-8px c-#7A869F font-size-12px line-height-12px">
                  <div v-if="scope.record.corp_id">
                    <a-tooltip>
                      <template #title
                        ><div>{{ scope.record.corp_id }}</div></template
                      >
                      <div class="text_overflow">ID：{{ scope.record.corp_id }}</div>
                    </a-tooltip>
                  </div>
                  <span v-else> ID：--</span>
                </div>
              </template>
              <template v-if="scope.column.key === 'link_id'">
                <a-tooltip>
                  <template #title>
                    <div>{{ scope.record.link_name || '-' }}</div>
                  </template>
                  <div class="flex-y-center">
                    <div class="text_overflow c-#313233">{{ scope.record.link_name || '-' }}</div>
                  </div>
                </a-tooltip>
                <a-tooltip v-if="scope.record.link">
                  <template #title>{{ scope.record.link }}</template>
                  <div class="flex-y-center">
                    <span class="goods_info_data_name"> 链接： {{ scope.record.link }} </span>
                    <CopyOutlined class="c-primary" @click="copy(scope.record.link)" />
                  </div>
                </a-tooltip>
                <div class="flex-y-center" v-else>
                  <span class="goods_info_data_name"> 链接： --</span>
                </div>
              </template>
              <template v-if="scope.column.key === 'user_name'">
                <div class="flex">
                  <div class="c-#313233 line-height-14px" v-if="scope.record.user_name?.length > 15">
                    <a-tooltip>
                      <template #title
                        ><div>{{ scope.record.user_name || '-' }}</div></template
                      >
                      <div class="text_overflow c-#313233">{{ scope.record.user_name || '-' }}</div>
                    </a-tooltip>
                  </div>
                  <div v-else>
                    <span>{{ scope.record.user_name || '--' }}</span>
                  </div>
                </div>
                <a-tooltip>
                  <template #title
                    ><div>{{ scope.record.wx_user_id || '-' }}</div></template
                  >
                  <div class="text_overflow mt-8px c-#7A869F font-size-12px" style="word-break: break-all">
                    ID：{{ scope.record.wx_user_id || '--' }}
                  </div>
                </a-tooltip>
                <div class="flex-y-center mt-4px" v-if="[1].includes(scope.record.fail_status)">
                  <ExclamationCircleOutlined class="c-red font-size-12px" />
                  <div class="c-red ml-4px font-size-12px h-12px line-height-12px">成员异常</div>
                </div>
              </template>
              <template v-if="scope.column.key === 'status'">
               <div class="flex-y-center">
                  <div
                    :class="kfLinkExceptionEnum[scope.record.status].className"
                    v-if="kfLinkExceptionEnum[scope.record.status]"
                  >
                    {{ kfLinkExceptionEnum[scope.record.status].text }}
                  </div>
                  <a-tooltip>
                    <template #title
                      ><div>{{ scope.record.fail_reason || '-' }}</div></template
                    >
                    <ExclamationCircleOutlined
                      v-if="[2, 4].includes(scope.record.status) && scope.record.fail_reason"
                      class="c-red font-size-12px mr-4px"
                    />
                  </a-tooltip>
                </div>
              </template>
              <template v-if="scope.column.key === 'tag_list'">
                <div class="flex">
                  <a-tooltip v-if="scope.record.tags">
                    <template #title><div>{{ scope.record.tags }}</div></template>
                    <div class="text_overflow c-#313233 max-w-120px">{{ scope.record.tags }}</div>
                  </a-tooltip>
                  <span v-else>--</span>
                </div>
              </template>
              <template v-if="scope.column.key === 'auth_on_sale'">
                <span class="text_overflow">{{ auth_on_sale_text(scope.record.auth_on_sale) || '--' }}</span>
                <EditOutlined v-if="data.change_onsale == 1" class="c-#FE9D35 ml4px" @click="updateLineEdit(scope.record)"></EditOutlined>
              </template>

              <template v-if="scope.column.key === 'on_sale'">
                <span class="item-tag" :class="{ online: scope.record?.on_sale == 1, offline: scope.record?.on_sale == 2 }">
                  {{ statusType(scope.record?.on_sale)?.text }}
                </span>
              </template>
              <template v-if="scope.column.key === 'add_fans_num'">
                <a-button class="h-auto pa-0" type="link" @click="onShowDialog(scope.record)">{{
                  scope.record.add_fans_num
                }}</a-button>
              </template>
              <template v-if="scope.column.key === 'welcome_txt'">
                <a-tooltip v-if="scope.record.welcome_txt">
                  <template #title><div>{{ scope.record.welcome_txt }}</div></template>
                  <div class="flex-y-center">
                    <span class="text_overflow inline-block max-w-120px">{{
                      scope.record.welcome_txt
                    }}</span>
                  </div>
                </a-tooltip>
                <span v-else>--</span>
              </template>
              <template v-if="scope.column.key === 'weight'">
                <span>{{ scope.record.weight || '--' }}</span>
              </template>
            </template>
          </TableZebraCrossing>
        </div>
      </div>
    </div>
    <a-drawer
      v-model:open="data.jsOPen"
      width="90%"
      title="进粉数"
      @close="data.jsOPen = false"
    >
      <SearchBaseLayout :data="jfSearchConfig.data" @changeValue="jfSearchForm" :actions="jfSearchConfig.options" :key="data.jsOPen" />
      <TableZebraCrossing :data="data.jfOptions" class="mt-24px">
        <template #headerCell="{ scope: { column } }">
        <div>
          <span>{{ column.title }}</span>
          <a-tooltip>
            <template #title>{{ column.tips }}</template>
            <QuestionCircleFilled style="color: #939599" class="m-l-8px w-12px" v-if="column.tips" />
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'customer_name'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.customer_name }}</template>
            <div class="text_overflow">{{ scope.record.customer_name }}</div>
          </a-tooltip>
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.extend_user_id }}</template>
            <div class="text_overflow number-id">ID：{{ scope.record.extend_user_id }}</div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.key === 'customer_tag'">
          <span>{{ scope.record.customer_tag || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'is_chat'">
          <div class="flex-y-center">
            <span class="rounds" :style="{ background: chatEnumCls(scope.record.is_chat) }"></span>
            <span :style="{ color: chatEnumCls(scope.record.is_chat) }">
              {{ chatEnum(scope.record.is_chat) }}
            </span>
          </div>
        </template>
        <template v-if="scope.column.key === 'wx_user_id'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.wx_user_name || '--' }}</template>
            <div class="text_overflow">{{ scope.record.wx_user_name || '--' }}</div>
          </a-tooltip>
          <div v-if="scope.record.wx_user_id">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.wx_user_id }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.wx_user_id }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'wx_link_id'">
          <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
            <template #title>{{ scope.record.link_name || '--' }}</template>
            <div class="text_overflow">{{ scope.record.link_name || '--' }}</div>
          </a-tooltip>
          <div v-if="scope.record.link">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.link }}</template>
              <div class="text_overflow number-id">链接：{{ scope.record.link }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="scope.column.key === 'callback_at'">
          <div>{{ scope.record.is_callback == 1 ? scope.record.callback_at : '--' }}</div>
        </template>
        <template v-if="scope.column.key === 'corp_name'">
          <div v-if="scope.record.corp_name.length > 15">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corp_name }}</template>
              <div class="text_overflow">{{ scope.record.corp_name }}</div>
            </a-tooltip>
          </div>
          <div v-else>{{ scope.record.corp_name || '--' }}</div>
          <div v-if="scope.record.corpid">
            <a-tooltip placement="topLeft" :overlayInnerStyle="{ width: 'max-content' }">
              <template #title>{{ scope.record.corpid }}</template>
              <div class="text_overflow number-id">ID：{{ scope.record.corpid }}</div>
            </a-tooltip>
          </div>
          <div v-else>--</div>
        </template>
      </template>
      </TableZebraCrossing>
    </a-drawer>
    <a-modal
      v-model:open="data.updateLineOpen"
      width="50%"
      title="更新客服上下线"
      @ok="updateLineOk"
    >
      <a-form-item required>
        <template #label>
          <div class="flex-align">
            <span>上下线</span>
            <a-tooltip>
              <template #title> 开启为上线，关闭为下线</template>
              <QuestionCircleFilled class="ml-3px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <a-switch v-model:checked="data.updateLineItem.on_sale" :checkedValue="1" :unCheckedValue="2" />
      </a-form-item>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { kfLinkExceptionEnum, chatEnum, chatEnumCls, copy } from '@/utils'
  import { EditOutlined, QuestionCircleFilled, CopyOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import datas from './data'
  const { data, handleOk, searchForm, jfSearchForm, onShowDialog, updateLineEdit, updateLineOk, passwordRules, passwordRef, searchConfig, jfSearchConfig, text_status, statusType, auth_on_sale_text } = datas()
</script>
<style lang="scss" scoped>
  .share_link_page {
    min-width: 100%;
    min-height: 100vh;
    background: #f8fafc;
  }
  .item-tag {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
  }
  .online {
    background-color: #f1ffea;
    color: #52c41a;
    border: 1px solid #c4eeb0;
  }
  .offline {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
</style>
