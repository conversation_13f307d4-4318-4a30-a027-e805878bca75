import { reactive } from 'vue'
import { validateMobile, checkReg } from '@/utils'
import { Rule } from 'ant-design-vue/es/form'

export default function rules(addForm = {}) {
  const validPassWord = (rule, value, callback) => {
    if (addForm.password !== value) {
      callback(new Error('确认密码和新的密码需要保持一致'))
    } else {
      callback()
    }
  }

  const validPhone = (rule, value, callback) => {
    if (!validateMobile(value)) {
      callback(new Error('请输入正确的手机号码'))
    } else {
      callback()
    }
  }

  const ruleData = reactive({
    username: [
      { required: true, message: '请输入账号', trigger: ['change', 'blur'] }
      // { pattern: checkReg.account, message: '账号支持输入6-16位英文和数字组合' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: ['change', 'blur'] },
      { pattern: checkReg.pwd, message: '设置6至16位英文/数字/符号组合，至少包含2种字符' }
    ],
    phone: [{ validator: validPhone, trigger: ['change', 'blur'] }],
    captcha: [{ required: true, message: '请输入验证码', trigger: ['change', 'blur'] }],
    confirm_password: [
      { required: true, message: '请输入确认密码', trigger: ['change', 'blur'] },
      { validator: validPassWord }
    ]
  })

  const ruleLoginData = reactive({
    username: [
      { required: true, message: '请输入账号', trigger: ['change', 'blur'] }
      // { pattern: checkReg.account, message: '账号支持输入6-16位英文和数字组合' }
    ],
    password: [{ required: true, message: '请输入密码', trigger: ['change', 'blur'] }],
    phone: [{ validator: validPhone, trigger: ['change', 'blur'] }],
    captcha: [{ required: true, message: '请输入验证码', trigger: ['change', 'blur'] }],
    confirm_password: [
      { required: true, message: '请输入确认密码', trigger: ['change', 'blur'] },
      { validator: validPassWord }
    ],
    agrement: [
      {
        type: 'boolean',
        validator: (_rule: Rule, value: string) => {
          console.log('ahahha', value)
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请阅读并同意平台使用协议')
          }
        },
        // required: true,
        // message: '请阅读并同意平台使用协议',
        trigger: ['change', 'blur']
      }
    ]
  })

  return {
    ruleData,
    ruleLoginData
  }
}
