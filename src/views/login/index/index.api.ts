// 导出请求封装方法
import http from '@/utils/request'

// 通过密码登录
export const loginByPassword = (data: any) => {
  return http('post', `/common/user/login`, data)
}

// 账号/忘记或重置密码
export const resetByPassword = (data: any) => {
  return http('post', `/common/user/reset`, data)
}

// 账号/发送验证码
export const setCaptcha = (data: any) => {
  return http('post', `/common/sms/code`, data)
}

// 验证账号密码是否与后台相同
export const checkPasswordRepeat = (data: any) => {
  return http('post', `/common/user/checkPassword`, data)
}
/**
 * 退出登录
 */
export const loginOutApi = (data?: any) => {
  return http('post', `/common/user/login_out`, data)
}
