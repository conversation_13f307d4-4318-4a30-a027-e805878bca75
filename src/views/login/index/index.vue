<template>
  <div class="login-container">
    <div class="loginBox">
      <img v-if="!isMobile" :src="requireImg('login/loginnewbg.png')" class="bgg" alt="" />
      <div class="loginBoxLeft">
        <h1>
          <span>{{ form.loginType === 1 ? '快速登录' : '账户登录' }}</span
          ><span class="s1">{{ form.loginType === 1 ? 'QUICK' : 'ACCOUNT' }} LOGIN</span>
        </h1>
        <a-form label-width="0" ref="ruleForm" class="login-form" :class="[isMobile && 'w-320px!']">
          <div>
            <div v-show="form.loginType === 1">
              <a-form-item label="" name="phone" v-bind="validateInfos.phone">
                <div class="logxt flex-y-center">
                  <span class="svg-container">
                    <SvgIcon icon="user"></SvgIcon>
                  </span>
                  <a-input
                    v-model:value.trim="form.phone"
                    @input="(_) => (form.phone = form.phone.slice(0, 11))"
                    placeholder="请输入手机号"
                  />
                </div>
              </a-form-item>

              <a-form-item name="captcha" v-bind="validateInfos.captcha">
                <div class="flex-y-center justify-between">
                  <div class="w-65%" :class="[isMobile && 'w-60%!']">
                    <span class="svg-container">
                      <SvgIcon icon="code" />
                    </span>
                    <a-input
                      v-model:value.trim="form.captcha"
                      @input="(_) => (form.captcha = form.captcha.slice(0, 6))"
                      :placeholder="!isMobile ? '请输入短信验证码' : '短信验证码'"
                      @pressEnter="submitForm(ruleForm)"
                    />
                  </div>
                  <div class="w-34%" :class="[isMobile && 'w-39%!']">
                    <a-button
                      type="primary"
                      class="loginCodeBtn"
                      :class="[isMobile && 'w-94px! p-5px']"
                      :disabled="!smsStatus"
                      @click.stop="getCatcha"
                      >{{ state.text }}</a-button
                    >
                  </div>
                </div>
              </a-form-item>
              <!-- <div class="timer" v-if="state.status">{{ state.time }}秒后重发</div> -->
            </div>
            <!-- 账户登录 -->
            <div v-show="form.loginType !== 1">
              <a-form-item label="" v-bind="validateInfos.username">
                <div class="logxt flex-y-center">
                  <span class="svg-container">
                    <SvgIcon icon="user"></SvgIcon>
                  </span>
                  <a-input v-model:value.trim="form.username" maxlength="16" placeholder="请输入登录账号" />
                </div>
              </a-form-item>
              <a-form-item label="" v-bind="validateInfos.password">
                <div class="logxt flex-y-center">
                  <span class="svg-container">
                    <SvgIcon icon="password"></SvgIcon>
                  </span>
                  <a-input-password
                    v-model:value.trim="form.password"
                    type="password"
                    maxlength="16"
                    placeholder="请输入密码"
                    @pressEnter="submitForm(ruleForm)"
                    class="password-wrapper"
                  >
                    <template #iconRender="v">
                      <img
                        src="@/assets/lddImages/login/eyeOpen.png"
                        style="width: 16px; height: 11px; cursor: pointer"
                        alt=""
                        v-if="v"
                      />
                      <img
                        src="@/assets/lddImages/login/eye.png"
                        style="width: 16px; height: 8px; cursor: pointer"
                        alt=""
                        v-else
                      />
                    </template>
                  </a-input-password>
                </div>
              </a-form-item>
            </div>
            <div class="flex-y-center justify-between">
              <div class="quick-login" @click="loginTypeHandler">
                {{ form.loginType === 2 ? '快速登录' : '账号登录' }}
              </div>
              <div class="cursor-pointer font-size-12px c-#999" @click="passEnt({ name: 'ForgotPwd' })">忘记密码</div>
            </div>

            <div class="flex flex-center">
              <div
                :style="{
                  marginTop: '10px',
                  position: 'relative',
                  width: 'auto',
                  height: '40px',
                  overflow: 'hidden'
                }"
                id="slidingId"
              ></div>
            </div>
          </div>

          <a-button id="submitLogin" class="loginbtn" type="primary" :disabled="!allowLogin" @click="onSubmit">
            {{ state.loading ? '登录中...' : '立即登录' }}
          </a-button>
        </a-form>
      </div>
    </div>
    <div class="bottomText">
      <p>{{ getConfig('PROPERTY_RIGHTS') }}</p>
    </div>
  </div>
</template>
<script setup lang="ts">
  interface State {
    loading: boolean
    text: string
    timer: number | null
    time: number
    status: boolean
    smsDisabled: boolean
    year: string
  }
  import { getConfig, requireImg, validateMobile, localStg, getFullPathQuery } from '@/utils'
  import rules from './src/rules'
  import code from './src/code'
  import { loginByPassword, setCaptcha } from './index.api'
  import { computed, onMounted, reactive, ref } from 'vue'
  import { useForm } from 'ant-design-vue/es/form'
  import moment from 'moment'
  import { debounce } from 'lodash-es'
  import { useLogin, useRouter, useVerify, useApp } from '@/hooks'
  import { isMobileDevice } from '@/utils'
  import { message } from 'ant-design-vue'
  import { useRoute } from 'vue-router'
  const { ruleLoginData } = rules()
  const { nvc, delay: loginDelay, isNvcPass, onclick: loginClick } = useVerify('submitLogin', 'slidingId', handleSubmit)
  defineOptions({ name: 'Login' })
  const { routerToBase, routerPush } = useRouter()
  const { isMobile } = useApp()
  // const { logout } = useLogin()
  // const user = useUserInfoStore()
  const ruleForm = ref(null)

  const route = useRoute()

  // 表单数据
  const form = reactive({
    loginType: 2,
    phone: '',
    captcha: '',
    username: '',
    password: '',
    validate: 'adhsajkdhaks',
    platform_type: 1
  })
  const state = reactive<State>({
    loading: false,
    text: '获取验证码',
    timer: null,
    time: 60,
    status: false,
    smsDisabled: true,
    year: moment().format('YYYY')
  })
  let validatePhoneArrObj = {
    phone: ruleLoginData.phone,
    captcha: ruleLoginData.captcha
  }
  let validateUserObj = {
    username: ruleLoginData.username,
    password: ruleLoginData.password
  }
  let validateObj = computed(() => {
    return form.loginType == 1 ? validatePhoneArrObj : validateUserObj
  })
  const { validateInfos, validate } = useForm(form, validateObj)

  const onSubmit = debounce(() => {
    loginClick()
  }, 100)

  async function handleSubmit() {
    await validate()
    await loginDelay()
    login()
  }
  const passEnt = (params: any) => {
    console.log('ertyeirut', params)
    routerPush(params)
  }
  const { getCode, codeData } = code(login, getCatcha, state, form)
  // 是否满足登录规则
  const allowLogin = computed(() => {
    if (form.loginType === 1) {
      form.password = ''
      return validateMobile(form.phone) && /^\d{6}$/.test(form.captcha) && !state.loading
    } else {
      form.phone = ''
      form.captcha = ''
      return !!form.username && form.password !== '' && !state.loading
    }
  })
  const loginTypeHandler = () => {
    if (form.loginType === 1) {
      form.loginType = 2
    } else {
      form.loginType = 1
    }
  }
  // 验证码按钮验证
  const smsStatus = computed(() => {
    return validateMobile(form.phone) && !state.status
  })
  // 登录
  const submitForm = async () => {
    try {
      console.log('eieieiei', form.username, form.password)
      await validate()
      console.log('登录')
      return
    } catch (error) {
      state.loading = false
      codeData.data = {}
      console.error('登录页面报错：', error)
    }
  }
  async function login() {
    state.loading = true
    // 账号登录
    try {
      const { login: hookLogin, isLogin } = useLogin()
      const result = await loginByPassword({
        username: form.loginType == 1 ? form.phone : form.username,
        password: form.password,
        loginType: form.loginType,
        captcha: form.captcha,
        platform_type: form.platform_type,
        ...codeData.data,
        nvc: nvc.value
      })
      state.loading = false
      if (isNvcPass && !isNvcPass(result)) {
        return
      } else {
        try {
          await hookLogin({ userInfo: result?.data?.user_info, token: result?.data?.token_info?.access_token })
          if (isLogin.value) {
            localStg.set('account', result.data?.user_info?.account)
            localStg.set('loginType', form.loginType)
            const redirect = route.query.redirect
            if (redirect) {
              // 如果带有重定向地址
              const res = getFullPathQuery(decodeURIComponent(redirect))
              if (res) {
                routerPush({ path: res.path, query: res.query })
              } else {
                routerToBase()
              }
            } else {
              routerToBase()
            }
            message.success('登录成功')
          } else {
            console.log('登录失败：')
          }
        } catch (e) {
          console.log(e, 'sdhfkj')
        } finally {
          state.loading = false
        }
      }
    } catch (error) {
      state.loading = false
      codeData.data = {}
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  //获取验证码
  async function getCatcha() {
    if (!codeData?.data?.randstr) {
      getCode()
      return
    }
    try {
      await setCaptcha({
        phone: form.phone,
        tp: 'login',
        ...codeData.data
      })
      state.status = true
      state.text = '获取验证码'
      state.time = 60
      state.timer = setInterval(() => {
        state.time--
        state.text = state.time + '秒后重试'
        if (state.time == 0) {
          clearInterval(state.timer)
          state.text = '重新获取'
          state.status = false
          codeData.data = {}
        }
      }, 1000)
    } catch (error) {
      codeData.data = {}
      console.error(error)
    }
  }
  onMounted(() => {
    localStg.clear()
    // if (!state.loading) {
    //   logout()
    // }
  })
</script>
<style lang="scss" scoped>
  $bg: #2d3a4b;
  $dark_gray: #889aa4;
  $light_gray: #eee;
  .login-container {
    width: 100%;
    height: 100vh;
    background-color: #eceff4;
    background-image: url('https://oss.jfb.qidianbox.com/assets/u9671_state0.svg');
    background-repeat: no-repeat;
    background-position: center center;
    background-attachment: scroll;
    background-origin: border-box;
    .loginBox {
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translateX(-50%) translateY(-50%);
      display: flex;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
      border-radius: 5px;
      overflow: hidden;
      .bgg {
        width: 330px;
        flex: none;
      }
    }
    .loginBoxLeft {
      padding: 35px 25px;
      background: #fff;
      width: 490px;
      h1 {
        font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
        font-weight: 700;
        font-size: 18px;
        color: #666666;
        margin-bottom: 40px;
        .s1 {
          font-size: 14px;
          color: #999;
          margin-left: 5px;
        }
      }
      .logxt {
        position: relative;
      }
      :deep(.ant-form-item) {
        margin-bottom: 20px;
        .ant-input {
          height: 50px;
          line-height: 50px;
          padding-left: 50px;
        }
      }
      .password-wrapper {
        height: 50px;
        line-height: 50px;
        :deep(.ant-input) {
          height: 40px;
          line-height: 40px;
          padding-left: 40px;
        }
      }
    }

    .login-form {
      position: relative;
      // width: 440px;
      max-width: 100%;
      overflow: hidden;
    }
    .bottomText {
      width: 100%;
      position: absolute;
      bottom: 5%;
      text-align: center;
      line-height: 20px;
      p {
        margin: 0;
        font-size: 12px;
        color: #ccc;
      }
    }
    .svg-container {
      position: absolute;
      color: $dark_gray;
      vertical-align: middle;
      padding: 0;
      width: 50px;
      line-height: 50px;
      text-align: center;
      display: inline-block;
      z-index: 2;
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 5px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .loginbtn {
      height: 50px;
      width: 100%;
      font-size: 18px;
      margin-top: 24px;
    }
    .loginCodeBtn {
      height: 50px;
      font-size: 14px;
      width: 150px;
    }
  }
  .quick-login {
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
    }
  }
  @media (max-width: 820px) {
    .bgg {
      display: none;
    }
    .loginBoxLeft {
      width: 100% !important;
    }
  }
</style>
