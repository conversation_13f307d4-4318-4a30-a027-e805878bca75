{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "allowImportingTsExtensions": true
  },
  "include": [
    "build/**/*.ts",
    "setting/**/*.ts",
    "setting/*.ts",
    "types/**/*.d.ts",
    "vite.config.ts",
    // "src/**/*.ts",
    // "src/**/*.d.ts",
    // "src/**/*.tsx",
  ]
}