import { Setting, SettingEnv } from '../types'

const jfSetting: Setting = {
  uiComponents: 'antd',
  mode: 'upAndDown',
  fontSize: 14,
  lineHeight: 1.6,
  borderRadius: 4,
  space: 16,
  height: 34,
  pageAnimation: 'fade-slide',
  layout: {
    headerHeight: 56,
    sidebarWidth: 240,
    minSidebarWidth: 90,
    foldSidebarWidth: 240,
    tagHeight: 36,
    isTag: true,
    layoutBgColor: '#F7F9FC',
    menuBgColor: '#F7F9FC',
    headerBgColor: '#F7F9FC',
    mainBgColor: '#F7F9FC',
    mainPadding: '16px 24px 24px 0'
  },
  colors: {
    info: '#FE9D35',
    primary: '#FE9D35',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    textColor: '#333333'
  },
  isMobileWidth: 600,
  env: {
    dev: {
      TITLE: '斗量智投',
      LANUCH: ['dev', 'jf'],
      NODE_ENV: 'dev',
      PROJECT: 'jf',
      LAYOUT: 'template1',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: '/',
      API_URL: 'https://tapi.douliang.pro/',
      // API_URL: 'http://192.168.8.192:11003/',
      NODE_API_URL: 'https://addpowdrnodessg-wxampupload-alevqehair.cn-beijing.fcapp.run',
      NODE_OSS_URL: 'https://th5.douliang.pro',
      IS_PROXY: true,
      PROXY_URI: ['/dev'],
      ICON_ID: 'locl-icon-ff',
      CAPTCHAAPPId: '2012878865',
      AMAP_KEY: '0000000',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2028. All Rights Reserved.',
      COS: {
        url: 'https://tcdn.douliang.pro',
        path: 'dev',
        headImgDir: '/wx_images/accupload'
      }
    },
    test: {
      TITLE: '斗量智投',
      LANUCH: ['test', 'jf'],
      NODE_ENV: 'test',
      PROJECT: 'jf',
      LAYOUT: 'template1',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: process.env.BASE_URL,
      API_URL: process.env.API_URL,
      NODE_API_URL: process.env.NODE_API_URL,
      NODE_OSS_URL: process.env.NODE_OSS_URL,
      ICON_ID: 'locl-icon-ff',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2028. All Rights Reserved.',
      UPLOAD_TYPE: (process.env.UPLOAD_TYPE as SettingEnv['UPLOAD_TYPE']) || 'ssh',
      AMAP_KEY: '0000000',
      COS: {
        url: process.env.COS_URL,
        path: 'dev',
        headImgDir: '/wx_images/accupload',
        put: {
          buket: process.env.COS_BUKET,
          region: process.env.COS_REGION,
          secretId: process.env.COS_SECRET_ID,
          secretKey: process.env.COS_SECRET_KEY,
          refreshSecretId: process.env.COS_REFRESH_SECRET_ID,
          refreshSecretKey: process.env.COS_REFRESH_SECRET_KEY,
          SSH_USERNAME: process.env.SSH_USERNAME,
          SSH_HOST: process.env.SSH_HOST,
          SSH_PORT: process.env.SSH_PORT,
          SSH_KEY: process.env.SSH_KEY,
          SSH_DIR: process.env.SSH_DIR
        }
      }
    },
    prod: {
      TITLE: '斗量智投',
      LANUCH: ['prod', 'jf'],
      NODE_ENV: 'prod',
      PROJECT: 'jf',
      LAYOUT: 'template1',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: process.env.BASE_URL,
      API_URL: process.env.API_URL,
      NODE_API_URL: process.env.NODE_API_URL,
      NODE_OSS_URL: process.env.NODE_OSS_URL,
      IS_PROXY: false,
      PROXY_URI: ['/prod'],
      ICON_ID: 'locl-icon-ff',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2028. All Rights Reserved.',
      UPLOAD_TYPE: (process.env.UPLOAD_TYPE as SettingEnv['UPLOAD_TYPE']) || 'oss',
      AMAP_KEY: '0000000',
      COS: {
        url: process.env.COS_URL || process.env.VITE_APP_OSS_URL,
        path: 'prod',
        headImgDir: '/wx_images/accupload',
        put: {
          buket: process.env.COS_BUKET || process.env.VITE_APP_OSS_BUCKET,
          region: process.env.COS_REGION || process.env.VITE_APP_OSS_REGION,
          secretId: process.env.COS_SECRET_ID || process.env.VITE_APP_OSS_ACCESSKEY_ID,
          secretKey: process.env.COS_SECRET_KEY || process.env.VITE_APP_OSS_ACCESSKEY_SECRET,
          refreshSecretId: process.env.COS_REFRESH_SECRET_ID,
          refreshSecretKey: process.env.COS_REFRESH_SECRET_KEY
        }
      }
    }
  },
  files: [
    {
      name: 'cache/kf_logo',
      baseDir: 'cache',
      type: 'png',
      assign: 'serverFile/kf_logo_ldd.png',
      w: 27,
      h: 27
    },
    {
      name: 'cache/favicon',
      baseDir: 'cache',
      type: 'svg',
      assign: 'serverFile/favicon_ldd.svg',
      w: 45,
      h: 48
    },
    {
      name: 'cache/logo',
      baseDir: 'cache',
      type: 'png',
      assign: 'serverFile/logo_jf.png',
      w: 119,
      h: 28
    },
    {
      name: 'cache/h5_logo',
      baseDir: 'cache',
      type: 'png',
      assign: 'serverFile/min-logo-ldd.png',
      w: 24,
      h: 26
    },
    {
      name: 'cache/logo_orange',
      baseDir: 'cache',
      type: 'png',
      assign: 'serverFile/logo_orange_ldd.png',
      w: 76,
      h: 26
    }
  ],
  exetrnal: {
    css: [
      {
        src: 'assets/css/jf.scss'
      }
    ]
  }
}
export default jfSetting
