import path from 'path'
import fs from 'fs'
import { getExportedContentFromRouterFiles, updateTargetFile } from '../utils'
import { flattenRouterData, buildRouterTree } from '../utils/disponneRouter'
import { isArray } from 'lodash-es'

async function addRoyterType(loadPath: string, outDirPath: string) {
  const getFilesList = await getExportedContentFromRouterFiles(loadPath, 'index.router.ts')
  const files = (isArray(getFilesList) ? getFilesList : []).map((v) => {
    v.routerPath = v.filePath
      .replace(/\\\\/g, '\\')
      .replace(loadPath + '\\', '')
      .replace('\\index.router.ts', '')
      .replace(/\\/g, '/')
      // 兼容ios
      .replace(loadPath, '')
      .replace('/index.router.ts', '')
    v.filePath = v.routerPath
    return v
  })

  // console.log(files)
  const fileList = flattenRouterData(buildRouterTree(files))
  // console.log(fileList)
  const routerType = `export interface RouterType {
    path:${files
      .map(
        (v, i) => `
        | '${fileList.find((g) => g.name == v.content.name).path}'`
      )
      .join('')}
    name:${files
      .map(
        (v, i) => `
        | '${v.content.name}'`
      )
      .join('')}
}
`
  await updateTargetFile(outDirPath, routerType)
}
/**
 * 处理路由类型，自动修改类型文件
 * @param loadPath 加载目录
 * @param outDirPath 导出文件，类型以RouterNames , RouterPath 两个类型形势输出
 * @returns
 */
export function vitePluginRouterType(loadPath: string, outDirPath: string) {
  return {
    name: 'vite-plugin-router-type',
    async buildStart() {
      await createFileIfNotExists(outDirPath, 'export interface RouterType {}')
      addRoyterType(loadPath, outDirPath)
    },
    configureServer(server) {
      server.watcher.on('change', (filePath) => {
        if (filePath.includes('index.router.ts')) {
          addRoyterType(loadPath, outDirPath)
        }
      })
    }
  }
}

/**
 * 创建文件
 * @param filePath
 * @param content
 */
function createFileIfNotExists(filePath, content) {
  return new Promise((reslove, reject) => {
    // 使用 path.resolve 来获取绝对路径
    const absolutePath = path.resolve(filePath)

    // 使用 fs.access 方法检查文件是否存在
    fs.access(absolutePath, fs.constants.F_OK, (err) => {
      if (err) {
        // 如果文件不存在，创建一个新文件
        fs.writeFile(absolutePath, content, (error) => {
          if (error) {
            console.error(`${filePath}无法创建文件: ${error}`)
            reject(error)
          } else {
            console.log('文件已成功创建!', filePath)
            reslove(true)
          }
        })
      } else {
        reslove(true)
      }
    })
  })
}
