import unplugin from './icon'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import babel from 'rollup-plugin-babel'
import fs from 'fs'
import path from 'path'
import getRouterList from '../utils/getRouterList.ts'
import { vitePluginRouterType } from './router'
import { getRootPath, getSetting, getSrcPath } from '../utils'
import { Setting, SettingEnv } from '../../types'
import { replaceFile } from './replaceFile'
import { uploadCosPlugin } from './vite-plugin-upload-cos'
import { filesImgsNames } from '../utils/imgFiles'
import { filesH5Names } from '../utils/h5Files'
import { filesPreviewNames } from '../utils/previewFiles'
import { filesCacheNames } from '../utils/cacheFiles.js'

import vitePluginUploadOss from './vite-plugin-upload-oss'
import vitePluginUploadSsh from './vite-plugin-upload-ssh'
/**
 * vite插件
 */
export async function setupVitePlugins(env: SettingEnv, mode: string) {
  let setting = await getSetting(mode)
  await getRouterList(env.PROJECT)
  let fileFaviconItem = setting.files.find((v) => v.name == 'favicon')
  let faviconName = fileFaviconItem ? 'favicon.' + fileFaviconItem.type : 'favicon.svg'

  let injectHtmlObj = {
    APP_INJECT_SCRIPT: '',
    APP_FAVICON: `<link rel="icon" type="image/svg+xml" href="/cache/${faviconName}" />`,
    APP_LAND_FONT: `<link rel="stylesheet" href="${env.NODE_OSS_URL}/css/font.css" crossorigin>`
  }
  const plugin = [
    vueJsx(),
    vue(),
    UnoCSS(),
    ...unplugin(env),
    vitePluginRouterType(
      path.resolve(getSrcPath(), 'views'),
      path.resolve(getRootPath(), 'types/cache/routerType.d.ts')
    ),
    createHtmlPlugin({
      APP_TITLE: env.TITLE,
      ...injectHtmlObj,
      link: setting.exetrnal?.css || [],
      script: setting.exetrnal?.js || []
    }),
    babel({ runtimeHelpers: true }),
    replaceFile({ env: env, filePath: setting.files })
  ]
  // 正式 测试需要的操作
  if (['test', 'prod', 'release'].includes(env.NODE_ENV)) {
    // jf项目删除 默认的favicon.ico
    if (env.PROJECT == 'jf') {
      console.log(`jf项目删除 默认的${faviconName}`)
      fs.unlinkSync(path.resolve(`public/${faviconName}`))
    }
    console.warn('当前选择的上传类型：', env.UPLOAD_TYPE)
    // 量多多 专用阿里云上传
    if (env.UPLOAD_TYPE == 'ssh') {
      plugin.push(
        vitePluginUploadSsh({
          SSH_USERNAME: env.COS.put.SSH_USERNAME,
          SSH_HOST: env.COS.put.SSH_HOST,
          SSH_PORT: env.COS.put.SSH_PORT,
          SSH_KEY: env.COS.put.SSH_KEY,
          SSH_DIR: env.COS.put.SSH_DIR,
          filterFile: env.PROJECT == 'jf' ? [`public/${faviconName}`] : []
        })
      )
    } else if (env.UPLOAD_TYPE == 'oss') {
      plugin.push(
        vitePluginUploadOss({
          ignore: '*',
          base: env.COS.url,
          baseUrl: env.BASE_URL,
          buildPath: 'dist',
          overwrite: true,
          timeout: '50000',
          region: env.COS.put.region,
          accessKeyId: env.COS.put.secretId,
          accessKeySecret: env.COS.put.secretKey,
          refreshSecretId: env.COS.put.refreshSecretId,
          refreshSecretKey: env.COS.put.refreshSecretKey,
          bucket: env.COS.put.buket,
          filterFile: env.PROJECT == 'jf' ? [`public/${faviconName}`] : []
        })
      )
    } else if (env.UPLOAD_TYPE == 'cos') {
      plugin.push(
        uploadCosPlugin({
          cosBaseDir: `/`,
          bucket: env.COS.put.buket,
          region: env.COS.put.region,
          SecretId: env.COS.put.secretId,
          SecretKey: env.COS.put.secretKey,
          uploadDir: 'dist',
          refreshCdn: true,
          webUrl: env.BASE_URL,
          filterFile: env.PROJECT == 'jf' ? [`public/${faviconName}`] : [],
          extraFiles: [
            ...setting.files.map((v) => `${v.name}.${v.type}`),
            'dount.js',
            ...filesImgsNames(),
            ...filesH5Names(),
            ...filesPreviewNames(),
            ...filesCacheNames(),
            'robots.txt'
          ]
        })
      )
    }
  }
  return plugin
}
interface HtmlPluginType {
  /**
   * 单独注入的js dount
   */
  APP_INJECT_SCRIPT: string
  /**
   * title
   */
  APP_TITLE: string
  /**
   * csse外链
   */
  link: Setting['exetrnal']['css']
  /**
   * js外链
   */
  script: Setting['exetrnal']['js']
}
function createHtmlPlugin(params: HtmlPluginType) {
  return {
    name: 'create-html-plugin',
    transformIndexHtml(html) {
      return html
        .replace('APP_FAVICON', params['APP_FAVICON'] || '')
        .replace('APP_TITLE', params['APP_TITLE'] || '')
        .replace('APP_INJECT_SCRIPT', params['APP_INJECT_SCRIPT'] || '')
        .replace('APP_LAND_FONT', params['APP_LAND_FONT'] || '')
    },
    transform(code: string, id: string) {
      if (id.endsWith('main.ts')) {
        const cssFiles = params.link || []
        const jsFiles = params.script || []
        const tags = [...cssFiles, ...jsFiles].map((file) => `import "@/${file.src}"`).join('\n')
        return tags + ' \n' + code
      }
    }
  }
}
