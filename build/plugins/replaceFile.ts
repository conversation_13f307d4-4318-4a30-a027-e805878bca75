import Logger from '../utils/log'
import axios from 'axios'
import sharp from 'sharp'
import path from 'path'
import fs from 'fs'
import pngToIco from 'png-to-ico'
import { SettingFileType, Setting, SettingFiles, SettingEnv } from '../../types'
import { getRootPath } from '../utils'
const instance = axios.create({
  timeout: 1000 * 10
})

type Params = { env: SettingEnv; filePath: Setting['files'] }
/**
 * 替换指定文件
 */
export function replaceFile(params: Params) {
  return {
    name: 'replaceFile',
    async buildStart() {
      Logger.warn('开始执行替换文件!')
      for (let i = 0; i < params.filePath.length; i++) {
        let item = params.filePath[i]
        // 设置文件本地地址
        item.origin = path.resolve(getRootPath(), `${item.origin || 'public'}/${item.name}.${item.type}`)
        // 设置远程地址
        if (params.env[item.assign]) item.assign = params.env[item.assign]

        // 如果远程地址为本地地址设置
        if (!/^(http|https|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(item.assign)) {
          item.assign = 'local_url:' + path.resolve(getRootPath(), `${item.assign}`)
        }
        if (item.assign)
          try {
            let address = path.resolve(process.cwd(), item.origin)
            if (!item.assign || !item.origin) {
              Logger.info(`${item.name}：文件无需替换`)
            } else if (item.origin == item.assign) {
              // 当有静态文件夹的时候 并且要替换的文件等于当前文件的时候不执行
              Logger.info(`${item.name}：文件无需替换`)
            } else if (item.type == 'ico') {
              await pngToIcoFun(item.assign, address, item.w, item.h)
              Logger.info(`${item.name}：文件替换成功`)
            } else {
              await dlConvImage(item.assign, address, item.type, item.w, item.h)
              Logger.info(`${item.name}，文件替换成功`)
            }
          } catch (error) {
            Logger.error(`下载文件失败：${item.name}，${error}`)
          }
      }
    }
  }
}

// 下载图片 转换格式
const dlConvImage = async (url: string, outputPath: string, format: SettingFileType, w?: number, h?: number) => {
  try {
    // 本地地址复制
    if (/local_url:/.test(url)) {
      let imageBuffer = fs.readFileSync(url.replace('local_url:', ''))
      fs.writeFileSync(outputPath, imageBuffer)
      return outputPath
    } else {
      // 网络地址 下载图片
      const response = await instance.get(url, { responseType: 'arraybuffer' })
      let imageBuffer = Buffer.from(response.data, 'binary')
      if (!imageBuffer) return await Promise.reject('图片下载错误')

      // 转换图片大小和格式
      await sharp(imageBuffer)
        // .resize(w * 2, h * 2)
        .toFormat(format)
        .toFile(outputPath)
      return outputPath
    }
  } catch (error) {
    return await Promise.reject(`${error}`)
  }
}

// png 转ico
const pngToIcoFun = async (url: string, outputPath: string, w = 24, h = 24) => {
  // 下载图片
  let icoBuffer, temFile
  if (url.split('.')[url.split('.').length - 1] == 'ico') {
    // ico类型直接下载存储
    const response = await instance.get(url, { responseType: 'arraybuffer' })
    icoBuffer = Buffer.from(response.data, 'binary')
  } else {
    temFile = path.resolve(process.cwd(), 'public/temFile.png')
    // , w, h
    let temFilePath = await dlConvImage(url, temFile, 'png')
    icoBuffer = await pngToIco([temFilePath])
  }
  icoBuffer && fs.writeFileSync(outputPath, icoBuffer)
  temFile && fs.unlinkSync(temFile)
}
