import Cdn20180510, { RefreshObjectCachesRequest } from '@alicloud/cdn20180510'
import * as $OpenApi from '@alicloud/openapi-client'
import * as $Util from '@alicloud/tea-util'
import path from 'path'
import { URL } from 'url'
import color from 'picocolors'
import { glob } from 'glob'
import OSS from 'ali-oss'
import { normalizePath } from 'vite'

function vitePluginUploadOss(options: any): any {
  let baseConfig: any = '/'
  let buildConfig: any = ''

  if (options.enabled !== void 0 && !options.enabled) {
    return
  }
  if (options.base) {
    baseConfig = options.base
  }

  return {
    name: 'vite-plugin-upload-oss',
    enforce: 'post',
    apply: 'build',
    configResolved(config: any) {
      buildConfig = config.build
    },
    async closeBundle() {
      if (!/^http/i.test(baseConfig)) {
        throw Error('[vite-plugin-upload-oss] base must be a url')
      }

      const outDirPath = normalizePath(path.resolve(normalizePath(buildConfig.outDir)))

      const { pathname: ossBasePath, origin: ossOrigin } = new URL(baseConfig)

      const createOssOption = { ...options }
      delete createOssOption.overwrite
      delete createOssOption.ignore
      delete createOssOption.headers
      delete createOssOption.test
      delete createOssOption.enabled
      console.log('createOssOption', createOssOption)

      const client = new OSS(createOssOption)
      const ssrClient = buildConfig.ssrManifest
      const ssrServer = buildConfig.ssr

      let files = glob.sync(`${outDirPath}/**/*`, {
        nodir: true,
        dot: true,
        ignore:
          // custom ignore
          options.ignore
            ? options.ignore
            : ssrClient
            ? ['**/ssr-manifest.json', '**/*.html']
            : ssrServer
            ? ['**']
            : '**/*.html'
      })
      files = files.filter((v) => options.filterFile.findIndex((g) => v.includes(g)) == -1)

      console.log('')
      console.log(`ali oss upload start${ssrClient ? ' (ssr client)' : ssrServer ? ' (ssr server)' : ''}`)
      console.log('')

      const startTime = new Date().getTime()

      for (let fileFullPath of files) {
        let filePath = fileFullPath.split(path.resolve(outDirPath))[1] // eg: '/assets/vendor.bfb92b77.js'

        let ossFilePath = ossBasePath.replace(/\/$/, '') + filePath // eg: '/base/assets/vendor.bfb92b77.js'

        let completePath = ossOrigin + ossFilePath // eg: 'https://foo.com/base/assets/vendor.bfb92b77.js'

        let output = `${buildConfig.outDir + filePath} => ${color.green(completePath)}`

        if (options.test) {
          console.log(`test upload path: ${output}`)
          continue
        }

        if (options.overwrite) {
          ossFilePath = ossFilePath.replace(/^\\/, '').replace(/\\/g, '/')
          fileFullPath = fileFullPath.replace(/\\/g, '/')
          await client.put(ossFilePath, fileFullPath, {
            headers: options.headers || {}
          })
          console.log(`upload complete: ${output}`)
        } else {
          try {
            await client.head(ossFilePath)
            console.log(`${color.gray('files exists')}: ${output}`)
          } catch (error: any) {
            if (error.code === 'NoSuchKey') {
              await client.put(ossFilePath, fileFullPath, {
                headers: Object.assign(options.headers || {}, { 'x-oss-forbid-overwrite': true })
              })
              console.log(`upload complete: ${output}`)
            } else {
              throw new Error(error)
            }
          }
        }
      }

      await Client.main(options.accessKeyId, options.accessKeySecret, options.baseUrl)
      // 刷新cdn多个
      if (process.env.VITE_APP_COS_REFRESH_URL && process.env.VITE_APP_COS_REFRESH_URL.split) {
        let arr = process.env.VITE_APP_COS_REFRESH_URL.split(',')
        // arr = arr.filter((v) => v != options.baseUrl)
        if (arr.length) {
          for (let i = 0; i < arr.length; i++) {
            await Client.main(
              options.refreshSecretId || options.accessKeyId,
              options.refreshSecretKey || options.accessKeySecret,
              arr[i]
            )
          }
        }
      }
    }
  }
}

// 刷新cdn
class Client {
  /**
   * 使用AK&SK初始化账号Client
   * @param accessKeyId
   * @param accessKeySecret
   * @return Client
   * @throws Exception
   */
  static createClient(accessKeyId: string, accessKeySecret: string): Cdn20180510 {
    let config = new $OpenApi.Config({
      // 必填，您的 AccessKey ID
      accessKeyId: accessKeyId,
      // 必填，您的 AccessKey Secret
      accessKeySecret: accessKeySecret
    })
    // Endpoint 请参考 https://api.aliyun.com/product/Cdn
    config.endpoint = `cdn.aliyuncs.com`
    return new Cdn20180510.default(config)
  }

  static async main(accessKeyId, accessKeySecret, baseUrl): Promise<void> {
    try {
      console.log(`${color.green('文件上传成功，开始刷新cdn' + baseUrl)}`)
      // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
      // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378664.html
      let client = Client.createClient(accessKeyId, accessKeySecret)
      let refreshObjectCachesRequest = new RefreshObjectCachesRequest({
        objectPath: baseUrl + '/',
        objectType: 'Directory'
      })
      let runtime = new $Util.RuntimeOptions({})
      await client.refreshObjectCachesWithOptions(refreshObjectCachesRequest, runtime)
      console.log(`${color.green('oss cdn刷新成功' + baseUrl)}`)
    } catch (error) {
      console.log(`${color.red('oss cdn刷新失败' + baseUrl)}`)
    }
  }
}
export default vitePluginUploadOss
