import color from 'picocolors'
import fs from 'fs'
import { Client } from 'ssh2'
import { glob } from 'glob'
import path from 'path'

function vitePluginUploadSsh(cfg: {
  SSH_USERNAME: string
  SSH_HOST: string
  SSH_PORT: string
  SSH_KEY: string
  SSH_DIR: string
  filterFile: string[]
}) {
  let buildConfig = {}
  const envConfig = {
    SSH_USERNAME: cfg.SSH_USERNAME,
    SSH_HOST: cfg.SSH_HOST,
    SSH_PORT: cfg.SSH_PORT,
    SSH_KEY: cfg.SSH_KEY,
    SSH_DIR: cfg.SSH_DIR,
    filterFile: cfg.filterFile || []
  }
  return {
    name: 'vite-plugin-upload-ssh',
    enforce: 'post',
    apply: 'build',
    configResolved(config: any) {
      buildConfig = config.build
    },
    async closeBundle() {
      const remoteUsername = envConfig.SSH_USERNAME
      const remoteHost = envConfig.SSH_HOST
      const remotePort = envConfig.SSH_PORT // 默认SSH端口是22

      const connSettings = {
        host: remoteHost,
        port: remotePort,
        username: remoteUsername,
        privateKey: envConfig.SSH_KEY.split('\\n').join('\n')
      }

      const conn = new Client()

      conn.on('ready', async () => {
        console.log(`${color.green('SSH connection successful链接成功')}`)
        const localDirectory = 'dist' // 本地目录路径，使用glob模式匹配所有文件
        const remoteDirectory = envConfig.SSH_DIR // 远程服务器上保存文件的路径

        // 删除目录下所有文件 避免占用空间
        function deleteFile() {
          let command = `rm -rf ${remoteDirectory}*`
          return new Promise((reslove) => {
            conn.exec(command, (err) => {
              if (err) {
                throw new Error(`文件删除失败：${command}`)
              }
              console.log(color.green('文件删除成功：' + command))
              reslove(true)
            })
          })
        }
        await deleteFile()

        let allFiles = glob.sync(`**/*`, {
          nodir: false,
          dot: true,
          cwd: localDirectory
        })
        allFiles = allFiles.filter((v) => cfg.filterFile.findIndex((g) => v.includes(g)) == -1)
        let files = allFiles.filter((v) => v.includes('.'))
        let dirs = allFiles.filter((v) => !v.includes('.'))
        conn.sftp(async (err, sftp) => {
          if (err) throw err
          // 检查文件夹是否存在，不存在创建
          function createdDir(dir) {
            return new Promise((reslove) => {
              let savePath = path.join(remoteDirectory, dir).replace(/\\/g, '/')
              sftp.stat(savePath, (err) => {
                if (err) {
                  sftp.mkdir(savePath, (err) => {
                    if (err) {
                      reslove(true)
                      throw err
                    }
                    console.log(color.green('创建目录成功：' + savePath))
                    reslove(true)
                  })
                } else reslove(true)
              })
            })
          }
          await Promise.all(dirs.map((v) => createdDir(v)))

          // 上传文件
          function uploadFile(filePath) {
            return new Promise((reslove) => {
              let p = path.resolve(process.cwd(), localDirectory, filePath)
              const readStream = fs.createReadStream(p)
              let savePath = path.join(remoteDirectory, filePath).replace(/\\/g, '/')
              const writeStream = sftp.createWriteStream(savePath)

              writeStream.on('close', () => {
                let log = `上传成功${p}：${savePath}`
                console.log(color.green(log))
                reslove(true)
              })
              readStream.pipe(writeStream)
            })
          }
          let chunkArrayFiles = chunkArray(files, 5) || []

          for (let i = 0; i < chunkArrayFiles.length; i++) {
            await Promise.all(chunkArrayFiles[i].map((v) => uploadFile(v)))
          }
          conn.end()
          console.log(color.green(`SSH：上传到宝塔面板成功`))
        })
      })
      conn.on('error', function (err) {
        console.error(color.red('上传文件到宝塔面板失败:' + err))
      })
      conn.connect(connSettings)
    }
  }
}

function chunkArray(arr, chunkSize) {
  return Array.from({ length: Math.ceil(arr.length / chunkSize) }, (v, i) =>
    arr.slice(i * chunkSize, i * chunkSize + chunkSize)
  )
}

export default vitePluginUploadSsh
