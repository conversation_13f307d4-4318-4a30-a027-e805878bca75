import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { getSrcPath } from '../utils'
import path from 'path'
import { SettingEnv } from '../../types'

export default function unplugin(env: SettingEnv) {
  const { ICON_ID } = env
  const VITE_ICON_PREFFIX = 'icon'

  const srcPath = getSrcPath()
  const localIconPath = `${srcPath}/assets/svg-icon`
  console.log(localIconPath)
  /** 本地svg图标集合名称 */
  const collectionName = ICON_ID.replace(`${VITE_ICON_PREFFIX}-`, '')
  console.log(collectionName, path.resolve(process.cwd(), 'src/assets/svg-icon'))
  return [
    // 注册组件
    Icons({
      compiler: 'vue3',
      customCollections: {
        [collectionName]: FileSystemIconLoader(localIconPath, (svg) =>
          svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: 'inline-block'
    }),
    // svg动态生成组件 加上ts组件注释
    Components({
      // dts: false, // 不生成component.d.ts
      dts: 'types/cache/components.d.ts',
      types: [{ from: 'vue-router', names: ['RouterLink', 'RouterView'] }],
      resolvers: [
        NaiveUiResolver(),
        IconsResolver({
          customCollections: [collectionName],
          componentPrefix: VITE_ICON_PREFFIX
        })
      ]
    }),
    // 组件方式访问svg图标
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), 'src/assets/svg-icon')],
      symbolId: `${ICON_ID}-[dir]-[name]`,
      inject: 'body-last',
      customDomId: '__SVG_ICON_LOCAL__'
    })
  ]
}
