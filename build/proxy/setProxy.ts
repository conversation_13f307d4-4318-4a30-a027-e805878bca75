import { ProxyOptions } from 'vite'
import { setEnv } from '..'
import { SettingEnv } from '../../types'
import { isArray, isString } from 'lodash-es'

function createProxy(uri: string | string[], env: SettingEnv): Record<string, string | ProxyOptions> {
  let _uri = isString(uri) ? [uri] : uri
  let _proxy = {}
  _uri.forEach((k) => {
    _proxy[k] = {
      target: env['API_URL'],
      changeOrigin: true,
      rewrite: (path) => path.replace(RegExp(`^${k}`), '')
    }
  })
  return _proxy
}

export async function setProxy(mode: string) {
  let env = (await setEnv(mode))['import.meta.env.VITE_VAR']
  let proxy: Record<string, string | ProxyOptions> = {}

  if (env['IS_PROXY']) {
    let proxyUrl = env['PROXY_URI']
    if (isString(proxyUrl)) {
      proxy = createProxy(proxyUrl, env)
    } else if (isArray(proxyUrl)) {
      proxy = createProxy(proxyUrl, env)
    }
  }
  return proxy
}
