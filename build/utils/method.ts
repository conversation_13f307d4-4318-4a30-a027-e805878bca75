import fs from 'fs'
import path from 'path'
import util from 'util'
import { Setting, SettingEnv } from '../../types'
import { cloneDeep, isFunction, isObject } from 'lodash-es'
const readFile = util.promisify(fs.readFile)
const writeFile = util.promisify(fs.writeFile)
/**
 * 获取项目根路径
 * @descrition 末尾不带斜杠
 */
export function getRootPath() {
  return path.resolve(process.cwd())
}

/**
 * 获取项目src路径
 * @param srcName - src目录名称(默认: "src")
 * @descrition 末尾不带斜杠
 */
export function getSrcPath(srcName = 'src') {
  const rootPath = getRootPath()

  return `${rootPath}/${srcName}`
}

/**
 * 获取配置 异步
 */
export async function getSetting(mode: string): Promise<Setting> {
  let project: SettingEnv['PROJECT']
  let nodeEnv: string
  if (mode) {
    project = mode.split('-')[1] as SettingEnv['PROJECT']
    nodeEnv = mode.split('-')[0]
  } else {
    console.error('启动命令 mode 错误')
    process.exit()
  }
  let config = {} as Setting
  try {
    config = await loadSetting(import(`../../setting/jfSetting`))
  } catch (error) {
    console.error('环境变量设置失败')
  } finally {
    //  删除当前不需要的环境的变量
    setSetting(config, nodeEnv)
    return config
  }
}

/**
 * 加载seeeting 配置
 * @param importSetting import引入文件
 * @param params 需要的参数
 * @returns
 */
export async function loadSetting(importSetting: any, params = {}) {
  let exportSetting = (await importSetting).default

  if (isFunction(exportSetting)) {
    return await exportSetting(params)
  } else if (isObject) {
    return exportSetting
  } else {
    return {}
  }
}

/**
 * 设置环境变量
 */
function setSetting(config: Setting, nodeEnv) {
  let devConfig = cloneDeep(config)
  for (let key in devConfig.env) {
    if (key !== nodeEnv) delete devConfig.env[key]
  }
  delete devConfig.env[nodeEnv]?.COS?.put
  delete devConfig.env[nodeEnv]?.LANUCH
  fs.writeFileSync(path.resolve(getRootPath(), 'setting/setting.json'), JSON.stringify(devConfig), 'utf-8')
}

/**
 * 导出目录下所有文件的导出内容
 * @param targetDirectory 指定目录
 * @param fileFormat 目录下的文件格式export
 * @returns
 */
export function getExportedContentFromRouterFiles(targetDirectory, fileFormat) {
  return new Promise((resolve, reject) => {
    fs.readdir(targetDirectory, { withFileTypes: true }, (err, files) => {
      if (err) {
        return reject(err)
      }

      const exportedContents = []

      let processedCount = 0

      files.forEach((item) => {
        const filePath = path.join(targetDirectory, item.name)

        if (item.isDirectory()) {
          getExportedContentFromRouterFiles(filePath, fileFormat)
            .then((contents: any) => {
              exportedContents.push(...contents)
              processedCount++

              if (processedCount === files.length) {
                resolve(exportedContents)
              }
            })
            .catch(reject)
        } else if (item.isFile() && item.name.endsWith(fileFormat)) {
          fs.readFile(filePath, 'utf8', async (err, content) => {
            if (err) {
              return reject(err)
            }
            const nameMatch = content.match(/name:\s*['"](.*?)['"]/)
            const pathMatch = content.match(/path:\s*['"](.*?)['"]/)
            const metaMatch = content.match(/meta:\s*{([^}]*)}/)
            let metaObject = {}
            if (metaMatch) {
              const metaString = metaMatch[1]
              const metaValues = metaString.split(',').map((item) => {
                const [key, value] = item.split(':').map((part) => part.trim())
                if (!key || !value) {
                  return {}
                } else {
                  return { [key]: value.replace(/['"]/g, '') }
                }
              })

              metaObject = Object.assign({}, ...metaValues)
            }

            const name = nameMatch ? nameMatch[1] : null
            const path = pathMatch ? pathMatch[1] : null
            exportedContents.push({ filePath, content: { name, path, meta: metaObject } })
            processedCount++
            if (processedCount === files.length) {
              resolve(exportedContents)
            }
          })
        } else {
          processedCount++

          if (processedCount === files.length) {
            resolve(exportedContents)
          }
        }
      })
    })
  })
}

/**
 * 添加type类型
 * @param targetFilePath
 * @param routerType
 */
export async function updateTargetFile(targetFilePath, routerType) {
  try {
    const data = await readFile(targetFilePath, 'utf8')
    const updatedData = data.replace(/export\s+interface\s+RouterType\s*{[^}]*}/, routerType)
    await writeFile(targetFilePath, updatedData || routerType, 'utf8')
    console.log('更新成功router.d.ts配置文件。')
  } catch (error) {
    console.error('更新失败router.d.ts配置文件：', error)
  }
}
