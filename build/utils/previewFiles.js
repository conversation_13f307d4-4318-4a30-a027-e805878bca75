import { glob } from 'glob'
import path from 'path'
const pathUrl = '/public/preview'
const ROOT_PATH = path.resolve() + pathUrl
const filesH5 = glob.sync(ROOT_PATH + '/**')
export const filesPreviewNames = () => {
  const arr = []
  filesH5.forEach((item) => {
    if (item) {
       arr.push(path.resolve(item).replace(path.resolve(process.cwd(), 'public'), '').substring(1))
    }
  })
  return arr
}
