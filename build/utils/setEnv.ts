// 设置env全局变量
import { ModeType, SettingEnv } from '../../types'
import { getSetting } from './method'

export async function setEnv(mode: string): Promise<Record<'import.meta.env.VITE_VAR', SettingEnv>> {
  let _mode: ModeType
  if (mode) {
    _mode = mode.split('-')[0] as ModeType
    let config = await getSetting(mode)
    return {
      'import.meta.env.VITE_VAR': config.env[_mode]
    }
  } else {
    console.error('启动命令 mode 错误')
    process.exit()
  }
}
