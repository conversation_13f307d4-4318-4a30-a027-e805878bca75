const COLOR_CODE = {
  black: 30,
  red: 31,
  green: 32,
  yellow: 33,
  blue: 34,
  purple: 35,
  cyan: 36,
  white: 37
}

const baseColor = (option: any = {}) => {
  const { color = 'white', colorBg = '' } = option
  const color_bg_code = COLOR_CODE[colorBg] ? COLOR_CODE[colorBg] + 10 : 0
  const color_code = COLOR_CODE[color] || 0
  return `\x1b[${color_bg_code};${color_code}m`
}

const CLEAR_STYLE_CODE = '\x1b[0m'
const BASE_COLOR_MAP = {
  INFO_TAG_COLOR: '\x1b[44;30m',
  INFO_MSEEAGE_COLOR: '\x1b[0;34m',
  WARN_TAG_COLOR: '\x1b[43;30m',
  WARN_MSEEAGE_COLOR: '\x1b[0;33m',
  ERROR_TAG_COLOR: '\x1b[41;30m',
  ERROR_MSEEAGE_COLOR: '\x1b[0;31m',
  LOG_TAG_COLOR: '\x1b[42;30m',
  LOG_MSEEAGE_COLOR: '\x1b[0;32m'
}

const baseMessage = (type, message) =>
  `${BASE_COLOR_MAP[`${type}_TAG_COLOR`]}${type}${
    BASE_COLOR_MAP[`${type}_MSEEAGE_COLOR`]
  } ${message}${CLEAR_STYLE_CODE}`

class Logger {
  info(message) {
    console.log(baseMessage('INFO', message))
  }

  warn(message) {
    console.log(baseMessage('WARN', message))
  }

  error(message) {
    console.log(baseMessage('ERROR', message))
  }

  log(message) {
    console.log(baseMessage('LOG', message))
  }

  logger(message, option: any = {}) {
    const { tagName = '', tagColor = 'black', tagColorBg = 'white' } = option
    console.log(
      `${baseColor({
        colorBg: tagColorBg,
        color: tagColor
      })}${tagName.toLocaleUpperCase()}${baseColor(option)}${tagName && ' '}${message}`
    )
  }
}

export default new Logger()
