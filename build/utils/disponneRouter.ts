import { cloneDeep } from 'lodash-es'
import { RouterMenu } from '../../types'
/**
 * 摊平路由
 * @param {RouterMenu[]} data
 * @returns
 */
export function flattenRouterData(data: RouterMenu[]): RouterMenu[] {
  const flattenedData: RouterMenu[] = []

  data.forEach((item) => {
    const flattenedItem: RouterMenu = {
      name: item.name,
      path: item.path,
      component: item.component,
      meta: item.meta,
      redirect: item.redirect,
      mode: item.mode,
      isBase: Boolean(item.isBase)
    }

    flattenedData.push(flattenedItem)

    if (item.children) {
      flattenedData.push(...flattenRouterData(item.children))
    }
  })

  return flattenedData
}

type TreeNode = { filePath: string; content: RouterMenu; children?: Array<TreeNode> }

/**把获取的文件转化为树状图 */
export function buildRouterTree(data: Array<TreeNode>): RouterMenu[] {
  const _data = cloneDeep(data)
  const tree: TreeNode = {
    filePath: '/',
    content: {} as RouterMenu,
    children: []
  }

  _data.forEach((entry) => {
    const filePathLevels = entry.filePath.split('/').filter((level) => level !== '')

    let currentLevel = tree

    filePathLevels.forEach((level) => {
      if (!currentLevel.children) {
        currentLevel.children = []
      }
      const existingNode = currentLevel.children.find((node) => node.filePath === `/${level}`)
      if (!existingNode) {
        const newNode: TreeNode = {
          filePath: `/${level}`,
          content: {} as RouterMenu,
          children: []
        }
        currentLevel.children.push(newNode)
        currentLevel = newNode
      } else {
        currentLevel = existingNode
      }
    })

    currentLevel.content = entry.content
  })

  return processData(tree.children || [], '/')
}
/**处理树状图为路由模式 */
function processData(data: any[], parentPath: string): RouterMenu[] {
  const processedData: RouterMenu[] = []

  data.forEach((item) => {
    const content = item.content
    const processedItem: RouterMenu = {
      name: content.name,
      path: parentPath + content.path,
      component: content.component,
      meta: content.meta,
      mode: content.mode,
      isBase: Boolean(content.isBase)
    }
    if (content.redirect) {
      processedItem.redirect = content.redirect
    }

    if (item.children && item.children.length > 0) {
      processedItem.children = processData(item.children, processedItem.path + '/')
    }

    processedData.push(processedItem)
  })

  return processedData
}
