import { readdir as _readdir, statSync, readFileSync, writeFileSync, existsSync } from 'fs'
import path, { normalize } from 'path'
import { promisify } from 'util'
import { getRootPath } from '.'

// 使用 promise 化的 fs.readdir 函数
const readdir = promisify(_readdir)

// 读取目标文件夹路径
const folderPath = './src/views'
let arr = []
let str = ''

async function getRouterList(project) {
  try {
    await traverseDirectory(folderPath, project)
  } catch (error) {
    console.error('模块加载错误:', error)
  }
  str += `export default [\n${arr.join(',\n')}\n]`
  try {
    let filePath = path.resolve(getRootPath(), 'setting/router.ts')
    console.log('走着喊着政治智慧', filePath)

    if (!existsSync(filePath)) {
      try {
        writeFileSync(filePath, str)
      } catch (err) {
        console.error('创建文件失败：', err)
      }
    } else {
      writeFileSync(filePath, str)
    }
  } catch (error) {
    console.error('error')
  }
}

async function traverseDirectory(directory, project) {
  const files = await readdir(directory)
  for (const file of files) {
    const filePath = path.join(directory, file)
    const stats = statSync(filePath)
    if (stats.isDirectory()) {
      await traverseDirectory(filePath, project) // 递归处理子目录
    } else if (file.endsWith('index.router.ts')) {
      await handleIndexRouterFile(filePath, project)
    }
  }
}

function generateRandomString(length) {
  let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let result = ''

  for (let i = 0; i < length; i++) {
    let randomIndex = Math.floor(Math.random() * characters.length)
    result += characters.charAt(randomIndex)
  }

  return result
}

async function handleIndexRouterFile(filePath, project) {
  // 读取文件内容
  const fileContent = readFileSync(filePath, 'utf8')

  const sceneRegex = /scene:\s*\[([\s\S]*?)\]/
  const match = fileContent.match(sceneRegex)
  if (!match || (match.length > 1 && match[1].trim().includes(project))) {
    if (match) console.log(match[1].trim(), fileContent)
    let key = generateRandomString(6)
    str += `import ${key} from '${filePath.replace(/\\/g, '/').replace('src', '../src')}'\n`
    arr.push(`
    {
      filePath: '${filePath.replace('index.router.ts', '').replace(/\\/g, '/').slice(0, -1).replace('src/views', '')}',
      content: ${key}
    }`)
  }
}

export default getRouterList
