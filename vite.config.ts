import { defineConfig, splitVendorChunkPlugin } from 'vite'
import path from 'path'
import { setEnv, setupVitePlugins, setProxy } from './build'
import { ModeType } from './types'
import { cloneDeep } from 'lodash-es'

const resolve = (dir: string) => path.resolve(__dirname, dir)

export default defineConfig(async ({ mode }) => {
  const env = (await setEnv(mode as ModeType))['import.meta.env.VITE_VAR']
  const timestamp = env.NODE_ENV == 'prod' ? '' : new Date().getTime()

  let backupEnv = cloneDeep(env)
  delete backupEnv.COS?.put
  console.log('当前环境变量：', mode, backupEnv)
  return {
    define: {
      ...{
        'import.meta.env.VITE_VAR': backupEnv,
        'process.env': backupEnv
      }
    },
    plugins: await setupVitePlugins(env, mode),
    server: {
      host: '0.0.0.0',
      port: 3010,
      open: false,
      strictPort: false,
      https: false,
      proxy: await setProxy(mode as ModeType)
    },
    build: {
      minify: 'terser',
      cssCodeSplit: false,
      terserOptions: {
        compress: {
          drop_console: env.NODE_ENV === 'prod',
          drop_debugger: env.NODE_ENV === 'prod'
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: `static/js/[name]-[hash]${timestamp}.js`,
          entryFileNames: `static/js/[name]-[hash]${timestamp}.js`,
          assetFileNames: `static/[ext]/[name]-[hash]${timestamp}.[ext]`,
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
            if (path.normalize(id).includes(path.normalize('src/components/ui/common'))) {
              return 'common-components'
            }
          }
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          javascriptEnabled: true,
          additionalData: `$imgSrc: "${env.NODE_OSS_URL}";`
        }
      }
    },
    resolve: {
      alias: {
        '@': resolve('src'),
        '@ng_visualization': resolve('src/views/workbench/landingPage/LandingPageEditor/ng_visualization'),
        '@build': resolve('build'),
        '@setting': resolve('setting')
      }
    }
  }
})
