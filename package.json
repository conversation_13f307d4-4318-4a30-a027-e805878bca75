{"name": "bg-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev-jf", "build": "vite build --mode dev-jf", "build:test": "vite build --mode test-jf", "build:prod": "vite build --mode prod-jf", "build:release": "vite build --mode release-jf", "preview": "vite preview --mode dev-jf", "lint": "npx eslint . --fix", "prettier": "prettier --write ."}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/vue": "^4.1.1", "@surely-vue/table": "^4.1.12", "@types/ali-oss": "^6.16.11", "@vant/area-data": "^2.0.0", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.0.3", "axios": "^1.5.1", "colord": "^2.9.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "default-passive-events": "^2.0.0", "echarts": "^5.4.3", "glob": "^10.3.10", "html2canvas": "^1.4.1", "imageinfo": "^1.0.4", "lodash-es": "^4.17.21", "moment": "^2.29.4", "pinia": "^2.1.6", "uuid": "^3.4.0", "vant": "^4.9.19", "vue": "3.3.4", "vue-clipboard3": "^2.0.0", "vue-router": "^4.2.4", "vuedraggable": "4.1.0"}, "devDependencies": {"@alicloud/cdn20180510": "3.1.2", "@alicloud/openapi-client": "0.4.4", "@alicloud/tea-util": "1.4.7", "@babel/core": "^7.23.0", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/preset-env": "^7.22.20", "@babel/preset-typescript": "^7.23.0", "@types/crypto-js": "^4.1.2", "@types/lodash-es": "^4.17.8", "@types/node": "^20.6.0", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "@unocss/vite": "^0.55.0", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/babel-plugin-jsx": "^1.1.5", "ali-oss": "^6.19.0", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "cos-nodejs-sdk-v5": "2.12.4", "crypto": "^1.0.1", "element-plus": "^2.3.12", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "moment": "^2.29.4", "naive-ui": "^2.34.4", "picocolors": "^1.0.0", "png-to-ico": "2.1.8", "prettier": "^3.0.3", "rollup-plugin-babel": "^4.4.0", "sass": "^1.64.2", "sharp": "0.32.4", "ssh2": "^1.15.0", "tencentcloud-sdk-nodejs-cdn": "4.0.675", "ts-migrate": "^0.1.35", "ts-node": "^10.9.1", "typescript": "^5.0.2", "unocss": "^0.54.1", "unplugin-icons": "^0.16.5", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.5"}}