{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "skipLibCheck": true,
    "moduleResolution": "Node",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "strictPropertyInitialization": false,
    "experimentalDecorators": true,
    // 路径别名
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "types/*": [
        "types/*"
      ],
      "@build/*": [
        "build/*"
      ],
      "@setting/*": [
        "setting/*"
      ]
    },
    // 提示
    "types": [
      "vue",
      "vite/client"
    ],
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "types/**/*.d.ts",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "setting/**/*.ts",
    "setting/*.ts",
    "vue-shim.d.ts"
  ],
  "exclude": [
    "node_modules"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}