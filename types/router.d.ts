import { Component, VNode } from 'vue'
import { RouterType } from './cache/routerType'
import { RouteRecordRaw } from 'vue-router'
import { SettingEnv } from './setting'

export type RouterMap = Map<RouterType['name'], RouterMenu>
type TitleComponentReturn = () => VNode | Array<VNode>
interface RouterMeta {
  /**
   * 菜单名称 会显示到浏览器头部
   */
  title: string

  /**
   * 菜单名称 组件样式方式显示
   */
  titleComponent?: ((title: String) => TitleComponentReturn) | TitleComponentReturn

  /**
   * 图标
   */
  icon?: string | Component

  /**
   * 是否为跳转链接 跳转链接点击直接跳转页面
   */
  isLink?: boolean

  /**
   * 是否隐藏 基本上菜单页都是要隐藏的不在侧边栏显示
   * 隐藏的菜单不受权限控制，即使没有权限路由也会返回回来
   */
  isHide?: boolean

  /**
   * 激活的父级菜单，特殊状态下，需要激活非父级的菜单
   * 特殊的还可以使用Router
   */
  parent?: RouterType['name']

  /**
   * 不需要任何授权
   */
  noAuth?: boolean

  /**
   * 权限码 走后台获取的
   */
  code?: string
  /**
   * 弹框打开
   */
  dialog?: string | number | any

  /**
   * 是否需要缓存
   */
  keepAlive?: boolean
}

/**
 * 路由菜单
 */
interface RouterMenu {
  /**
   * 组件唯一标识
   */
  name: string

  /**
   * 组件访问路径
   */
  path: string

  /**
   * 组件
   */
  component: Component | 'self'

  /**
   * 额外内容
   */
  meta: RouterMeta

  /**
   * 属于那个项目
   */
  scene?: Array<SettingEnv['PROJECT']>

  /**
   * 重定向路由，当前组件为router-view容器的时候需要使用的
   */
  redirect?: RouterType['path']

  /**
   * 是否为一直存在的路由
   * 一直存在的路由是不会在侧边栏显示
   */
  isBase?: boolean

  /**
   *进入页面之前处理 可以用来动态控制当前应该显示那个父级，
   */
  beforeEnter?: RouteRecordRaw['beforeEnter']

  /**
   * 模式
   */
  mode?: 'Layout' | 'Empty'

  /**
   * 子级
   */
  children?: RouterMenu[]

  /**
   * 排序
   */
  sort?: number
}

/**
 * 侧边栏需要的数据
 */
interface MenuData {
  name: RouterType['name']
  path: RouterType['path']
  title: string
  meta: RouterMeta
  icon: string | Component
  isHide: boolean
  parent: RouterType['name'] | null
  children: Array<MenuData> | null
}
