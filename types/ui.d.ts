import { PropType } from 'vue'
import { MapEventsKey, MapPropsKey, MapSlotsKey } from './utils'

/**ui组件库需要参数 */
export declare namespace UI {
  export type UiCompProps = { [key: string]: { type: any; default: any } }
  export type UiCompEvents = { [key: string]: (...args: any) => any }
  export type UiCompSlots = { [key: string]: any }
  /**组件格式 */
  export interface UiComp {
    props: UiCompProps
    events: UiCompEvents
    slots?: UiCompSlots
  }
  /**
   * 组件属性映射
   * 逻辑props,events，slots都有一个map，map的作用就是为了映射组件库里面的值
   * dispone这个是为了处理父组件传递过来的值不是当前组件支持的，这样的情况下就需要把数据处理过后传递到组件内
   * slot有点特殊，如果定义的slot组件里面不支持的情况下，需要把组件定义一个插槽单独写出来通过dispone返回指定类型的数据
   */
  export interface UiCompMap<T extends UiComp = any> {
    props: MapPropsKey<T['props']>
    events: MapEventsKey<T['events']>
    slots: MapSlotsKey<T['slots']>
  }

  /**Button组件 */
  export interface Button {
    props: {
      /**按钮的 DOM 的 type 属性 */
      'attr-type': {
        type: PropType<'button' | 'submit' | 'reset'>
        default: 'button'
      }
      /**按钮是否禁用 */
      disabled: {
        type: PropType<boolean>
        default: false
      }
      /**按钮的类型 */
      type: {
        type: PropType<'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error'>
        default: 'default'
      }
    }
    events: {
      /**点击事件 */
      click: ($event: Event) => void
    }
    // 逻辑 接受到父组件传递过来的slot值后，传入组件库组件里面，然后
    slots: {
      /**默认插槽 */
      default: {}
      /**噶傻瓜 */
      item: {
        /**safaf */
        a: string
        b: number
      }
    }
  }
}
