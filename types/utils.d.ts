import { PropType } from 'vue'
import { UI } from '.'
export type ExtractPropType<T> = T extends PropType<infer U> ? U : never
type ArgumentNames<T> = T extends (...args: infer U) => any ? U[number] : never

export type MapPropsKey<T> = {
  [K in keyof T]:
    | {
        map: string
        dispone?: (params: ExtractPropType<T[K]['type']>) => any
      }
    | string
}

export type MapEventsKey<T> = {
  [K in keyof T]:
    | {
        map: string
        // TODO 这个地方返回需要限制现在类型有问题
        dispone?: (...args: Parameters<T[K]>) => any
      }
    | string
}

export type MapSlotsKey<T> = {
  [K in keyof T]:
    | {
        map: string
        // TODO 这个地方返回需要限制现在类型有问题
        dispone?: (params: { [key in keyof T[K]]: any }) => T[K]
      }
    | string
}
/**深层次转换类型为可选 */
export type RecursivePartial<T> = {
  [P in keyof T]?: T[P] extends object ? RecursivePartial<T[P]> : T[P]
}
