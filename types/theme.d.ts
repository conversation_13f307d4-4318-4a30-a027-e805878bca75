import { type } from 'os'

/**
 * 主题配置文件
 */
export interface ThemeConfig {
  /**
   * ui组件库，选择使用那个，时间有限暂时只支持antd
   */
  uiComponents: 'element' | 'naive' | 'antd'

  /**
   * 布局方法，每个template应该都要支持layout
   * around 左右布局 左边侧边栏，logo在侧边栏里面，左右分离
   * upAndDown 上下布局 logo在header里面 侧边栏和视图区域分享下面一块区域
   * top 侧边栏在顶部
   * minSidebar 小型侧边栏 一级侧边栏一直显示，其它的折叠起来支持固定，总体固定为侧边栏的宽度
   * sidebarFullHeader 侧边栏分为两部分 一级侧边栏在header里面,其它的在侧边栏
   */
  mode: 'around' | 'upAndDown' | 'sidebarFullHeader' | 'minSidebar' | 'top'

  /**
   * 页面切换动画
   * fade-slide 滑动
   * fade 消退
   * fade-bottom 底部消退
   * fade-scale 缩放消退
   * zoom-fade 渐变
   * zoom-out 闪现
   */
  pageAnimation: 'fade-slide' | 'fade' | 'fade-bottom' | 'fade-scale' | 'zoom-fade' | 'zoom-out' | 'none'

  /**布局 */
  layout: ThemeConfigLayout

  /**
   * 全局字体大小 默认值14
   */
  fontSize: number

  /**
   * 行高 默认值1.6
   */
  lineHeight: number

  /**
   * 内外间距
   */
  space: 16

  /**
   * 全局圆角 默认值4
   */
  borderRadius: number

  /**
   * 显示组件高度 默认值34
   */
  height: number

  /**
   * 色彩集合
   */
  colors: ThemeConfigColor
  /**
   * 多大为手机端
   */
  isMobileWidth: number
}
/**
 * 色彩集合
 */
export interface ThemeConfigColor {
  /**主题色 */
  primary: string
  /**信息色 */
  info: string
  /**成功色 */
  success: string
  /**警告 */
  warning: string
  /**错误 */
  error: string
  /**文本色彩 */
  textColor: string
}
/**布局 */
export interface ThemeConfigLayout {
  /**
   * 头部高度 默认值60
   */
  headerHeight: number
  /**
   * 侧边栏宽度 默认值200
   */
  sidebarWidth: number
  /**
   * 是否显示标签
   */
  isTag: boolean
  /**
   * 标签高度
   */
  tagHeight?: number
  /**
   * 小型侧边栏宽度 默认90
   */
  minSidebarWidth: number
  /**
   * 折叠起来的侧边栏宽度 默认64
   */
  foldSidebarWidth: number
  /**
   * 全局背景色
   */
  layoutBgColor?: string
  /**
   * 侧边栏目背景色
   */
  menuBgColor?: string
  /**
   * 头部背景色
   */
  headerBgColor?: string
  /**
   * 主要视图区域显示背景色
   */
  mainBgColor?: string
  /**
   * 主要视图区域间距
   */
  mainPadding?: number | string
}

export type CssVarAction = {
  scene: string
  handler: (value: any) => string | number
}

export type CssVar = {
  value: string | number | undefined
  type?: 'size' | 'color'
  action: CssVarAction[]
}
type AgeStringify<T> = {
  [K in keyof T]: CssVar
}
export interface NaiveCssVar
  extends AgeStringify<ThemeConfigLayout>,
    AgeStringify<ThemeConfigColor>,
    AgeStringify<ThemeConfig> {
  [key: string]: CssVar
}

export type BaseCssVarAction = (themeConfig: ThemeConfig) => Partial<NaiveCssVar>
