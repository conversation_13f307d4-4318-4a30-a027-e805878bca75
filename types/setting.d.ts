// 项目的配置项

import { type } from 'os'
import { ThemeConfig } from '.'
import { FormatEnum, AvailableFormatInfo } from 'sharp'

export type ModeType = 'dev' | 'test' | 'release' | 'prod'
export interface SettingEnv {
  /**
   * 启动命令
   */
  LANUCH: [SettingEnv['NODE_ENV'], SettingEnv['PROJECT']]
  /**
   * 当前环境
   */
  NODE_ENV: ModeType
  /**
   * 当前使用的为那个项目
   */
  PROJECT: 'jf'
  /**
   * 使用那个模板 对应的是layout文件下的文件夹
   */
  LAYOUT: 'template1' | 'template2'
  /**
   * storge存储超时时间
   */
  STORGE_TIME_OUT: number
  /**
   * 页面部署地址
   */
  BASE_URL: string
  /**
   * 接口请求地址
   */
  API_URL: string
  /**
   * node接口请求地址
   */
  NODE_API_URL: string
  /**
   * 落地页使用资源地址
   */
  NODE_OSS_URL: string
  /**
   * 是否开发代理
   */
  IS_PROXY?: boolean
  /**
   * 开始代理的时候 通用的请求头
   */
  PROXY_URI?: string | Array<string>
  /**
   * icon开头
   */
  ICON_ID: string
  /**
   * 项目title
   */
  TITLE: string

  /**
   * 登录腾讯验证码appid
   */
  CAPTCHAAPPId: string
  /**
   * 版权
   */
  PROPERTY_RIGHTS: string

  /**
   * 上传类型
   */
  UPLOAD_TYPE?: 'oss' | 'cos' | 'ssh'
  /**
   * 高德key
   */
  AMAP_KEY?: string
  /**
   *
   */
  /**
   * 打包上传cos配置
   */
  COS: {
    /**
     * COS加速域名访问
     */
    url: string

    /**
     * 访问文件夹
     */
    path: string

    /**
     * 头像库地址
     */
    headImgDir: string

    /**
     * 上传配置
     */
    put?: {
      buket: string
      region: string
      secretId: string
      secretKey: string
      // ssh上传配置
      SSH_USERNAME?: string
      SSH_HOST?: string
      SSH_PORT?: string
      SSH_KEY?: string
      SSH_DIR?: string

      refreshSecretId?: string
      refreshSecretKey?: string
    }
  }

  /**
   * 测试文件使用环境变量提供的
   */
  TESTLOGO?: string
}
export type SettingFileType = keyof FormatEnum | AvailableFormatInfo
export interface SettingFiles {
  name: SettingFileNames
  type: FileType | 'ico'
  baseDir?: string
  origin?: keyof SettingEnv | string
  assign: string
  w: number
  h: number
}
export type SettingFileNames =
  | 'logo'
  | 'h5_logo'
  | 'favicon'
  | 'kf_logo'
  | 'logo_orange'
  | 'cache/logo'
  | 'cache/h5_logo'
  | 'cache/favicon'
  | 'cache/kf_logo'
  | 'cache/logo_orange'

export interface Setting extends ThemeConfig {
  /**环境变量 */
  env: {
    [key in ModeType]?: SettingEnv
  }
  /**文件 */
  files: Array<SettingFiles>

  /**
   * 外部链接
   */
  exetrnal?: {
    css?: Array<{ src: string }>
    js?: Array<{ src: string }>
  }
}
